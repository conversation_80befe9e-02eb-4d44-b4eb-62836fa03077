########################################################################################################################
#### PHP BASE IMAGE
########################################################################################################################
FROM php:8.2-fpm as php-base

# php extension installed with mlocati/docker-php-extension-installer https://github.com/mlocati/docker-php-extension-installer
ADD https://github.com/mlocati/docker-php-extension-installer/releases/latest/download/install-php-extensions /usr/local/bin/

RUN set -x &&\
    chmod +x /usr/local/bin/install-php-extensions && sync && \
    install-php-extensions redis &&\
    install-php-extensions yaml &&\
    install-php-extensions gd &&\
    install-php-extensions pdo_mysql &&\
    install-php-extensions intl &&\
    install-php-extensions mysqli &&\
    install-php-extensions zip &&\
    install-php-extensions soap &&\
    install-php-extensions bcmath &&\
    install-php-extensions sockets &&\
    install-php-extensions apcu &&\
    install-php-extensions opcache

RUN set -x &&\
  apt-get -y update &&\
  apt-get -y install gnupg zip gzip libzip-dev wget

# Add user
RUN usermod -u 1000 www-data

RUN mkdir -p /var/cache/symfony \
    && chown -R www-data:www-data /var/cache/symfony \
    && chown -R www-data:www-data /var/www

EXPOSE 9000
CMD ["php-fpm"]

########################################################################################################################
#### PHP DEV IMAGE
########################################################################################################################
FROM php-base as php-dev

# Install PHPCS
RUN install-php-extensions @composer &&\
    install-php-extensions xdebug

# Install Symfony client
RUN set -x &&\
  apt-get -y update &&\
  wget https://get.symfony.com/cli/installer -O - | bash &&\
  mv /root/.symfony5/bin/symfony /usr/local/bin/symfony

# Configure composer home dir
RUN mkdir /usr/local/composer
ENV COMPOSER_HOME="/usr/local/composer"

# Install PHPCS
RUN composer global require 'squizlabs/php_codesniffer ~3.6'
RUN composer global config --no-plugins allow-plugins.dealerdirect/phpcodesniffer-composer-installer true
RUN composer global require 'dealerdirect/phpcodesniffer-composer-installer'
RUN composer global require 'pheromone/phpcs-security-audit'

# Install Psalm
RUN composer global require 'vimeo/psalm ^4.13' -W
RUN composer global require 'psalm/plugin-symfony ^3' -W

# Register composer vendor bin directory.
ENV PATH=$PATH:$COMPOSER_HOME/vendor/bin/

RUN cp /usr/local/etc/php/php.ini-development /usr/local/etc/php/php.ini \
    && sed -i 's/memory_limit = 128M/memory_limit = 256M/' /usr/local/etc/php/php.ini \
    && chown -R www-data:www-data /usr/local/composer
