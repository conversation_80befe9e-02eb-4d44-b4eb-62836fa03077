# Xdebug Configuration for Station One

This document provides comprehensive instructions for using Xdebug with the Station One Symfony 4 project in Docker.

## 🎯 Overview

Xdebug has been successfully configured for the Station One project with the following setup:
- **Xdebug Version**: 3.4.1
- **Debug Mode**: Enabled
- **Port**: 9003 (Xdebug 3 default)
- **IDE Key**: PHPSTORM
- **Host**: host.docker.internal

## 🚀 Quick Start

1. **Start the development environment:**
   ```bash
   make start
   ```

2. **Verify Xdebug is working:**
   - Access http://localhost/xdebug_verification.php in your browser
   - Check that all status indicators are green

3. **Configure your IDE** (see IDE-specific instructions below)

4. **Start debugging** by setting breakpoints and accessing your application

## ⚙️ Configuration Details

### Environment Variables
The following Xdebug environment variables are configured in `.dev-env/docker-compose/.env`:

```bash
XDEBUG_MODE=debug
XDEBUG_START_WITH_REQUEST=yes
XDEBUG_CLIENT_HOST=host.docker.internal
XDEBUG_CLIENT_PORT=9003
XDEBUG_IDEKEY=PHPSTORM
XDEBUG_LOG_LEVEL=0
```

### Docker Configuration
- **Container**: `station-one-symfony4`
- **PHP Image**: `${PROJECT_REGISTRY}/php-dev:8.2`
- **Xdebug Config File**: `.dev-env/docker-images/php-8.2/xdebug.ini`
- **Host Mapping**: `host.docker.internal:host-gateway`

## 🔧 IDE Setup Instructions

### PhpStorm

1. **Configure Debug Settings:**
   - Go to `File → Settings → PHP → Debug`
   - Set **Xdebug port** to `9003`
   - Check **"Can accept external connections"**
   - Uncheck **"Force break at first line when no path mapping specified"**

2. **Configure Server:**
   - Go to `PHP → Servers`
   - Click **"+"** to add a new server
   - **Name**: `station-one`
   - **Host**: `localhost`
   - **Port**: `80`
   - **Debugger**: `Xdebug`
   - Check **"Use path mappings"**
   - Map your local `symfony4` folder to `/var/www/symfony4`

3. **Start Debugging:**
   - Click the **phone icon** in the toolbar to start listening
   - Set breakpoints in your code
   - Access your application in the browser

### VS Code

1. **Install PHP Debug Extension:**
   ```bash
   ext install xdebug.php-debug
   ```

2. **Create `.vscode/launch.json`:**
   ```json
   {
       "version": "0.2.0",
       "configurations": [
           {
               "name": "Listen for Xdebug",
               "type": "php",
               "request": "launch",
               "port": 9003,
               "pathMappings": {
                   "/var/www/symfony4": "${workspaceFolder}/symfony4"
               }
           }
       ]
   }
   ```

3. **Start Debugging:**
   - Press `F5` or go to `Run → Start Debugging`
   - Set breakpoints and access your application

## 🧪 Testing Xdebug

### Web Interface Test
1. Access http://localhost/xdebug_verification.php
2. Verify all status indicators are green
3. Set a breakpoint on the indicated line
4. Reload the page to test breakpoint functionality

### Command Line Test
```bash
# Access the PHP container
make symfony4.console

# Check Xdebug is loaded
php -m | grep xdebug

# Verify configuration
php -i | grep -E "xdebug\.(mode|start_with_request|client_host|client_port|idekey)"

# Check environment variables
env | grep XDEBUG
```

### Symfony Console Debugging
```bash
# Debug a Symfony command
make symfony4.console
php bin/console debug:router --env=dev
```

## 🔍 Troubleshooting

### Common Issues

1. **Breakpoints not triggering:**
   - Ensure your IDE is listening on port 9003
   - Check path mappings are correct
   - Verify `host.docker.internal` resolves correctly

2. **Connection refused:**
   - Check firewall settings allow port 9003
   - Ensure Docker has access to host network
   - Try using your actual IP address instead of `host.docker.internal`

3. **Xdebug not loading:**
   - Restart containers: `make stop && make start`
   - Check container logs: `docker logs station-one-symfony4`

### Enable Debug Logging

To troubleshoot connection issues, enable Xdebug logging:

1. Edit `.dev-env/docker-compose/.env`:
   ```bash
   XDEBUG_LOG_LEVEL=7
   ```

2. Restart containers:
   ```bash
   make stop && make start
   ```

3. Check logs:
   ```bash
   make symfony4.console
   tail -f /tmp/xdebug.log
   ```

### Alternative Host Configuration

If `host.docker.internal` doesn't work, try using your actual IP:

1. Find your IP address:
   ```bash
   # Linux/Mac
   ip route show default | awk '/default/ {print $3}'

   # Or
   hostname -I | awk '{print $1}'
   ```

2. Update `.dev-env/docker-compose/.env`:
   ```bash
   XDEBUG_CLIENT_HOST=*************  # Replace with your IP
   ```

## 📁 File Structure

```
station-one/
├── .dev-env/
│   ├── docker-compose/
│   │   ├── .env                    # Main environment variables
│   │   ├── .env.symfony4          # Symfony-specific variables
│   │   └── symfony4.yml           # Docker Compose configuration
│   └── docker-images/
│       └── php-8.2/
│           └── xdebug.ini         # Xdebug PHP configuration
├── xdebug_verification.php        # Verification script
└── XDEBUG_SETUP.md               # This documentation
```

## 🔄 Maintenance

### Updating Xdebug Configuration

1. **Environment variables**: Edit `.dev-env/docker-compose/.env`
2. **PHP settings**: Edit `.dev-env/docker-images/php-8.2/xdebug.ini`
3. **Docker settings**: Edit `.dev-env/docker-compose/symfony4.yml`

After changes, restart containers:
```bash
make stop && make start
```

### Performance Considerations

- Xdebug can slow down execution significantly
- For production-like testing, disable Xdebug:
  ```bash
  # In .dev-env/docker-compose/.env
  XDEBUG_MODE=off
  ```

## 📞 Support

If you encounter issues:
1. Check the verification script at http://localhost/xdebug_verification.php
2. Review the troubleshooting section above
3. Check container logs: `docker logs station-one-symfony4`
4. Verify your IDE configuration matches the instructions

---

**Last Updated**: $(date)
**Xdebug Version**: 3.4.1
**PHP Version**: 8.2
