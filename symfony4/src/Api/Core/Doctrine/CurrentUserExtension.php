<?php

declare(strict_types=1);

namespace Api\Core\Doctrine;

use Api\Core\Entity\UserOwnedInterface;
use ApiPlatform\Doctrine\Orm\Extension\QueryCollectionExtensionInterface;
use ApiPlatform\Doctrine\Orm\Extension\QueryItemExtensionInterface;
use ApiPlatform\Doctrine\Orm\Util\QueryNameGeneratorInterface;
use ApiPlatform\Metadata\Operation;
use AppBundle\Entity\User;
use Doctrine\ORM\QueryBuilder;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

final class CurrentUserExtension implements QueryCollectionExtensionInterface, QueryItemExtensionInterface
{
    public function __construct(private readonly TokenStorageInterface $tokenStorage)
    {
    }

    public function applyToCollection(
        QueryBuilder $queryBuilder,
        QueryNameGeneratorInterface $queryNameGenerator,
        string $resourceClass,
        Operation $operation = null,
        array $context = []
    ): void {
        $this->addWhere(resourceClass: $resourceClass, queryBuilder: $queryBuilder);
    }

    public function applyToItem(
        QueryBuilder $queryBuilder,
        QueryNameGeneratorInterface $queryNameGenerator,
        string $resourceClass,
        array $identifiers,
        Operation $operation = null,
        array $context = []
    ): void {
        $this->addWhere(resourceClass: $resourceClass, queryBuilder: $queryBuilder);
    }

    private function addWhere(string $resourceClass, QueryBuilder $queryBuilder): void
    {
        if (!is_a($resourceClass, UserOwnedInterface::class, true)) {
            return;
        }

        /** @var User $user */
        $user = $this->security->getUser();
        $alias = $queryBuilder->getRootAliases()[0];
        $queryBuilder
            ->andWhere("$alias.company = :current_company")
            ->setParameter('current_company', $user->getCompany()->getId());
    }
}
