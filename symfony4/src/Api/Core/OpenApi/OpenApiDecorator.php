<?php

declare(strict_types=1);

namespace Api\Core\OpenApi;

use ApiPlatform\OpenApi\Factory\OpenApiFactoryInterface;

use ApiPlatform\OpenApi\OpenApi;
use ApiPlatform\OpenApi\Model\PathItem;
use ApiPlatform\OpenApi\Model\Parameter;


/** @codeCoverageIgnore  */
final class OpenApiDecorator implements OpenApiFactoryInterface
{
    public const HIDDEN_RESOURCE = 'hidden';

    private OpenApiFactoryInterface $decorated;

    public function __construct(OpenApiFactoryInterface $decorated)
    {
        $this->decorated = $decorated;
    }

    /**
     * @inheritDoc
     */
    public function __invoke(array $context = []): OpenApi
    {
        $openApi = ($this->decorated)($context);

        /** @var PathItem $path */
        foreach ($openApi->getPaths()->getPaths() as $key => $path) {
            if ($path->getGet() && $path->getGet()->getSummary() === self::HIDDEN_RESOURCE) {
                $openApi->getPaths()->addPath($key, $path->withGet(null));
            }
        }

        $pathItem = $openApi->getPaths()->getPath('/middleware/api/orders/last');
        if ($pathItem !== null) {
            $operation = $pathItem->getGet();
            if ($operation !== null) {
                $openApi->getPaths()->addPath('/middleware/api/orders/last', $pathItem->withGet(
                    $operation->withParameters(array_merge(
                        $operation->getParameters(),
                        [new Parameter('limit', 'query', 'Orders result limit (3 default)', false)]
                    ))
                ));
            }
        }

        /** @var \ArrayObject $schemas */
        $schemas = $openApi->getComponents()->getSecuritySchemes();
        $schemas['bearerAuth'] = new \ArrayObject([
            'type' => 'http',
            'scheme' => 'bearer',
            'bearerFormat' => 'JWT'
        ]);
        $schemas['basicAuth'] = new \ArrayObject([
            'type' => 'http',
            'scheme' => 'basic',
            'description' => 'Basic HTTP Authentication',
        ]);

        return $openApi;
    }
}
