<?php

namespace Api\Core\OpenApi;

use ApiPlatform\Metadata\Operation\PathSegmentNameGeneratorInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;

class SingularPathSegmentNameGenerator implements PathSegmentNameGeneratorInterface, LoggerAwareInterface
{

    protected LoggerInterface $logger;
    public function getSegmentName(string $name, bool $collection = true): string
    {
        // $this->logger->info("aaaa : " . print_r($name, true));
        if ($name === 'PurchaseRequest') {
            return 'purchase-request';
        } else {
            $segment = $this->dashize($name);
            if ($collection) {
                return $this->pluralize($segment);
            } else {
                return $segment;
            }
        }
    }

    private function dashize(string $string): string
    {
        return strtolower(preg_replace('~(?<=\w)([A-Z])~', '-$1', $string));
    }

    private function pluralize(string $string): string
    {
        if (substr($string, -1) == 's') {
            return $string;
        } else {
            return $string . 's';
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}

