<?php

declare(strict_types=1);

namespace Symfony\Component\DependencyInjection\Loader\Configurator;

use Api\Core\OpenApi\JwtDecorator;
use Api\Core\OpenApi\OpenApiDecorator;
use Api\Domain\Invoice\Builder\BuyerInvoicePayloadIzbergBuilder;
use Api\Domain\Invoice\Builder\InvoiceLinesPayloadIzbergBuilder;
use Api\Domain\Invoice\Builder\InvoicePayloadBuilderInterface;
use Api\Domain\Invoice\Builder\InvoicePayloadIzbergBuilder;
use Api\Domain\Invoice\Builder\VendorInvoicePayloadIzbergBuilder;
use Api\Domain\Order\Payment\PaymentMethodHandler;
use Api\Domain\Order\Service\DataOrder\GetOrderDataService;
use Api\Domain\Order\Service\DataOrder\GetOrderDataServiceInterface;

return static function (ContainerConfigurator $container) {

    $container = $container->services()
        ->defaults()
        ->autoconfigure()
        ->autowire()
    ;

    $container
        ->load('Api\\', __DIR__.'/../../../')
        ->exclude([
            __DIR__ . '/../',
        ])
    ;

    $container
        ->load('Api\\Domain\\PurchaseRequest\\Controller\\', __DIR__.'/../../../Domain/PurchaseRequest/Controller')
        ->tag('controller.service_arguments');

    $container
        ->load('Api\\Domain\\User\\Controller\\', __DIR__.'/../../../Domain/User/Controller')
        ->tag('controller.service_arguments');

    $container
        ->load('Api\\Domain\\Order\\Controller\\', __DIR__.'/../../../Domain/Order/Controller')
        ->tag('controller.service_arguments');

    $container
        ->load('Api\\Core\\Controller\\', __DIR__.'/../../../Core/Controller')
        ->tag('controller.service_arguments');

    $container->set(JwtDecorator::class)
        ->decorate('api_platform.openapi.factory')
        ->args([service(sprintf('%s.inner', JwtDecorator::class))])
    ;
    $container->set(OpenApiDecorator::class)
        ->decorate('api_platform.openapi.factory')
        ->args([service(sprintf('%s.inner', OpenApiDecorator::class))])
    ;

    $container->set(InvoicePayloadBuilderInterface::class, InvoicePayloadIzbergBuilder::class);

    $container
        ->set(VendorInvoicePayloadIzbergBuilder::class)
        ->decorate(InvoicePayloadBuilderInterface::class)
        ->args([service(sprintf('%s.inner', VendorInvoicePayloadIzbergBuilder::class))])
    ;

    $container
        ->set(BuyerInvoicePayloadIzbergBuilder::class)
        ->decorate(InvoicePayloadBuilderInterface::class)
        ->args([service(sprintf('%s.inner', BuyerInvoicePayloadIzbergBuilder::class))])
    ;
    $container
        ->set(InvoiceLinesPayloadIzbergBuilder::class)
        ->decorate(InvoicePayloadBuilderInterface::class)
        ->args([service(sprintf('%s.inner', InvoiceLinesPayloadIzbergBuilder::class))])
    ;

    $container->set(PaymentMethodHandler::class)
        ->args([
            tagged_iterator('payment.method.action')
        ])
    ;

    $container
        ->set(GetOrderDataServiceInterface::class)
        ->class(GetOrderDataService::class)
    ;
};
