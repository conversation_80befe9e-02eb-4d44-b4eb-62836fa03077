<?php

declare(strict_types=1);

namespace Api\Core\Security;

use Api\Core\Exception\AccountApiNoAccessException;
use AppBundle\Entity\User;
use Symfony\Component\Security\Core\User\UserCheckerInterface;
use Symfony\Component\Security\Core\User\UserInterface;

final class User<PERSON>piChecker implements UserCheckerInterface
{
    public function checkPreAuth(UserInterface $user): void
    {
    }

    /**
     * @param UserInterface|User $user
     */
    public function checkPostAuth(UserInterface $user): void
    {
        if (!$user->isBuyerApi() || !$user->isEnabled()) {
            throw new AccountApiNoAccessException();
        }
    }
}
