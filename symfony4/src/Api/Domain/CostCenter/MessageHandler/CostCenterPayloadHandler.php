<?php

declare(strict_types=1);

namespace Api\Domain\CostCenter\MessageHandler;

use Api\Core\Service\MiddlewarePayload\SendMiddlewarePayloadServiceInterface;
use Api\Domain\CostCenter\Builder\CostCenterPayloadDataBuilderInterface;
use Api\Domain\CostCenter\Message\CostCenterPayloadMessage;
use AppBundle\Entity\Middleware\CostCenterPayload;
use AppBundle\Repository\Payload\PayloadRepository;
use AppBundle\Services\SiteService;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
class CostCenterPayloadHandler
{
    private SendMiddlewarePayloadServiceInterface $middlewarePayloadService;
    private CostCenterPayloadDataBuilderInterface $costCenterPayloadDataBuilder;
    private SiteService $siteService;
    private PayloadRepository $payloadRepository;


    public function __construct(
        SendMiddlewarePayloadServiceInterface $middlewarePayloadService,
        CostCenterPayloadDataBuilderInterface $costCenterPayloadDataBuilder,
        SiteService                           $siteService,
        PayloadRepository                     $payloadRepository
    )
    {
        $this->middlewarePayloadService = $middlewarePayloadService;
        $this->costCenterPayloadDataBuilder = $costCenterPayloadDataBuilder;
        $this->siteService = $siteService;
        $this->payloadRepository = $payloadRepository;
    }

    public function __invoke(CostCenterPayloadMessage $message): void
    {
        $site = $this->siteService->get($message->costCenterId);

        if (!$site) {
            return;
        }

        $payloadData = $this->costCenterPayloadDataBuilder->build($site);

        $payload = CostCenterPayload::createFromData($payloadData, $message->action, $message->costCenterId);
        $payload->setUserType($site->getCompany()->getName());

        $this->middlewarePayloadService->send(
            $payload,
            $site->getCompany()->getEndpointUrl()
        );

        $this->payloadRepository->save($payload);
    }
}
