<?php

declare(strict_types=1);

namespace Api\Domain\CostCenter\MessageHandler;

use Api\Core\Service\MiddlewarePayload\SendMiddlewarePayloadServiceInterface;
use Api\Domain\CostCenter\Message\DeleteCostCenterPayloadMessage;
use App<PERSON>undle\Entity\Middleware\CostCenterPayload;
use AppBundle\Repository\Payload\PayloadRepository;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
class DeleteCostCenterPayloadHandler
{
    private SendMiddlewarePayloadServiceInterface $middlewarePayloadService;
    private PayloadRepository $payloadRepository;


    public function __construct(
        SendMiddlewarePayloadServiceInterface $middlewarePayloadService,
        PayloadRepository                     $payloadRepository
    )
    {
        $this->middlewarePayloadService = $middlewarePayloadService;
        $this->payloadRepository = $payloadRepository;
    }

    public function __invoke(DeleteCostCenterPayloadMessage $message): void
    {
        $payload = CostCenterPayload::createFromData([], $message->action, $message->costCenterId);

        $this->middlewarePayloadService->send(
            $payload,
            $message->companyEndpointUrl
        );

        $this->payloadRepository->save($payload);
    }
}
