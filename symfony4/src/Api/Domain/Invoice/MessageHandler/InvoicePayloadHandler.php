<?php

declare(strict_types=1);

namespace Api\Domain\Invoice\MessageHandler;

use Api\Core\Service\MiddlewarePayload\SendMiddlewarePayloadServiceInterface;
use Api\Domain\Invoice\Builder\InvoicePayloadBuilderInterface;
use Api\Domain\Invoice\Message\InvoicePayloadMessage;
use Api\Domain\Invoice\ValueObject\DocumentType;
use AppBundle\Entity\Middleware\InvoicePayload;
use AppBundle\Repository\CompanyRepository;
use AppBundle\Repository\Payload\PayloadRepository;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final class InvoicePayloadHandler implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    private SendMiddlewarePayloadServiceInterface $middlewarePayloadService;
    private PayloadRepository $payloadRepository;
    private CompanyRepository $companyRepository;
    private InvoicePayloadBuilderInterface $payloadBuilder;

    public function __construct(
        SendMiddlewarePayloadServiceInterface $middlewarePayloadService,
        PayloadRepository $payloadRepository,
        CompanyRepository $companyRepository,
        InvoicePayloadBuilderInterface $payloadBuilder
    ) {
        $this->middlewarePayloadService = $middlewarePayloadService;
        $this->payloadRepository = $payloadRepository;
        $this->companyRepository = $companyRepository;
        $this->payloadBuilder = $payloadBuilder;
    }

    public function __invoke(InvoicePayloadMessage $invoicePayloadMessage): void
    {
        $invoice = $invoicePayloadMessage->invoice;
        $documentType = $invoicePayloadMessage->documentType;
        $invoice->setDocumentType(DocumentType::create($documentType));
        $companyId = $invoice->getReceiver()->getId();
        $company = $this->companyRepository->getCompanyById($companyId);
        $companyName = $company->getName();
        $data = $this->payloadBuilder->build($invoice);
        ksort($data);
        $payload = InvoicePayload::createFromData(
            $data,
            sprintf('%s_%s', strtoupper($documentType), strtoupper($invoice->getStatus())),
            $invoice->getId()
        )
            ->setUserType($companyName ?? '')
        ;

        $companyEndpointUrl = $company->getEndpointUrl();

        if (null === $companyEndpointUrl) {
            $this->logger->warning(sprintf('The company endpoint url is not found for company id %d', $companyId));
            return;
        }

        $this->middlewarePayloadService->send($payload, $companyEndpointUrl);

        $this->payloadRepository->save($payload);
    }
}
