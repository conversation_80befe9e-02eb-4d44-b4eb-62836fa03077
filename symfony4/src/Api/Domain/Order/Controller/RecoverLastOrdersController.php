<?php

declare(strict_types=1);

namespace Api\Domain\Order\Controller;

use Api\Core\Bus\Query\QueryBus;
use Api\Domain\Order\UseCase\Query\RecoverLastOrders\RecoverLastOrdersQuery;
use AppBundle\Entity\User;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\HttpFoundation\JsonResponse;

final class RecoverLastOrdersController
{
    private const DEFAULT_ORDERS_LIMIT = 3;

    private QueryBus $queryBus;
    private $tokenStorage;

    public function __construct(QueryBus $queryBus, TokenStorageInterface $tokenStorage)
    {
        $this->queryBus = $queryBus;
        $this->tokenStorage = $tokenStorage;
    }

    public function __invoke(Request $request): JsonResponse
    {
        $limit = $request->query->getInt('limit', self::DEFAULT_ORDERS_LIMIT);

        $token = $this->tokenStorage->getToken();
        /** @var User|null $user */
        $user = $token ? $token->getUser() : null;
        $companyId = $user->getCompany()->getId();

        $data = $this->queryBus->ask(new RecoverLastOrdersQuery($companyId, $limit));
        return new JsonResponse($data);
    }
}
