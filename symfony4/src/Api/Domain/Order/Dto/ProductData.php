<?php

declare(strict_types=1);

namespace Api\Domain\Order\Dto;

use Symfony\Component\Validator\Constraints as Assert;

final class ProductData
{
    #[Assert\GreaterThan(value: 0)]
    public int $izbProductId;

    #[Assert\GreaterThan(value: 0)]
    public int $quantity;

    public ?string $orderLine = null;

    public ?string $buyerReference = null;
    public ?string $cartItemComment = null;

    public function __construct(
        int $izbProductId,
        int $quantity,
        ?string $orderLine = null,
        ?string $buyerReference = null,
        ?string $cartItemComment = null,
    ) {
        $this->quantity = $quantity;
        $this->izbProductId = $izbProductId;
        $this->orderLine = $orderLine;
        $this->buyerReference = $buyerReference;
        $this->cartItemComment = $cartItemComment;
    }
}
