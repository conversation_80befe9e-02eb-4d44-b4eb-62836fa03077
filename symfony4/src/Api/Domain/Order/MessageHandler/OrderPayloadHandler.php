<?php

declare(strict_types=1);

namespace Api\Domain\Order\MessageHandler;

use Api\Core\Service\MiddlewarePayload\SendMiddlewarePayloadServiceInterface;
use Api\Domain\Order\Message\OrderPayloadMessage;
use Api\Domain\Order\Status\MerchantOrderStatus;
use Api\Domain\Order\Status\Status;
use AppBundle\Entity\Middleware\OrderPayload;
use AppBundle\Repository\CompanyRepository;
use AppBundle\Repository\OrderRepository;
use AppBundle\Repository\Payload\PayloadRepository;
use AppBundle\Services\AlstomCustomAttributes;
use AppBundle\Services\OrderService;
use Open\IzbergBundle\Api\OrderApi;
use Open\IzbergBundle\Model\Item;
use Open\IzbergBundle\Model\MerchantOrder;
use Open\IzbergBundle\Model\Order;
use Symfony\Contracts\Translation\TranslatorInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final class OrderPayloadHandler
{
    private SendMiddlewarePayloadServiceInterface $middlewarePayloadService;
    private PayloadRepository $payloadRepository;
    private CompanyRepository $companyRepository;
    private AlstomCustomAttributes $customAttributes;
    private OrderApi $orderApi;
    private TranslatorInterface $translator;
    private OrderService $orderService;
    private OrderRepository $orderRepository;

    public function __construct(
        SendMiddlewarePayloadServiceInterface $middlewarePayloadService,
        PayloadRepository $payloadRepository,
        CompanyRepository $companyRepository,
        AlstomCustomAttributes $customAttributes,
        OrderApi $orderApi,
        TranslatorInterface $translator,
        OrderService $orderService,
        OrderRepository $orderRepository,
    ) {
        $this->middlewarePayloadService = $middlewarePayloadService;
        $this->payloadRepository = $payloadRepository;
        $this->companyRepository = $companyRepository;
        $this->customAttributes = $customAttributes;
        $this->orderApi = $orderApi;
        $this->translator = $translator;
        $this->orderService = $orderService;
        $this->orderRepository = $orderRepository;
    }

    public function __invoke(OrderPayloadMessage $orderCreated): void
    {
        $order = $orderCreated->order;
        $doctrineOrder = $this->orderRepository->getOrderByIzbergId($order->getId());

        if (!$doctrineOrder->isCreatedByApi()) {
            return;
        }
        $buyerOrderNumber = $doctrineOrder->getValidationNumber() ?? '';

        // Get reconciliation key from Merchant Order
        $reconciliationKey = $this->orderService->getReconciliationKeyFromOrder($order);

        $orderTranslatorKey = sprintf('orders.status.status_%s', $order->getStatus());
        /** @var \AppBundle\Entity\Order $orderEntity */
        $payload = OrderPayload::createFromData([
            'izb_order_id' => $order->getId(),
            'external_order_id' => $order->getIdNumber(),
            'order_status' => $this->translator->trans($orderTranslatorKey, [], 'AppBundle'),
            'reconciliation_key' => $reconciliationKey,
            'buyer_order_number' => $buyerOrderNumber,
        ] + $this->buildMerchantOrderData($order),
            Status::izbergCodeTOAction((int) $order->getStatus()),
            $order->getId()
        );

        $company = $this->companyRepository->getCompanyById($orderCreated->order->getUser()->getId());

        $payload->setUserType($company->getName());

        $this->middlewarePayloadService->send($payload, $company->getEndpointUrl());

        $this->payloadRepository->save($payload);
    }

    private function buildMerchantOrderData(Order $order): array
    {
        $merchantOrdersInfo = $order->getMerchantOrders()->map(function (MerchantOrder $merchantOrder): array {
            $merchantOrderOfIzberg = $this->orderApi->fetchMerchantOrderById($merchantOrder->getId());
            $merchantOrderUrlPdfAttribute = $this->customAttributes->getMerchantOrderUrlPdf();

            $merchantName = $merchantOrderOfIzberg->getMerchant()->getName();
            $merchantId = $merchantOrderOfIzberg->getMerchant()->getId();

            $cancelledItems = $this->orderService->getItemsByStatus($merchantOrderOfIzberg, Item::ITEM_CANCELLED_STATUS);
            $cancelledItemsId = array_map(
                function(Item $item) {
                    return ['izb_product_id' => (string)$item->getOfferId()];
                },
                $cancelledItems)
            ;

            return [
                'merchant_name' => $merchantName,
                'merchant_id' => $merchantId,
                'izb_merchant_order_id' => $merchantOrder->getId(),
                'merchant_order_url' => $merchantOrderOfIzberg->getAttributes()[$merchantOrderUrlPdfAttribute] ?? '',
                'transport_cost' => $merchantOrder->getShipping(),
                'merchant_order_status' => $this->translator->trans(
                    MerchantOrderStatus::izbergToLabel($merchantOrder->getStatus()),
                    [],
                    'AppBundle'
                ),
                'cancelled_items' => $cancelledItemsId,
            ];
        })->toArray();

        return ['merchant_orders' => $merchantOrdersInfo];
    }
}
