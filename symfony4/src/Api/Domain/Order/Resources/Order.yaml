resources:
  AppBundle\entity\Order:
    attributes:
      read: false
    operations:
      get_last_orders:
        class: ApiPlatform\Metadata\GetCollection
        method: 'GET'
        name: 'recover_last_orders'
        uriTemplate: /orders/last
        #        path: '/orders/last'
        controller: Api\Domain\Order\Controller\RecoverLastOrdersController
        output: Api\Domain\Order\Output\OrderOutput
        openapi:
          security:
            - bearerAuth: [ ]
            - basicAuth: [ ]
      get_item:
        class: ApiPlatform\Metadata\Get
        method: 'GET'
        name: 'get_item'
        path: '/orders/{id}'
        controller: Api\Domain\Order\Controller\GetOrderController
        output: Api\Domain\Order\Output\OrderOutput
        openapi:
          security:
            - bearerAuth: []
            - basicAuth: []
      post_collection:
        class: ApiPlatform\Metadata\Post
        method: 'POST'
        name: 'post_collection'
        path: '/orders'
        input: Api\Domain\Order\UseCase\Command\Create\CreateOrderInput
        output: Api\Domain\Order\UseCase\Command\Create\CreateOrderOutput
        messenger: 'input'
        openapi:
          security:
            - bearerAuth: []
            - basicAuth: []
