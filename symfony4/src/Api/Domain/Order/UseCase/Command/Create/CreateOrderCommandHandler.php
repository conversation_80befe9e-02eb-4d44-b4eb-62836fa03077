<?php

declare(strict_types=1);

namespace Api\Domain\Order\UseCase\Command\Create;

use Api\Core\Bus\Command\CommandHandlerInterface;
use Api\Core\Exception\InvalidArgumentException;
use Api\Domain\Order\Message\OrderPayloadMessage;
use Api\Domain\Order\Payment\PaymentMethodHandler;
use Api\Domain\Order\Service\CartProcess\CartCreateProcessInterface;
use AppBundle\Entity\Address;
use AppBundle\Entity\CreationSource\AbstractCreationSource;
use AppBundle\Entity\Order;
use AppBundle\Entity\User;
use AppBundle\Repository\OrderRepository;
use AppBundle\Repository\ShippingPointRepository;
use AppBundle\Services\OrderService;
use AppBundle\ValueObject\PaymentMethod;
use Open\FrontBundle\Form\PaymentModeSelectForm;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

#[AsMessageHandler]
final class CreateOrderCommandHandler implements CommandHandlerInterface
{
    public function __construct(
        private readonly TokenStorageInterface $tokenStorage,
        private readonly OrderService $orderService,
        private readonly OrderRepository $orderRepository,
        private readonly CartCreateProcessInterface $cartCreateProcess,
        private readonly MessageBusInterface $messageBus,
        private readonly PaymentMethodHandler $paymentMethodHandler,
        private readonly ShippingPointRepository $shippingPointRepository,
    ) {
    }

    public function __invoke(CreateOrderInput $input): Order|JsonResponse|null
    {
        $token = $this->tokenStorage->getToken();
        /** @var User|null $user */
        $currentUser = $token ? $token->getUser() : null;

        if ($currentUser === null) {
            return null;
        }

        $paymentMethod = PaymentMethod::createToLabel($input->paymentMethod);
        if ((string)$paymentMethod === PaymentModeSelectForm::PAYMENT_PRE_CARD) {
            throw new InvalidArgumentException('Creating an order is not allowed with credit card payment');
        }

        $deliveryAddressId = $input->deliveryAddressId;
        $buyerOrderNumber = $input->buyerOrderNumber;
        $accountingEmail = $this->getAccountingEmailValue(
            accountingEmail: $input->accountingEmail,
            deliveryAddressId: $deliveryAddressId,
            siteId: $input->costCenterId
        );

        $country = $currentUser->getCompany()->getMainAddress()->getCountry();
        $billingAddress = null;
        if ($inputBillingAddress = $input->billingAddress) {
            $billingAddress = new Address();
            $billingAddress->setAddress($inputBillingAddress->getAddress());
            $billingAddress->setAddress2($inputBillingAddress->getAddressComplement());
            $billingAddress->setZipCode($inputBillingAddress->getZipCode());
            $billingAddress->setCity($inputBillingAddress->getCity());
            $billingAddress->setRegionText($inputBillingAddress->getArea());
            $billingAddress->setRegionText($inputBillingAddress->getArea());
            $billingAddress->setCountry($country);
        }

        $cart = $this->cartCreateProcess
            ->withUser($currentUser)
            ->createCart($input->products, $deliveryAddressId, $buyerOrderNumber, (string)$paymentMethod, $input->documentationRequestedName)
            ->validateMinimumAmount($deliveryAddressId, $buyerOrderNumber, (string)$paymentMethod, $input->documentationRequestedName)
            ->checkoutCart(
                $deliveryAddressId,
                $paymentMethod,
                $input->totalPriceTaxIncl,
                $buyerOrderNumber,
                $accountingEmail,
                $billingAddress,
                $input->documentationRequestedName
            );
        $this->cartCreateProcess->setRequestedDocumentation($input->documentationRequestedName);
        if (!$currentUser->getCompany()->getFullAuto()) {
            return new JsonResponse(
                ["The full auto order API is not set, so the cart needs approval, please check your email"],
                Response::HTTP_OK
            );
        }
        $this->cartCreateProcess->selectedCheapestShipping();

        $cart->setCreationSource(AbstractCreationSource::SOURCE_API);

        $this->paymentMethodHandler->payment(
            $paymentMethod,
            $cart,
            $currentUser,
            $buyerOrderNumber,
            $accountingEmail
        );

        $order = $this->orderRepository->fetchByCartId($cart->getId());
        $order->setCreatedByApi(createdByApi: true);
        $order->setAccountingEmail($accountingEmail);
        if ($input->buyerOrderNumber !== null) {
            $order->setValidationNumber($input->buyerOrderNumber);
            $this->orderRepository->save($order);
        }

        $this->messageBus->dispatch(
            new OrderPayloadMessage($this->orderService->fetchOrderById($order->getIzbergId()))
        );

        return $order;
    }

    private function getAccountingEmailValue(?string $accountingEmail, int $deliveryAddressId, int $siteId): string
    {
        $deliveryAddress = $this->shippingPointRepository->byId(id: $deliveryAddressId, siteId: $siteId);

        return !empty($accountingEmail) ? $accountingEmail : $deliveryAddress->getAccountantEmail();
    }
}
