<?php

declare(strict_types=1);

namespace Api\Domain\PurchaseRequest\DataPersister;

use Api\Domain\PurchaseRequest\Input\CreatePurchaseRequestInput;
use Api\Domain\PurchaseRequest\Service\ConvertPayloadToFileObjectInterface;
use Api\Domain\PurchaseRequest\Message\CreatePurchaseRequest;
use ApiPlatform\Core\DataPersister\ContextAwareDataPersisterInterface;
use AppBundle\Entity\User;
use AppBundle\Services\PurchaseRequestService;
use BadMethodCallException;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\HttpFoundation\JsonResponse;

final class CreatePurchaseRequestPersister implements ContextAwareDataPersisterInterface
{
    private ConvertPayloadToFileObjectInterface $convertPayloadToFileObject;
    private PurchaseRequestService $purchaseRequestService;
    private TokenStorageInterface $tokenStorage;
    private MessageBusInterface $messageBus;

    public function __construct(
        ConvertPayloadToFileObjectInterface $convertPayloadToFileObject,
        PurchaseRequestService $purchaseRequestService,
        TokenStorageInterface $tokenStorage,
        MessageBusInterface $messageBus
    ) {
        $this->convertPayloadToFileObject = $convertPayloadToFileObject;
        $this->purchaseRequestService = $purchaseRequestService;
        $this->tokenStorage = $tokenStorage;
        $this->messageBus = $messageBus;
    }

    /**
     * @param CreatePurchaseRequestInput $data
     */
    public function persist($data, array $context = []): JsonResponse
    {
        // Convert the paylod in UplaodtedFile and call the messenger handler
        $purchaseRequestItems = $data->purchaseRequestItems;
        /** @var UploadedFile $uploadFile */
        $uploadFile = $this->convertPayloadToFileObject->convert($purchaseRequestItems);

        $token = $this->tokenStorage->getToken();
        /** @var User|null $user */
        $user = $token ? $token->getUser() : null;
        $purchaseRequest = $this->purchaseRequestService->uploadPurchaseRequestFromCsv($user, $uploadFile);
        foreach ($purchaseRequest->getItems() as $index => $purchaseRequestItem) {
            $this->messageBus->dispatch(new CreatePurchaseRequest(
                $user->getLocale(),
                $user->getCompany()->getEndpointUrl(),
                $index + 1,
                $purchaseRequestItem->getId()
            ));
        }
        return new JsonResponse(['purchase_request_id' => $purchaseRequest->getId()], 201);
    }

    /** @codeCoverageIgnore  */
    public function remove($data, array $context = []): never
    {
        throw new BadMethodCallException(sprintf('%s() should not be called.', __METHOD__));
    }

    public function supports($data, array $context = []): bool
    {
        return $data instanceof CreatePurchaseRequestInput;
    }
}
