<?php

declare(strict_types=1);

namespace Api\Domain\PurchaseRequest\Input;

use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

final class PurchaseRequestItemInput
{
    public ?string $buyerReference = null;

    public ?string $vendorReference = null;

    public int $quantityRequested = 0;

    public ?string $vendorName = null;

    public ?string $productName = null;

    #[Assert\Callback]
    public function validate(ExecutionContextInterface $context, $payload): void
    {
        /** @var self $input */
        $input = $context->getObject();

        if (empty($input->buyerReference) && empty($input->vendorReference)) {
            $context->buildViolation('api.purchase_request.create_required_field')
                ->atPath('buyerReference')
                ->atPath('vendorReference')
                ->addViolation()
            ;
        }
    }
}
