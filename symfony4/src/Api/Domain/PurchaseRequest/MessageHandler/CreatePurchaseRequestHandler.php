<?php

namespace Api\Domain\PurchaseRequest\MessageHandler;

use Api\Core\Service\MiddlewarePayload\SendMiddlewarePayloadServiceInterface;
use Api\Domain\PurchaseRequest\Message\CreatePurchaseRequest;
use Api\Domain\PurchaseRequest\Service\PurchaseRequestItemPayloadInterface;
use AppBundle\Repository\Payload\PayloadRepository;
use Psr\Log\LoggerAwareTrait;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
class CreatePurchaseRequestHandler
{
    use LoggerAwareTrait;

    private PurchaseRequestItemPayloadInterface $purchaseRequestItemPayload;
    private HttpClientInterface $httpClient;
    private SendMiddlewarePayloadServiceInterface $middlewarePayloadService;
    private PayloadRepository $payloadRepository;

    public function __construct(
        PurchaseRequestItemPayloadInterface   $purchaseRequestItemPayload,
        SendMiddlewarePayloadServiceInterface $middlewarePayloadService,
        PayloadRepository                     $payloadRepository
    ) {
        $this->purchaseRequestItemPayload = $purchaseRequestItemPayload;
        $this->middlewarePayloadService = $middlewarePayloadService;
        $this->payloadRepository = $payloadRepository;
    }

    public function __invoke(CreatePurchaseRequest $message): void
    {
        $payload = $this->purchaseRequestItemPayload->create(
            $message->purchaseRequestItemId,
            $message->purchaseRequestItemIndex,
            $message->locale
        );

        if ($payload === null) {
            return;
        }

        $errorMessage = sprintf(
            'Error while sending purchase request item [id: %s] to company endpoint [%s]',
            $message->purchaseRequestItemId,
            $message->companyEndpointUrl
        );

        $this->middlewarePayloadService
            ->errorMessage($errorMessage)
            ->send($payload, $message->companyEndpointUrl)
        ;

        $this->payloadRepository->save($payload);
    }
}
