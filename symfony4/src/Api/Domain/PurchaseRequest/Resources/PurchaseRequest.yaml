AppBundle\Entity\PurchaseRequest:
  operations:
    get_purchase_request:
      class: ApiPlatform\Metadata\Get
      method: 'GET'
      path: '/purchase-request/{id}'
      controller: Api\Domain\PurchaseRequest\Controller\PurchaseRequestController::getPurchaseRequest
      read: false
      output: false
      openapi:
        security:
          - bearerAuth: []
          - basicAuth: []
    create_purchase_request:
      class: ApiPlatform\Metadata\Post
      method: 'POST'
      path: '/purchase-request'
      messenger: true
      output: false
      read: false
      validate: false
      status: 201
      openapi:
        requestBody:
          required: true
          content:
            application/json:
              schema:
                properties:
                  purchaseRequestItems:
                    type: array
                    items:
                      type: object
                      properties:
                        buyerReference:
                          type: "string"
                          example: ""
                        vendorReference:
                          type: "string"
                          example: ""
                        quantityRequested:
                          type: "integer"
                          example: 1
                        vendorName:
                          type: "string"
                          example: ""
                        productName:
                          type: string
                          example: ""
        responses:
          201:
            description: Purchase request created successful
          400:
            description: Invalid input
        security:
          - bearerAuth: []
          - basicAuth: []