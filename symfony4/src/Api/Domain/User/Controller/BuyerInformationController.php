<?php

declare(strict_types=1);

namespace Api\Domain\User\Controller;

use Api\Core\Bus\Query\QueryBus;
use Api\Domain\User\Dto\CostCenterData;
use Api\Domain\User\Model\BuyerInformation;
use Api\Domain\User\Output\BuyerInformationOutput;
use Api\Domain\User\UseCase\Query\BuyerInformation\BuyerInformationQuery;
use AppBundle\Entity\Site;
use AppBundle\Entity\User;
use AppBundle\Services\PaymentModes\PaymentModesServiceInterface;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\HttpFoundation\JsonResponse;

final class BuyerInformationController
{
    public function __construct(
        private readonly QueryBus $queryBus,
        private readonly TokenStorageInterface $tokenStorage,
        private readonly PaymentModesServiceInterface $paymentModesService
    ) {
    }

    public function __invoke(): JsonResponse
    {
        $token = $this->tokenStorage->getToken();
        /** @var User|null $user */
        $user = $token ? $token->getUser() : null;
        if ($user === null) {
            throw new AccessDeniedException();
        }

        $userCompany = $this->queryBus->ask(new BuyerInformationQuery(userId: $user->getId()));

        $sitesOfUser = $userCompany->getSites()
            ->filter(fn (Site $site) => $site->getDefaultUser() !== null);

        $informationOutput = new BuyerInformationOutput();
        if (!empty($sitesOfUser)) {
            $informationOutput->costCenters = $sitesOfUser
                ->map(\Closure::fromCallable([CostCenterData::class, 'createFromSite']))
                ->toArray();
        }

        $informationOutput->paymentModes = $this->paymentModesService->computePaymentModesOfCompany($userCompany);

        $data = BuyerInformation::fromOutput($informationOutput);
        return new JsonResponse($data);
    }
}
