AppBundle\Entity\User:
  operations:
    get_item:
      class: ApiPlatform\Metadata\Get
      name: 'get_user_item'
      openapi:
        security:
          - bearerAuth: []
          - basicAuth: []
    get_buyer_info:
      class: ApiPlatform\Metadata\Get
      name: 'get_buyer_info'
#      path: '/buyer-information'
      controller: Api\Domain\User\Controller\BuyerInformationController
      read: false
      output: Api\Domain\User\Output\BuyerInformationOutput
      uriTemplate: /buyer-information
      uriVariables: {}
      openapi:
        parameters: []
        summary: 'Retrieves buyer information'
        security:
          - bearerAuth: []
          - basicAuth: []