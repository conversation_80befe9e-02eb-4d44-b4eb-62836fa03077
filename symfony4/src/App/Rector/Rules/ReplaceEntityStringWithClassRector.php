<?php

declare(strict_types=1);

namespace App\Rector\Rules;

use AppBundle\Entity\ActionHistorization;
use AppBundle\Entity\Address;
use AppBundle\Entity\Cart;
use AppBundle\Entity\Company;
use AppBundle\Entity\Connection;
use AppBundle\Entity\Invoice;
use AppBundle\Entity\Order;
use AppBundle\Entity\SearchHistorization;
use AppBundle\Entity\ShippingPoint;
use AppBundle\Entity\Site;
use AppBundle\Entity\SpecificPrice;
use AppBundle\Entity\TvaGroup;
use AppBundle\Entity\User;
use AppBundle\Entity\UserBafvMerchantList;
use PhpParser\Node;
use PhpParser\Node\Scalar\String_;
use Rector\Rector\AbstractRector;

final class ReplaceEntityStringWithClassRector extends AbstractRector
{
    public function getNodeTypes(): array
    {
        return [String_::class];
    }

    public function refactor(Node $node): ?Node
    {
        if (!$node instanceof String_) {
            return null;
        }

        $replacements = [
            'AppBundle:Cart' => Cart::class,
            'AppBundle:ShippingPoint' => ShippingPoint::class,
            'AppBundle:ActionHistorization' => ActionHistorization::class,
            'AppBundle:Connection' => Connection::class,
            'AppBundle:Order' => Order::class,
            'AppBundle:Invoice' => Invoice::class,
            'AppBundle:TvaGroup' => TvaGroup::class,
            'AppBundle:UserBafvMerchantList' => UserBafvMerchantList::class,
            'AppBundle:User' => User::class,
            'AppBundle:Site' => Site::class,
            'AppBundle:Address' => Address::class,
            'AppBundle:SearchHistorization' => SearchHistorization::class,
            'AppBundle:SpecificPrice' => SpecificPrice::class,
            'AppBundle:Company' => Company::class,
        ];

        if (isset($replacements[$node->value])) {
            return $this->nodeFactory->createClassConstFetch($replacements[$node->value], 'class');
        }

        return null;
    }
}
