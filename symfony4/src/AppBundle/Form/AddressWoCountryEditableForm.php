<?php
/**
 * Created by Php<PERSON>torm.
 * User: AQU04740
 * Date: 23/03/2017
 * Time: 10:31
 */

namespace AppBundle\Form;

use AppBundle\Entity\Address;
use AppBundle\Entity\Country;
use AppBundle\Entity\Region;
use AppBundle\Enum\CompanyTypeEnum;
use Doctrine\ORM\EntityRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Contracts\Translation\TranslatorInterface;


class AddressWoCountryEditableForm extends AbstractType
{
    const LABEL = "label";
    const REQUIRED = "required";
    const CONSTRAINTS = "constraints";
    const GROUPS = "groups";
    const TRANSLATION_DOMAIN = 'AppBundle';



	/**
	 * @var TranslatorInterface
	 */
	private $translator;

	/**
	 * @param $translator
	 */
	public function __construct(TranslatorInterface $translator)
	{
		$this->translator = $translator;
	}

    /**
     * Build address new/edit form
     * @param FormBuilderInterface $builder
     * @param array $options
     */
  public function buildForm(FormBuilderInterface $builder, array $options)
  {

    $builder
      ->add(
        'check',
        CheckboxType::class,
        array(
          self::LABEL => 'address.form.check',
        )
      );

    $builder->add('country',
      EntityType::class,
      array(
      self::LABEL => 'address.form.country',
      'class' => Country::class,
      'choice_translation_domain' => true,
      'disabled' => true,
    ));


    $builder->add(
      'regionText',
      TextType::class,
      array(
        self::LABEL => 'address.form.region',
      )
    );


    $builder->add(
      'city',
      TextType::class,
      array(
        self::LABEL => 'address.form.city',
        self::REQUIRED => true,
      )
    )
      ->add(
        'zipCode',
        TextType::class,
        array(
          self::LABEL => 'address.form.zipcode',
          self::REQUIRED => true,
        )
      )
      ->add(
        'address',
        TextType::class,
        array(
          self::LABEL => 'address.form.address',
          self::REQUIRED => true,
        )
      )
      ->add(
        'address2',
        TextType::class,
        array(
          self::LABEL => 'address.form.address2',
          self::REQUIRED => false,
        )
      );
    /*->add(
        'longitude',
        HiddenType::class,
        array(
            self::LABEL => 'address.form.longitude',
        )
    )
    ->add(
        'latitude',
        HiddenType::class,
        array(
            self::LABEL => 'address.form.latitude',
        )
    );*/
  }

    /**
     * Configure default options
     * @param OptionsResolver $resolver
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(array(
            'jquery_validation_groups' => array('Default'),
            'data_class' => Address::class,
            'translation_domain' => self::TRANSLATION_DOMAIN,
        ));
    }
}
