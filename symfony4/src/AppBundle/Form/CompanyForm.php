<?php

namespace AppBundle\Form;

use AppBundle\Entity\Company;
use AppBundle\Repository\CategoryRepository;
use AppBundle\Validator\Constraints\CompanyIdentification;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\All;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Validator\Constraints as Assert;

class CompanyForm extends AbstractType
{
  const LABEL = "label";
  const SUBMIT = "submit";
  const VALIDATION_GROUPS = "validation_groups";
  const MAIN_ADDRESS = "mainAddress";
  const BILLING_ADDRESS = "billingAddress";
  const MULTIPLE = "multiple";
  const REQUIRED = 'required';
  const CONSTRAINTS = 'constraints';
  const CLAZZ = "class";

  public function buildForm(FormBuilderInterface $builder, array $options)
  {
    $builder->add(
        'identification',
        null,
        array(
          'mapped' => true,
          self::CONSTRAINTS => array(
            new CompanyIdentification(),
          ),
          self::LABEL => 'company.form.identification',
        )
      )->add(
        'name',
        TextType::class,
        array(
          self::LABEL => 'company.form.name',
        )
      )->add(
        self::MAIN_ADDRESS,
        AddressForm::class,
        array(
          self::LABEL => false,
          'country_is_locked' => $options['country_is_locked'],
        )
      )->add('category',
        EntityType::class,
        [
            self::CLAZZ => 'AppBundle\Entity\Category',
            'attr' => [self::CLAZZ => 'form-control'],
            self::LABEL => 'company.form.type',
            'query_builder' => function(CategoryRepository $er) {
                return $er->createQueryBuilder('c')
                    ->orderBy('c.label', 'ASC');
            },
            'choice_translation_domain' => 'AppBundle',
        ]
      )->add(
          'fullAuto',
          CheckboxType::class,
          [
              self::LABEL => 'company.form.fullAuto'
          ]
      )->add(
        'eCatalog',
        CheckboxType::class,
        [
            self::LABEL => 'company.form.eCatalog'
        ]
      )->add('documents',
        FileType::class,
        [
            self::LABEL => 'company.form.legal_documents',
            self::REQUIRED => false,
            self::MULTIPLE => true,
            'constraints' => new All(new File(['maxSize' => '5M'])),
        ]
      )->add(
        'billingService',
        TextType::class,
        array(
          self::LABEL => 'company.form.service',
        )
      )->addEventListener(FormEvents::PRE_SET_DATA, function (FormEvent $event) {
        $form = $event->getForm();

        $form->add(self::BILLING_ADDRESS, AddressForm::class, array(
            'validation_groups' => function (FormInterface $form) {
              if ($form->get('check')->getData() === false) {
                return array();
              }
              return array('Default', 'Address');
            }
          , self::LABEL => false,
          )
        );

      })
        ->add('endpointUrl', TextType::class, [
            self::LABEL => 'company.form.endpointUrl',
            self::REQUIRED => false,
            self::CONSTRAINTS => [
              new Assert\Url([
                  'protocols' => ['https'],
                  'message' => 'The url "{{ value }}" is not a valid url, only the https is allow.',
              ])
            ]
        ])
        ->add(
        'save',
        SubmitType::class,
        array(
          "attr" => array(
            "value" => "save",
            "class" => "Button button_margin",
          ),
          self::LABEL => 'company.form.next',
        )
      )
      ->setMethod('POST');

  }

  public function configureOptions(OptionsResolver $resolver)
  {
    $resolver->setDefaults(
      array(
        'jquery_validation_groups' => array('Default'),
        'data_class' => Company::class,
        'translation_domain' => 'AppBundle',
        'country_is_locked' => false,
      )
    );

  }
}
