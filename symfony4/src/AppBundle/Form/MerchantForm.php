<?php

namespace AppBundle\Form;

use AppBundle\Entity\Country;
use AppBundle\Form\Type\PhoneType;
use AppBundle\Validator\Constraints\CompanyIdentification;
use Doctrine\ORM\EntityRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Contracts\Translation\TranslatorInterface;
use Symfony\Component\Validator\Constraints\NotBlank;

class MerchantForm extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add(
                'country',
                EntityType::class,
                [
                    'class' => Country::class,
                    'label' => 'back.merchant.merchantInfo.country',
                    'query_builder' => function(EntityRepository $repository) {
                        return $repository->createQueryBuilder('c')
                            ->orderBy('c.code', 'ASC');
                    },
                    'choice_label' => function(Country $country) use ($options){
                        $label = 'country.' . $country->getCode();
                        if ($translator = $options['translator']) {
                            if ($translator instanceof TranslatorInterface) {
                                $label = $translator->trans($label, [], 'AppBundle');
                            }
                        }

                        return $label;
                    },
                ]
            )
            ->add(
                'currency',
                ChoiceType::class,
                [
                    'label' => 'back.merchant.merchantInfo.currency.title',
                    'choices' => [
                        'back.merchant.merchantInfo.currency.eur' => 'EUR',
                        'back.merchant.merchantInfo.currency.usd' => 'USD',
                    ],
                    'constraints' => [new NotBlank()],
                ]
            )
            ->add(
                'name',
                TextType::class,
                [
                    'label' => 'back.merchant.merchantInfo.name',
                    'constraints' => [new NotBlank()]
                ]
            )
            ->add(
                'identification',
                TextType::class,
                [
                    'label' => 'back.merchant.merchantInfo.identification',
                    'constraints' => [
                        new CompanyIdentification(),
                        new NotBlank(),
                    ]
                ]
            )
            ->add(
                'firstname',
                TextType::class,
                [
                    'label' => 'back.merchant.merchantInfo.firstname',
                    'constraints' => [new NotBlank()]
                ]
            )
            ->add(
                'lastname',
                TextType::class,
                [
                    'label' => 'back.merchant.merchantInfo.lastname',
                    'constraints' => [new NotBlank()]
                ]
            )
            ->add(
                'email',
                EmailType::class,
                [
                    'label' => 'back.merchant.merchantInfo.email',
                    'constraints' => [new NotBlank()]
                ]
            )
            ->add(
                'phoneNumber',
                PhoneType::class,
                [
                    'label' => 'back.merchant.merchantInfo.phoneNumber',
                    'constraints' => [
                        new NotBlank()
                    ],
                    'attr' => [
                        'pattern' => '^[\+]?[0-9]+',
                    ],
                ]
            )
            ->add(
                'save',
                SubmitType::class,
                [
                    'label' => 'form.user.save_edit',
                    'attr' => [
                        'value' => 'save',
                        'class' => 'button_margin',
                    ]
                ]
            )
        ;
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(
            [
                'translation_domain' => 'AppBundle',
                'translator' => null,
            ]
        );
    }
}
