<?php

namespace AppBundle\Form;

use AppBundle\Entity\Site;
use AppBundle\Entity\User;
use AppBundle\Services\CompanyService;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;

class SiteForm extends AbstractType
{
    const LABEL = "label";
    const PLACEHOLDER = "placeholder";
    const EXPANDED = "expanded";
    const DEFAULTS = "Default";
    const CHOICES = "choices";
    const EMPTY_DATA = "empty_data";
    const MULTIPLE = "multiple";
    const VALIDATION_GROUPS = "validation_groups";
    const REQUIRED = "required";
    const MAPPED = "mapped";
    const QUERY_BUILDER = "query_builder";
    const CLAZZ = "class";
    const CHOICE_LABEL = "choice_label";
    const COMPANYID = "companyId";
    const MAIN_ADDRESS = "mainAddress";
    const MAIN_CONTACT = "mainContact";
    const OTHER_CONTACT = "otherContact";

    private CompanyService $companyService;


    public function __construct(CompanyService $companyService)
    {
        $this->companyService = $companyService;
    }

    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('name', TextType::class, [
                    self::LABEL => 'site.form.name',
                    'attr' => ['placeholder' => 'site.form.placeholder.name']
                ]
            )
           ->addEventListener(FormEvents::PRE_SET_DATA, function (FormEvent $event) use ($options): void {
               /** @var Site $site */
               $site = $event->getData();
               $form = $event->getForm();

               if (!$options['isCreation'] && $site->getCompany() && !$site->getCompany()->hasUserApi()) {
                   return;
               }

               $choices = $this->getUsersForDefaultUserChoice($site);
               $constraints = [];

               if ($options['isCreation']) {
                   $choices = $this->getCompanyUsersForDefaultUserChoice($options['companyId']);
                   $constraints = [
                       new NotBlank(),
                   ];
               }

               if($options['hasUserApi']) {
                   $form->add('defaultUser', EntityType::class, [
                       'label' => 'site.form.default_user.label',
                       'placeholder' => $options['userApiPlaceholder'] ?? 'site.form.default_user.placeholder',
                       'class' => User::class,
                       'choice_label' => fn (User $user) => $user->display(),
                       'choices' => $choices,
                       'constraints' => $constraints
                   ]);
               }
           })
            ->add('save', SubmitType::class,
                array(
                    "attr" => array(
                        'value' => "save",
                        self::CLAZZ => "Button button_margin"
                    ),
                    self::VALIDATION_GROUPS => array(self::DEFAULTS),
                    self::LABEL => 'site.form.save'
                )
            )
            ->setMethod('POST');

    }

    /**
     * @param OptionsResolver $resolver
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(array(
            'isCreation' => false,
            'jquery_validation_groups' => array(
                'site',
                self::DEFAULTS
            ),
            'data_class' => Site::class,
            'translation_domain' => 'AppBundle',
            'operator' => false,
            'country' => 'france',
            self::COMPANYID => null,
            'hasUserApi' => false,
            'userApiPlaceholder' => 'site.form.default_user.placeholder'
        ));
    }

    private function getUsersForDefaultUserChoice(Site $site): array
    {
        $buyerAdmin = [];
        $siteUsers = $site
            ->getUsers()
            ->filter(fn (User $user) => $user->isBuyerApi() === false)
            ->filter(fn (User $user) => $user->isBuyerBuyer() === false)
            ->toArray()
        ;

        if ($site->getCompany()) {
            $buyerAdmin = $site->getCompany()
                ->getUsers()
                ->filter(fn (User $user) => $user->isBuyerAdmin() === true)
                ->toArray();
        }

        return $siteUsers + $buyerAdmin;
    }

    private function getCompanyUsersForDefaultUserChoice(int $companyId): array
    {
       $company = $this->companyService->get($companyId);

        if (!$company) {
            return [];
        }

        return $company
            ->getUsers()
            ->filter(fn (User $user) => $user->isBuyerAdmin() === true)
            ->toArray()
        ;
    }
}
