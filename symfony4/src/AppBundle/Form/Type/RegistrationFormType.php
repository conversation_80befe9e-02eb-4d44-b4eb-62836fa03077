<?php
/** Add new fields to the FOS User registration form */

namespace AppBundle\Form\Type;

use AppBundle\Entity\Country;
use AppBundle\Services\CompanyService;
use AppBundle\Services\CountryService;
use AppBundle\Validator\Constraints\CompanyIdentification;
use AppBundle\Validator\Constraints\ReCaptcha;
use Ddeboer\Vatin\Validator;
use Doctrine\ORM\EntityRepository;
use EWZ\Bundle\RecaptchaBundle\Form\Type\EWZRecaptchaType;
use EWZ\Bundle\RecaptchaBundle\Validator\Constraints\IsTrue;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Contracts\Translation\TranslatorInterface;
use Symfony\Component\Validator\Constraints\Callback;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;


class RegistrationFormType extends AbstractType
{
    const LABEL = "label";
    const REQUIRED = 'required';
    const MAPPED = 'mapped';
    const CONSTRAINTS = 'constraints';

    private $class;

    /**
     * @var TranslatorInterface
     */
    private $translator;

    /**
     * @var RequestStack
     */
    private $requestStack;

    /**
     * @var CompanyService
     */
    private $companyService;

    /**
     * @var Validator
     */
    private $vatValidator;

    /**
     * @var CountryService
     */
    private $countryService;

    public function __construct(
        string $class,
        TranslatorInterface $translator,
        RequestStack $requestStack,
        CompanyService $companyService,
        Validator $vatValidator,
        CountryService $countryService
    )
    {
        $this->class = $class;
        $this->translator = $translator;
        $this->requestStack = $requestStack;
        $this->companyService = $companyService;
        $this->vatValidator = $vatValidator;
        $this->countryService = $countryService;
    }


    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        // callback de validation du champs 'identification'
        $identificationCallback = function ($object, $context) use ($options) {
            $form_values = $this->requestStack->getCurrentRequest()->request->get('fos_user_registration_form');

            $country = $form_values['country'];
            $identification = $form_values['identification'];

            // On ne fait cette vérif que pour les buyers
            if (array_key_exists('merchant', $options) && !$options['merchant']) {
                if ($this->companyService->company_identification_already_exists($country,
                    $identification)) {
                    $msg = $this->translator->trans('registration.error.identification_already_used',
                        [], 'AppBundle');
                    $context->addViolation($msg, array(), null);
                }
            }

            //also add a check of the vat number if user is in UE
            try {
                $countryEntity = $this->countryService->getCountryById($country);
                if ($countryEntity->isInEU() && !$this->vatValidator->isValid($identification, true)) {
                    $context->addViolation($this->translator->trans('form.company.ident_number.unknown', [], 'validators'), array(), null);
                }

                $regex = $countryEntity->getCompanyIdentRegex();
                if (!preg_match('/'.$regex.'/', $identification)) {
                    $context->addViolation($this->translator->trans('form.company.ident_number.invalid', [], 'validators'), array(), null);
                }

            }catch (\Exception $e){
                //do nothing, we can't validate
            }

        };

        $builder
            ->remove('username')
            ->remove('plainPassword')
            ->remove('email');

        $builder->add(
            'country',
            EntityType::class,
            [
                'class' => Country::class,
                'placeholder' => 'address.form.country_placeholder',
                'translation_domain' => 'AppBundle',
                'query_builder' => function (EntityRepository $er) use ($options
                ) {

                    if (array_key_exists('merchant', $options) && $options['merchant']) {
                        $where = 'c.vendor = 1';
                    } else {
                        $where = 'c.buyer = 1';
                    }

                    // Show only enabled countries
                    $where .= " and c.enabled = 1 ";

                    return $er->createQueryBuilder('c')
                        ->where($where)
                        ->orderBy('c.code', 'ASC');
                },
                'choice_attr' => function ($country) {

                    $label = $this->translator->trans('country.ident.' . $country->getCode(),
                        [], 'AppBundle');
                    $help = $this->translator->trans('country.ident_helper.' . $country->getCode(),
                        [], 'AppBundle');

                    return [
                        'data-regexp' => $country->getCompanyIdentRegex(),
                        'data-country-code' => $country->getCode(),
                        'data-label' => $label,
                        'data-help' => $help,
                    ];
                },
                'mapped' => false,
                'choice_translation_domain' => 'AppBundle',
                'label' => 'address.form.country',
                'constraints' => new NotBlank(),
                'attr' => [
                    'required' => 'required' // needed because mapped = false
                ],
            ]
        );

        $builder->add(
            'raisonSociale',
            null,
            array(
                self::MAPPED => false,
                self::LABEL => 'buyer.registration.raison_sociale',
                self::CONSTRAINTS => array(
                    new NotBlank(),
                    new Length(
                        array(
                            'max' => 50
                        )
                    )
                ),
                'attr' => array(
                    self::REQUIRED => self::REQUIRED, // needed because mapped = false
                ),
            )
        );


        $builder->add(
            'identification',
            null,
            array(
                self::MAPPED => false,
                self::LABEL => 'buyer.registration.identification',
                self::CONSTRAINTS => array(
                    new CompanyIdentification(),
                    new NotBlank(['groups' => ['merchant_registration', 'Registration', 'user_registration']]),
                    new Callback([
                        'callback' => $identificationCallback,
                        'groups' => ['merchant_registration', 'Registration', 'user_registration'],
                    ]),
                ),
                'attr' => array(
                    self::REQUIRED => self::REQUIRED, // needed because mapped = false
                ),
            )
        );


        $builder->add(
            'lastname',
            null,
            array(
                self::CONSTRAINTS => array(
                    new NotBlank(),
                    new Length(
                        array(
                            'max' => 50
                        )
                    )
                ),
                self::LABEL => 'buyer.registration.lastname',
            )
        );


        $builder->add(
            'firstname',
            null,
            array(
                self::CONSTRAINTS => array(
                    new NotBlank(),
                    new Length(
                        array(
                            'max' => 50
                        )
                    )
                ),
                self::LABEL => 'buyer.registration.firstname',
            )
        );

        $builder->add(
            'email',
            EmailType::class,
            array(
                self::LABEL => 'buyer.registration.email',
            )
        );

        $builder->add(
            'mainPhoneNumber',
            PhoneType::class,
            array(
                self::LABEL => 'buyer.registration.main_phone',
                self::CONSTRAINTS => array(
                    new NotBlank(),
                ),
                'attr' => array(
                    'pattern' => '^[\+]?[0-9]+$',
                ),
            )
        );


        $builder->add(
            'plainPassword',
            PasswordType::class,
            array(
                self::LABEL => 'buyer.registration.password',
            )
        );

        $builder->add(
            'function',
            TextType::class,
            array(
                self::LABEL => 'buyer.registration.function',
            )
        );

        // Devise pour les vendeurs
        if (array_key_exists('merchant', $options) && $options['merchant']) {
            $builder->add(
                'currency',
                ChoiceType::class,
                array(
                    self::LABEL => 'buyer.registration.currency.title',
                    'placeholder' => 'buyer.registration.currency.placeholder',
                    'choices' => array(
                        'buyer.registration.currency.eur' => 'EUR',
                        'buyer.registration.currency.usd' => 'USD',
                    ),
                    'choice_translation_domain' => null,
                    'constraints' => new NotBlank(['groups' => ['merchant_registration']]),
                    'attr' => [
                        'required' => 'required'
                    ],
                )
            );
        }

        $builder->add(
            'recaptcha',
            EWZRecaptchaType::class,
            [
                'label' => false,
                'mapped' => false,
                'constraints' => [
                    new IsTrue(
                        ['groups' => ['merchant_registration', 'Registration', 'user_registration']]
                    )
                ],
            ]
        );
    }

    public function getParent()
    {
        return 'FOS\UserBundle\Form\Type\RegistrationFormType';
    }

    /**
     * {@inheritdoc}
     */
    public function getBlockPrefix()
    {
        return 'front_user_registration';
    }

    /**
     * {@inheritdoc}
     */
    public function getName()
    {
        return $this->getBlockPrefix();
    }

    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(array(
            'data_class' => $this->class,
            'csrf_token_id' => 'registration',
            'merchant' => false,
        ));
    }

}
