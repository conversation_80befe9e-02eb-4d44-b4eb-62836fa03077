<?php

namespace AppBundle\Form;

use AppBundle\Entity\Company;
use AppBundle\Entity\Site;
use AppBundle\Entity\User;
use AppBundle\Repository\SiteRepository;
use AppBundle\Services\LanguageService;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;

class UserForm extends AbstractType
{
    const LABEL = "label";
    const SUBMIT = "submit";
    const VALIDATION_GROUPS = "validation_groups";
    const MAIN_ADDRESS = "mainAddress";
    const BILLING_ADDRESS = "billingAddress";
    const MULTIPLE = "multiple";
    const REQUIRED = "required";
    const CLAZZ = "class";
    const FORM_CONTROL = 'form_control';

    const FROM_COMPANY_LIST = "from_company_list";

    const LABEL_ROLE_BUYER_BUYER = 'user.form.ROLE_BUYER_BUYER';
    const LABEL_ROLE_BUYER_PAYER = 'user.form.ROLE_BUYER_PAYER';
    const LABEL_ROLE_BUYER_ADMIN = 'user.form.ROLE_BUYER_ADMIN';
    const LABEL_ROLE_API = 'user.form.ROLE_API';

    const ROLE_BUYER_BUYER = 'ROLE_BUYER_BUYER';
    const ROLE_BUYER_PAYER = 'ROLE_BUYER_PAYER';
    const ROLE_BUYER_ADMIN = 'ROLE_BUYER_ADMIN';

    const ROLES = 'roles';

    const PROPERTY_PATH = 'property_path';

    const CHOICES_PROPERTY = 'choices';

    const FORM_ROLE_NAME = 'user.form.role';


    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $languageService = $options['languageService'] ?? null;

        $builder
            ->add(
                'email',
                TextType::class,
                array(
                    self::LABEL => 'user.form.email',
                    self::REQUIRED => true,
                )
            )->add(
                'firstname',
                TextType::class,
                array(
                    self::LABEL => 'user.form.firstname',
                    self::REQUIRED => true,
                )
            )->add(
                'lastname',
                TextType::class,
                array(
                    self::LABEL => 'user.form.lastname',
                    self::REQUIRED => true,
                )

            )->add(
                'mainPhoneNumber',
                TextType::class,
                array(
                    self::LABEL => 'user.form.phone1',
                    self::REQUIRED => false,
                )
            )->add(
                'function',
                TextType::class,
                array(
                    self::LABEL => 'user.form.function.label',
                    self::REQUIRED => false,
                )
            )
        ;

        if ($languageService instanceof LanguageService) {
            $builder->add(
                'locale',
                ChoiceType::class,
                [
                    'label' => 'back.user.form.language',
                    'choices' => $languageService->getLanguageCodes(),
                    'choice_label' => function($choiceValue, $key, $value) {
                        return 'node.form.lang.' . $value;
                    },
                    'choice_translation_domain' => 'AppBundle',
                    'placeholder' => false,
                    'translation_domain' => 'AppBundle',
                    'constraints' => [new NotBlank()],
                    'attr' => [
                        'required' => 'required',
                    ],
                ]
            );
        }

        if ($options[self::FROM_COMPANY_LIST]) {
            $builder->add(
                'roles',
                ChoiceType::class,
                array(
                    self::PROPERTY_PATH => 'role',
                    self::LABEL => self::FORM_ROLE_NAME,
                    self::CHOICES_PROPERTY => array(
                        self::LABEL_ROLE_BUYER_BUYER => self::ROLE_BUYER_BUYER,
                        self::LABEL_ROLE_BUYER_PAYER => self::ROLE_BUYER_PAYER,
                        self::LABEL_ROLE_BUYER_ADMIN => self::ROLE_BUYER_ADMIN,
                        self::LABEL_ROLE_API => User::ROLE_API,
                    )
                )
            );
        } else {
            if ($options['super_admin']) {
                $builder->add(
                    self::ROLES,
                    ChoiceType::class,
                    array(
                        self::PROPERTY_PATH => 'role',
                        self::LABEL => self::FORM_ROLE_NAME,
                        self::CHOICES_PROPERTY => array(
                            self::LABEL_ROLE_BUYER_BUYER => self::ROLE_BUYER_BUYER,
                            self::LABEL_ROLE_BUYER_PAYER => self::ROLE_BUYER_PAYER,
                            self::LABEL_ROLE_BUYER_ADMIN => self::ROLE_BUYER_ADMIN,
                            self::LABEL_ROLE_API => User::ROLE_API,
                            'user.form.ROLE_OPERATOR' => 'ROLE_OPERATOR',
                            'user.form.ROLE_SUPER_ADMIN' => 'ROLE_SUPER_ADMIN',
                        )
                    )
                );
            } else {
                $builder->add(
                    self::ROLES,
                    ChoiceType::class,
                    array(
                        self::PROPERTY_PATH => 'role',
                        self::LABEL => self::FORM_ROLE_NAME,
                        self::CHOICES_PROPERTY => array(
                            self::LABEL_ROLE_BUYER_BUYER => self::ROLE_BUYER_BUYER,
                            self::LABEL_ROLE_BUYER_PAYER => self::ROLE_BUYER_PAYER,
                            self::LABEL_ROLE_BUYER_ADMIN => self::ROLE_BUYER_ADMIN,
                            self::LABEL_ROLE_API => User::ROLE_API,
                            'user.form.ROLE_OPERATOR' => 'ROLE_OPERATOR'
                        )
                    )
                );
            }
        }

        if ($options[self::FROM_COMPANY_LIST]) {
            $builder->add('company', EntityType::class,
                array(
                    self::LABEL => 'user.form.company',
                    self::CLAZZ => Company::class,
                    'disabled' => true,
                    'choice_attr' => function (Company $company) {
                        return [
                            'data-company' => $company->getId()
                        ];
                    }
                ));
            $builder->add(
                'sites',
                EntityType::class,
                array(
                    self::LABEL => 'form.user.sites.label',
                    self::CLAZZ => \AppBundle\Entity\Site::class,
                    'choice_label' => 'name',
                    self::MULTIPLE => true,
                    'expanded' => true,
                    'required' => false,
                    'choice_translation_domain' => null,
                    'query_builder' => function (SiteRepository $er) use (&$options) {
                        return $er->createQueryBuilder('s')
                            ->where("s.company = :userCompanyId")
                            ->orderBy('s.name', 'ASC')
                            ->setParameter("userCompanyId", $options['company_id']);
                    }
                )
            );
        } else {
            $builder->add('company', EntityType::class,
                array(
                    self::LABEL => 'user.form.company',
                    self::CLAZZ => Company::class,
                    'choice_attr' => function (Company $company) {
                        return [
                            'data-company' => $company->getId()
                        ];
                    }
                ))->add(
                'sites',
                EntityType::class,
                array(
                    self::CLAZZ => Site::class,
                    self::MULTIPLE => true,
                    'expanded' => true,
                    'choice_attr' => function (Site $site) {
                        return [
                            'data-company' => $site->getCompany()->getId(),
                        ];
                    },
                    self::LABEL => 'user.form.site'
                )
            );
        }


        $builder->add(
            self::SUBMIT,
            SubmitType::class,
            array(
                "attr" => array(
                    "value" => "submit",
                    self::CLAZZ => "Button"
                ),
                self::LABEL => 'company.form.submit'
            )
        )
            ->setMethod('POST');
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'jquery_validation_groups' => ['Default', 'profil', 'user_sites'],
            'super_admin' => false,
            'data_class' => User::class,
            'translation_domain' => 'AppBundle',
            self::FROM_COMPANY_LIST => false,
            'company_id' => null,
            'validation_groups' => ['Default', 'profil', 'user_sites'],
            'languageService' => null,
        ]);
    }
}
