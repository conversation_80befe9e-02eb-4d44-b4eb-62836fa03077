<?php

namespace AppBundle\Twig;

use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;
use Twig\TwigFunction;

class AppExtension extends AbstractExtension
{
    public function getFunctions()
    {
        return [
            new TwigFunction('current_flag', [AppRuntime::class, 'currentFlag']),
            new TwigFunction('flags', [AppRuntime::class, 'flags']),
            new TwigFunction('firstLevelCategoryOnly', [AppRuntime::class, 'firstLevelCategoryOnly']),
            new TwigFunction('userCanModifyShippingPoint', [AppRuntime::class, 'userCanModifyShippingPoint']),
            new TwigFunction('userCanModifyCartValidationNumber', [AppRuntime::class, 'userCanModifyCartValidationNumber']),
            new TwigFunction('userCanRejectCart', [AppRuntime::class, 'userCanRejectCart']),
            new TwigFunction('userCanValidateCart', [AppRuntime::class, 'userCanValidateCart']),
            new TwigFunction('userCanAssignCart', [AppRuntime::class, 'userCanAssignCart']),
            new TwigFunction('getECatalogUrl', [AppRuntime::class, 'getECatalogUrl']),
        ];
    }

    public function getFilters()
    {
        return [
            new TwigFilter('price', [$this, 'formatPrice']),
            new TwigFilter('shortDescription', [$this, 'formatDescription']),
        ];
    }

    public function formatPrice($number, $locale = 'en')
    {
        $decimals = 2;
        $decPoint = ',';
        $thousandsSep = ' ';

        if ($locale === 'en') {
            $decPoint = '.';
            $thousandsSep = ',';
        }

        return number_format($number, $decimals, $decPoint, $thousandsSep);
    }

    public function formatDescription($shortDescription, $limit=null)
    {
        $shortDescription = str_replace("\n", '<br>', $shortDescription);

        if($limit && strlen($shortDescription) > $limit) {
            return html_entity_decode(substr($shortDescription, 0, $limit). " ...");
        }

        return html_entity_decode($shortDescription);
    }
}
