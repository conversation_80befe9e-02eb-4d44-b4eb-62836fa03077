<?php

namespace AppBundle\Util;

use Symfony\Component\HttpFoundation\RequestStack;

class Locale
{
    static public function fetchFromRequestStack(RequestStack $requestStack)
    {
        $locale = 'en';
        $request = $requestStack->getCurrentRequest();

        if (!is_null($request)) {
            $locale = $request->getLocale();
            $urlArray = explode('/',$request->getPathInfo());
            if (isset($urlArray[1])) {
                $urlLocale = $urlArray[1];
            }
            if ($urlLocale != $locale && in_array($urlLocale, ['en', 'fr', 'de', 'it', 'es', 'nl'])) {
                $locale = $urlLocale;
            }
        }

        return $locale;
    }
}
