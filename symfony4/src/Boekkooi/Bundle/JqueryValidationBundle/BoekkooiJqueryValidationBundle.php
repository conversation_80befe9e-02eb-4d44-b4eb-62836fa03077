<?php
namespace <PERSON>ek<PERSON><PERSON>\Bundle\JqueryValidationBundle;

use Boekkooi\Bundle\JqueryValidationBundle\DependencyInjection\Compiler\ExtensionPass;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\HttpKernel\Bundle\Bundle;

/**
 * <AUTHOR> <<EMAIL>>
 * @deprecated since symfony 4
 */
class BoekkooiJqueryValidationBundle extends Bundle
{
    public function build(ContainerBuilder $container): void
    {
        $container->addCompilerPass(new ExtensionPass());
    }
}
