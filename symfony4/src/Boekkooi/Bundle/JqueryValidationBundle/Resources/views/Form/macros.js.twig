{% macro form_jquery_selector(form) %}
    {%- if form.vars.attr.id is defined -%}
        #{{- form.vars.attr.id|e('js') -}}
    {%- else -%}
        form[name=\"{{- form.vars.full_name|e('js') -}}\"]
    {%- endif -%}
{% endmacro %}

{% macro validation_groups(groups, activeGroups) %}
    {%- set activeGroups = activeGroups|default([]) -%}
    {%- for group in groups -%}
        "{{ group|e('js') }}": {{ group in activeGroups ? 'true' : 'false' }}{% if not loop.last %}, {% endif %}
    {%- endfor -%}
{% endmacro %}

{# Generate all rules for all fields #}
{% macro form_rules(fields, includeGroupDeps) %}
    {%- import _self as gen -%}

    {% set first = true %}
    {% for field in fields %}
        {%- if field.rules|length > 0 -%}
            {%- if not first %},{% endif -%}
            "{{ field.name|e('js') }}": {
                {{- gen.rules(field.rules, includeGroupDeps) -}}
            }
            {%- set first = false -%}
        {%- endif -%}
    {% endfor %}
{% endmacro %}

{# Generate all rules #}
{% macro rules(field_rules, includeGroupDeps, includeMessage) %}
    {%- import _self as gen -%}

    {%- for rule in field_rules -%}
        "{{ rule.name|e('js') }}": {{- gen.rule(rule, includeGroupDeps) -}}{% if not loop.last %}, {%- endif -%}
    {%- endfor -%}

    {%- if includeMessage|default(false) -%}
        ,"messages": { {{- gen.rules_messages(field_rules) -}} }
    {%- endif -%}
{% endmacro %}

{# Generate a rule #}
{% macro rule(rule, includeGroupDeps) %}
    {%- set hasGroups = includeGroupDeps|default(false) and rule.groups is defined and rule.groups|length > 0 -%}
    {%- set hasConditions = (rule.conditions is not empty) -%}

    {%- if hasGroups or hasConditions -%}
        {%- set hasParams = rule.options is not same as(true) -%}

        {
        {%- if hasParams -%}
            param: {{- rule.options|json_encode()|raw -}},
        {%- endif -%}
            depends: function() {
                {%- if hasConditions %}
                    {%- if hasGroups -%}
                        if (!(
                            {%- for group in rule.groups -%}
                                validator.settings.validation_groups["{{ group|e('js') }}"] {%- if not loop.last %} || {% endif -%}
                            {%- endfor -%}
                        )) {
                            return false;
                        }
                    {%- endif -%}

                    {%- for condition in rule.conditions -%}
                        {% include '@BoekkooiJqueryValidationBundle/Form/' ~ condition.macro ~ '.js.twig' with { config: [condition, rule] } only %}
                    {%- endfor -%}

                    return true;
                {% else %}
                    return (
                        {%- for group in rule.groups -%}
                            validator.settings.validation_groups["{{ group|e('js') }}"] {%- if not loop.last %} || {% endif -%}
                        {%- endfor -%}
                    );
                {%- endif -%}
            }
        }
    {%- else -%}
        {{- rule.options|json_encode()|raw -}}
    {%- endif -%}
{% endmacro %}

{# Generate all messages for all fields #}
{% macro form_messages(fields) %}
    {%- import _self as gen -%}

    {% set first = true %}
    {%- for field in fields -%}
        {%- if field.rules|length > 0 -%}
            {%- if not first %},{% endif -%}
            "{{ field.name|e('js') }}": {
                {{- gen.rules_messages(field.rules) -}}
            }
            {%- set first = false -%}
        {%- endif -%}
    {%- endfor -%}
{% endmacro %}

{# Generate all messages for a set of rules #}
{% macro rules_messages(field_rules) %}
    {%- import _self as gen -%}

    {%- for rule in field_rules -%}
        {%- if rule.message -%}
            "{{ rule.name|e('js') }}": "{{ gen.rule_message(rule.message) }}"
            {%- if not loop.last -%}, {%- endif -%}
        {%- endif -%}
    {%- endfor -%}
{% endmacro %}

{# Create a string based on a form rule message #}
{% macro rule_message(msg) %}
    {%- if msg.plural is null -%}
        {{- msg.message|trans(msg.parameters, 'validators')|e('js') -}}
    {%- else -%}
{#        {{- msg.message|transchoice(msg.plural, msg.parameters|merge({'%count%': msg.plural}), 'validators')|e('js') -}}#}
        {{ msg.message|trans(
            msg.parameters|merge({'%count%': msg.plural}),
            'validators'
        )|e('js') }}
    {%- endif -%}
{% endmacro %}
