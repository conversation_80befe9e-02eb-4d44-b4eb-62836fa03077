<?php

namespace Open\BCEBundle\Service;

use Doctrine\ORM\EntityManager;
use Open\BCEBundle\Model\BCE;
use Open\IzbergBundle\Service\RedisService;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Unirest\Exception;

/***
 * Class BCEService
 *
 * @package Open\BCEBundle\Service
 */
class BCEService implements LoggerAwareInterface
{
    const REPO = "OpenBCEBundle:BCE";

    const CURR_EUR = 'EUR'; // EURO
    const CURR_USD = 'USD'; // US DOLLAR
    const CURR_JPY = 'JOY'; // JAPANESE YEN
    const CURR_BGN = 'BGN'; // BULGARIAN LEV
    const CURR_CZK = 'CZK'; // CZECH KORUNA
    const CURR_DKK = 'DKK'; // DANISH KRONE
    const CURR_GBP = 'GBP'; // POUND STERLING
    const CURR_HUF = 'HUF'; // HUNGARIAN FORINT
    const CURR_PLN = 'PLN'; // POLISH ZLOTY
    const CURR_RON = 'RON'; // ROMANIAN LEU
    const CURR_SEK = 'SEK'; // SWEDISH KRONA
    const CURR_CHF = 'CHF'; // SWISS FRANC
    const CURR_ISK = 'ISK'; // ICELANDIC KRONA
    const CURR_NOK = 'NOK'; // NORWEGIAN KRONE
    const CURR_HRK = 'HRK'; // CROATIAN KUNA
    const CURR_RUB = 'RUB'; // RUSSIAN ROUBLE
    const CURR_TRY = 'TRY'; // TURKISH LIRA
    const CURR_AUD = 'AUD'; // AUSTRALIAN DOLLAR
    const CURR_BRL = 'BRL'; // BRAZILIAN REAL
    const CURR_CAD = 'CAD'; // CANADIAN DOLLAR
    const CURR_CNY = 'CNY'; // CHINESE YUAN RENMINBI
    const CURR_HKD = 'HKD'; // HONG KONG DOLLAR
    const CURR_IDR = 'IDR'; // INDONESIAN RUPIAH
    const CURR_ILS = 'ILS'; // ISRAELI SHEKEL
    const CURR_INR = 'INR'; // INDIAN RUPEE
    const CURR_KRW = 'KRW'; // SOUTH KOREAN WON
    const CURR_MXN = 'MXN'; // MEXICAN PESO
    const CURR_MYR = 'MYR'; // MALAYSIAN RINGGIT
    const CURR_NZD = 'NZD'; // NEW ZEALAND DOLLAR
    const CURR_PHP = 'PHP'; // PHILIPPINE PISO
    const CURR_SGD = 'SGD'; // SINGAPORE DOLLAR
    const CURR_THB = 'THB'; // THAI BAHT
    const CURR_ZAR = 'ZAR'; // SOUTH AFRICAN RAND

    // LEGACY CURRENCIES
    const CURR_ATS  = 'ATS '; // AUSTRIAN SCHILLING
    const CURR_BEF  = 'BEF '; // BELGIAN FRANC
    const CURR_CYP  = 'CYP '; // CYPRIOT POUND
    const CURR_NLG  = 'NLG '; // DUTCH GUILDER
    const CURR_EEK  = 'EEK '; // ESTONIAN KROON
    const CURR_FIM  = 'FIM '; // FINNISH MARKKA
    const CURR_FRF  = 'FRF '; // FRENCH FRANC
    const CURR_DEM  = 'DEM '; // GERMAN MARK
    const CURR_GRD  = 'GRD '; // GREEK DRACHMA
    const CURR_IEP  = 'IEP '; // IRISH POUND
    const CURR_ITL  = 'ITL '; // ITALIAN LIRA
    const CURR_LVL  = 'LVL '; // LATVIAN LATS
    const CURR_LTL  = 'LTL '; // LITHUANIAN LITAS
    const CURR_LUF  = 'LUF '; // LUXEMBOURGISH FRANC
    const CURR_MTL  = 'MTL '; // MALTESE LIRA
    const CURR_MCF  = 'MCF '; // MONÉGASQUE FRANC
    const CURR_PTE  = 'PTE '; // PORTUGUESE ESCUDO
    const CURR_SML  = 'SML '; // SAMMARINESE LIRA
    const CURR_SKK  = 'SKK '; // SLOVAK KORUNA
    const CURR_SIT  = 'SIT '; // SLOVENIAN TOLAR
    const CURR_ESP  = 'ESP '; // SPANISH PESETA
    const CURR_VAL  = 'VAL '; // VATICAN LIRA


    const  RETRIEVE_METHOD_UNDEFINED = 'undefined';
    const  RETRIEVE_METHOD_LOCAL_CACHE = 'Retrieved from local cache';
    const  RETRIEVE_METHOD_REDIS = 'Retrieved from Redis';
    const  RETRIEVE_METHOD_BCE = 'Retrieved from BCE';
    const  RETRIEVE_METHOD_ERROR = 'Error while retrieving from BCE';

    const RATES = 'rates';
    const OFFICIAL_DATE = 'official date';

    const BCE_KEY = "taux_change";
    public const ERROR_REFRESH_RATES = 'Impossible to get last rates, next try at ';
    protected $url;

    protected $refresh_period_in_minutes;

    protected RedisService $redis;

    protected $current_exchange_rates = null;

    protected $local_cache = null;

    protected $retrieve_method = null;

    protected $em;

    private LoggerInterface $logger;


    /**
     * BCEService constructor.
     * @param EntityManager $em
     * @param $url
     * @param $refresh_period_in_minutes
     * @param $logger
     * @param $redis
     */
    public function __construct(EntityManager $em, $url, $refresh_period_in_minutes, $redis)
    {
        $this->em  = $em;
        $this->url = $url;
        $this->refresh_period_in_minutes = $refresh_period_in_minutes;
        $this->enableLocalCache();
        $this->resetRetrieveMethod();
        $this->redis = $redis;
    }

    private function resetRetrieveMethod(): void
    {
        $this->retrieve_method = array();
    }

    /***
     * @param $what
     */
    private function addRetrieveMethod($what): void
    {
        array_push($this->retrieve_method, $what);
    }

    /**
     * @param $object
     * @param $attribute
     * @return string
     * @throws Exception
     */
    private function xml_attribute($object, $attribute): string
    {
        if(isset($object[$attribute])){
          return (string) $object[$attribute];
        }
        else{
          throw new Exception("Wrong XML");
        }
    }

    /**
     *
     */
    public function disableLocalCache(): void
    {
        $this->local_cache = false;
    }

    public function enableLocalCache(): void
    {
        $this->local_cache = true;
    }


    /***
     * @return string
     */
    public function getUrl(): string
    {
        return $this->url;
    }

    /**
     * @param mixed $url
     */
    public function setUrl($url): void
    {
        $this->url = $url;
    }

    /**
     * @return mixed
     */
    public function getRefreshPeriodInMinutes()
    {
        return $this->refresh_period_in_minutes;
    }

    /**
     * @param mixed $refresh_period_in_minutes
     */
    public function setRefreshPeriodInMinutes($refresh_period_in_minutes): void
    {
        $this->refresh_period_in_minutes = $refresh_period_in_minutes;
    }


    /**
     * @return null|string
     */
    public function getRetrieveMethod()
    {
        return $this->retrieve_method;
    }

    /***
     * @return array
     */
    public function getCurrencies()
    {
        // EUR and USD should be well positioned in this array

        return array(
          self::CURR_EUR,
          self::CURR_USD,

          self::CURR_JPY,
          self::CURR_BGN,
          self::CURR_CZK,
          self::CURR_DKK,
          self::CURR_GBP,
          self::CURR_HUF,
          self::CURR_PLN,
          self::CURR_RON,
          self::CURR_SEK,
          self::CURR_CHF,
          self::CURR_ISK,
          self::CURR_NOK,
          self::CURR_HRK,
          self::CURR_RUB,
          self::CURR_TRY,
          self::CURR_AUD,
          self::CURR_BRL,
          self::CURR_CAD,
          self::CURR_CNY,
          self::CURR_HKD,
          self::CURR_IDR,
          self::CURR_ILS,
          self::CURR_INR,
          self::CURR_KRW,
          self::CURR_MXN,
          self::CURR_MYR,
          self::CURR_NZD,
          self::CURR_PHP,
          self::CURR_SGD,
          self::CURR_THB,
          self::CURR_ZAR,
        );
    }

    public function buildBasicRates()
    {
        $rates = $this->initializeRates();
        foreach ($this->getCurrencies() as $curr){
            $rates[$curr] = 1;
        }
        return array(
          self::OFFICIAL_DATE => \DateTime::createFromFormat('Y-m-d', '2002-01-01'),
          self::RATES => $rates,
        );
    }

  /**
   * @return array
   */
    private function initializeRates()
    {
      $ret = array();

      $ret[self::CURR_EUR] = 1;

      $ret[self::CURR_ATS] = 13.7603;
      $ret[self::CURR_BEF] = 40.3399;
      $ret[self::CURR_CYP] = 0.585274;
      $ret[self::CURR_NLG] = 2.20371;
      $ret[self::CURR_EEK] = 15.6466;
      $ret[self::CURR_FIM] = 5.94573;
      $ret[self::CURR_FRF] = 6.55957;
      $ret[self::CURR_DEM] = 1.95583;
      $ret[self::CURR_GRD] = 340.75;
      $ret[self::CURR_IEP] = 0.787564;
      $ret[self::CURR_ITL] = 1936.27;
      $ret[self::CURR_LVL] = 0.702804;
      $ret[self::CURR_LTL] = 3.4528;
      $ret[self::CURR_LUF] = 40.3399;
      $ret[self::CURR_MTL] = 0.4293;
      $ret[self::CURR_MCF] = 6.55957;
      $ret[self::CURR_PTE] = 200.482;
      $ret[self::CURR_SML] = 1936.27;
      $ret[self::CURR_SKK] = 30.126;
      $ret[self::CURR_SIT] = 239.64;
      $ret[self::CURR_ESP] = 166.386;
      $ret[self::CURR_VAL] = 1936.27;

      return $ret;
    }

    /***
     * @return array|bool
     * @throws \Unirest\Exception
     */
    public function retrieveFromBCE()
    {
        $XML = @simplexml_load_file($this->url);
        if ($XML !== false){
            try{
                $official_date = \DateTime::createFromFormat('Y-m-d H:i:s', $this->xml_attribute($XML->Cube->Cube, 'time').' 00:00:00');
            }catch(Exception $e){
                $this->logger->error("The XML structure return by BCE changed ",
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => $e->getMessage()
                    ])
                );
                return false;
            }

            $ret = $this->initializeRates(); // Add additional (non maintained) values (EURO 1/1 rate is not retrieved from the API)

            try{
                foreach($XML->Cube->Cube->Cube as $rate){
                    $ret[$this->xml_attribute($rate, 'currency')] = $this->xml_attribute($rate, 'rate');
                }
            }catch (\ErrorException $e){
                //LOG IT
                $this->logger->error("The XML structure return by BCE changed ",
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => $e->getMessage()
                    ])
                );
                return false;
            }
            return array(
              self::OFFICIAL_DATE => $official_date,
              self::RATES => $ret,
            );
        }
        else{
            return false;
        }
    }

    public function isRedisInitialized()
    {
        return (!empty($this->redis->getItem(self::BCE_KEY)));
    }

    public function retrieveFromRedis()
    {
        $this->addRetrieveMethod(self::RETRIEVE_METHOD_REDIS);

        // Check if Redis has been populated once
        /** @var BCE $BCE */
        $BCE = $this->redis->getItem(self::BCE_KEY);

        if (empty($BCE)){
            // Redis has not been initialized
            $rates = $this->retrieveFromBCE();

            if ($rates === false){
                // We do not save
                $this->addRetrieveMethod(self::RETRIEVE_METHOD_ERROR);
                return $this->buildBasicRates();
            }
            else {
                // Save in Redis
                /** @var BCE $BCE */
                $BCE = new BCE();
                $BCE->setDerniereMaj();
                $BCE->setRates($rates[self::RATES]);
                $BCE->setRateDate($rates[self::OFFICIAL_DATE]);
                $this->redis->saveItem(self::BCE_KEY, $BCE, $this->getRefreshPeriodInMinutes());

                $this->addRetrieveMethod(self::RETRIEVE_METHOD_BCE);

                return array(
                  self::OFFICIAL_DATE => $rates[self::OFFICIAL_DATE],
                  self::RATES => $rates[self::RATES],
                );
            }
        }
        else{
            // Redis is initialized

            /** @var \DateTime $lastUpdated */
            $lastUpdated = clone $BCE->getDerniereMaj(); // Be aware of ORM caching
            $next_refresh = $lastUpdated->add(\DateInterval::createFromDateString($this->refresh_period_in_minutes.' minutes'));
            $now = new \DateTime();

            // Compare it to see if it is obsolete
            if ($next_refresh <  $now){
                // We have to refresh
                $rates = $this->retrieveFromBCE();

                if ($rates !== false){
                    $BCE->setDerniereMaj();
                    $BCE->setRates($rates[self::RATES]);
                    $BCE->setRateDate($rates[self::OFFICIAL_DATE]);
                    $this->redis->saveItem(self::BCE_KEY, $BCE, $this->getRefreshPeriodInMinutes());

                    $this->addRetrieveMethod(self::RETRIEVE_METHOD_BCE);

                    return array(
                      self::OFFICIAL_DATE => $rates[self::OFFICIAL_DATE],
                      self::RATES => $rates[self::RATES],
                    );
                }
                else{
                    // Problem occured while retrieving from BCE
                    $this->addRetrieveMethod( self::RETRIEVE_METHOD_ERROR);
                    $this->addRetrieveMethod( self::RETRIEVE_METHOD_REDIS);

                    //change next date of attempt changing LastUpdatedAt
                    /** @var \DateTime $lastUpdate */
                    $lastUpdate = clone $BCE->getDerniereMaj();
                    $lastUpdate->modify('+ '. $this->getRefreshPeriodInMinutes()*60/3 .' seconds');
                    $BCE->setDerniereMaj($lastUpdate);
                    $this->redis->saveItem(self::BCE_KEY, $BCE, $this->getRefreshPeriodInMinutes());

                    //LOG IT
                    $this->logger->error(self::ERROR_REFRESH_RATES.$lastUpdate->format("Y-m-d H:i:s"));

                    // In case of difficulty return Redis value
                    return array(
                      self::OFFICIAL_DATE => $BCE->getRateDate(),
                      self::RATES => $BCE->getRates(),
                    );
                }
            }
            else{
                $this->addRetrieveMethod( self::RETRIEVE_METHOD_REDIS);

                // return from DB
                return array(
                  self::OFFICIAL_DATE => $BCE->getRateDate(),
                  self::RATES => $BCE->getRates(),
                );
            }
        }
    }

  /***
   * @param $what
   *
   * @return mixed
   */
    public function getInfo($what)
    {
        $this->resetRetrieveMethod();
        if ($this->local_cache){
            if ($this->current_exchange_rates === null){
                $this->current_exchange_rates = $this->retrieveFromRedis();
            }
            $this->addRetrieveMethod(self::RETRIEVE_METHOD_LOCAL_CACHE);
            return $this->current_exchange_rates[$what];
        }
        else{
            return $this->retrieveFromRedis()[$what];
        }
    }

    /**
     * Methode utilisée uniquement lors des tests unitaires, afin de déterminé si l'url de la BCE n'est plus valide
     * @return bool
     */
    function urlIsUp() {
        $ch = curl_init($this->url);
        curl_setopt($ch, CURLOPT_HEADER, true);    // we want headers
        curl_setopt($ch, CURLOPT_NOBODY, true);    // we don't need body
        curl_setopt($ch, CURLOPT_RETURNTRANSFER,1);
        curl_setopt($ch, CURLOPT_TIMEOUT,10);
        curl_exec($ch);
        $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        if ($httpcode != 200 && $httpcode != 302 && $httpcode != 304) {
            return false;
        }else{
            return true;
        }
    }

    /*********************************************************************************************************************************
     *
     * BUSINESS LOGIC API
     *
     * ALL OTHER METHODS ARE USED INTERNALLY (EVEN IF THEY ARE PUBLIC) OR BY UNIT TESTING
     *
     ********************************************************************************************************************************/

    public function getOfficialDate() :\DateTime
    {
        return $this->getInfo(self::OFFICIAL_DATE);
    }


    public function getExchangeRates()
    {
        return $this->getInfo(self::RATES);
    }

    /**
     * @param $curr
     *
     * @return int
     */
    public function getExchangeRate($curr)
    {
        return $this->getExchangeRates()[$curr];
    }

    /**
     * @param $value
     * @param string $curr
     *
     * @return float|int
     */
    public function fromCurrToEuro($value, $curr)
    {
        return $value / $this->getExchangeRate($curr);
    }

    /**
     * @param $value
     * @param string $curr
     *
     * @return mixed
     */
    public function fromEURToCurr($value, $curr)
    {
        return $value * $this->getExchangeRate($curr);
    }

    /**
     * @param $value
     *
     * @return mixed
     */
    public function fromEURToUSD($value)
    {
        return $this->fromEURToCurr($value, self::CURR_USD);
    }

    /**
     * @param $value
     *
     * @return float|int
     */
    public function fromUSDToEUR($value)
    {
        return $this->fromCurrToEuro($value, self::CURR_USD);
    }

    public function cleanRedis(){
        $this->redis->removeItem(self::BCE_KEY);
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
