<?php

namespace Open\BackBundle\Controller;

use App<PERSON><PERSON>le\Controller\MkoController;
use AppB<PERSON>le\Repository\BafvRequestRepository;
use Open\BackBundle\Form\FilterBafvRequestType;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

class BafvRequestController extends MkoController
{
    /**
     * @param Request               $request
     * @param BafvRequestRepository $bafvRequestRepository
     * @return \Symfony\Component\HttpFoundation\Response|null
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/bafv-request/list', name: 'admin.bafv_requests.list')]
    public function listBafvRequest(Request $request, BafvRequestRepository $bafvRequestRepository)
    {
        $page = 1;
        if($request->query->get('page') != null){
            $page = $request->query->get('page');
        }

        $form = $this->createForm(FilterBafvRequestType::class);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $filter = $form->getData();
        }
        else{
            $filter = [];
            $filter['status'] = 'all';
        }

        $paginator = $bafvRequestRepository->paginatedRequests($filter, $page, 25);

        return $this->render(
            '@OpenBack/bafvRequest/bafvRequest_list.html.twig',
            [
                'form' => $form->createView(),
                'pagination' => $paginator,
                'locale' => $request->getLocale(),
            ]
        );
    }
}
