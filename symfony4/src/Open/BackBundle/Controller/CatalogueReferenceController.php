<?php

namespace Open\BackBundle\Controller;

use App<PERSON><PERSON>le\Controller\MkoController;
use AppBundle\Services\CompanyCatalogService;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

class CatalogueReferenceController extends MkoController
{
    /**
     * @param CompanyCatalogService $companyCatalogService
     * @return \Symfony\Component\HttpFoundation\Response|null
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/catalogue_reference/top-mismatch', name: 'admin.top_mismatch_catalog_references')]
    public function topMismatchCatalogueReference(CompanyCatalogService $companyCatalogService)
    {
        return $this->render(
            '@OpenBack/catalogReference/top_mismatch_references.html.twig',
            [
                'references' => $companyCatalogService->topMismatchReferences(),
            ]
        );
    }
}
