<?php

namespace Open\BackBundle\Controller;

use AppBundle\Controller\MkoController;
use AppBundle\Entity\Category;
use AppBundle\Entity\CheckableZipCodeTrait;
use AppBundle\Entity\Company;
use AppBundle\Entity\Document;
use AppBundle\Entity\Merchant;
use AppBundle\Entity\Site;
use AppBundle\Entity\User;
use AppBundle\Entity\UserBafvMerchantList;
use AppBundle\Exception\CSVException;
use AppBundle\Exception\UnexpectedValueException;
use AppBundle\Form\CompanyForm;
use AppBundle\Model\Id;
use AppBundle\Repository\CategoryRepository;
use AppBundle\Repository\DocumentRepository;
use AppBundle\Services\AddressService;
use AppBundle\Services\CompanyService;
use AppBundle\Services\CSVService;
use AppBundle\Services\MailService;
use AppBundle\Services\OrderPDFGenerator;
use AppBundle\Services\OrderService;
use AppBundle\Services\SecurityService;
use AppBundle\Services\SiteService;
use AppBundle\Services\UserBddService;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\ORMException;
use Exception;
use Mpdf\Mpdf;
use Mpdf\Output\Destination;
use Open\BackBundle\Form\DeactivateCompanyForm;
use Open\BackBundle\Form\FilterCompanyType;
use Open\BackBundle\Form\FilterCompanyUserType;
use Open\BackBundle\Form\IdForm;
use Open\IzbergBundle\Api\ApiClientManager;
use Open\IzbergBundle\Api\ApiConfigurator;
use Open\IzbergBundle\Api\MerchantApi;
use Open\IzbergBundle\Api\MessageApi;
use Open\IzbergBundle\Model\Order;
use Open\IzbergBundle\Model\OrderItem;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\Form\Extension\Core\Type\FormType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Contracts\Translation\TranslatorInterface;

class CompanyController extends MkoController
{
    use CheckableZipCodeTrait;

    const TRANSLATOR = 'translator';
    const TRANSLATION_DOMAIN = 'AppBundle';
    const PAGINATION = 'pagination';
    const LOCALE = 'locale';
    const COMPANY_BUNDLE = \AppBundle\Entity\Company::class;
    const VALIDATION_GROUPS = 'validation_groups';
    const DEF = 'Default';
    const VALID = 'valid';
    const SUBMIT = 'submit';
    const BILLING_ADDRESS = 'billingAddress';
    const LOGGER_SAVE = 'log.company.save';
    const STATUS = 'status';
    const SUCCESS = 'success';
    const ERROR = 'error';
    const COMPANY_UPDATE_SUCCESS_MESSAGE = 'company.form.update.success';
    const COMPANY_UPDATE_ERROR_MESSAGE = 'company.form.update.error';
    const COMPANY = 'company';
    const TAB_ACTIVE = 'tabActive';
    const ERROR_UPLOAD_DOCUMENT = 'document.upload.error';
    const PROPERTY_FORMATTEDLASTCONNECTION = 'formattedLastConnection';
    const COMPANY_ID = "company_id";
    const MAIN_ADDRESS = 'mainAddress';
    const COUNTRY = 'country';
    const CATEGORY = 'translatedCategory';
    const ACL_PROVIDER = 'security.acl.provider';
    const FILES = "files";
    const GLOBALE = "global";

    /**
     * @param Request $request
     * @param $qualifier
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/list/{qualifier}', name: 'admin.company.list')]
    public function listCompany(Request $request, $qualifier, CompanyService $companyService)
    {

        $formModal = $this->siteNameForm();
        $form = $this->createForm(FilterCompanyType::class);

        // remove term payment relative fields
        $form->remove('termpayment_moneytransfert_enabled');
        $form->remove('termpayment_moneytransfert_pending');
        $form->remove('term_payment_date_min');
        $form->remove('term_payment_date_max');
        $form->remove('termpaymentMoneyTransfertRequestDate');
        $form->remove('termpaymentMoneyTransfertAcceptDate');

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $filter = $form->getData();
        } else {
            $filter = [];
            $filter['status'] = 'all';
        }

        //render list of users
        return $this->render(
            '@OpenBack/company/company_list.html.twig',
            [
                self::PAGINATION => $companyService->getCustomFilteredPaginator(1, 25, $request, $filter, $qualifier),
                'qualifier_val' => $qualifier,
                'form' => $form->createView(),
                'formModal' => $formModal->createView(),
                self::LOCALE => $request->getLocale(),
            ]
        );
    }

    /***
     * @return FormInterface
     */
    private function siteNameForm()
    {
        return $this->createFormBuilder()
            ->add('name', TextType::class, array('label' => 'site.form.name', 'translation_domain' => 'AppBundle'))
            ->add(self::COMPANY, HiddenType::class)
            ->setAction($this->generateUrl('admin.company.site.new'))
            ->setMethod('POST')
            ->getForm();
    }

    /**
     * @param Request             $request
     * @param TranslatorInterface $translator
     *
     * @return mixed
     * @throws ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/admin/company/site/new', name: 'admin.company.site.new')]
    public function intermediateSiteSaveAction(Request $request, TranslatorInterface $translator)
    {
        $form = $this->siteNameForm();

        /** @var EntityManager $em */
        $em = $this->doctrine->getManager();

        $site = new Site();

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $site->setName($data['name']);
            /** @var Company $company */
            $company = $em->getRepository(Company::class)->find($data[self::COMPANY]);

            $site->setCompany($company);

            // persist
            $em->persist($site);
            $em->flush();

            // Add success message to session
            $this
                ->addFlash(
                    'success',
                    $translator->trans('site.form.created', array(), self::TRANSLATION_DOMAIN)
                );

            // Redirect to newly created site
            return $this->redirectToRoute('admin.company.sites', array('id' => $company->getId()));
        }

        throw new BadRequestHttpException("CompanyController: creation form is not valid: Request is invalid");
    }

    /**
     * @param Request $request
     * @param                     $id
     * @param TranslatorInterface $translator
     * @param MailService $mailService
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/{id}/info/edit', name: 'admin.company.info.edit')]
    public function editCompanyInfo(Request $request, $id, TranslatorInterface $translator, MailService $mailService)
    {
        $em = $this->doctrine->getManager();

        /** @var User $user */
        $user = $this->getUser();

        /** @var Company $company */
        $company = $em->getRepository(Company::class)->find($id);

        $options = array(
            self::VALIDATION_GROUPS => [self::DEF],
            'method' => 'patch',
            'country_is_locked' => true,
        );


        $form = $this->createForm(
            CompanyForm::class,
            $company,
            $options
        );

        $this->setCheck($form, $company->getBillingAddress(), self::BILLING_ADDRESS);

        // Remove buttons if company is valid
        if ($company->getStatus() == self::VALID) {
            $form->remove(self::SUBMIT);
        }

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $error[] = $this->checkZipCode($form, self::MAIN_ADDRESS, $form->get(self::MAIN_ADDRESS)->get(self::COUNTRY)->getData()->getCode());
            $error[] = $this->checkZipCode($form, 'billingAddress', $form->get(self::MAIN_ADDRESS)->get(self::COUNTRY)->getData()->getCode());
            if (!in_array(true, $error)) {

                /** @var Company $company */
                $company = $form->getData();
                $company = $this->cleanCompanyAddress($form, $company);

                // Gestion des documents à ajouter
                $docs = [];
                $files = $company->getDocuments();
                foreach ($files as $file) {
                    $doc = new Document();
                    $doc->setCompany($company);
                    $doc->setFilename($file->getClientOriginalName());
                    $doc->setMimeType($file->getMimeType());
                    $doc->setSize($file->getSize());
                    $doc->setBinary(file_get_contents($file->getRealPath()));
                    $docs[] = $doc;
                }
                $company->setDocuments($docs);

                $this->logger
                    ->info(
                        $translator->trans(self::LOGGER_SAVE),
                        LogUtil::buildContext([
                            LogUtil::EVENT_NAME => EventNameEnum::COMPANY_SAVE,
                            LogUtil::USER_NAME => $user->getUsername(),
                            'id' => $company->getId(),
                            self::STATUS => $company->getStatus()
                        ])
                    );

                try {
                    $em->merge($company);
                    $em->flush();
                    // flash message
                    $this->addFlash(self::SUCCESS, $translator->trans(self::COMPANY_UPDATE_SUCCESS_MESSAGE, array(), self::TRANSLATION_DOMAIN));
                    //send notifications
                    $this->sendCompanyModificationNotificationToOperator($company, MailService::OWNERS_COMPANY_INFO_MODIFIED, $mailService);
                    // return to company informations page
                    return $this->redirectToRoute('admin.company.generalInfo', array('id' => $company->getId()));
                } catch (Exception $e) {
                    $this->logger->error("error while saving company: " . $e->getMessage(),
                        LogUtil::buildContext([
                            LogUtil::EVENT_NAME => EventNameEnum::TECHNICAL_ERROR,
                            LogUtil::USER_NAME => $this->getUser()->getUsername(),
                            self::COMPANY_ID => $company->getId(),
                            'exception' => $e
                        ]));
                    $this->addFlash(self::ERROR, $translator->trans(self::COMPANY_UPDATE_ERROR_MESSAGE, array(), self::TRANSLATION_DOMAIN));
                }
            }
        }

        return $this->render('@OpenBack/company/company_info_edit.html.twig',
            array('form' => $form->createView(),
                self::COMPANY => $company,
                self::TAB_ACTIVE => 1));
    }


    /**
     * @param                      $id
     * @param FormFactoryInterface $formAccountType
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/{id}/companyInfo', name: 'admin.company.generalInfo')]
    public function getCompanyGeneralInformations($id, FormFactoryInterface $formAccountType)
    {
        $em = $this->doctrine->getManager();

        /** @var Company $company */
        $company = $em->getRepository(Company::class)->find($id);

        $formModal = $this->paymentTermDenyForm();

        $formAccount = $formAccountType->createNamedBuilder('', FormType::class, null, ['csrf_protection' => true, 'allow_extra_fields' => true,])
            ->add('category',
                EntityType::class,
                [
                    'class' => 'AppBundle\Entity\Category',
                    'attr' => ['class' => 'form-control'],
                    'label' => 'back.company.list.user_type',
                    'query_builder' => function (CategoryRepository $er) {
                        return $er->createQueryBuilder('c')
                            ->orderBy('c.label', 'ASC');
                    },
                    'choice_translation_domain' => 'AppBundle',
                    'translation_domain' => 'AppBundle',
                    'data' => $company->getCategory(),
                ]
            )
            ->add('companyId', HiddenType::class, ['data' => $company->getId()])
            ->getForm();

        return $this->render(
            '@OpenBack/company/company_companyInfo.html.twig',
            [
                self::COMPANY => $company,
                self::TAB_ACTIVE => 1,
                'formModal' => $formModal->createView(),
                'form_activate_company' => $this->createForm(IdForm::class, new Id($company->getId()))->createView(),
                'form_deactivate_company' => $this->createForm(IdForm::class, new Id($company->getId()))->createView(),
                'form_reject_company' => $this->createForm(IdForm::class, new Id($company->getId()))->createView(),
                'form_activate_term_payment' => $this->createForm(IdForm::class, new Id($company->getId()))->createView(),
                'form_deactivate_term_payment' => $this->createForm(IdForm::class, new Id($company->getId()))->createView(),
                'form_account_type' => $formAccount->createView()
            ]
        );
    }

    /***
     * @return FormInterface
     */
    private function paymentTermDenyForm()
    {
        return $this->createFormBuilder()
            ->add('reason', TextareaType::class, array('label' => 'back.company.companyInfo.termpayment.reason', 'translation_domain' => 'AppBundle', 'required' => true))
            ->add('company', HiddenType::class)
            ->setAction($this->generateUrl('admin.payment_term.deny'))
            ->setMethod('POST')
            ->getForm();
    }

    /**
     * @param Request $request
     * @param TranslatorInterface $translator
     *
     * @param MailService $mailService
     * @param SecurityService $securityService
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/activate', name: 'admin.company.activate', methods: ['POST'])]
    public function activateCompany(Request $request, TranslatorInterface $translator, MailService $mailService, SecurityService $securityService)
    {
        $form = $this->createForm(IdForm::class, null, []);
        $form->handleRequest($request);
        $id = $form->getData()['id'];
        $em = $this->doctrine->getManager();

        //add some logs
        $this->logger->info("company has been validated",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME=>EventNameEnum::COMPANY_VALIDATE,
                LogUtil::USER_NAME => $this->getUser()->getUsername(),
                self::COMPANY_ID => $id
            ]));


        /** @var Company $company */
        $company = $em->getRepository(Company::class)->find($id);
        $company->setEnabled(true);
        $company->setRejected(false);

        $company->setDeactivationReason(null);
        $company->setRejectedReason(null);

        try {
            $users = $securityService->getCompanyAdminUsers($company);

            /**
             * @var User $user
             */
            foreach ($users as $user) {
                $mailService->sendEmailMessage(MailService::COMPANY_VALIDATED, $user->getLocale(), $user->getEmail(), array(
                    MailService::FIRST_NAME_VAR => $user->getFirstname(),
                    MailService::LAST_NAME_VAR => $user->getLastname(),
                    "companyName" => $company->getName(),
                    "email" => $user->getEmail()));
                $user->setEnabled(true);
                $em->merge($user);
                $em->flush();
            }

            $em->merge($company);
            $em->flush();
            $this->addFlash(self::SUCCESS, $translator->trans('back.company.companyInfo.validate.ok', [], self::TRANSLATION_DOMAIN));
        } catch (Exception $e) {
            $this->addFlash(self::ERROR, $translator->trans('back.company.companyInfo.validate.ko', [], self::TRANSLATION_DOMAIN));
        }

        return $this->redirectToRoute('admin.company.generalInfo', array('id' => $company->getId()));
    }

    /**
     * @param Request             $request
     * @param TranslatorInterface $translator
     *
     * @param MailService         $mailService
     * @param SecurityService     $securityService
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/reject', name: 'admin.company.reject', methods: ['POST'])]
    public function rejectCompany(Request $request, TranslatorInterface $translator, MailService $mailService, SecurityService $securityService)
    {

        $form = $this->createForm(IdForm::class, null, []);
        $form->handleRequest($request);
        $id = $form->getData()['id'];

        $em = $this->doctrine->getManager();

        $form = $this->createForm(DeactivateCompanyForm::class, new Id($id));
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $formData = $request->get('deactivate_company_form');
            $id = $formData['id'];
            $reason = $formData['reason'];

            /** @var Company $company */
            $company = $em->getRepository(Company::class)->find($id);

            //add some logs
            $this->logger->info("company has been rejected",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME=>EventNameEnum::COMPANY_REJECT,
                    LogUtil::USER_NAME => $this->getUser()->getUsername(),
                    self::COMPANY_ID => $id,
                    'reason' => $reason
                ])
            );

            $company->setEnabled(false);
            $company->setRejected(true);
            $company->setRejectedReason($reason);

            try {
                $users = $securityService->getCompanyAdminUsers($company);

                /**
                 * @var User $user
                 */
                foreach ($users as $user) {
                    $mailService->sendEmailMessage(MailService::COMPANY_REJECTED, $user->getLocale(), $user->getEmail(), array(
                        MailService::FIRST_NAME_VAR => $user->getFirstname(),
                        MailService::LAST_NAME_VAR => $user->getLastname(),
                        "companyName" => $company->getName(),
                        'comment' => $reason));
                    $user->setEnabled(false);
                    $em->merge($user);
                    $em->flush();
                }

                $em->merge($company);
                $em->flush();
                $this->addFlash(self::SUCCESS, $translator->trans('back.company.companyInfo.reject.ok', [], self::TRANSLATION_DOMAIN));

            } catch (Exception $e) {
                $this->logger
                    ->error(
                        $translator->trans('back.company.companyInfo.reject.ko', [], self::TRANSLATION_DOMAIN),
                        LogUtil::buildContext([
                            LogUtil::EVENT_NAME=>EventNameEnum::COMPANY_REJECT,
                            LogUtil::USER_NAME => $user->getUsername(),
                            'id' => $company->getId(),
                            self::STATUS => $company->getStatus(),
                            'exception' => $e->getMessage()
                        ])
                    );

                $this->addFlash(
                    self::ERROR,
                    $translator->trans('back.company.companyInfo.reject.ko', [], self::TRANSLATION_DOMAIN)
                );
            }

            return $this->redirectToRoute('admin.company.generalInfo', ['id' => $company->getId()]);
        }

        // Display form
        return $this->render(
            '@OpenBack/company/company_deactivate.html.twig',
            ['form' => $form->createView()]
        );
    }

    /**
     * @param Request $request
     * @param MailService $mailService
     * @param SecurityService $securityService
     *
     * @param TranslatorInterface $translator
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/deactivate', name: 'admin.company.deactivate', methods: ['POST'])]
    public function deactivateCompany(Request $request, MailService $mailService, SecurityService $securityService, TranslatorInterface $translator)
    {
        $formId = $this->createForm(IdForm::class, null, []);
        $formId->handleRequest($request);
        $id = $formId->getData()['id'];

        $form = $this->createForm(DeactivateCompanyForm::class, new Id($id));
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $formData = $request->get('deactivate_company_form');
            $id = $formData['id'];
            $reason = $formData['reason'];

            $em = $this->doctrine->getManager();

            //add some logs
            $this->logger->info("company has been disabled",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::COMPANY_DISABLE,
                    LogUtil::USER_NAME => $this->getUser()->getUsername(),
                    self::COMPANY_ID => $id,
                    'reason' => $reason
                ])
            );


            /** @var Company $company */
            $company = $em->getRepository(Company::class)->find($id);

            $company->setEnabled(false);
            $company->setRejected(true);
            $company->setDeactivationReason($reason);

            try {
                $em->merge($company);
                $em->flush();
            } catch (Exception $e) {
                $this->addFlash(self::ERROR, $translator->trans('back.company.companyInfo.deactivation.ko', array(), self::TRANSLATION_DOMAIN));
            }

            try {

                $users = $securityService->getCompanyAdminUsers($company);

                /**
                 * @var User $user
                 */
                foreach ($users as $user) {
                    if ($user->isEnabled()) {
                        $mailService->sendEmailMessage(MailService::COMPANY_DEACTIVATED, $user->getLocale(), $user->getEmail(),
                            array(
                                MailService::FIRST_NAME_VAR => $user->getFirstname(),
                                MailService::LAST_NAME_VAR => $user->getLastname(),
                                "companyName" => $company->getName(),
                                "comment" => $reason,
                            ));

                        $user->setEnabled(false);
                        $em->merge($user);
                        $em->flush();
                    }
                }

                $em->merge($company);
                $em->flush();
                $this->addFlash(self::SUCCESS, $translator->trans('back.company.companyInfo.deactivation.ok', array(), self::TRANSLATION_DOMAIN));
            } catch (Exception $e) {
                $this->addFlash(self::ERROR, $translator->trans('back.company.companyInfo.deactivation.ko', array(), self::TRANSLATION_DOMAIN));
            }

            return $this->redirectToRoute('admin.company.generalInfo', array('id' => $company->getId()));
        }


        // Display form
        return $this->render('@OpenBack/company/company_deactivate.html.twig',
            ['form' => $form->createView()]);

    }


    /**
     * @param Request     $request
     * @param             $id
     * @param SiteService $siteService
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/{id}/sites', name: 'admin.company.sites')]
    public function getSites(Request $request, $id, SiteService $siteService)
    {

        $em = $this->doctrine->getManager();

        /** @var Company $company */
        $company = $em->getRepository(Company::class)->find($id);

        $formModal = $this->siteNameForm();


        //render list of users
        return $this->render('@OpenBack/company/company_sites.html.twig',
            array(self::PAGINATION => $siteService->getCompanyCustomFilteredPaginator(1, 25, $request, [], $id),
                'formModal' => $formModal->createView(),
                'form_delete_shippingPoint' => $this->createForm(IdForm::class, null)->createView(),
                self::COMPANY => $company,
                self::LOCALE => $request->getLocale(),
                self::TAB_ACTIVE => 2));
    }

    /**
     * @param Request        $request
     * @param                $id
     * @param SiteService    $siteService
     *
     * @param UserBddService $userBddService
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/{id}/users', name: 'admin.company.users')]
    public function getUsers(Request $request, $id, SiteService $siteService, UserBddService $userBddService)
    {

        $em = $this->doctrine->getManager();

        /** @var Company $company */
        $company = $em->getRepository(Company::class)->find($id);


        $form = $this->createForm(FilterCompanyUserType::class);
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $filter = $form->getData();
        } else {
            $filter = [];
        }

        $paginator = $userBddService->getCompanyCustomFilteredPaginator(1, 25, $request, $filter, $id);

        return $this->render('@OpenBack/company/company_users.html.twig',
            array('form' => $form->createView(),
                self::PAGINATION => $paginator,
                self::COMPANY => $company,
                'hasSite' => $siteService->checkIfCompanyHasSites($id),
                self::LOCALE => $request->getLocale(),
                self::TAB_ACTIVE => 3));
    }

    /**
     * @param Request          $request
     * @param                  $id
     * @param OrderService     $orderService
     * @param AddressService   $addressService
     *
     * @param ApiClientManager $apiClientManager
     * @param ApiConfigurator  $apiConfigurator
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/{id}/orders', name: 'admin.company.orders')]
    public function getOrders(Request $request, $id, OrderService $orderService, AddressService $addressService, ApiClientManager $apiClientManager, ApiConfigurator $apiConfigurator)
    {
        $this->configureApiConnection('operator', $apiClientManager, $apiConfigurator);
        $em = $this->doctrine->getManager();

        /** @var Company $company */
        $company = $em->getRepository(Company::class)->find($id);
        $userId = $company->getIzbergUserId();

        $totalCount = 0;
        $page = $request->query->get('page', 1);
        $offset = ($page - 1) * 25;

        $allOrder = [];

        if ($userId != null) {
            $orders = $orderService->fetchOrdersByUserIdWithPagination($userId, $totalCount, $offset, 25);
            /**
             * @var Order $order
             */
            foreach ($orders->getObjects() as $order) {
                $shippingPoint = $addressService->getCostCenterByIzbergAddressId($order->getShippingAddress()->getId());
                if ($shippingPoint != null) {
                    $name = $shippingPoint->getSite()->getName() . ' (' . $shippingPoint->getName() . ')';
                    $order->getShippingAddress()->setName($name);
                }
                if ($order->getStatus() === 85 || $order->getStatus() === 110) {
                    $order->setStatusFront("past");
                } elseif ($order->getStatus() === 2000 || $order->getStatus() === 3000) {
                    $order->setStatusFront("cancelled");
                } elseif ($order->getStatus() === 0 || $order->getStatus() === 60 || $order->getStatus() === 80) {
                    $order->setStatusFront("running");
                }
                $allOrder[] = $order;
            }
        }

        return $this->render('@OpenBack/company/company_orders.html.twig', [
            self::COMPANY => $company,
            'data' => $orderService->paginateArray(range(1, $totalCount), 25, $request, 'page'),
            'orders' => $allOrder,
            //'activeTab' => $activeTab,
            'customShowMenu' => false,
            self::TAB_ACTIVE => 4
        ]);

    }


    /**
     * @param                $id
     * @param                $orderId
     * @param OrderService   $orderService
     * @param MessageApi     $messageApi
     * @param CompanyService $companyService
     * @param AddressService $addressService
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/{id}/order/{orderId}', name: 'admin.company.order')]
    public function getOrder($id, $orderId, OrderService $orderService, MessageApi $messageApi, CompanyService $companyService, AddressService $addressService)
    {
        $company = $companyService->get($id);

        $order = $orderService->fetchOrderById($orderId);
        $shippingPoint = $addressService->getCostCenterByIzbergAddressId($order->getShippingAddress()->getId());
        if ($shippingPoint != null) {
            $name = $shippingPoint->getSite()->getName() . ' (' . $shippingPoint->getName() . ')';
            $order->getShippingAddress()->setName($name);
        }

        $merchantOrders = [];
        $subTotalVat = [];
        $hasInvoice = false;

        foreach ($order->getMerchantOrders() as $merchantOrder) {
            $merchantSubTotalVat = [];
            $mOrder = $orderService->fetchMerchantsOrderByOrderId($merchantOrder->getId());
            if (!$mOrder) {
                throw new UnexpectedValueException(sprintf('Cannot find merchant order with id %s', $merchantOrder->getId()));
            }

            $invoices = $orderService->getMerchantOrderInvoices($merchantOrder->getId(), true);
            if (count($invoices) > 0) {
                $hasInvoice = true;
            }

            $disputes = $messageApi->getDisputesByMerchantOrderId($merchantOrder->getId());
            $mOrder->setInvoices([]);
            if (count($disputes->objects) > 0) {
                $mOrder->setSeeDisputes(true);
            }

            /** @var OrderItem $article */
            foreach ($mOrder->getItems() as $article) {
                $percent = ($article->getVat() * 100) / $article->getPrice();
                $merchantSubTotalVat[$percent] = $article->getVat() * $article->getQuantity();
                $subTotalVat[$percent] = $article->getVat() * $article->getQuantity();
            }
            $mOrder->setSubTotalVat($merchantSubTotalVat);
            $merchantOrders[] = $mOrder;
        }

        $order->setSubTotalVat($subTotalVat);

        return $this->render('@OpenBack/company/company_order_detail.html.twig', [
            'company' => $company,
            'orders' => $merchantOrders,
            'order' => $order,
            'hasInvoice' => $hasInvoice,
            self::TAB_ACTIVE => 4
        ]);
    }

    /**
     * @param Request        $request
     * @param                $qualifier string the filter
     * @param CompanyService $companyService
     * @param CSVService     $csvService
     *
     * @return mixed
     * @throws CSVException when an error occurred while generating the CSV file
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/export/{qualifier}', name: 'admin.company.export')]
    public function exportCompanies(Request $request, $qualifier, CompanyService $companyService, CSVService $csvService, TranslatorInterface $translator)
    {
        $dateFormat = "d/m/Y H:i:s";

        $form = $this->createForm(FilterCompanyType::class);
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $filter = $form->getData();
        } else {
            $filter = [];
            $filter['status'] = 'all';
        }

        /**
         * @var Company $companies
         */
        $companies = $companyService->getAdminsByFilterAndQualifier($filter, $qualifier);

        /*
         * STEP 1: add custom columns for export
         */

        /**
         * @var Company $company
         */
        foreach ($companies as &$company) {

            //add shortcut to the city and country
            if ($company->getMainAddress() !== null) {
                $company->addProperty("city", $company->getMainAddress()->getCity());
                $company->addProperty(self::COUNTRY, $translator->trans($company->getMainAddress()->getCountry()->getCode(), array(), self::TRANSLATION_DOMAIN));
            } else {
                $company->addProperty("city");
                $company->addProperty(self::COUNTRY);
            }

            $company->addProperty(self::CATEGORY, $translator->trans($company->getCategory()->getLabel(), array(), self::TRANSLATION_DOMAIN));

            //format creation date
            $company->addProperty("formattedCreationAt", $company->getCreatedAt()->format($dateFormat));

            //format last connexion date
            if ($company->getLastConnexion() != null) {
                $company->addProperty(self::PROPERTY_FORMATTEDLASTCONNECTION, $company->getLastConnexion()->format($dateFormat));
            } else {
                $company->addProperty(self::PROPERTY_FORMATTEDLASTCONNECTION);
            }

            //translated status
            $company->addProperty('translatedStatus', $translator->trans("back.company.list.status_company." . $company->getStatus(), array(), self::TRANSLATION_DOMAIN));
        }

        /*
         * STEP 2: Now define the content of our export
         */
        $headers = [
            "Id",
            "back.company.list.name",
            "back.company.list.city",
            "back.company.list.country",
            "back.company.list.category",
            "back.company.list.created",
            "back.company.list.last",
            "back.company.list.status"
        ];

        $properties = [
            "id",
            "name",
            "city",
            self::COUNTRY,
            self::CATEGORY,
            "formattedCreationAt",
            self::PROPERTY_FORMATTEDLASTCONNECTION,
            "translatedStatus"
        ];

        /*
         * STEP 3: run the export
         */
        $content = $csvService->objArrayToCSV($companies, $headers, $properties);

        /*
         * return the result
         */
        return new Response($content, 200, array(
            'Content-Encoding' => 'UTF-16',
            'Content-Type' => 'application/CSV; charset=UTF-16',
            'Content-Disposition' => 'attachment; filename="companies.csv"'));
    }

    /**
     * @param Request           $request
     * @param int               $orderId
     * @param int               $companyId
     * @param OrderPDFGenerator $orderPDFGenerator
     *
     * @return Response
     * @throws \Mpdf\MpdfException
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/order/export/{companyId}/{orderId}', name: 'admin.company.order.export')]
    public function exportOrder(Request $request, int $orderId, int $companyId, OrderPDFGenerator $orderPDFGenerator, ApiClientManager $apiClientManager, ApiConfigurator $apiConfigurator): Response
    {

        /** @var Mpdf $pdf */
        $this->configureApiConnection('operator', $apiClientManager, $apiConfigurator);
        $pdf = $orderPDFGenerator->computeOrderPDF($orderId, $companyId, $request->getLocale());
        $pdf->output('order.pdf', Destination::DOWNLOAD);

        return $this->redirectToRoute('admin.company.orders');
    }

    /**
     * @param                     $id
     * @param TranslatorInterface $translator
     * @return mixed
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/document/{id}/view', name: 'document.view', options: ['expose' => true])]
    public function viewDocAction($id, TranslatorInterface $translator)
    {
        /** @var EntityRepository $documentRepository */
        $documentRepository = $this->doctrine->getRepository(Document::class);

        /** @var Document $document */
        $document = $documentRepository->find($id);

        $this->securityCheck($document, $translator);

        $file = stream_get_contents($document->getBinary());

        return new Response(
            $file,
            200,
            array(
                'Content-Type' => $document->getMimeType(),
                'Content-Disposition' => 'inline; filename="' . $document->getFilename() . '"'
            )
        );
    }

    private function securityCheck(Document $document, TranslatorInterface $translator)
    {
        /** @var DocumentRepository $documentRepository */
        $documentRepository = $this->doctrine->getRepository(Document::class);

        /**
         * security checks
         */
        $siteOrCompany = $documentRepository->getSiteOrCompanyByTypeAndId($document->getId());
        if (empty($siteOrCompany)) {
            throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_EXCEPTION));
        } else {
            if ($siteOrCompany[0] instanceof Site) {
                if (!$this->isGranted(self::ROLE_OPERATOR) && !$this->isGranted(self::ROLE_SUPER_ADMIN)) {
                    if ($this->getUser()->getCompany()->getId() != $siteOrCompany[0]->getCompany()->getId() || (!$this->isGranted(self::ROLE_BUYER_ADMIN))) {
                        throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_EXCEPTION));
                    }
                }
            }
            if ($siteOrCompany[0] instanceof Company) {
                if (!$this->isGranted(self::ROLE_OPERATOR) && !$this->isGranted(self::ROLE_SUPER_ADMIN)) {
                    if ($this->getUser()->getCompany()->getId() != $siteOrCompany[0]->getId() || (!$this->isGranted(self::ROLE_BUYER_ADMIN))) {
                        throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_EXCEPTION));
                    }
                }
            }
        }
    }

    /**
     * @param Request             $request
     * @param TranslatorInterface $translator
     * @return mixed
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/document/remove', name: 'document.remove', methods: ['POST'], options: ['expose' => true])]
    public function removeDocAction(Request $request, TranslatorInterface $translator)
    {
        /** @var EntityRepository $documentRepository */
        $documentRepository = $this->doctrine->getRepository(Document::class);
        $companyRepository = $this->doctrine->getRepository(Company::class);

        $companyId = $request->get('companyId', 0);
        $docId = $request->get('docId', 0);

        /** @var Document $document */
        $document = $documentRepository->find($docId);

        //security check
        $this->securityCheck($document, $translator);

        $company = null;

        if ($companyId != null) {
            /** @var Company $company */
            $company = $companyRepository->find($companyId);
        }

        // Get manager
        $em = $this->doctrine->getManager();

        // Persist
        if ($company != null) {
            $em->persist($company);
        }

        // Delete document
        $em->remove($document);

        // flush
        $em->flush();

        $json = array(
            'success' => true
        );

        return new JsonResponse(json_encode($json), 200, array(), true);
    }

    /**
     * @param Request        $request
     * @param CompanyService $companyService
     * @return RedirectResponse to companyInfo
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/companyInfo/activateTermPayment', name: 'admin.company.generalInfo.activateTermPayment', methods: ['POST'])]
    public function activateTermPayment(Request $request, CompanyService $companyService, TranslatorInterface $translator)
    {

        $formId = $this->createForm(IdForm::class, null, []);
        $formId->handleRequest($request);
        $id = $formId->getData()['id'];

        $em = $this->doctrine->getManager();

        try {
            /** @var Company $company */
            $company = $em->getRepository(Company::class)->find($id);
            $companyService->acceptTermPayment($company);
            $companyService->save($company);
            $this->addFlash(self::SUCCESS, $translator->trans('back.company.companyInfo.termpayment.askOk', array(), self::TRANSLATION_DOMAIN));
        } catch (Exception $e) {
            $this->addFlash(self::ERROR, $translator->trans('back.company.companyInfo.termpayment.askKo', array(), self::TRANSLATION_DOMAIN));
        }

        return $this->redirectToRoute('admin.company.generalInfo', array('id' => $company->getId()));
    }

    /**
     * @param Request        $request
     * @param CompanyService $companyService
     * @return RedirectResponse to companyInfo
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/companyInfo/removeTermPayment', name: 'admin.company.generalInfo.removeTermPayment', methods: ['POST'])]
    public function removeTermPayment(Request $request, CompanyService $companyService, TranslatorInterface $translator)
    {
        $formId = $this->createForm(IdForm::class, null, []);
        $formId->handleRequest($request);
        $id = $formId->getData()['id'];

        $em = $this->doctrine->getManager();

        try {
            /** @var Company $company */
            $company = $em->getRepository(Company::class)->find($id);
            $companyService->removeTermPayment($company);
            $companyService->save($company);
            $this->addFlash(self::SUCCESS, $translator->trans('back.company.companyInfo.termpayment.removeOk', array(), self::TRANSLATION_DOMAIN));
        } catch (Exception $e) {
            $this->addFlash(self::ERROR, $translator->trans('back.company.companyInfo.termpayment.removeKo', array(), self::TRANSLATION_DOMAIN));
        }

        return $this->redirectToRoute('admin.company.generalInfo', array('id' => $company->getId()));
    }

    /**
     * @param Request $request
     * @param MerchantApi $merchantApi
     *
     * @return JsonResponse
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/companyInfo/updateAccountType', name: 'admin.company.generalInfo.updateAccountType', methods: ['POST'])]
    public function updateAccountType(Request $request, MerchantApi $merchantApi): JsonResponse
    {
        try {
            $companyId = $request->get('companyId');
            $categoryId = $request->get('categoryId');

            /** @var EntityManager $em */
            $em = $this->doctrine->getManager();

            /** @var Company $company */
            $company = $em->getRepository(Company::class)->find($companyId);
            $category = $em->getReference(Category::class, $categoryId);
            $company->setCategory($category);
            $em->persist($company);

            if ($company->getCategory()->getLabel() === Category::CATEGORY_BAFV) {
                $merchantRepository = $em->getRepository(Merchant::class);
                /** @var Merchant $merchant */
                $merchant = $merchantRepository->findOneBy(['identification' => $company->getIdentification()]);

                if (!empty($merchant)) {
                    $userBafvMerchantListRepository = $em->getRepository(UserBafvMerchantList::class);

                    $userBafvMerchantList = $userBafvMerchantListRepository->findBy(
                        ['company' => $company, 'merchantId' => $merchant->getId()]
                    );

                    if (empty($userBafvMerchantList)) {
                        $izbMerchant = $merchantApi->getMerchantByIdent($company->getIdentification());
                        $merchantId = $izbMerchant->objects[0]->id;

                        $userBafvMerchantList = (new UserBafvMerchantList())
                            ->setCompany($company)
                            ->setMerchantId($merchantId);

                        $em->persist($userBafvMerchantList);
                    }
                }
            }

            $em->flush();
            return $this->json(['message' => 'Success']);

        } catch (Exception $e) {
            $this->logger->error(" * error while processing updateAccountType",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::TECHNICAL_ERROR,
                    LogUtil::USER_NAME => $this->getUsername(),
                    "message" => $e->getMessage()
                ])
            );

            return $this->json(['message' => 'Error while updating account type !'], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Send an email to owners to inform them that their company has been modified
     * @param Company $company the modified company
     * @param string $templateName the email template to ise
     */
    private function sendCompanyModificationNotificationToOperator($company, $templateName, MailService $mailService)
    {
        //send an email to owners of the company

        if (!empty($company->getUsers())) {

            /**
             * @var User $companyUser
             */
            foreach ($company->getUsers() as $companyUser) {

                if ($companyUser->isEnabled() && !empty($companyUser->getRoles()) && $companyUser->hasRole('ROLE_BUYER_ADMIN')) {
                    $url = $this->generateUrl('front.company.info', [], UrlGeneratorInterface::ABSOLUTE_URL);
                    $mailService->sendEmailMessage($templateName, $companyUser->getLocale(), $companyUser->getEmail(), [
                        MailService::FIRST_NAME_VAR => $companyUser->getFirstname(),
                        MailService::LAST_NAME_VAR => $companyUser->getLastname(),
                        "companyName" => $company->getName(),
                        "url" => $url
                    ]);
                }
            }
        }
    }

    /**
     * @param null|FormInterface $form
     * @param $contact
     * @param string $contactId
     */
    private function setCheck(?FormInterface $form, $contact, string $contactId)
    {
        if ($contact != null) {
            $form->get($contactId)->get('check')->setData(true);
        }
    }

    /**
     * @param FormInterface $form
     * @param Company $company
     * @return Company
     */
    private function cleanCompanyAddress(?FormInterface $form, $company)
    {
        if ($form->get(self::BILLING_ADDRESS)->get('check')->getData() === false) {
            $company->setBillingAddress(null);
        }
        return $company;
    }
}

