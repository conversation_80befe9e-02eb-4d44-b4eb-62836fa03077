<?php
/**
 * Created by Php<PERSON>torm.
 * User: PPH04712
 * Date: 30/11/2017
 * Time: 16:37
 */

namespace Open\BackBundle\Controller;




use AppBundle\Services\CompanyService;
use Open\BackBundle\Form\IdForm;
use Open\TicketBundle\Services\TicketService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class DefaultAdminController extends AbstractController
{


    /**
     * @param Request        $request
     * @param CompanyService $companyService
     * @param TicketService  $ticketService
     * @return mixed
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/admin', name: 'admin.dashboard', defaults: ['_locale' => 'en'])]
    public function defaultAdminAction (Request $request, CompanyService $companyService, TicketService $ticketService)
    {

        $acceptableCompany = $companyService->getAcceptableCompany(1, 5, $request, []);

        $companyWithTermPaymentRequest = $companyService->getCompaniesWithTermPaymentPaginator(5, $request);

        $newTicket = $ticketService->getAdminNotReadTicket(1, 5, $request);

        $formModal = $this->paymentTermDenyForm();

        return $this->render("@OpenBack/default/index.html.twig",
            array(
                'acceptableCompany' => $acceptableCompany,
                'companyWithTermPaymentRequest' => $companyWithTermPaymentRequest,
                'newTicket' => $newTicket,
                'formModal' => $formModal->createView(),
                'locale' => $request->getLocale(),
                'form_accept_paymentTerm' => $this->createForm(IdForm::class, null)->createView()
            ));
    }

    /***
     * @return \Symfony\Component\Form\FormInterface
     */
    private function paymentTermDenyForm (){
        return $this->createFormBuilder()
            ->add('reason', TextareaType::class, array('label' => 'back.company.companyInfo.termpayment.reason',  'translation_domain' => 'AppBundle', 'required' => true))
            ->add('company', HiddenType::class)
            ->setAction($this->generateUrl('admin.payment_term.deny'))
            ->setMethod('POST')
            ->getForm();
    }

}
