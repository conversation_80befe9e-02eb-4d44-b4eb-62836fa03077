<?php

namespace Open\BackBundle\Controller;

use AppBundle\Controller\MkoController;
use Open\BackBundle\Service\HealthMonitorService;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

class HealthMonitor extends MkoController
{
    /**
     * @param Request              $request
     * @param HealthMonitorService $healthMonitorService
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/monitor', name: 'admin.monitor')]
    public function indexAction(Request $request, HealthMonitorService $healthMonitorService)
    {
        $statusCheck = $healthMonitorService->checkStatus();
        $globalSystem = $healthMonitorService->isSystemOperational();
        return $this->render(
            '@OpenBack/monitor/index.html.twig',
            [
                'globalSystem' => $globalSystem,
                'statusCheck' => $statusCheck,
            ]
        );
    }

    /**
     * @param Request              $request
     * @param HealthMonitorService $healthMonitorService
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/monitor/daily', name: 'admin.monitordaily')]
    public function dailyMonitor(Request $request, HealthMonitorService $healthMonitorService)
    {
        $globalSystem = $healthMonitorService->isSystemOperational();
        $fullHistory = $healthMonitorService->getFullHistory();

        return $this->render(
            '@OpenBack/monitor/dailyView.html.twig',
            [
                'globalSystem' => $globalSystem,
                'fullHistory' => $fullHistory,
            ]
        );
    }
}
