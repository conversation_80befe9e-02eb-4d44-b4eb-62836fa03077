<?php

namespace Open\BackBundle\Controller;

use AppBundle\Controller\MkoController;
use AppBundle\Services\InvoiceService;
use AppBundle\Services\MailService;
use Open\IzbergBundle\Api\InvoiceApi;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Contracts\Translation\TranslatorInterface;

class InvoiceController extends MkoController
{
    /**
     * @param Request $request
     * @param                     $invoiceId
     * @param TranslatorInterface $translator
     *
     * @param InvoiceApi $invoiceApi
     * @param InvoiceService $invoiceService
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_SUPER_ADMIN")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/invoice/reminder/{invoiceId}', name: 'invoice.reminder', options: ['expose' => true], methods: ['GET'])]
    public function sendInvoiceReminder(Request $request, $invoiceId, TranslatorInterface $translator, InvoiceApi $invoiceApi, InvoiceService $invoiceService)
    {
        $this->sendInvoiceReminderEmail($invoiceId, MailService::PAYMENT_TERMPAYMENT_BANK_TRANSFER_INSCTRUCTIONS_TO_BUYER, $invoiceApi, $invoiceService);

        return $this->render('@OpenBack/invoice_reminder.html.twig',
            array('text' => $translator->trans('invoice.reminder', [], self::TRANSLATION_DOMAIN)));
    }

    /**
     * @param Request             $request
     * @param                     $invoiceId
     * @param TranslatorInterface $translator
     *
     * @param InvoiceApi          $invoiceApi
     *
     * @param InvoiceService      $invoiceService
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_SUPER_ADMIN")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/invoice/lateReminder/{invoiceId}', name: 'invoice.latereminder', methods: ['GET'], options: ['expose' => true])]
    public function sendInvoiceLateReminder(Request $request, $invoiceId, TranslatorInterface $translator, InvoiceApi $invoiceApi, InvoiceService $invoiceService)
    {
        $this->sendInvoiceReminderEmail($invoiceId, MailService::PAYMENT_TERMPAYMENT_BANK_TRANSFER_REMINDER_TO_BUYER, $invoiceApi, $invoiceService);
        return $this->render('@OpenBack/invoice_reminder.html.twig',
            array('text' => $translator->trans('invoice.reminder', [], self::TRANSLATION_DOMAIN)));
    }

    private function sendInvoiceReminderEmail($invoiceId, $template, InvoiceApi $invoiceApi, InvoiceService $invoiceService){
        if($invoiceId){
            $invoice = $invoiceApi->fetchCustomerInvoice($invoiceId);
            if($invoice){
                $invoiceService->sendInvoiceReminderByInvoice($invoice, $template);
            }
        }
    }
}
