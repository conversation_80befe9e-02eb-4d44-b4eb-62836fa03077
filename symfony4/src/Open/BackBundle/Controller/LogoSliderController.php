<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 26/01/2018
 * Time: 10:45
 */

namespace Open\BackBundle\Controller;


use AppBundle\Controller\MkoController;
use AppBundle\Entity\Image;
use AppBundle\Entity\Node;
use AppBundle\Entity\NodeContent;
use AppBundle\Repository\NodeRepository;
use AppBundle\Services\NotificationService;
use Open\BackBundle\Form\IdForm;
use Open\BackBundle\Form\LogoForm;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Contracts\Translation\TranslatorInterface;

class LogoSliderController extends MkoController
{

    private const SLIDER = 'logo';
    private const BACKGROUND_SLIDER_DIR_PARAM = 'logo_directory';


    /**
     * @param Request             $request
     * @param NotificationService $notificationService
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/logo/list/', name: 'admin.logo.list')]
    public function listLogoSlidersAction(Request $request, NotificationService $notificationService)
    {

        $qb = $notificationService->getDefaultQueryBuilder();
        $qb->andWhere("e.type = :logo");
        $qb->setParameter(self::SLIDER, self::SLIDER);
        $qb->orderBy('e.orderNode', 'ASC');

        $paginator = $notificationService->getPaginatorByQb($qb, 1, 25, $request);

        return $this->render('@OpenBack/logo/logo_list.html.twig',
            [
                'pagination' => $paginator,
                'locale' => $request->getLocale(),
                'form_change_slider_status' => $this->createForm(IdForm::class, null)->createView()
            ]
        );
    }

    /**
     * @param Request $request
     * @param TranslatorInterface $translator
     *
     * @param int|null $id the identifier of the slider to edit
     *
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/logo/{id}/edit', name: 'admin.logo.edit')]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/logo/add', name: 'admin.logo.add')]
    public function editLogoSliderAction(Request $request, TranslatorInterface $translator, ?int $id = null){
        /**
         * @var NodeRepository $nodeRepository
         */
        $nodeRepository = $this->doctrine->getRepository(Node::class);
        $em = $this->doctrine->getManager();
        $isBackgroundSet = false;

        if (!is_null($id)) {
            /** @var Node $node */
            $node = $nodeRepository->findOneById($id);
            $actualNode = clone $node;
            if(! is_null($actualNode->getBackgroundImage()) && ! empty($actualNode->getBackgroundImage()))
            {
                $isBackgroundSet = true;
            }

            if(is_null($node)) {
                throw new AccessDeniedHttpException("the specified entity is not exists");
            }

            //we only want slider here
            if ($node->getType() != self::SLIDER){
                throw new AccessDeniedHttpException("the specified entity is not a slider item");
            }
        } else {
            /** @var Node $node */
            $node = new Node();
            $node->setType(self::SLIDER);
            $node->setStatus('draft');
            $node->setExternalLinkType(true);
            $node->setTitleVisible(false);
        }

        // If no en content found for this node then add an one
        if (is_null($node->getContent('en'))) {
            $en = new NodeContent();
            $en->setNode($node);
            $en->setLang('en');
            $en->setExternalLinkType(true);
            $node->addContent($en);
        }

		// If no fr content found for this node then add an one
		if (is_null($node->getContent('fr'))) {
			$fr = new NodeContent();
			$fr->setNode($node);
			$fr->setLang('fr');
            $fr->setExternalLinkType(true);
			$node->addContent($fr);
		}

        // If no en content found for this node then add an one
        if (is_null($node->getContent('es'))) {
            $es = new NodeContent();
            $es->setNode($node);
            $es->setLang('es');
            $es->setExternalLinkType(true);
            $node->addContent($es);
        }

        // If no en content found for this node then add an one
        if (is_null($node->getContent('de'))) {
            $de = new NodeContent();
            $de->setNode($node);
            $de->setLang('de');
            $de->setExternalLinkType(true);
            $node->addContent($de);
        }

        // If no en content found for this node then add an one
        if (is_null($node->getContent('it'))) {
            $it = new NodeContent();
            $it->setNode($node);
            $it->setLang('it');
            $it->setExternalLinkType(true);
            $node->addContent($it);
        }

        if (is_null($node->getContent('nl'))) {
            $nl = new NodeContent();
            $nl->setNode($node);
            $nl->setLang('nl');
            // $nl->setExternalLinkType(true);
            $node->addContent($nl);
        }

        $form = $this->createForm(
            LogoForm::class,
            $node
        );

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $node = $form->getData();

            // Background image
            if(! is_null($node->getBackgroundImage()) && !empty($node->getBackgroundImage())) {
                $file = $node->getBackgroundImage();
                $img = new Image();
                $img->setName($file->getClientOriginalName());
                $img->setMime($file->getMimeType());
                $img->setBlob(file_get_contents($file->getRealPath()));
                $node->setBackgroundImage($img);
            }

            // Si le backgroud n'est pas vide, on le laisse tel quel
            else if($isBackgroundSet) {
                $node->setBackgroundImage($actualNode->getBackgroundImage());
            }
            else {
                $node->setBackgroundImage(null);
            }

            /** @var NodeContent $content */
            /* foreach ($node->getContent() as $content) {
                if (empty($content->getTitle()) && empty($content->getBody())) {
                    $node->removeContent($content);

                    $em->remove($content);
                    $em->flush();
                }
            }*/

            try {
                $em->persist($node);
                $em->flush();
                if($id !== null) {
                    $this->addFlash('success', $translator->trans('back.logo.update.confirm', [], self::TRANSLATION_DOMAIN));
                } else {
                    $this->addFlash('success', $translator->trans('back.logo.create.confirm', [], self::TRANSLATION_DOMAIN));
                }
                return $this->redirectToRoute('admin.logo.list');
            } catch(\Exception $e) {
                $this->logger->error("Error while saving slider item: ".$e->getMessage(),
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::TECHNICAL_ERROR,
                        LogUtil::USER_NAME => $this->getUser()->getUsername(),
                        "exception" => $e->getTraceAsString()
                    ])
                );
                if (!is_null($id)) {
                    $this->addFlash('error', $translator->trans('back.logo.update.error', [], self::TRANSLATION_DOMAIN));
                } else {
                    $this->addFlash('error', $translator->trans('back.logo.create.error', [], self::TRANSLATION_DOMAIN));
                }
            }
        }

        return $this->render(
            '@OpenBack/logo/logo_edit.html.twig',
            [
                'form' => $form->createView(),
                'id' => $id,
				'nodeType' => self::SLIDER,
                'logoItem' => $node
            ]
        );
    }

    /**
     * @param int $id the identifier of the slider to delete
     * @param TranslatorInterface $translator
     *
     * @param NotificationService $notificationService
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/logo/{id}/delete', name: 'admin.logo.delete')]
    public function deleteLogoSliderAction($id, TranslatorInterface $translator, NotificationService $notificationService){
        /** @var string $imageDirectory */
        $imageDirectory = $this->getParameter(self::BACKGROUND_SLIDER_DIR_PARAM);

        /**
         * @var NodeRepository $nodeRepository
         */
        $nodeRepository = $this->doctrine->getRepository(Node::class);
        $em = $this->doctrine->getManager();

        /** @var Node $node */
        $node = $nodeRepository->findOneById($id);
        if(is_null($node)) {
            throw new AccessDeniedHttpException("the specified entity does not exists");
        }

        //we only want slider here
        if ($node->getType() != self::SLIDER){
            throw new AccessDeniedHttpException("the specified entity is not a slider item");
        }

        try {
            /** @var NodeContent $content */
            foreach ($node->getContent() as $content) {
                if (empty($content->getTitle()) && empty($content->getBody())) {
                    $node->removeContent($content);

                    $em->remove($content);
                    $em->flush();
                }
            }

            if(! is_null($node->getBackgroundImage())) {
                $fileSystem = new Filesystem();
                $fileSystem->remove($imageDirectory . $node->getBackgroundImage());
            }

            $em->remove($node);
            $em->flush();

            $this->removeOldBackgroundFiles($notificationService);

            $this->addFlash('success', $translator->trans('back.logo.delete.confirm', [], self::TRANSLATION_DOMAIN));
        } catch(\Exception $e) {
            $this->logger->error("Error while removing slider item: ".$e->getMessage(),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::TECHNICAL_ERROR,
                    LogUtil::USER_NAME => $this->getUser()->getUsername(),
                    "exception" => $e->getTraceAsString()
                ])
            );
            $this->addFlash('error', $translator->trans('back.logo.delete.error', [], self::TRANSLATION_DOMAIN));
        }

        return $this->redirectToRoute('admin.logo.list');
    }

    /**
     * @param Request $request
     * @param $status
     * @param TranslatorInterface $translator
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/logo/changeStatus/{status}', name: 'admin.logo.status')]
    public function changeLogoSliderStatusAction(Request $request, $status, TranslatorInterface $translator){

        $form = $this->createForm(IdForm::class, null, []);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $id = $form->getData()['id'];

            /**
             * @var NodeRepository $nodeRepository
             */
            $nodeRepository = $this->doctrine->getRepository(Node::class);
            $em = $this->doctrine->getManager();

            /** @var Node $node */
            $node = $nodeRepository->findOneById($id);
            if(is_null($node)) {
                throw new AccessDeniedHttpException("the specified entity is not exists");
            }

            //we only want slider here
            if ($node->getType() != self::SLIDER){
                throw new AccessDeniedHttpException("the specified entity is not a slider item");
            }

            try {
                if($status === 'enable')
                {
                    $node->setStatus(Node::STATUS_PUBLISHED);
                }
                else if($status === 'disable')
                {
                    $node->setStatus(Node::STATUS_DRAFT);
                }

                $em->persist($node);
                $em->flush();

                $this->addFlash('success', $translator->trans('back.logo.update.confirm', [], self::TRANSLATION_DOMAIN));
            } catch(\Exception $e) {
                $this->logger->error("Error while removing slider item: ".$e->getMessage(),
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::TECHNICAL_ERROR,
                        LogUtil::USER_NAME => $this->getUser()->getUsername(),
                        "exception" => $e->getTraceAsString()
                    ])
                );
                $this->addFlash('error', $translator->trans('back.logo.update.error', [], self::TRANSLATION_DOMAIN));
            }
        }else{
            throw new BadRequestHttpException('invalid form');
        }


        return $this->redirectToRoute('admin.logo.list');
    }

    private function removeOldBackgroundFiles(NotificationService $notificationService) {
        $nodesBackground = [];
        $fileSystem = new Filesystem();

        // Get files in background image directory
        /** @var string $imageDirectory */
        $imageDirectory = $this->getParameter(self::BACKGROUND_SLIDER_DIR_PARAM);
        $files = array_diff(scandir($imageDirectory), array('.', '..', '.gitkeep'));

        $qb = $notificationService->getDefaultQueryBuilder();
        $qb->andWhere("e.type = :logo");
        $qb->setParameter(self::SLIDER, self::SLIDER);
        $query = $qb->getQuery();
        $nodes = $query->getResult();
        foreach($nodes as $node) {
            if(! is_null($node->getBackgroundImage())) {
                $nodesBackground[] = $node->getBackgroundImage();
            }
        }


        foreach($files as $file) {
            if(! in_array($file, $nodesBackground, true)) {
                $fileSystem->remove($imageDirectory . $file);
            }
        }
    }

    private function createBackgeroundDirectory()
    {
        $fileSystem = new Filesystem();
        /** @var string $imageDirectory */
        $imageDirectory = $this->getParameter(self::BACKGROUND_SLIDER_DIR_PARAM);

        if(! $fileSystem->exists($imageDirectory)) {
            $fileSystem->mkdir($imageDirectory);
        }
    }

    /**
     * @return string
     */
    private function generateUniqueFileName()
    {
        // md5() reduces the similarity of the file names generated by
        // uniqid(), which is based on timestamps
        return md5(uniqid());
    }

}
