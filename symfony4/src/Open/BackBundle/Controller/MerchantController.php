<?php

namespace Open\BackBundle\Controller;

use AppBundle\Controller\MkoController;
use AppBundle\Entity\Merchant;
use AppBundle\Form\MerchantForm;
use AppBundle\Model\Id;
use AppBundle\Services\MerchantService;
use Open\BackBundle\Form\DeactivateMerchantForm;
use Open\BackBundle\Form\FilterMerchantType;
use Open\BackBundle\Form\IdForm;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Contracts\Translation\TranslatorInterface;

class MerchantController extends MkoController
{
    /**
     * @param Request         $request
     * @param MerchantService $merchantService
     * @return \Symfony\Component\HttpFoundation\Response|null
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/vendor/list', name: 'admin.merchant.list')]
    public function listMerchant(Request $request, MerchantService $merchantService)
    {

        $page = 1;
        if($request->query->get('page') != null){
            $page = $request->query->get('page');
        }

        $form = $this->createForm(FilterMerchantType::class);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $filter = $form->getData();
        }
        else{
            $filter = [];
            $filter['status'] = 'all';
        }

        $paginator = $merchantService->paginatedMerchants($filter, $page, 25);

        return $this->render(
            '@OpenBack/merchant/merchant_list.html.twig',
            [
                'form' => $form->createView(),
                'pagination' => $paginator,
                'locale' => $request->getLocale(),
            ]
        );
    }

    /**
     *
     * @param Request             $request
     * @param                     $id
     * @param MerchantService     $merchantService
     *
     * @param TranslatorInterface $translator
     * @return \Symfony\Component\HttpFoundation\RedirectResponse|\Symfony\Component\HttpFoundation\Response|null
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/vendor/{id}/info/edit', name: 'admin.merchant.info.edit')]
    public function editMerchantInfo(Request $request, $id, MerchantService $merchantService, TranslatorInterface $translator)
    {
        $user = $this->getUser();
        $merchant = $merchantService->findMerchantEntityById($id);

        $form = $this->createForm(
            MerchantForm::class,
            $merchant,
            [
                'validation_groups' =>['default'],
                'method' => 'patch',
                'translator' => $translator,
            ]
        );

        if ($merchant->getStatus() === Merchant::STATUS_ACCEPTED) {
            $form->remove('submit');
        }

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {

            if ($merchantService->updateMerchantInformation($merchant, $user)) {
                $this->addFlash('success', $translator->trans('back.merchant.form.update.success', [], 'AppBundle'));
                return $this->redirectToRoute('admin.merchant.generalInfo', ['id'=> $merchant->getId()]);
            } else {
                $this->addFlash('error', $translator->trans('back.merchant.form.update.error', [], 'AppBundle'));
            }
        }

        return $this->render(
            '@OpenBack/merchant/merchant_info_edit.html.twig',
            [
                'form' => $form->createView(),
                'merchant' => $merchant,
                'tabActive' => 1,
            ]
        );
    }

    /**
     * @param                     $id
     * @param MerchantService     $merchantService
     *
     * @param TranslatorInterface $translator
     * @return \Symfony\Component\HttpFoundation\Response|null
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/vendor/{id}/merchantInfo', name: 'admin.merchant.generalInfo')]
    public function getMerchantGeneralInformations($id, MerchantService $merchantService, TranslatorInterface $translator)
    {
        /** @var Merchant $merchant */
        $merchant = $merchantService->findMerchantEntityById($id);

        if(!$merchant->isTvaChecked()){
            $this->addFlash('error', $translator->trans('home.register_merchant_tva_not_checked', [], 'AppBundle'));
        }

        return $this->render(
            '@OpenBack/merchant/merchant_merchantInfo.html.twig',
            [
                'merchant' => $merchant,
                'tabActive' => 1,
                'form_activate_merchant' => $this->createForm(IdForm::class, new Id($merchant->getId()))->createView(),
            ]
        );
    }

    /**
     * @param MerchantService $merchantService
     * @param TranslatorInterface $translator
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\RedirectResponse
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/vendor/activate', name: 'admin.merchant.activate', methods: ['POST'])]
    public function activateMerchant(MerchantService $merchantService, TranslatorInterface $translator, Request $request)
    {
        $form = $this->createForm(IdForm::class, null, []);
        $form->handleRequest($request);

        $id = $form->getData()['id'];
        $merchant = $merchantService->findMerchantEntityById($id);

        $user = $this->getUser();

        if ($merchantService->activateMerchant($merchant, $user)) {
            $this->addFlash('success', $translator->trans('back.merchant.merchantInfo.validate.ok', [], self::TRANSLATION_DOMAIN));
        } else {
            $this->addFlash('error', $translator->trans('back.merchant.merchantInfo.validate.ko', [], self::TRANSLATION_DOMAIN));
        }

        return $this->redirectToRoute(
            'admin.merchant.generalInfo',
            [
                'id'=> $merchant->getId(),
            ]
        );
    }

    /**
     *
     * @param Request         $request
     * @param                 $id
     * @param MerchantService $merchantService
     *
     * @return \Symfony\Component\HttpFoundation\RedirectResponse|\Symfony\Component\HttpFoundation\Response|null
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/vendor/{id}/reject', name: 'admin.merchant.reject')]
    public function rejectMerchant(Request $request, $id,MerchantService $merchantService, TranslatorInterface $translator)
    {

        $form = $this->createForm(DeactivateMerchantForm::class);
        $form->handleRequest($request);

        $merchant = $merchantService->findMerchantEntityById($id);

        $user = $this->getUser();

        if ($form->isSubmitted() && $form->isValid()) {
            $formData = $form->getData();
            $reason = $formData['reason'] ?? '';
            if ($merchantService->rejectMerchant($merchant, $reason, $user)) {
                $this->addFlash(
                    'success',
                    $translator->trans('back.merchant.merchantInfo.reject.ok', [], self::TRANSLATION_DOMAIN)
                );
            } else {
                $this->addFlash(
                    'error',
                    $translator->trans('back.merchant.merchantInfo.reject.ko', [], self::TRANSLATION_DOMAIN)
                );
            }

            return $this->redirectToRoute('admin.merchant.generalInfo', ['id'=> $merchant->getId()]);
        }

        return $this->render(
            '@OpenBack/merchant/merchant_deactivate.html.twig',
            [
                'form' => $form->createView(),
            ]
        );
    }
}
