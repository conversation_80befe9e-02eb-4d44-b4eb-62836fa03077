<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 26/01/2018
 * Time: 10:45
 */

namespace Open\BackBundle\Controller;


use AppBundle\Controller\MkoController;
use AppBundle\Entity\Node;
use AppBundle\Entity\NodeContent;
use AppBundle\Entity\TransactionalEmail;
use AppBundle\Exception\MailException;
use AppBundle\Exception\MailValidationException;
use AppBundle\Exception\TemplateException;
use AppBundle\Repository\NodeRepository;
use AppBundle\Repository\TransactionalEmailRepository;
use AppBundle\Services\EmailTemplateService;
use AppBundle\Services\MailService;
use AppBundle\Services\NotificationService;
use Doctrine\ORM\Query\Expr\Join;
use Open\BackBundle\Form\NotificationForm;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\SubmitButton;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Contracts\Translation\TranslatorInterface;

class NotificationController extends MkoController
{

    private const EMAIL = 'email';

    /**
     * @param Request             $request
     * @param NotificationService $notificationService
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/notification/list/', name: 'admin.notification.list')]
    public function listNotificationsAction(Request $request, NotificationService $notificationService): Response
    {
        $qb = $notificationService->getDefaultQueryBuilder();
        $qb->addSelect("te.active")
            ->leftJoin(TransactionalEmail::class, "te", Join::WITH, "te.emailIdentifier = e.slug")
            ->andWhere("e.type = :email")
            ->setParameter(self::EMAIL, self::EMAIL)
            ->orderBy("e.slug");

        $paginator = $notificationService->getPaginatorByQb($qb, 1, 200, $request);


        return $this->render('@OpenBack/notification/notification_list.html.twig',
            [
                'pagination' => $paginator,
                'locale' => $request->getLocale()
            ]
        );


    }

    /**
     * @param Request $request
     * @param int $id the identifier of the notification to edit
     * @param NotificationService $notificationService
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/notification/{id}/switchTemplateActivation', name: 'admin.notification.switchTemplateActivation')]
    public function switchTemplateActivationAction(Request $request, int $id, NotificationService $notificationService): Response
    {
        $nodeRepository = $this->doctrine->getRepository(Node::class);
        $email = $nodeRepository->findOneById($id);

        /** @var TransactionalEmailRepository $transactionalEmailRepository */
        $transactionalEmailRepository = $this->doctrine->getRepository(TransactionalEmail::class);
        /** @var TransactionalEmail $transactionalEmail */
        $transactionalEmail = $transactionalEmailRepository->findOneByEmailIdentifier($email->getSlug());

        $transactionalEmail->setActive(!$transactionalEmail->isActive());
        $transactionalEmailRepository->updateTransactionalEmail($transactionalEmail);

        return $this->listNotificationsAction($request, $notificationService);
    }

    /**
     * @param Request $request
     * @param int $id the identifier of the notification to edit
     * @param TranslatorInterface $translator
     *
     * @param EmailTemplateService $emailTemplateService
     * @param MailService $emailService
     * @return mixed
     * @throws TemplateException
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/notification/{id}/edit', name: 'admin.notification.edit')]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/notification/add', name: 'admin.page.add')]
    public function editNotificationAction(Request $request, $id, TranslatorInterface $translator, EmailTemplateService $emailTemplateService, MailService $emailService)
    {
        /**
         * @var NodeRepository $nodeRepository
         */
        $nodeRepository = $this->doctrine->getRepository(Node::class);
        $em = $this->doctrine->getManager();

        /** @var Node $node */
        $node = $nodeRepository->findOneById($id);

        //we only want emails here
        if ($node->getType() != self::EMAIL){
            throw new AccessDeniedHttpException("the specified entity is not an email");
        }

        //list of variables for this template. If an error occurred while trying to get the list of variables, the list will be empty
        //so the editor will not show any available variables.
        $variables = [];

        try {
            $variables = array_keys($emailTemplateService->getTemplate($node->getSlug())->getVariables());
        }catch (TemplateException $e){
            $this->logger->error("enable to get list of variables for template",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::EMAIL_ERROR,
                    LogUtil::USER_NAME => $this->getUsername(),
                    "templateName" => $node->getSlug(),
                    "message" => $e->getMessage()
                ])
            );
        }

        // If no en content found for this node then add an one
        if (is_null($node->getContent('en'))) {
            $en = new NodeContent();
            $en->setNode($node);
            $en->setLang('en');
            $node->addContent($en);
        }

		// If no fr content found for this node then add an one
		if (is_null($node->getContent('fr'))) {
			$fr = new NodeContent();
			$fr->setNode($node);
			$fr->setLang('fr');

			$node->addContent($fr);
		}

        // If no es content found for this node then add an one
        if (is_null($node->getContent('es'))) {
            $es = new NodeContent();
            $es->setNode($node);
            $es->setLang('es');
            $node->addContent($es);
        }

        // If no it content found for this node then add an one
        if (is_null($node->getContent('it'))) {
            $it = new NodeContent();
            $it->setNode($node);
            $it->setLang('it');
            $node->addContent($it);
        }

        // If no de content found for this node then add an one
        if (is_null($node->getContent('de'))) {
            $de = new NodeContent();
            $de->setNode($node);
            $de->setLang('de');
            $node->addContent($de);
        }

        if (is_null($node->getContent('nl'))) {
            $nl = new NodeContent();
            $nl->setNode($node);
            $nl->setLang('nl');
            $node->addContent($nl);
        }

        $form = $this->createForm(
            NotificationForm::class,
            $node
        );

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            /** @var SubmitButton $saveAndSendButton */
            $saveAndSendButton = $form->get("saveAndSend");

            if ($saveAndSendButton->isClicked() && empty($form->get("email")->getData()) ){
                $form->get("email")->addError(new FormError($translator->trans("back.notification.edit.empty_email_error", [], self::TRANSLATION_DOMAIN)));
            }
            else {
                $sendTestEmail = false;
                $recipientEmail = $form->get("email")->getData();
                /** @var  SubmitButton $saveAndSendButton*/
                $saveAndSendButton = $form->get("saveAndSend");
                if ($saveAndSendButton->isClicked()){
                    $sendTestEmail = true;
                }

                $node = $form->getData();

                $baseTemplate = $emailTemplateService->getTemplate($node->getSlug());

                $currentValidatedLanguage = null;
                try {

                    /** @var NodeContent $content */
                    foreach ($node->getContent() as $content) {
                        $currentValidatedLanguage = $content->getLang();
                        //modify the content of the base template
                        $baseTemplate->setContent($content->getBody());
                        //set the subject to validate
                        $baseTemplate->setSubject($content->getTitle());

                        //validate the content
                        $emailService->validateEmailTemplate($baseTemplate);

                        if ($sendTestEmail) {
                            $emailService->sendEmailMessage($baseTemplate->getTemplateName(),
                                $currentValidatedLanguage, $recipientEmail, $baseTemplate->getVariables());
                        }
                    }

                    $em->flush();

                    $this->addFlash('success', $translator->trans('back.notification.edit.confirm', [], self::TRANSLATION_DOMAIN));

                    if ($sendTestEmail) {
                        $message = $translator->trans('node.form.test', [], self::TRANSLATION_DOMAIN);
                        $message = str_replace('%email%', $recipientEmail, $message);
                        $this->addFlash('success', $message);
                    }


                } catch (MailException $e) {
                    $message = $translator->trans('node.form.error.template_validation', [], self::TRANSLATION_DOMAIN);
                    $message = str_replace('%message%', preg_replace("/\"__string_template__.*\"/", "template", $e->getMessage()), $message);
                    $message = str_replace('%locale%', $currentValidatedLanguage, $message);

                    $this->addFlash('danger', $message);
                } catch (MailValidationException $e) {
                    $this->addFlash('danger', $e->getMessage());
                } catch (\Exception $e) {
                    $this->logger->error("Error while saving notification: " . $e->getMessage(),
                        LogUtil::buildContext([
                            LogUtil::EVENT_NAME => EventNameEnum::TECHNICAL_ERROR,
                            LogUtil::USER_NAME => $this->getUser()->getUsername(),
                            "exception" => $e->getTraceAsString()
                        ])
                    );
                    if (!is_null($id)) {
                        $this->addFlash('danger', $translator->trans('node.form.submit.error.update', [], self::TRANSLATION_DOMAIN));
                    } else {
                        $this->addFlash('danger', $translator->trans('node.form.submit.error.create', [], self::TRANSLATION_DOMAIN));
                    }
                }
            }
        }

        return $this->render(
            '@OpenBack/notification/notification_edit.html.twig',
            array(
                'form' => $form->createView(),
                'id' => $id,
				'nodeType' => 'Email',
                'templateVariables' => $variables
            )
        );


    }
}
