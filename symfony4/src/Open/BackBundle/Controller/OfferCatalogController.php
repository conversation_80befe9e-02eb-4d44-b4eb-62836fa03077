<?php

namespace Open\BackBundle\Controller;

use AppBundle\Controller\MkoController;
use AppBundle\Services\OfferCatalogService;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

class OfferCatalogController extends MkoController
{
    /**
     * @param Request             $request
     * @param OfferCatalogService $offerCatalogService
     * @param null                $language
     *
     * @return StreamedResponse
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/offer-catalog/export', name: 'admin.offer_catalog_export_all')]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/offer-catalog/export/{language}', name: 'admin.offer_catalog_export')]
    public function exportOfferCatalog(Request $request, OfferCatalogService $offerCatalogService,$language = null)
    {
        return new StreamedResponse(
            function () use ($offerCatalogService, $language) {
                $offerCatalogService->export(true,null,$language);
            },
            Response::HTTP_OK,
            [
                'Content-Type' => 'text',
                'Content-Disposition' => 'attachment; filename="offer-catalog.csv"',
            ]
        );
    }

    /**
     * @param Request             $request
     * @param OfferCatalogService $offerCatalogService
     * @param null                $locale
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/offer-catalog/export/vendor/{merchantId}', name: 'admin.offer_catalog_export_for_merchant_all')]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/offer-catalog/export/vendor/{merchantId}/{locale}', name: 'admin.offer_catalog_export_for_merchant')]
    public function exportOfferCatalogForMerchant(Request $request, $merchantId, OfferCatalogService $offerCatalogService, $locale = null)
    {
        return new StreamedResponse(
            function () use ($offerCatalogService, $merchantId, $locale) {
                $offerCatalogService->export(true,$merchantId, $locale);
            },
            Response::HTTP_OK,
            [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="offer-catalog.csv"',
            ]
        );
    }
}
