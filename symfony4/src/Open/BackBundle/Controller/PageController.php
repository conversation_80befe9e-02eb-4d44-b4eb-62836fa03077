<?php

namespace Open\BackBundle\Controller;


use AppBundle\Controller\MkoController;
use AppBundle\Doctrine\NodeStatusFilter;
use AppBundle\Entity\Node;
use AppBundle\Entity\NodeContent;
use AppBundle\Entity\Redirect;
use AppBundle\Entity\User;
use AppBundle\Repository\NodeRepository;
use AppBundle\Repository\RedirectRepository;
use AppBundle\Repository\UserRepository;
use AppBundle\Services\PageService;
use Doctrine\ORM\EntityManager;
use Open\BackBundle\Form\PageForm;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use \Symfony\Component\Config\Definition\Exception\Exception;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Contracts\Translation\TranslatorInterface;

class PageController extends MkoController
{

    const TRANSLATOR = 'translator';
    const TRANSLATION_DOMAIN = 'AppBundle';
    const INVALID_SLUG_EXIST = 'form.node.slug.invalid_exist';
    const VALIDATORS = 'validators';
    const AUTHOR = 'author';

    /**
     * @param Request $request
     * @param RouterInterface $router
     *
     * @param NodeRepository $nodeRepository
     * @param RedirectRepository $redirectRepository
     *
     * @param TranslatorInterface $translator
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/page/validateSlug', name: 'admin.page.validate_slug', methods: ['GET'], options: ['expose' => true])]
    public function validateSlugAction(Request $request, RouterInterface $router, NodeRepository $nodeRepository, RedirectRepository $redirectRepository, TranslatorInterface $translator)
    {
        // is it an Ajax request?
        $isAjax = $request->isXmlHttpRequest();

        if (!$isAjax) {
            return new NotFoundHttpException($translator->trans(self::NOT_FOUND_EXCEPTION));
        }

        $response = array('success' => true, 'error' => null);

        // Get request params
        $value = $request->get('slug');
        $node_id = intval($request->get('id'));

        // Ignore empty value because another rule take care of that
        if (!empty($value)) {

            $route = $router->match('/fr/' . $value); // the router throw an exception if we don't provide a language

            // The route exist somewhere
            if ($route['_route'] !== 'catch_all') {

                $response['success'] = false;
                $response['error'] = $translator->trans(self::INVALID_SLUG_EXIST, array(), self::VALIDATORS);

                // If the route does not exist then our catch all route will reply to it
                // It means we have to look in the db for matching content
            } else {

                /** @var NodeStatusFilter $filter */
                // Filter by published nodes
                /** @var EntityManager $entityManager */
                $entityManager = $this->doctrine->getManager();
                $entityManager->getFilters()->enable('published_node');

                /** @var Node $node */
                $node = $nodeRepository->findBySlug($value);

                // If the node found is not the same as the one we are editing then it is an error
                if ($node && $node->getId() !== $node_id) {
                    $response['success'] = false;
                    $response['error'] = $translator->trans(self::INVALID_SLUG_EXIST, array(), self::VALIDATORS);
                } else {

                    // Find a redirect starting from this slug
                    $redirect = $redirectRepository->findByOrigin($value);

                    // If there is a redirection starting from this slug
                    // the request might end up looping or lead to a 404
                    // We cannot allow this
                    if ($redirect !== null) {
                        $response['success'] = false;
                        $response['error'] = $translator->trans(self::INVALID_SLUG_EXIST, array(), self::VALIDATORS);
                    }
                }

            }
        }

        return new JsonResponse(json_encode($response));
    }

    /**
     * @param Request $request
     * @param PageService $servicePage
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/page/list/', name: 'admin.page.list', defaults: ['_locale' => 'en'])]
    public function listPagesAction(Request $request, PageService $servicePage)
    {

        $qb = $servicePage->getDefaultQueryBuilder();
        $qb->andWhere("e.type = :type");
        $qb->setParameter("type", "page");


        $paginator = $servicePage->getPaginatorByQb($qb, 1, 25, $request);


        //render list of users
        return $this->render('@OpenBack/Page/list.html.twig',
            array('pagination' => $paginator,
                'local' => $request->getLocale()));
    }

    /**
     * @param Request $request
     * @param TranslatorInterface $translator
     * @param UserRepository $userRepository
     * @param int|null $id
     *
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/page/{id}/edit', name: 'admin.page.edit')]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/page/add', name: 'admin.page.add')]
    public function editPageAction(Request $request, TranslatorInterface $translator, UserRepository $userRepository, ?int $id = null)
    {
        /** @var NodeRepository $nodeRepository */
        $nodeRepository = $this->doctrine->getRepository(Node::class);
        $em = $this->doctrine->getManager();


        if (!is_null($id)) {
            /** @var Node $node */
            $node = $nodeRepository->findOneById($id);
        } else {
            /** @var Node $node */
            $node = new Node();

            $node->setStatus('draft');
        }

        // If no en content found for this node then add an one
        if (is_null($node->getContent('en'))) {
            $en = new NodeContent();
            $en->setNode($node);
            $en->setLang('en');
            $node->addContent($en);
        }

        // If no fr content found for this node then add an one
        if (is_null($node->getContent('fr'))) {
            $fr = new NodeContent();
            $fr->setNode($node);
            $fr->setLang('fr');

            $node->addContent($fr);
        }

        // If no es content found for this node then add an one
        if (is_null($node->getContent('es'))) {
            $es = new NodeContent();
            $es->setNode($node);
            $es->setLang('es');
            $node->addContent($es);
        }

        // If no de content found for this node then add an one
        if (is_null($node->getContent('de'))) {
            $de = new NodeContent();
            $de->setNode($node);
            $de->setLang('de');
            $node->addContent($de);
        }

        // If no it content found for this node then add an one
        if (is_null($node->getContent('it'))) {
            $it = new NodeContent();
            $it->setNode($node);
            $it->setLang('it');
            $node->addContent($it);
        }

        if (is_null($node->getContent('nl'))) {
            $nl = new NodeContent();
            $nl->setNode($node);
            $nl->setLang('nl');
            $node->addContent($nl);
        }


        $form = $this->createForm(
            PageForm::class,
            $node
        );

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {

            $node = $form->getData();
            $node->setTemplate('default');

            /** @var NodeContent $content */
            foreach ($node->getContent() as $content) {
                if (empty($content->getTitle()) && empty($content->getBody())) {
                    $node->removeContent($content);

                    $em->remove($content);
                    $em->flush();
                }
            }

            try {

                if (!is_null($id)) {
                    $this->preUpdate($node, $userRepository);
                }

                $em->persist($node);
                $em->flush();

                if (!is_null($id)) {
                    $this->addFlash('success', $translator->trans('node.form.submit.success.update', [], 'AppBundle'));
                } else {
                    $this->addFlash('success', $translator->trans('node.form.submit.success.create', [], 'AppBundle'));
                }

                if (is_null($id)) {
                    return $this->redirectToRoute('admin.page.edit', array('id' => $node->getId()));
                }


            } catch (\Exception $e) {
                $this->logger->error("Error while saving page: " . $e->getMessage(),
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::TECHNICAL_ERROR,
                        LogUtil::USER_NAME => $this->getUser()->getUsername(),
                        "exception" => $e->getTraceAsString()
                    ])
                );
                if (!is_null($id)) {
                    $this->addFlash('error', $translator->trans('node.form.submit.error.update', [], 'AppBundle'));
                } else {
                    $this->addFlash('error', $translator->trans('node.form.submit.error.create', [], 'AppBundle'));
                }
            }


        }

        return $this->render(
            '@OpenBack/Page/edit.html.twig',
            array(
                'form' => $form->createView(),
                'id' => $id,
                'nodeType' => 'Page',
                'userId' => $this->getUser()->getId()
            )
        );
    }

/**
 * @param                     $id
 * @param TranslatorInterface $translator
 * @return mixed
 */
#[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
#[\Symfony\Component\Routing\Attribute\Route(path: '/page/{id}/delete', name: 'admin.page.delete')]
public
function deletePage($id, TranslatorInterface $translator){

    /** @var NodeRepository $nodeRepository */
    $nodeRepository = $this->doctrine->getRepository(Node::class);
    $em = $this->doctrine->getManager();

    $node = $nodeRepository->findOneById($id);

    try {
        /** @var NodeContent $content */
        foreach ($node->getContent() as $content) {
            if (empty($content->getTitle()) && empty($content->getBody())) {
                $node->removeContent($content);
                $em->remove($content);
                $em->flush();
            }
        }

        $em->remove($node);
        $em->flush();

        $this->addFlash('success', $translator->trans('node.form.delete.success', [], 'AppBundle'));

        return $this->redirectToRoute('admin.page.list');
    } catch (\Exception $e) {
        $this->logger->error("Error while saving page: " . $e->getMessage(),
            LogUtil::buildContext([
                LogUtil::EVENT_NAME => EventNameEnum::TECHNICAL_ERROR,
                LogUtil::USER_NAME => $this->getUser()->getUsername(),
                "exception" => $e->getTraceAsString()
            ])
        );
        $this->addFlash('error', $translator->trans('node.form.delete.error', [], 'AppBundle'));
    }
}

    /**
     * @param                $newObject Node
     * @param UserRepository $userRepository
     *
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
	private function preUpdate($newObject, UserRepository $userRepository)
{
    /** @var EntityManager $em */
    $em = $this->doctrine->getManager();

    $originalObject = $em->getUnitOfWork()->getOriginalEntityData($newObject);
    // Get the original author id

    $originalAuthor = $originalObject["author"];
    if (!$originalAuthor instanceof User) {
        throw new Exception("The author can not be null");
    }

    // Get the new author entity
    /** @var User $newAuthor */
    $newAuthor = $newObject->getAuthor();

    // When logged in user is admin we receive a user object
    if ($newAuthor instanceof User) {
        $newAuthorId = $newAuthor->getId();

        // for other roles we receive only the user id
    } else {
        $newAuthorId = $newAuthor;
    }

    // Only Admin can change the author of a node
    // So if the ids doesn't match then reverse to the original author
    if (
        $newAuthorId != $originalAuthor->getId() &&
        !$this->isGranted(self::ROLE_OPERATOR)
    ) {
        $newObject->setAuthor($originalAuthor);
    } else {
        // Symfony complains about receiving an id instead of a user object
        $user = $userRepository->find($newAuthorId);
        $newObject->setAuthor($user);
    }

    // new slug
    $newSlug = $newObject->getSlug();

    // previous slug
    $oldSlug = $originalObject['slug'];

    // If the slugs are different then create a redirection to the new url
    if ($newSlug !== $oldSlug) {

        $redirect = new Redirect();

        $redirect->setOrigin($oldSlug);
        $redirect->setDestination($newSlug);
        $redirect->setType($this->getParameter('default_redirect_type')); // will be 301 in prod

        $em->persist($redirect);
        $em->flush();
    }
}
}
