<?php


namespace Open\BackBundle\Controller;


use A<PERSON><PERSON><PERSON><PERSON>\Controller\MkoController;
use Illuminate\Encryption\Encrypter;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

class PasswordController extends MkoController
{


    /**
     * @param Request $request
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_SUPER_ADMIN")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/password', name: 'password.decrypt', methods: ['GET'], options: ['expose' => true])]
    public function getPassword(Request $request)
    {

        $key = $_ENV['CRYPT_KEY'];

        $encrypter = new Encrypter($key);

        $password = $encrypter->decrypt($request->query->get('password'));

        return $this->render('@OpenBack/password.html.twig',
            array('password' => $password));
    }
}
