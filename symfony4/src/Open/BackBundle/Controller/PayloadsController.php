<?php

namespace Open\BackBundle\Controller;

use AppBundle\Services\PayloadsService;
use Open\BackBundle\Dto\FilterPayloadData;
use Open\BackBundle\Form\Payload\FilterPayloadType;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class PayloadsController extends AbstractController
{
    private const DEFAULT_PAGE = 1;

    private PayloadsService $payloadsService;

    public function __construct(PayloadsService $payloadsService)
    {
        $this->payloadsService = $payloadsService;
    }

    #[\Symfony\Component\Routing\Attribute\Route(path: '/payloads', name: 'admin.payloads.list')]
    public function all(Request $request): Response
    {
        $data = new FilterPayloadData();
        $page = $request->get('page', self::DEFAULT_PAGE);
        $data->page = $page;
        $form = $this->createForm(FilterPayloadType::class, $data)->handleRequest($request);
        $payloads = $this->payloadsService->getAllPayloads($data);
        return $this->render('@OpenBack/payload/list.html.twig', [
            'payloads' => $payloads,
            'form' => $form->createView(),
        ]);
    }
}
