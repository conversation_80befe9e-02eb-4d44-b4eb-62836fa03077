<?php
/**
 * Created by PhpStorm.
 * User: LRO16285
 * Date: 23/04/2018
 * Time: 15:52
 */

namespace Open\BackBundle\Controller;

use AppBundle\Controller\MkoController;
use AppBundle\Services\CompanyService;
use Open\BackBundle\Form\IdForm;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\HttpFoundation\Request;
use Open\BackBundle\Form\FilterCompanyType;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Contracts\Translation\TranslatorInterface;
use Unirest\Exception;

class PaymentTermController extends MkoController
{
  const TRANSLATION_DOMAIN = 'AppBundle';
  const PAGINATION = 'pagination';
  const LOCALE = 'locale';


    /**
   * @param Request        $request
   * @param                $qualifier
   *
   * @param CompanyService $companyService
   * @return mixed
   */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
  #[\Symfony\Component\Routing\Attribute\Route(path: '/payment_term/list/{qualifier}', name: 'admin.payment_term.list')]
  public function list(Request $request, $qualifier, CompanyService $companyService)
  {
    $form = $this->createForm(FilterCompanyType::class);

    // remove term payment useless relative fields
    $form->remove('status');
    $form->remove('termpayment_moneytransfert_enabled');
    $form->remove('termpayment_moneytransfert_pending');
    $form->remove('creationMin');
    $form->remove('creationMax');
    $form->remove('category');
    $form->remove('termpaymentMoneyTransfertRequestDate');
    $form->remove('termpaymentMoneyTransfertAcceptDate');

    $form->handleRequest($request);
    if ($form->isSubmitted() && $form->isValid()) {
      $filter = $form->getData();
    }
    else{
      $filter = [];
    }

    $formModal = $this->paymentTermDenyForm();

    //render list of users
    return $this->render('@OpenBack/company/company_with_payment_term_list.html.twig',
      array(self::PAGINATION => $companyService->getCompaniesWithTermPaymentFilteredPaginator(10, $request, $filter, $qualifier),
        'qualifier_val' => $qualifier,
        'formModal' => $formModal->createView(),
        'form' => $form->createView(),
        'form_accept_paymentTerm' => $this->createForm(IdForm::class, null)->createView(),
        self::LOCALE => 'en'));
  }

    /***
     * @return \Symfony\Component\Form\FormInterface
     */
    private function paymentTermDenyForm (){
        return $this->createFormBuilder()
            ->add('reason', TextareaType::class, array('label' => 'back.company.companyInfo.termpayment.reason',  'translation_domain' => 'AppBundle', 'required' => true))
            ->add('company', HiddenType::class)
            ->setAction($this->generateUrl('admin.payment_term.deny'))
            ->setMethod('POST')
            ->getForm();
    }

    /**
   * @param \Symfony\Component\HttpFoundation\Request $request
   *
   * @param CompanyService                            $companyService
   * @return \Symfony\Component\HttpFoundation\RedirectResponse
   */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
  #[\Symfony\Component\Routing\Attribute\Route(path: '/payment_term/accept', name: 'admin.payment_term.accept')]
  public function accept(Request $request, CompanyService $companyService, TranslatorInterface $translator)
  {
      $form = $this->createForm(IdForm::class, null, []);
      $form->handleRequest($request);

      if ($form->isSubmitted() && $form->isValid()) {
          $id = $form->getData()['id'];

          $company = $companyService->get($id);
          try{
              $companyService->acceptTermPayment($company);
              $companyService->save($company);
              $this->addFlash('success', $translator->trans('back.company.termpayment_moneytransfert_accepted', array(), 'AppBundle'));
          }
          catch (Exception $e){
              $this->addFlash('error', $translator->trans('back.company.termpayment_moneytransfert_accepted_error', array(), 'AppBundle'));
          }
      }else{
          throw new BadRequestHttpException('invalid form');
      }




    return $this->redirectToRoute('admin.payment_term.list', array('qualifier'=> 'all'));
  }


    /**
   * @param \Symfony\Component\HttpFoundation\Request $request
   *
   * @param CompanyService                            $companyService
   *
   * @param TranslatorInterface                       $translator
   *
   * @return \Symfony\Component\HttpFoundation\RedirectResponse
   * @throws \Doctrine\ORM\ORMException
   * @throws \Doctrine\ORM\OptimisticLockException
   */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
  #[\Symfony\Component\Routing\Attribute\Route(path: '/payment_term/deny/', name: 'admin.payment_term.deny')]
  public function deny(Request $request, CompanyService $companyService, TranslatorInterface $translator)
  {

      $form = $this->paymentTermDenyForm();
      $form->handleRequest($request);
      try {
          if ($form->isSubmitted() && $form->isValid()) {
              $data = $form->getData();
              $company = $companyService->get($data['company']);
              $companyService->rejectTermPayment($company, $data['reason']);

              $companyService->save($company);

              $this->addFlash('success', $translator->trans('back.company.termpayment_moneytransfert_rejected', array(), 'AppBundle'));
          }
      } catch (Exception $e) {
          $this->addFlash('error', $translator->trans('back.company.termpayment_moneytransfert_rejected_error', array(), 'AppBundle'));
      }

      return $this->redirectToRoute('admin.payment_term.list', array('qualifier' => 'all'));
  }


}
