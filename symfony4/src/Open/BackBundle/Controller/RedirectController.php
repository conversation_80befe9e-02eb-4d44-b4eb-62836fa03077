<?php
/**
 * Created by PhpStorm.
 * User: CMA15450
 * Date: 23/01/2018
 * Time: 17:06
 */

namespace Open\BackBundle\Controller;


use AppBundle\Controller\MkoController;
use AppB<PERSON>le\Entity\Redirect;
use AppB<PERSON>le\Repository\RedirectRepository;
use AppBundle\Services\RedirectBddService;
use Open\BackBundle\Form\FilterPageType;
use Open\BackBundle\Form\RedirectForm;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\Form\FormError;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Contracts\Translation\TranslatorInterface;

class RedirectController extends MkoController
{

    /**
  * @param Request            $request
  * @param                    $qualifier
  * @param RedirectBddService $redirectBddService
  * @return mixed
  */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
 #[\Symfony\Component\Routing\Attribute\Route(path: '/redirect/list/{qualifier}', name: 'admin.redirect.list')]
 public function listRedirectAction(Request $request, $qualifier, RedirectBddService $redirectBddService)
	{

		$form = $this->createForm(FilterPageType::class);
		$form->handleRequest($request);
		if ($form->isSubmitted() && $form->isValid()) {
			$filter = $form->getData();
		}
		else{
			$filter = [];
		}

		$paginator = $redirectBddService->getCustomFilteredPaginator(1, 25, $request, $filter, $qualifier);




		//render list of users
		return $this->render('@OpenBack/Redirect/list.html.twig',
			array('pagination' => $paginator,
				'qualifier_val' => $qualifier,
				'form' => $form->createView(),
				'locale' => $request->getLocale()));
	}

    /**
  * @param Request             $request
  * @param                     $id
  * @param TranslatorInterface $translator
  * @return \Symfony\Component\HttpFoundation\RedirectResponse
  */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
 #[\Symfony\Component\Routing\Attribute\Route(path: '/redirect/{id}/delete', name: 'admin.redirect.delete')]
 public function deleteRedirectAction(Request $request, $id, TranslatorInterface $translator)
	{

		/** @var RedirectRepository $repo */
		$repo = $this->doctrine->getRepository(Redirect::class);

		$redirect = $repo->find($id);

		$em = $this->doctrine->getManager();

		try {
			$em->remove($redirect);
			$em->flush();

			$this->addFlash('success', $translator->trans('redirect.form.delete.success', [], 'AppBundle'));
		} catch (\Exception $e) {
			$this->addFlash('success', $translator->trans('redirect.form.delete.error', [], 'AppBundle'));
		}

		return $this->redirectToRoute('admin.redirect.list', array('qualifier' => 'all'));
	}

    /**
  * @param Request             $request
  * @param TranslatorInterface $translator
  * @param int|null                $id
  *
  * @return mixed
  */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
 #[\Symfony\Component\Routing\Attribute\Route(path: '/redirect/{id}/edit', name: 'admin.redirect.edit')]
 #[\Symfony\Component\Routing\Attribute\Route(path: '/redirect/add', name: 'admin.redirect.add')]
 public function addRedirectAction(Request $request,TranslatorInterface $translator, ?int $id = null)
	{
		$redirectRepository = $this->doctrine->getRepository(Redirect::class);
		$em = $this->doctrine->getManager();

		if (is_null($id)) {
			$redirect = new Redirect();
		} else {
			$redirect = $redirectRepository->find($id);
		}

		$form = $this->createForm(
			RedirectForm::class,
			$redirect
		);

		if ($redirect->getOrigin() === $redirect->getDestination()) {
			$form->get('origin')->addError(new FormError($translator->trans('form.redirect.samedest',[], 'validators')));
			$form->get('destination')->addError(new FormError($translator->trans('form.redirect.sameorigin',[], 'validators')));
		}

		$form->handleRequest($request);
		if ($form->isSubmitted() && $form->isValid()) {

			/** @var Redirect $redirect */
			$redirect = $form->getData();

			// Remove trailing slashes
			$dest = rtrim($redirect->getDestination(), '/');

			// Update value in entity
			$redirect->setDestination($dest);

			//try {
				$em->persist($redirect);
				$em->flush();

				if (!is_null($id)) {
					$this->addFlash('success', $translator->trans('redirect.form.submit.success.update', [], 'AppBundle'));
				} else {
					$this->addFlash('success', $translator->trans('redirect.form.submit.success.create', [], 'AppBundle'));
				}

				// Force redirect to see the slashes rtrim effects
				return $this->redirectToRoute('admin.redirect.edit', array('id'=> $redirect->getId()));

//			} catch(\Exception $e) {
//
//				if (!is_null($id)) {
//					$this->addFlash('error', $translator->trans('redirect.form.submit.error.update', [], 'AppBundle'));
//				} else {
//					$this->addFlash('error', $translator->trans('redirect.form.submit.error.create', [], 'AppBundle'));
//				}
//			}

		}

		return $this->render(
			'@OpenBack/Redirect/edit.html.twig',
			array(
				'form' => $form->createView(),
				'id' => $id
			)
		);
	}
}
