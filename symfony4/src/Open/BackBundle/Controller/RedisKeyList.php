<?php


namespace Open\BackBundle\Controller;

use AppBundle\Controller\MkoController;
use Open\IzbergBundle\Service\RedisService;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Translation;
use Symfony\Contracts\Translation\TranslatorInterface;

class RedisKeyList extends MkoController
{
    /**
     * @param Request      $request
     * @param RedisService $redisService
     * @return Response|null
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/rediskeys', name: 'admin.rediskeys')]
    public function indexAction(Request $request, RedisService $redisService)
    {
        $allRedisKeys = $redisService->getAllItems();

        return $this->render(
            '@OpenBack/redisKeyList/redis_key_list.html.twig',
            [
                'allRedisKeys' => $allRedisKeys,
            ]
        );
    }

    /**
     * @param Request             $request
     * @param RedisService        $redisService
     *
     * @param TranslatorInterface $translator
     * @return RedirectResponse
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/rediskeys/delete/', name: 'admin.delete_key', methods: ['POST'])]
    public function deleteKeyAction(Request $request, RedisService $redisService, TranslatorInterface $translator) {
        $key = $request->request->get('key');
        if ($redisService->deleteItem($key)) {
            $this->addFlash(
                'notice',
                $translator->trans('redislist.alert.success', ['%key%' => $key], self::TRANSLATION_DOMAIN)
            );
        } else {
            $this->addFlash(
                'error',
                $translator->trans('redislist.alert.error', ['%key%' => $key], self::TRANSLATION_DOMAIN)
            );
        }

        return $this->redirectToRoute('admin.rediskeys');
    }

    /**
     * @param Request      $request
     * @param RedisService $redisService
     * @return RedirectResponse
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/rediskeys/delete/all', name: 'admin.delete_all_keys', methods: ['POST'])]
    public function deleteAllKeysAction(Request $request, RedisService $redisService, TranslatorInterface $translator) {
        $redisService->deleteAllItems();
        $this->addFlash(
            'notice',
            $translator->trans('redislist.alert.delete_all', [], self::TRANSLATION_DOMAIN)
        );

        return $this->redirectToRoute('admin.rediskeys');
    }

}
