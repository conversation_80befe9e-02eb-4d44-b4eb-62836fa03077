<?php

namespace Open\BackBundle\Controller;

use A<PERSON><PERSON><PERSON><PERSON>\Controller\MkoController;
use Doctrine\DBAL\Types\DateType;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\HttpFoundation\Request;
use AppBundle\Services\SearchHistorizationService;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use AppBundle\FilterQueryBuilder\SearchHistorizationQueryBuilder;
use AppBundle\Entity\SearchHistorization;
use Open\BackBundle\Form\FilterSearchHistorizationType;
use AppBundle\Services\CSVService;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

class SearchHistorizationController extends MkoController
{
    const TRANSLATION_DOMAIN = 'AppBundle';
    const PAGINATION = 'pagination';
    const LOCALE = 'locale';

    const DATEMIN = 'datemin';
    const DATE = 'date';
    const SEARCHED_TERM = 'search_term';
    const IS_ANONYMOUS = 'is_anonymous';
    const IS_ANONYMOUS_STRING = 'is_anonymous_string';
    const COMPANY_NAME = 'company_name';
    const USER_FULL_NAME = 'user_full_name';
    const OFFER_SKU = 'offer_sku';
    const OFFER_NAME = 'offer_name';
    const FILTER = 'filter';
    const NB_HITS = 'nb_hits';

    /**
     * @param Request                    $request
     * @param SearchHistorizationService $searchHistorizationService
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/search_historization/list', name: 'admin.search_historization.list')]
    public function list(Request $request, SearchHistorizationService $searchHistorizationService){

        $minDate = new \DateTime();
        $maxDate = new \DateTime();

        $filter=['daterange'=>$minDate->sub(new \DateInterval('P6M'))->format('m/d/Y') . ' - ' . $maxDate->format('m/d/Y')];
        $filtersKey = ['searchedTerm', 'filter', 'id', 'isAnonymous', 'companyName', 'userFullName', 'offerSku', 'offerName', 'nbHits'];
        $params = $request->get('filter_search_historization');

        if (!empty($params)) {
            foreach($params as $key=>$val){
                if(in_array($key,$filtersKey) && !empty($val)){
                    $filter[$key] = $val;
                }
            }
        }

        $request->setLocale('en');
        $form = $this->createForm(FilterSearchHistorizationType::class, $filter);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $filter = $form->getData();
        } else {
            $form->setData($filter);
        }
        $dates = explode(" - ", $filter['daterange']);
        $filter['datemin'] = new \DateTime($dates[0]);
        $filter['datemax'] = new \DateTime($dates[1]);

        return $this->render(
            '@OpenBack/search/search_historization_list.html.twig',
            array(
                self::PAGINATION => $searchHistorizationService->getAll( $request, $filter, 25),
                'form' => $form->createView(),
                self::LOCALE => 'en',
                'query' => $filter,
            )
        );
    }

    /**
     * @param Request                    $request
     * @param SearchHistorizationService $searchHistorizationService
     *
     * @param CSVService                 $csvService
     *
     * @return Response
     * @throws \AppBundle\Exception\CSVException
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/search_historization/export', name: 'search_historization.export')]
    public function exportCSV(Request $request, SearchHistorizationService $searchHistorizationService, CSVService $csvService){
        $dateFormat = "d/m/Y H:i:s";

        $form = $this->createForm(FilterSearchHistorizationType::class);
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $filter = $form->getData();
        }
        else{
            $filter = [];
            $filter['status'] = 'all';
        }
        unset($form);

        $dates = explode(" - ", $filter['daterange']);
        $filter['datemin'] = new \DateTime($dates[0]);
        $filter['datemax'] = new \DateTime($dates[1]);

        unset($dates);
        unset($filter['daterange']);

        $listOfSearch = $searchHistorizationService->getAllByFilter($filter);
        unset($filter);

        /*
        * STEP 1: add custom columns for export
        */
        /** @var SearchHistorization $search */
        foreach ($listOfSearch as &$search){
            $search->addProperty(self::DATE, $search->getCreatedAt()->format($dateFormat));
            $search->addProperty(self::IS_ANONYMOUS_STRING, $search->getIsAnonymous() ? 'Yes' : 'No');
        }

        $headers = [
            "back.search_historization.id",
            "back.search_historization.date",
            "back.search_historization.is_anonymous",
            "back.search_historization.company_name",
            "back.search_historization.user_full_name",
            "back.search_historization.searched_term",
            "back.search_historization.nb_hits",
        ];

        $properties = [
            "id",
            self::DATE,
            self::IS_ANONYMOUS_STRING,
            self::COMPANY_NAME,
            self::USER_FULL_NAME,
            self::SEARCHED_TERM,
            self::NB_HITS,
        ];

        /*
         * STEP 3: run the export
         */
        $content = $csvService->objArrayToCSV($listOfSearch, $headers, $properties);
        unset($listOfSearch);
        unset($headers);
        unset($properties);

        return new Response($content, 200, array(
            'Content-Encoding' => 'UTF-16',
            'Content-Type' => 'application/CSV; charset=UTF-16',
            'Content-Disposition' => 'attachment; filename="search.csv"'));
    }
}
