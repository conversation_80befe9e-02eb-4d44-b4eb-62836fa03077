<?php
/**
 * Created by PhpStorm.
 * User: CMA15450
 * Date: 08/03/2017
 * Time: 10:30
 */

namespace Open\BackBundle\Controller;

use AppBundle\Entity\SettingsCollection;
use AppBundle\Entity\Setting;
use AppBundle\Repository\SettingRepository;
use Doctrine\Persistence\ManagerRegistry;
use Open\BackBundle\Events\UpdateSettingsEvent;
use Psr\Cache\CacheItemPoolInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Open\BackBundle\Form\SettingsForm;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;
use Symfony\Contracts\Translation\TranslatorInterface;


class SettingsController extends AbstractController
{
    const DOMAIN = 'domain';


    protected ManagerRegistry $doctrine;

    public function __construct(ManagerRegistry $doctrine)
    {
        $this->doctrine = $doctrine;
    }
    /**
     * @param $operatorCan
     * Throw 403 if user don't have an Admin role
     */
    private function checkAccess($operatorCan)
    {
        if (!$this->isGranted('ROLE_SUPER_ADMIN') && (!$operatorCan || !$this->isGranted('ROLE_OPERATOR'))) {
            throw new AccessDeniedException(sprintf('Access Denied to the action %s', 'INDEX'));
        }
    }

        /**
     * Get param from config file
     * @return mixed
     */
    private function getSettingsFromConfig()
    {
        return $this->getParameter('app.settings');
    }

    /**
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\Response
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/settings', name: 'admin.settings.index')]
    public function indexAction(Request $request)
    {
      // Don't build magic admin for these settings (domain settings)
      $toExclude = array('offers');

      $this->checkAccess(false);

      $domains = $this->getSettingsFromConfig();
      foreach ($domains as $key => $domain){
        foreach ($toExclude as $excluded) {
          if ($excluded == $key){
            unset($domains[$key]);
          }
        }
      }

      return $this->render('@OpenBack/settings/index.html.twig', array('domains' => $domains));
    }

    /**
     * @param Request $request
     * @param $domain
     * @return \Symfony\Component\HttpFoundation\Response
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/settings/{domain}', name: 'system.settings.domain')]
    public function domainAction(Request $request, $domain)
    {
        $this->checkAccess(false);

        $domains = $this->getSettingsFromConfig();

        if (!isset($domains[$domain])) {
            throw new AccessDeniedException(sprintf('Access Denied'));
        }

        return $this->render(
            '@OpenBack/settings/domain.html.twig',
            array(
                self::DOMAIN => $domain,
                'groups' => $domains[$domain]
            )
        );
    }

    /**
     * @param Request                  $request
     * @param                          $domain
     * @param                          $group
     * @param CacheItemPoolInterface   $cacheItemPool
     * @param TranslatorInterface      $translator
     * @param EventDispatcherInterface $eventDispatcher
     *
     * @return \Symfony\Component\HttpFoundation\Response
     * @psalm-suppress InvalidThrow
     * @throws \Psr\Cache\InvalidArgumentException
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/settings/{domain}/{group}', name: 'system.settings.group')]
    public function groupAction(Request $request, $domain, $group, CacheItemPoolInterface $cacheItemPool, TranslatorInterface $translator, EventDispatcherInterface $eventDispatcher)
    {
        $operatorCan = false;
        if($domain == 'offers'){
            $operatorCan = true;
        }

        // Check if access is granted (403 otherwise)
        $this->checkAccess($operatorCan);

        $domains = $this->getSettingsFromConfig();

        // If the domain doesn't exist then 403
        if (!isset($domains[$domain])) {
            throw new AccessDeniedException(sprintf('Access Denied'));
        }

        // If the group doesn't exist then redirect to the parent domain
        if (!isset($domains[$domain][$group])) {
            return $this->redirectToRoute(
                'admin.settings.domain',
                array(
                    self::DOMAIN => $domain
                )
            );
        }

        $keys = array();

        foreach ($domains[$domain][$group] as $key => $params) {
            $keys[] = $key;
        }




        // Create an empty collection of setting
        $settingsCollection = new SettingsCollection();

        /** @var SettingRepository $repository */
        $repository = $this->doctrine->getRepository(Setting::class);

        // Find parameters needed for the page
        $settings = $repository->findByDomainAndKeys($domain, $keys);


        // Build a collection with all the settings we want in the form
        $settingsCollection->setSettings($settings);

        // Create the form
        $form = $this->createForm(
            SettingsForm::class,
            $settingsCollection
        );

        $form->handleRequest($request);

        // Handle form submission
        if ($form->isSubmitted() && $form->isValid()) {

            $em = $this->doctrine->getManager();

            /** @var SettingsCollection $data */
            $data = $form->getData();

            $settings = $data->getSettings();

            /** @var Setting $setting */
            foreach ($settings as $setting) {
                $em->persist($setting);
            }

            try {
                $em->flush();

                /** @var Setting $setting */
                foreach ($settings as $setting) {
                    $cachedSetting = $cacheItemPool->getItem($setting->getFullName());
                    $cachedSetting->set($setting->getValue());
                    $cacheItemPool->saveDeferred($cachedSetting);
                }

                // Commit defered save
                $cacheItemPool->commit();

                //dispatch event
                $event = new UpdateSettingsEvent();
                $event->setGroup($group);
                $event->setDomain($domain);
                $eventDispatcher->dispatch($event);

                $this->addFlash(
                    'success',
                    $translator->trans('system.settings.update_success', array(), 'AppBundle')
                );

            } catch (\Exception $e) {
                $this->addFlash(
                    'error',
                    $translator->trans('system.settings.update_error', array(), 'AppBundle')
                );
				$this->addFlash(
					'error',
					$e->getMessage()
				);
			}
        }

        // Render form
        return $this->render(
            '@OpenBack/settings/group.html.twig',
            array(
                self::DOMAIN => $domain,
                'group' => $group,
                'form' => $form->createView()
            )
        );
    }

}
