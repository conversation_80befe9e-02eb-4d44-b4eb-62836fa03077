<?php

namespace Open\BackBundle\Controller;

use AppBundle\Controller\MkoController;
use AppBundle\Entity\Company;
use AppBundle\Entity\ShippingPoint;
use AppBundle\Entity\Site;
use AppBundle\Entity\User;
use Exception;
use Open\BackBundle\Form\IdForm;
use Open\BackBundle\Form\ShippingPointForm;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Contracts\Translation\TranslatorInterface;

class ShippingPointController extends MkoController
{
    const REPO_SITE = \AppBundle\Entity\Site::class;
    const REPO_SHIPPING_POINT = \AppBundle\Entity\ShippingPoint::class;
    const LOGGER_SAVE = 'log.site.save';

    /**
     * @param Request $request
     * @param                     $id
     * @param LoggerInterface $logger
     * @param TranslatorInterface $translator
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/site/{id}/shippingPoint/add', name: 'admin.shipping_point.add')]
    public function addShippingPoint(Request $request, $id, LoggerInterface $logger, TranslatorInterface $translator): Response
    {
        $em = $this->doctrine->getManager();

        /** @var User $user */
        $user = $this->getUser();

        /** @var Site $site */
        $site = $em->getRepository(self::REPO_SITE)->find($id);
        $shippingPoint = new ShippingPoint();
        $shippingPoint->setSite($site);

        /** @var Company $company */
        $company = $site->getCompany();

        $form = $this->createForm(ShippingPointForm::class, $shippingPoint, []);
        $addressForm = $form->get("address");
        $addressForm->remove('check');
        $addressForm->remove('country');
        $contactForm = $form->get('contact');
        $contactForm->remove("check");

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            // Data retrieve for form
            $shippingPoint = $form->getData();

            try {
                // persist
                $em->persist($shippingPoint);
                $em->flush();

                $logger
                    ->info(
                        $translator->trans(self::LOGGER_SAVE),
                        LogUtil::buildContext([
                            LogUtil::EVENT_NAME => EventNameEnum::SHIPPING_POINT_ADD,
                            LogUtil::USER_NAME => $user->getUsername(),
                            'id' => $site->getId(),
                        ])
                    );

                // Add success message to session
                $this
                    ->addFlash(
                        'success',
                        $translator->trans('back.shipping_points.success_add', array(), self::TRANSLATION_DOMAIN)
                    );
                // Redirect to cost center company list
                return $this->redirectToRoute('admin.company.sites', array('id' => $company->getId()));
            } catch (Exception $e) {
                $this
                    ->addFlash(
                        'error',
                        $translator->trans('back.shipping_points.error_add', array(), self::TRANSLATION_DOMAIN)
                    );
            }
        }
        // Redirect to form if not submit or error
        return $this->render('@OpenBack/shippingpoint/shippingpoint_edit.html.twig', [
            'id' => $site->getId(),
            'form' => $form->createView()
        ]);
    }

    /**
     * @param Request $request
     * @param                     $siteId
     * @param                     $shippingId
     * @param LoggerInterface $logger
     * @param TranslatorInterface $translator
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/site/{siteId}/shippingPoint/edit/{shippingId}', name: 'admin.shippingpoint.edit')]
    public function editShippingPoint(Request $request, $siteId, $shippingId, LoggerInterface $logger, TranslatorInterface $translator): Response
    {
        $em = $this->doctrine->getManager();

        /** @var User $user */
        $user = $this->getUser();

        /** @var Site $site */
        $site = $em->getRepository(self::REPO_SITE)->find($siteId);
        $shippingPoint = $em->getRepository(self::REPO_SHIPPING_POINT)->find($shippingId);

        /** @var Company $company */
        $company = $site->getCompany();

        $form = $this->createForm(ShippingPointForm::class, $shippingPoint, []);
        $addressForm = $form->get("address");
        $addressForm->remove('check');
        $contactForm = $form->get('contact');
        $contactForm->remove("check");

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $shippingPoint = $form->getData();

                // persist
                $em->persist($shippingPoint);
                $em->flush();

                $logger
                    ->info(
                        $translator->trans(self::LOGGER_SAVE),
                        LogUtil::buildContext([
                            LogUtil::EVENT_NAME => EventNameEnum::SHIPPING_POINT_EDIT,
                            LogUtil::USER_NAME => $user->getUsername(),
                            'id' => $site->getId(),
                        ])
                    );

                // Add success message to session
                $this
                    ->addFlash(
                        'success',
                        $translator->trans('back.shipping_points.success_edit', array(), self::TRANSLATION_DOMAIN)
                    );
                // Redirect to cost center company list
                return $this->redirectToRoute('admin.company.sites', array('id' => $company->getId()));
            } catch (Exception $e) {
                $this
                    ->addFlash(
                        'error',
                        $translator->trans('back.shipping_points.error_edit', array(), self::TRANSLATION_DOMAIN)
                    );
            }
        }
        // Redirect to form if not submit or error
        return $this->render('@OpenBack/shippingpoint/shippingpoint_edit.html.twig', [
            'id' => $site->getId(),
            'form' => $form->createView()
        ]);
    }

    /**
     * @param Request $request
     * @param LoggerInterface $logger
     * @param TranslatorInterface $translator
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/shippingPoint/delete', name: 'admin.shippingpoint.delete')]
    public function deleteShippingPoint(Request $request, LoggerInterface $logger, TranslatorInterface $translator): Response
    {

        $form = $this->createForm(IdForm::class, null, []);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $shippingId = $form->getData()['id'];

            $em = $this->doctrine->getManager();

            /** @var User $user */
            $user = $this->getUser();

            $shippingPoint = $em->getRepository(self::REPO_SHIPPING_POINT)->find($shippingId);

            /** @var Company $company */
            $company = $shippingPoint->getSite()->getCompany();

            // persist
            $em->remove($shippingPoint);
            $em->flush();

            $logger
                ->info(
                    $translator->trans(self::LOGGER_SAVE),
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::SHIPPING_POINT_DELETE,
                        LogUtil::USER_NAME => $user->getUsername(),
                        'id' => $shippingId,
                    ])
                );

            // Add success message to session
            $this->addFlash(
                'success',
                $translator->trans('back.shipping_points.success_delete', array(), self::TRANSLATION_DOMAIN)
            );
        } else {
            throw new BadRequestHttpException('invalid form');
        }

        // Redirect to cost center company list
        return $this->redirectToRoute('admin.company.sites', array('id' => $company->getId()));
    }
}
