<?php

namespace Open\BackBundle\Controller;

use AppBundle\Controller\MkoController;
use AppBundle\Entity\CheckableZipCodeTrait;
use AppBundle\Entity\Company;
use AppBundle\Entity\Site;
use AppBundle\Entity\User;
use AppBundle\Form\SiteForm;
use AppBundle\Services\CSVService;
use AppBundle\Services\SiteService;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityRepository;
use Exception;
use Open\BackBundle\Form\FilterSiteType;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Contracts\Translation\TranslatorInterface;

class SiteController extends MkoController
{

    use CheckableZipCodeTrait;

    const TRANSLATOR = 'translator';
    const TRANSLATION_DOMAIN = 'AppBundle';
    const LOGGER_SAVE = 'log.site.save';
    const SITE_BUNDLE = \AppBundle\Entity\Site::class;
    const SUCCESS = 'success';
    const ERROR = 'error';
    const INFO_ROUTE = 'admin.site.info';
    const SITE_ID = 'SITE_ID';
    const LABEL = 'label';
    const VALIDATION_GROUPS = 'validation_groups';
    const DEF = 'Default';

    /**
     * @param Request     $request
     * @param             $qualifier
     * @param SiteService $siteService
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/site/{qualifier}', name: 'admin.site.list')]
    public function listSite(Request $request, $qualifier, SiteService $siteService): Response
    {
        $form = $this->createForm(FilterSiteType::class);
        $form->remove('address');
        $form->remove('zipCode');
        $form->remove('city');
        $form->remove('country');
        $form->remove('contactName');
        $form->remove('contactPhone');

        $formModal = $this->siteNameForm();

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $filter = $form->getData();
        } else {
            $filter = [];
            $filter['status'] = 'all';
        }

        //render list of users
        return $this->render('@OpenBack/site/site_list.html.twig', array(
            'pagination' => $siteService->getCustomFilteredPaginator(1, 25, $request, $filter, $qualifier),
            'qualifier_val' => $qualifier,
            'form' => $form->createView(),
            'formModal' => $formModal->createView(),
            'locale' => $request->getLocale()
        ));
    }

    /**
     * @param             $id
     * @param SiteService $siteService
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/site/{id}/info', name: 'admin.site.info')]
    public function getSiteInformation(Request $request, $id, SiteService $siteService, TranslatorInterface $translator): Response
    {
        $em = $this->doctrine->getManager();

        /** @var Site $site */
        $site = $em->getRepository(self::SITE_BUNDLE)->find($id);

        $users = $siteService->getUsersOfOneSite($site->getId());

        $request->setLocale('en');
        return $this->render('@OpenBack/site/site_info.html.twig', array(
            'site' => $site,
            'company' => $site->getCompany(),
            'errorValid' => $translator->trans('back.site.validate.error', array(), self::TRANSLATION_DOMAIN),
            'users' => $users
        ));

    }

    private function siteNameForm($siteId = 0)
    {
        $form = $this->createFormBuilder()
            ->add('name', TextType::class,
                array(self::LABEL => 'site.form.name',
                    'translation_domain' => self::TRANSLATION_DOMAIN)
            )->add(
                'company',
                EntityType::class,
                array(
                    'class' => Company::class,
                    'query_builder' => function (EntityRepository $er) {

                        return $er->createQueryBuilder('c');

                    },
                    'choice_translation_domain' => 'AppBundle',
                    self::LABEL => 'back.site.form.company',
                    'translation_domain' => self::TRANSLATION_DOMAIN,
                    'required' => true,
                )
            )->add('save', SubmitType::class,
                array(
                    "attr" => array(
                        "value" => "save",
                        "class" => "Button button_margin",
                    ),
                    self::LABEL => 'company.form.next',
                    'translation_domain' => self::TRANSLATION_DOMAIN,
                )
            )->setMethod('POST');

        if ($siteId > 0) {
            $form->setAction($this->generateUrl('admin.site.edit', ['id' => $siteId]));
        } else {
            $form->setAction($this->generateUrl('admin.site.new'));
        }
        return $form->getForm();
    }

    /**
     * @param Request             $request
     * @param TranslatorInterface $translator
     *
     * @return Response
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/admin/site/new', name: 'admin.site.new')]
    public function createNewSite(Request $request, TranslatorInterface $translator): Response
    {
        $form = $this->siteNameForm(0);

        /** @var EntityManager $em */
        $em = $this->doctrine->getManager();

        $site = new Site();

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $site->setName($data['name']);
            // assign company of the current user
            $site->setCompany($data['company']);

            // persist
            $em->persist($site);
            $em->flush();

            // Add success message to session
            $this
                ->addFlash(
                    'success',
                    $translator->trans('site.form.created', array(), self::TRANSLATION_DOMAIN)
                );

            // Redirect to cost center company list
            return $this->redirectToRoute('admin.company.sites', array('id' => $site->getCompany()->getId()));
        }

        return new Response('', Response::HTTP_BAD_REQUEST);
    }

    /**
     * @param Request $request
     * @param $id
     * @param TranslatorInterface $translator
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/site/{id}/edit', name: 'admin.site.edit')]
    public function editSiteName(Request $request, $id, TranslatorInterface $translator): Response
    {
        $em = $this->doctrine->getManager();

        /** @var User $user */
        $user = $this->getUser();

        /** @var Site $site */
        $site = $em->getRepository(self::SITE_BUNDLE)->find($id);

        /** @var Company $company */
        $company = $site->getCompany();

        $options = array(
            self::VALIDATION_GROUPS => [self::DEF],
            'method' => 'patch',
            'companyId' => $company->getId(),
            'country' => $company->getMainAddress()->getCountry()->getId(),
            'hasUserApi' => $company->hasUserApi(),
            'userApiPlaceholder' => '',
        );
        $form = $this->createForm(SiteForm::class, $site, $options);
        $form->remove('shipping_points');

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $site->setName($data->getName());

            $this->logger
                ->info(
                    $translator->trans(self::LOGGER_SAVE),
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::SITE_EDIT,
                        LogUtil::USER_NAME => $user->getUsername(),
                        'id' => $site->getId(),
                    ])
                );

            $em->merge($site);
            $em->flush();
            $this->addFlash(self::SUCCESS, $translator->trans('back.site.modification.ok', array(), self::TRANSLATION_DOMAIN));

            // Redirect to cost center company list
            return $this->redirectToRoute('admin.company.sites', array('id' => $company->getId()));
        } else {
            // Show "Edit" page
            return $this->render('@OpenBack/site/site_edit.html.twig', [
                'id' => $site->getId(),
                'form' => $form->createView(),
                'company' => $company
            ]);
        }
    }


    /**
     * @param $id
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/site/{id}/activate', name: 'admin.site.activate')]
    public function activateSite($id, TranslatorInterface $translator)
    {

        $em = $this->doctrine->getManager();

        //add some logs
        $this->logger->info("site has been enabled",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME => EventNameEnum::SITE_ENABLE,
                LogUtil::USER_NAME => $this->getUsername(),
                self::SITE_ID => $id,
            ])
        );

        /** @var Site $site */
        $site = $em->getRepository(self::SITE_BUNDLE)->find($id);

        $site->setEnabled(true);

        try {
            $em->merge($site);
            $em->flush();
            $this->addFlash(self::SUCCESS, $translator->trans('back.site.activation.ok', array(), self::TRANSLATION_DOMAIN));
        } catch (Exception $e) {
            $this->addFlash(self::ERROR, $translator->trans('back.site.activation.ok', array(), self::TRANSLATION_DOMAIN));
        }

        return $this->redirectToRoute(self::INFO_ROUTE, array('id' => $site->getId()));
    }


    /**
     * @param                     $qualifier string the filter
     * @param SiteService         $siteService
     * @param CSVService          $cvsService
     * @param TranslatorInterface $translator
     *
     * @return mixed
     * @throws \AppBundle\Exception\CSVException
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/site/export/{qualifier}', name: 'admin.site.export')]
    public function exportSites($qualifier, SiteService $siteService, CSVService $cvsService, TranslatorInterface $translator)
    {

        /*
        * load data
        */
        $sites = $siteService->getByQualifier($qualifier);

        /*
       * STEP 1: add custom columns
       */
        /**
         * @var Site $site
         */
        foreach ($sites as $site) {
            //shortcuts to company info
            if ($site->getCompany() != null) {
                $site->addProperty("companyName", $site->getCompany()->getName());
            } else {
                $site->addProperty("companyName");

            }

            //shortcuts to address info
            $shippingPoints = $site->getShippingPoints();
            if (!is_null($shippingPoints) && $shippingPoints->count() >= 1) {
                $shippingPoint = $site->getShippingPoints()->first();
                if (!is_null($shippingPoint->getAddress())) {
                    $site->addProperty("siteAddress", $shippingPoint->getAddress()->getAddress());
                    $site->addProperty("zipcode", $shippingPoint->getAddress()->getZipCode());
                    $site->addProperty("city", $shippingPoint->getAddress()->getCity());
                    if ($shippingPoint->getAddress()->getCountry() != null) {
                        $site->addProperty("country", $translator->trans($shippingPoint->getAddress()->getCountry(), array(), self::TRANSLATION_DOMAIN));
                    } else {
                        $site->addProperty("country");
                    }
                } else {
                    $site->addProperty("siteAddress");
                    $site->addProperty("zipcode");
                    $site->addProperty("city");
                    $site->addProperty("country");
                }

                //shortcuts to contact info
                if (!is_null($shippingPoint->getContact())) {
                    $site->addProperty("mainContact", $shippingPoint->getContact()->getFirstName() . " " . $shippingPoint->getContact()->getLastName());
                    $site->addProperty("contactPhone", $shippingPoint->getContact()->getPhone1());
                } else {
                    $site->addProperty("maintContact");
                    $site->addProperty("contactPhone");
                }
            }

            //translated status
            if ($site->getEnabled()) {
                $site->addProperty("translatedStatus", $translator->trans("back.site.list.status_site.enabled", array(), self::TRANSLATION_DOMAIN));
            } else {
                $site->addProperty("translatedStatus", $translator->trans("back.site.list.status_site.disabled", array(), self::TRANSLATION_DOMAIN));
            }
        }

        /*
         * STEP 2: Now define the content of our export
         */
        $headers = [
            "Id",
            "back.site.list.name",
            "back.site.list.company",
            "back.site.list.address",
            "back.site.list.zipCode",
            "back.site.list.city",
            "back.site.list.country",
            "back.site.list.contactName",
            "back.site.list.contactPhone",
            "back.site.list.status"
        ];

        $properties = [
            "id",
            "name",
            "companyName",
            "siteAddress",
            "zipcode",
            "city",
            "country",
            "mainContact",
            "contactPhone",
            "translatedStatus"
        ];

        /*
        * STEP 3: run the export
        */
        $content = $cvsService->objArrayToCSV($sites, $headers, $properties);

        return new Response($content, 200, array(
            'Content-Encoding' => 'UTF-16',
            'Content-Type' => 'application/CSV; charset=UTF-16',
            'Content-Disposition' => 'attachment; filename="cost_centers.csv"'
        ));
    }
}
