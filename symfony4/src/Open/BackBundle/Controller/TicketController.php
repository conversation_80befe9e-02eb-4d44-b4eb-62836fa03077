<?php

namespace Open\BackBundle\Controller;

use AppBundle\Controller\MkoController;
use AppBundle\Entity\Company;
use AppBundle\Exception\CSVException;
use AppBundle\Services\CompanyService;
use AppBundle\Services\CSVService;
use Open\BackBundle\Form\FilterTicketType;
use Open\TicketBundle\Entity\Ticket;
use Open\TicketBundle\Services\TicketService;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Contracts\Translation\TranslatorInterface;

class TicketController extends MkoController
{
    const COMPANY_BUNDLE = \AppBundle\Entity\Company::class;
    const TRANSLATION_DOMAIN = 'AppBundle';
    const COMPANY = 'company';
    const TAB_ACTIVE = 'tabActive';
    const LOCALE = 'locale';
    const PAGINATION = 'pagination';
    const COUNTRY = 'country';
    const CATEGORY = 'translatedCategory';

    /**
     * @param Request       $request
     * @param               $id
     * @param TicketService $ticketService
     * @param string        $qualifier
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/{id}/messages/{qualifier}', name: 'admin.company.messages')]
    public function getMessages(Request $request, $id, TicketService $ticketService, $qualifier = 'all'): Response
    {

        $em = $this->doctrine->getManager();

        /** @var Company $company */
        $company = $em->getRepository(Company::class)->find($id);

        $form = $this->createForm(FilterTicketType::class);
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $filter = $form->getData();
        } else {
            $filter = [];
            $filter['status'] = 'ALL';
        }
        $filter['companyId'] = $id;
        $filter['qualifier'] = $qualifier;

        //render list of users
        return $this->render('@OpenBack/company/company_messages.html.twig', array(
            self::PAGINATION => $ticketService->getAdminTicketsCustomPaginator($request, $filter),
            'form' => $form->createView(),
            self::COMPANY => $company,
            'qualifier_val' => $qualifier,
            self::LOCALE => $request->getLocale(),
            self::TAB_ACTIVE => 5
        ));
    }

    /**
     * @param Request       $request
     * @param               $companyId
     * @param               $qualifier
     * @param TicketService $ticketService
     * @param CSVService    $csvService
     *
     * @return Response
     * @throws CSVException
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/{companyId}/tickets/export/{qualifier}', name: 'admin.company.tickets.export')]
    public function exportTickets(Request $request, $companyId, $qualifier, TicketService $ticketService, CSVService $csvService, TranslatorInterface $translator): Response
    {
        $dateFormat = "d/m/Y H:i:s";


        $form = $this->createForm(FilterTicketType::class);
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $filter = $form->getData();
        } else {
            $filter = [];
            $filter['status'] = 'ALL';
        }
        $filter['companyId'] = $companyId;
        $filter['qualifier'] = $qualifier;

        $tickets = $ticketService->getAdminTicketsByFilter($filter);

        // STEP 1: add custom columns for export
        /** @var Ticket $ticket */
        foreach ($tickets as $ticket) {
            $ticket->addProperty('translatedStatus', $translator->trans('ticket.status.' . $ticket->getStatusAsString(), array(), self::TRANSLATION_DOMAIN));
            if (!is_null($ticket->getAuthor())) {
                $ticket->addProperty('translatedAuthor', $ticket->getAuthor()->getMainContact());
            } else {
                $ticket->addProperty('translatedAuthor', '');
            }
            $ticket->addProperty('companyName', $ticket->getCompany()->getName());
            $ticket->addProperty('formattedCreatedAt', $ticket->getCreatedAt()->format($dateFormat));
            $ticket->addProperty('formattedLastAt', $ticket->getLastMessage()->getCreatedAt()->format($dateFormat));
            $ticket->addProperty('nbMessages', count($ticket->getMessages()));
        }

        // STEP 2: Now define the content of our export
        $headers = [
            'ticket.list.number',
            'ticket.list.status',
            'ticket.list.author',
            'ticket.list.company',
            'ticket.list.createdAt',
            'ticket.list.lastAt',
            'ticket.list.subject',
            'ticket.list.nb_messages'
        ];

        $properties = [
            "id",
            "translatedStatus",
            "translatedAuthor",
            "companyName",
            "formattedCreatedAt",
            "formattedLastAt",
            "subject",
            "nbMessages"
        ];


        // STEP 3: run the export
        $content = $csvService->objArrayToCSV($tickets, $headers, $properties);

        // return the result
        return new Response($content, 200, array(
            'Content-Encoding' => 'UTF-16',
            'Content-Type' => 'application/CSV; charset=UTF-16',
            'Content-Disposition' => 'attachment; filename="tickets.csv"'
        ));
    }
}
