<?php

namespace Open\BackBundle\Controller;

use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

class ToolsController extends AbstractController
{
    /**
     * @param string $text
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_SUPER_ADMIN")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/hash/{text}', name: 'admin.hash')]
    public function hash(string $text)
    {
        return new Response(sha1($text));
    }
}
