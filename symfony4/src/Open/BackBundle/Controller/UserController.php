<?php

namespace Open\BackBundle\Controller;

use AppBundle\Controller\MkoController;
use AppBundle\Entity\Company;
use AppBundle\Entity\Site;
use AppBundle\Entity\User;
use AppBundle\Exception\CSVException;
use AppBundle\Form\UserForm;
use AppBundle\Model\Id;
use AppBundle\Services\ActionHistoryService;
use AppBundle\Services\CSVService;
use AppBundle\Services\LanguageService;
use AppBundle\Services\MailService;
use AppBundle\Services\UserBddService;
use Doctrine\Common\Collections\ArrayCollection;
use FOS\UserBundle\Event\FormEvent;
use FOS\UserBundle\Model\UserManagerInterface;
use FOS\UserBundle\Util\TokenGeneratorInterface;
use Open\BackBundle\Events\CreateUserEvent;
use Open\BackBundle\Events\DeactivationUserEvent;
use Open\BackBundle\Form\FilterAdminType;
use Open\BackBundle\Form\IdForm;
use Open\LogBundle\Utils\LogUtil;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Open\LogBundle\Utils\EventNameEnum;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\ExpressionLanguage\Expression;


class UserController extends MkoController
{

    const TRANSLATOR = 'translator';
    const TRANSLATION_DOMAIN = 'AppBundle';
    const USER_BUNDLE = \AppBundle\Entity\User::class;
    const ROLE_SUPER_ADMIN = 'ROLE_SUPER_ADMIN';
    const SUCCESS = 'success';
    const ERROR = 'error';
    const ROUTE_INFO = 'admin.user.info';

    private EventDispatcherInterface $dispatcher;

    public function __construct(EventDispatcherInterface $dispatcher, ManagerRegistry $doctrine)
    {
        parent::__construct($doctrine);
        $this->dispatcher = $dispatcher;
    }

    /**
     * @param Request $request
     * @param                $qualifier
     * @param UserBddService $userBddService
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/user/list/{qualifier}', name: 'admin.user.list', defaults: ['_locale' => 'en'])]
    public function listUsers(Request $request, $qualifier, UserBddService $userBddService)
    {
        $options = [];
        $options['admin'] = false;

        $form = $this->createForm(FilterAdminType::class, null, $options);
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $filter = $form->getData();
        } else {
            $filter = [];
        }
        $paginator = $userBddService->getCustomUsersFilteredPaginator(1, 25, $request, $filter, $qualifier);

        //render list of users
        return $this->render('@OpenBack/user/user_list.html.twig',
            array('pagination' => $paginator,
                'qualifier_val' => $qualifier,
                'form' => $form->createView(),
                'locale' => $request->getLocale()));
    }

    /**
     * @param Request $request
     * @param                $qualifier
     * @param UserBddService $userBddService
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/admin/list/{qualifier}', name: 'admin.admin.list', defaults: ['_locale' => 'en'])]
    public function listAdmins(Request $request, $qualifier, UserBddService $userBddService)
    {
        $options = [];
        $options['admin'] = true;

        $form = $this->createForm(FilterAdminType::class, null, $options);
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $filter = $form->getData();
        } else {
            $filter = [];
        }

        $filter['page'] = 'adminList';

        $paginator = $userBddService->getCustomUsersFilteredPaginator(1, 25, $request, $filter, $qualifier);

        //render list of users
        return $this->render('@OpenBack/user/admin_list.html.twig',
            array('pagination' => $paginator,
                'qualifier_val' => $qualifier,
                'form' => $form->createView(),
                'locale' => $request->getLocale()));
    }

    /**
     * @param Request $request
     * @param                      $id
     * @param ActionHistoryService $actionHistoryService
     *
     * @param TranslatorInterface $translator
     * @param UserBddService $userBddService
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/user/{id}/info', name: 'admin.user.info')]
    public function getUserInformations(Request $request, $id, ActionHistoryService $actionHistoryService, TranslatorInterface $translator, UserBddService $userBddService)
    {
        $em = $this->doctrine->getManager();

        /** @var User $user */
        $user = $em->getRepository(self::USER_BUNDLE)->find($id);

        //operator cannot see admin informations
        if (!$this->isGranted(self::ROLE_SUPER_ADMIN) && $user->getRoles()[0] == self::ROLE_SUPER_ADMIN) {
            throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_EXCEPTION));
        }

        $isAdmin = false;
        if ($user->getRoles()[0] == self::ROLE_SUPER_ADMIN || $user->getRoles()[0] == self::ROLE_OPERATOR) {
            $isAdmin = true;
        }

        $paginator = $userBddService->getUserConnectionsPaginator(1, 10, $request, $id);

        $actionPaginator = $actionHistoryService->getHistoryPaginatorForUser(1, 10, $request, $id);

        return $this->render('@OpenBack/user/user_info.html.twig',
            [
                'user' => $user,
                'isAdmin' => $isAdmin,
                'pagination' => $paginator,
                'actionPagination' => $actionPaginator,
                'locale' => $request->getLocale(),
                'currentUser' => $this->getUser(),
                'form_activate_user' => $this->createForm(IdForm::class, new Id($user->getId()))->createView(),
                'form_reset_password_user' => $this->createForm(IdForm::class, new Id($user->getId()))->createView()
            ]
        );
    }

    /**
     * @param Request $request
     * @param Request $id
     * @param LanguageService $languageService
     *
     * @param EventDispatcherInterface $eventDispatcher
     *
     * @param UserManagerInterface $userManager
     *
     * @param TranslatorInterface $translator
     *
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/user/add', name: 'admin.user.add', defaults: ['id' => null])]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/{id}/user/add', name: 'admin.company.user.add')]
    public function addUser(Request $request, $id, LanguageService $languageService, EventDispatcherInterface $eventDispatcher, UserManagerInterface $userManager, TranslatorInterface $translator)
    {

        $em = $this->doctrine->getManager();

        $company = null;
        $error = false;

        $sites = new ArrayCollection();

        $from_company_list = false;

        /** @var User $user */
        $user = new User();

        $groups = ['user', 'Default', 'user_registration', 'user_sites'];

        $options = array(
            'validation_groups' => $groups,
            'method' => 'patch',
            'super_admin' => $this->isGranted(self::ROLE_SUPER_ADMIN),
            'languageService' => $languageService
        );

        if ($id) {
            /** @var Company $company */
            $company = $em->getRepository(Company::class)->find($id);
            $sites = $company->getSites();
            $from_company_list = true;
            $options['from_company_list'] = true;
            $options['company_id'] = $company->getId();
        }

        $form = $this->createForm(
            UserForm::class,
            $user,
            $options
        );

        if ($from_company_list) {
            $form->remove('company');
        }


        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                /** @var User $user */
                $user = $form->getData();

                $username = uniqid('user_');
                $user->setUsername($username);
                $user->setUsernameCanonical($username);
                $user->setEnabled(true);
                $password = $this->createFakePassword();
                $user->setPassword($password);
                $user->setPlainPassword($password);

                if ($user->getRole() == 'ROLE_OPERATOR' || $user->getRole() == 'ROLE_SUPER_ADMIN') {
                    $user->setCompany(null);
                    $user->setSites(new ArrayCollection());
                }

                if ($user->isBuyerApi()) {
                    $user->setSites(new ArrayCollection());
                }

                if ($id) {
                    $user->setCompany($company);
                    // If sites is array
                    if (
                        $user->isBuyerApi() === false
                        && is_null($user->getSites())
                        || (is_array($user->getSites()) && empty($user->getSites()))
                    ) {
                        $this->addFlash('danger', $translator->trans('back.user.form.sitesUserKo', [], 'AppBundle'));
                        $error = true;
                    } // if sites is ArrayCollection
                    else if (
                        $user->isBuyerApi() === false
                        && $user->getSites() instanceof ArrayCollection
                        && $user->getSites()->isEmpty()
                        && !$sites->isEmpty()
                    ) {
                        $this->addFlash('danger', $translator->trans('back.user.form.sitesUserKo', [], 'AppBundle'));
                        $error = true;
                    } else {
                        $userSiteList = new ArrayCollection();
                        foreach ($user->getSites() as $siteId) {
                            $site = $em->getRepository(Site::class)->find($siteId);
                            if ($site) {
                                /** @psalm-suppress InvalidArgument */
                                $userSiteList->add($site);
                            }

                        }
                        $user->setSites($userSiteList);
                    }
                }

                $event = new FormEvent($form, $request);
                $eventDispatcher->dispatch($event);
                $eventDispatcher->dispatch(new CreateUserEvent($user));
                $this->logger
                    ->info(
                        $translator->trans('log.profile.save'),
                        LogUtil::buildContext([
                            LogUtil::EVENT_NAME => EventNameEnum::PROFILE_SAVE,
                            LogUtil::USER_NAME => $user->getUsername(),
                            'id' => $user->getId()
                        ])
                    );

                if (!$error) {
                    $em->merge($user);
                    $em->flush();
                    $this->addFlash(self::SUCCESS, $translator->trans('back.user.form.creation.ok', array(), self::TRANSLATION_DOMAIN));
                    if ($id) {
                        return $this->redirectToRoute('admin.company.users', array('id' => $company->getId()));
                    } else {
                        return $this->redirectToRoute('admin.admin.list', array('qualifier' => 'all'));
                    }
                }
            } catch (\Exception $e) {
                $username_email = $form->getData()->getEmail();
                $user = $userManager->findUserByUsernameOrEmail($username_email);

                // If the user exists
                if ($user) {
                    if (!$user->isEnabled()) {
                        $this->addFlash('danger', $translator->trans('back.user.form.creation.mailKoDisabled', [], 'AppBundle'));
                    } else {
                        $this->addFlash('danger', $translator->trans('back.user.form.creation.mailKo', [], 'AppBundle'));
                    }
                } else {
                    $this->addFlash('danger', $translator->trans('back.user.form.creation.ko', [], 'AppBundle'));
                }
            }
        }

        return $this->render(
            '@OpenBack/user/user_edit.html.twig',
            array(
                'form' => $form->createView(),
                'from_company_list' => $from_company_list
            ));
    }


    /**
     * @param Request $request
     * @param                     $id
     * @param LanguageService $languageService
     * @param TranslatorInterface $translator
     * @param null $companyId
     *
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/user/{id}/edit', name: 'admin.user.edit')]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/{companyId}/user/{id}/edit', name: 'admin.company.user.edit')]
    public
    function editUser(Request $request, $id, LanguageService $languageService, TranslatorInterface $translator, $companyId = null)
    {

        $error = false;
        $from_company_list = false;
        $sites = new ArrayCollection();

        $em = $this->doctrine->getManager();

        /** @var User $user */
        $user = $em->getRepository(self::USER_BUNDLE)->find($id);

        //operator cannot see admin informations
        if (!$this->isGranted(self::ROLE_SUPER_ADMIN) && $user->getRoles()[0] == self::ROLE_SUPER_ADMIN) {
            throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_EXCEPTION));
        }

        $options = array(
            'validation_groups' => ['Default'],
            'method' => 'patch',
            'super_admin' => $this->isGranted(self::ROLE_SUPER_ADMIN),
            'languageService' => $languageService,
        );

        if ($companyId) {
            $company = $em->getRepository(Company::class)->find($companyId);
            $sites = $company->getSites();
            $from_company_list = true;
            $options['from_company_list'] = true;
            $options['company_id'] = $companyId;
        }

        $form = $this->createForm(
            UserForm::class,
            $user,
            $options
        );

        if ($from_company_list) {
            $form->remove('company');
        }

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                /** @var User $user */
                $user = $form->getData();

                $user->setEmailCanonical($user->getEmail());

                if ($user->getRole() == 'ROLE_OPERATOR' || $user->getRole() == 'ROLE_SUPER_ADMIN') {
                    $user->setCompany(null);
                    $user->setSites(new ArrayCollection());
                } else {
                    if ($from_company_list) {
                        $user->setCompany($company);
                    }
                    if ((is_null($user->getSites()) || empty($user->getSites()) || $user->getSites()->isEmpty()) && !$sites->isEmpty()) {
                        $this->addFlash('danger', $translator->trans('back.user.form.sitesUserKo', [], 'AppBundle'));
                        $error = true;
                    } else {
                        $userSiteList = new ArrayCollection();
                        foreach ($user->getSites() as $siteId) {
                            $site = $em->getRepository(Site::class)->find($siteId);
                            if ($site) {
                                /** @psalm-suppress InvalidArgument */
                                $userSiteList->add($site);
                            }

                        }
                        $user->setSites($userSiteList);
                    }
                }


                $this->logger->info(
                    $translator->trans('log.profile.save'),
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::PROFILE_SAVE,
                        LogUtil::USER_NAME => $user->getUsername(),
                        'id' => $user->getId()
                    ])
                );

                if (!$error) {
                    $em->merge($user);
                    $em->flush();
                    $this->addFlash(self::SUCCESS, $translator->trans('back.user.form.modification.ok', array(), self::TRANSLATION_DOMAIN));
                    if ($from_company_list) {
                        return $this->redirectToRoute('admin.company.users', array('id' => $company->getId()));
                    } else {
                        return $this->redirectToRoute(self::ROUTE_INFO, array('id' => $user->getId()));
                    }
                }
            } catch (\Exception $e) {
                $this->addFlash(self::ERROR, $translator->trans('back.user.form.modification.ko', array(), self::TRANSLATION_DOMAIN));
            }
        }

        return $this->render(
            '@OpenBack/user/user_edit.html.twig',
            array(
                'form' => $form->createView(),
                'from_company_list' => $from_company_list,
                'userId' => $id
            ));
    }

    /**
     * @param Request $request
     * @param TranslatorInterface $translator
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/user/activate', name: 'admin.user.activate', methods: ['POST'])]
    public function activateUser(Request $request, TranslatorInterface $translator)
    {

        $em = $this->doctrine->getManager();

        $form = $this->createForm(IdForm::class, null, []);
        $form->handleRequest($request);

        $id = $form->getData()['id'];

        if ($form->isSubmitted() && $id !== null) {

            //add some logs
            $this->logger->info("user has been enabled",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::USER_ENABLE,
                    LogUtil::USER_NAME => $this->getUser()->getUsername(),
                    "USER_ID" => $id
                ])
            );

            /** @var User $user */
            $user = $em->getRepository(self::USER_BUNDLE)->find($id);

            //operator cannot see admin informations
            if (!$this->isGranted(self::ROLE_SUPER_ADMIN) && $user->getRoles()[0] == self::ROLE_SUPER_ADMIN) {
                throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_EXCEPTION));
            }

            $user->setEnabled(true);
            $user->setDisabledAt(null);

            try {
                $em->merge($user);
                $em->flush();
                $this->addFlash(self::SUCCESS, $translator->trans('back.user.form.activation.ok', array(), self::TRANSLATION_DOMAIN));
            } catch (\Exception $e) {
                $this->addFlash(self::ERROR, $translator->trans('back.user.form.activation.ko', array(), self::TRANSLATION_DOMAIN));
            }

            return $this->redirectToRoute(self::ROUTE_INFO, array('id' => $user->getId()));
        } else {
            $this->logger->error("user sends invalid values for activating user",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::SECURITY_EVENT,
                    LogUtil::USER_NAME => $this->getUsername()
                ])
            );
            throw $this->createAccessDeniedException();
        }
    }

    /**
     * @param Request $request
     * @param TranslatorInterface $translator
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/user/deactivate', name: 'admin.user.deactivate', methods: ['POST'])]
    public function deactivateUserFromUserPage(Request $request, TranslatorInterface $translator)
    {
        return $this->deactivateUser($request, false, $translator);
    }


    /**
     * @param Request $request
     * @param TranslatorInterface $translator
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/user/deactivate', name: 'admin.company.user.deactivate', methods: ['GET'])]
    public function deactivateUserFromCompany(Request $request, TranslatorInterface $translator)
    {
        $id = $request->get('id');
        return $this->deactivateUserById($id, true, $translator);
    }


    /**
     * @param Request $request
     * @param bool $fromCompany
     * @param TranslatorInterface $translator
     *
     * @return mixed
     */
    private function deactivateUser(Request $request, bool $fromCompany, TranslatorInterface $translator)
    {
        $form = $this->createForm(IdForm::class, null, []);
        $form->handleRequest($request);

        $id = $form->getData()['id'];

        if ($form->isSubmitted() && $id !== null) {
            return $this->deactivateUserById($id, $fromCompany, $translator);
        } else {
            $this->logger->error("user sends invalid values for activating user",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::SECURITY_EVENT,
                    LogUtil::USER_NAME => $this->getUsername()
                ])
            );
            throw $this->createAccessDeniedException();
        }
    }

    private function deactivateUserById(int $id, bool $fromCompany, TranslatorInterface $translator)
    {
        $em = $this->doctrine->getManager();
        //add some logs
        $this->logger->info("user has been disabled",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME => EventNameEnum::USER_DISABLE,
                LogUtil::USER_NAME => $this->getUser()->getUsername(),
                "USER_ID" => $id
            ])
        );


        /** @var User $user */
        $user = $em->getRepository(self::USER_BUNDLE)->find($id);

        //operator cannot see admin informations
        if (!$this->isGranted(self::ROLE_SUPER_ADMIN) && $user->getRoles()[0] == self::ROLE_SUPER_ADMIN) {
            throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_EXCEPTION));
        }

        if ($id == $this->getUser()->getId()) {
            throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_EXCEPTION));
        }

        $user->setEnabled(false);
        $user->setDisabledAt(new \DateTime());

        try {
            $em->merge($user);
            $em->flush();
            $this->dispatcher->dispatch(new DeactivationUserEvent($user));
            $this->addFlash(self::SUCCESS, $translator->trans('back.user.form.deactivation.ok', array(), self::TRANSLATION_DOMAIN));
        } catch (\Exception $e) {
            $this->addFlash(self::ERROR, $translator->trans('back.user.form.deactivation.ko', array(), self::TRANSLATION_DOMAIN));
        }

        if ($fromCompany) {
            return $this->redirectToRoute('admin.company.users', array('id' => $user->getCompany()->getId()));
        } else {
            return $this->redirectToRoute(self::ROUTE_INFO, array('id' => $user->getId()));
        }
    }

    /**
     * @param Request $request
     * @param TokenGeneratorInterface $tokenGenerator
     *
     * @param UserManagerInterface $userManager
     * @param MailService $mailer
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/user/resetPassword', name: 'admin.user.resetPassword', methods: ['POST'])]
    public function resetPasswordUser(Request $request, TokenGeneratorInterface $tokenGenerator, UserManagerInterface $userManager, MailService $mailer, TranslatorInterface $translator)
    {

        $form = $this->createForm(IdForm::class, null, []);
        $form->handleRequest($request);

        $id = $form->getData()['id'];

        $em = $this->doctrine->getManager();

        /** @var User $user */
        $user = $em->getRepository(self::USER_BUNDLE)->find($id);

        try {
            if (null === $user->getConfirmationToken()) {
                $user->setConfirmationToken($tokenGenerator->generateToken());
            }
            $mailer->sendResettingEmailMessage($user);
            $user->setPasswordRequestedAt(new \DateTime());
            $userManager->updateUser($user);
            $this->addFlash(self::SUCCESS, $translator->trans('back.user.form.resetingPassword.ok', array(), self::TRANSLATION_DOMAIN));
        } catch (\Exception $e) {
            $this->addFlash(self::ERROR, $translator->trans('back.user.form.resetingPassword.ko', array(), self::TRANSLATION_DOMAIN));
        }

        return $this->redirectToRoute(self::ROUTE_INFO, array('id' => $user->getId()));
    }


    /**
     * @param Request $request
     * @param CSVService $cvsService
     *
     * @return mixed
     * @throws CSVException
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/admin/export/admins', name: 'admin.admin.export')]
    public function exportAdmins(Request $request, CSVService $cvsService, UserBddService $userBddService, TranslatorInterface $translator)
    {
        $dateFormat = "d/m/Y H:i:s";

        $filter['page'] = 'adminList';

        /*
         * load data
         */
        $users = $userBddService->getAdminsByQualifier($filter);

        /*
         * STEP 1: add custom columns
         */

        /**
         * @var User $user
         */
        foreach ($users as $user) {

            $user->addProperty('id', $user->getId());
            $user->addProperty('email', $user->getEmail());
            $user->addProperty('firstname', $user->getFirstname());
            $user->addProperty('lastname', $user->getLastname());
            $user->addProperty('mainPhoneNumber', $user->getMainPhoneNumber());

            $user->addProperty("translatedType", $translator->trans("back.user.role.main." . $user->getRoles()[0], array(), self::TRANSLATION_DOMAIN));
            $user->addProperty("translatedRole", $translator->trans("back.user.role.secondary." . $user->getRoles()[0], array(), self::TRANSLATION_DOMAIN));

            //format creation date
            $user->addProperty("formattedCreationAt", $user->getCreatedAt()->format($dateFormat));

            //format last connexion date
            if ($user->getLastLogin() != null) {
                $user->addProperty("formattedLastConnection", $user->getLastLogin()->format($dateFormat));
            } else {
                $user->addProperty("formattedLastConnection");
            }

            //format disabled at
            if ($user->getDisabledAt() != null) {
                $user->addProperty("formattedDisabledAt", $user->getDisabledAt()->format($dateFormat));
            } else {
                $user->addProperty("formattedDisabledAt");
            }
        }


        /*
         * STEP 2: Now define the content of our export
         */
        $headers = [
            "Id",
            "back.user.list.email",
            "back.user.list.firstname",
            "back.user.list.lastname",
            "back.user.list.role",
            "back.user.list.phone",
            "back.user.list.creation"
        ];

        $properties = [
            "id",
            "email",
            "firstname",
            "lastname",
            "translatedRole",
            "mainPhoneNumber",
            "formattedCreationAt"
        ];


        /*
         * STEP 3: run the export
         */
        $content = $cvsService->objArrayToCSV($users, $headers, $properties);

        return new Response($content, 200, array(
            'Content-Encoding' => 'UTF-16',
            'Content-Type' => 'application/CSV; charset=UTF-16',
            'Content-Disposition' => 'attachment; filename="users.csv"'));

    }

    /**
     * @param Request $request
     * @param CSVService $cvsService
     *
     * @param UserBddService $userBddService
     *
     * @return mixed
     * @throws CSVException
     */
    #[IsGranted(new Expression('is_granted("ROLE_ADMIN") or is_granted("ROLE_OPERATOR")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/admin/export/users', name: 'admin.user.export')]
    public function exportUsers(Request $request, CSVService $cvsService, UserBddService $userBddService, TranslatorInterface $translator)
    {

        $dateFormat = "d/m/Y H:i:s";


        $filter = [];

        $users = $userBddService->getOnlyUsersByQualifier($filter);

        /**
         * @var User $user
         */
        foreach ($users as $user) {
            $user->addProperty('id', $user->getId());
            $user->addProperty('email', $user->getEmail());
            $user->addProperty('firstname', $user->getFirstname());
            $user->addProperty('lastname', $user->getLastname());

            if ($user->getCompany() != null) {
                $user->addProperty("companyName", $user->getCompany()->getName());
            } else {
                $user->addProperty("companyName");
            }
            $user->addProperty('mainPhoneNumber', $user->getMainPhoneNumber());


            $user->addProperty("translatedType", $translator->trans("back.user.role.main." . $user->getRoles()[0], array(), self::TRANSLATION_DOMAIN));
            $user->addProperty("translatedRole", $translator->trans("back.user.role.secondary." . $user->getRoles()[0], array(), self::TRANSLATION_DOMAIN));

            //format creation date
            $user->addProperty("formattedCreationAt", $user->getCreatedAt()->format($dateFormat));

            //format last connexion date
            if ($user->getLastLogin() != null) {
                $user->addProperty("formattedLastConnection", $user->getLastLogin()->format($dateFormat));
            } else {
                $user->addProperty("formattedLastConnection");
            }

            //format disabled at
            if ($user->getDisabledAt() != null) {
                $user->addProperty("formattedDisabledAt", $user->getDisabledAt()->format($dateFormat));
            } else {
                $user->addProperty("formattedDisabledAt");
            }
        }

        $headers = [
            "Id",
            "back.user.list.email",
            "back.user.list.firstname",
            "back.user.list.lastname",
            "back.user.list.company",
            "back.user.list.role",
            "back.user.list.phone",
            "back.user.list.creation"
        ];

        $properties = [
            "id",
            "email",
            "firstname",
            "lastname",
            "companyName",
            "translatedRole",
            "mainPhoneNumber",
            "formattedCreationAt"
        ];

        $content = $cvsService->objArrayToCSV($users, $headers, $properties);

        return new Response($content, 200, array(
            'Content-Encoding' => 'UTF-16',
            'Content-Type' => 'application/CSV; charset=UTF-16',
            'Content-Disposition' => 'attachment; filename="users.csv"'));

    }

    /**
     * create a fake generated password
     * @return string the generated fake password
     */
    private function createFakePassword()
    {
        // This array must match regexp in AppBundle\Validator\Constraints\SecurePasswordValidator #line 34
        $schars = array('!', '@', '#', '$', '%', '^', '*', '_', '-');

        $password = substr(md5(uniqid(strval(rand()), true)), 0, 15);

        if (strtolower($password) === $password) {
            $password .= chr(rand(65, 90));
        }

        return $password . $schars[array_rand($schars)];
    }
}
