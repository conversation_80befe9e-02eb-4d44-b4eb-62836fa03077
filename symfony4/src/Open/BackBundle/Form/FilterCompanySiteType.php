<?php

namespace Open\BackBundle\Form;

use AppBundle\Entity\Country;
use Doctrine\ORM\EntityRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class FilterCompanySiteType extends AbstractType
{
    const TRANSLATION_DOMAIN = 'AppBundle';

    public function buildForm(FormBuilderInterface $builder, array $options){
        $builder
            ->add(
                'id',
                TextType::class
            )
            ->add(
                'name',
                TextType::class
            )->add(
                'zipCode',
                TextType::class
            )->add(
                'city',
                TextType::class
            )->add(
                'country',
                EntityType::class,
                array(
                    'class' => Country::class,
                    'placeholder' => 'address.form.all_country_placeholder',
                    'query_builder' => function (EntityRepository $er) {
                        return $er->createQueryBuilder('c')
                            ->orderBy('c.code', 'ASC');
                    },
                    'translation_domain' => self::TRANSLATION_DOMAIN,
                    'choice_translation_domain' => self::TRANSLATION_DOMAIN
                )
            )->add(
                'contactName',
                TextType::class
            )->add(
                'contactPhone',
                TextType::class
            )->add(
                'status',
                ChoiceType::class,
                array(
                    'choices' => array(
                        'back.company.list.status_company.all' => 'all',
                        'back.company.list.status_company.draft' => 'draft',
                        'back.company.list.status_company.pending' => 'pending',
                        'back.company.list.status_company.valid' => 'valid',
                        'back.company.list.status_company.disabled' => 'disabled'
                    ),
                    'choice_translation_domain' => self::TRANSLATION_DOMAIN
                )
            );
    }

    /**
     * @return string
     */
    public function getName()
    {
        return 'site_filter';
    }

    /**
     * Configures the options for this type.
     *
     * @param OptionsResolver $resolver The resolver for the options.
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(
            [
                'csrf_protection' => false
            ]
        );
    }

}
