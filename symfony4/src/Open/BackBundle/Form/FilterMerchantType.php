<?php

namespace Open\BackBundle\Form;



use AppBundle\Repository\CategoryRepository;
use Doctrine\ORM\EntityRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Contracts\Translation\TranslatorInterface;


class FilterMerchantType extends AbstractType
{

    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add(
                'id',
                TextType::class
            )
            ->add(
                'name',
                TextType::class
            )
            ->add(
                'firstname',
                TextType::class
            )
            ->add(
                'lastname',
                TextType::class
            )->add(
                'identification',
                TextType::class
            )->add(
                'status',
                ChoiceType::class,
                array(
                    'choices' => array(
                        'back.merchant.list.status.all' => 'all',
                        'back.merchant.list.status.accepted' => 'accepted',
                        'back.merchant.list.status.rejected' => 'rejected',
                        'back.merchant.list.status.pending' => 'pending',

                    ),
                    'choice_translation_domain' => 'AppBundle',
                )
         )
        ->setMethod('GET');
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(
            [
                'csrf_protection' => false
            ]
        );
    }
}
