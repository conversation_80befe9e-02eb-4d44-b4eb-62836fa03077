<?php
/**
 * Created by PhpStorm.
 * User: EDE16590
 * Date: 15/05/2018
 * Time: 10:45
 */

namespace Open\BackBundle\Form;
use Faker\Provider\Text;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Contracts\Translation\TranslatorInterface;

class FilterSearchHistorizationType extends AbstractType
{
    const TRANSLATION_DOMAIN = 'AppBundle';
    const CLAZZ='class';
    const LABEL='label';

    private $translator;

    /**
     * FilterSearchHistorizationType constructor.
     * @param $translator
     */
    public function __construct(TranslatorInterface $translator)
    {
        $this->translator =  $translator;
    }

    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options){

        $builder
            ->setMethod('GET')
            ->add(
                'daterange',
                TextType::class,
                array(
                    'label' => 'back.search_historization.datemin',
                    'attr' => [
                        self::CLAZZ => 'form-control input-inline',
                        'required' => true,
                    ],
                    'translation_domain' => self::TRANSLATION_DOMAIN,
                )
            )->add(
                'id',
                TextType::class
            )->add(
                'isAnonymous',
                ChoiceType::class,
                array(
                    'choices' => array(
                        'filter.yes' => 'Yes',
                        'filter.no' => 'No'
                    ),
                    'choice_translation_domain' => 'AppBundle',
                    'placeholder' => 'All'
                )
            )->add(
                'companyName',
                TextType::class
            )->add(
                'userFullName',
                TextType::class
            )->add(
                'searchedTerm',
                TextType::class
            )->add(
                'nbHits',
                TextType::class
            )->add(
                'save',
                SubmitType::class,
                array(
                    'label' => 'back.company.list.filter.submit',
                    'attr' => array(self::CLAZZ => 'save'),
                    'translation_domain' => self::TRANSLATION_DOMAIN,
                )
            )->add(
                'filter',
                TextType::class
            );

    }

    /**
     * @return string
     */
    public function getName()
    {
        return 'search_historization_list';
    }

    /**
     * Configures the options for this type.
     *
     * @param OptionsResolver $resolver The resolver for the options.
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(
            [
                'csrf_protection' => false
            ]
        );
    }

}
