<?php

namespace Open\BackBundle\Form;

use AppBundle\Entity\Country;
use Doctrine\ORM\EntityRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Contracts\Translation\TranslatorInterface;

class FilterSiteType extends AbstractType
{

    const TRANSLATION_DOMAIN = 'AppBundle';

    /**
     * @var TranslatorInterface
     */
    private $translator;

    public function __construct(TranslatorInterface $translator)
    {
        $this->translator = $translator;
    }

    public function buildForm(FormBuilderInterface $builder, array $options){
        $builder
            ->add(
                'id',
                TextType::class
            )->add(
                'name',
                TextType::class
            )->add(
                'company',
                TextType::class
            )->add(
                'address',
                TextType::class
            )->add(
                'zipCode',
                TextType::class
            )->add(
                'city',
                TextType::class
            )->add(
                'country',
                EntityType::class,
                array(
                    'class' => Country::class,
                    'placeholder' => 'address.form.all_country_placeholder',
                    'query_builder' => function (EntityRepository $er) {
                        return $er->createQueryBuilder('c')
                            ->orderBy('c.code', 'ASC');
                    },
                    'translation_domain' => self::TRANSLATION_DOMAIN,
                    'choice_translation_domain' => self::TRANSLATION_DOMAIN
                )
            )->add(
                'contactName',
                TextType::class
            )->add(
                'contactPhone',
                TextType::class
            )->setMethod('GET');
    }

    /**
     * @return string
     */
    public function getName()
    {
        return 'site_filter';
    }

    /**
     * Configures the options for this type.
     *
     * @param OptionsResolver $resolver The resolver for the options.
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(
            [
                'csrf_protection' => false
            ]
        );
    }

}
