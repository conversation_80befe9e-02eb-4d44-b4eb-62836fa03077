<?php
/**
 * Created by PhpStorm.
 * User: QAR14123
 * Date: 13/04/2018
 * Time: 13:38
 */

namespace Open\BackBundle\Form;


use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Contracts\Translation\TranslatorInterface;

class FilterTicketType extends AbstractType
{

    const TRANSLATION_DOMAIN = 'AppBundle';
    const LABEL = "label";

    /**
     * @var TranslatorInterface
     */
    private $translator;

    /**
     * @param $translator
     */
    public function __construct(TranslatorInterface $translator)
    {
        $this->translator = $translator;
    }

    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add(
                'object',
                TextType::class,
                array(
                    self::LABEL => 'back.ticket.filter.object',
                    'translation_domain' => self::TRANSLATION_DOMAIN
                )
            )
            ->add(
                'author',
                TextType::class,
                array(
                    self::LABEL => 'back.ticket.filter.company',
                    'translation_domain' => self::TRANSLATION_DOMAIN
                )
            )->add(
                'creationMin',
                DateType::class,
                array(self::LABEL => 'back.ticket.filter.creationMin',
                    'widget' => 'single_text',
                    'format' => 'dd-MM-yyyy',
                    'html5' => false,
                    'attr' => [
                        'class' => 'form-control input-inline datepicker',
                        'data-provide' => 'datepicker',
                        'data-date-format' => 'dd-mm-yyyy'
                    ],
                    'translation_domain' => self::TRANSLATION_DOMAIN)
            )->add(
                'creationMax',
                DateType::class,
                array(self::LABEL => 'back.ticket.filter.creationMax',
                    'widget' => 'single_text',
                    'format' => 'dd-MM-yyyy',
                    'html5' => false,
                    'attr' => [
                        'class' => 'form-control input-inline datepicker',
                        'data-provide' => 'datepicker',
                        'data-date-format' => 'dd-mm-yyyy'
                    ],
                    'translation_domain' => self::TRANSLATION_DOMAIN)
            )->add(
                'modificationMin',
                DateType::class,
                array(self::LABEL => 'back.ticket.filter.modificationMin',
                    'widget' => 'single_text',
                    'format' => 'dd-MM-yyyy',
                    'html5' => false,
                    'attr' => [
                        'class' => 'form-control input-inline datepicker',
                        'data-provide' => 'datepicker',
                        'data-date-format' => 'dd-mm-yyyy'
                    ],
                    'translation_domain' => self::TRANSLATION_DOMAIN)
            )->add(
                'modificationMax',
                DateType::class,
                array(self::LABEL => 'back.ticket.filter.modificationMax',
                    'widget' => 'single_text',
                    'format' => 'dd-MM-yyyy',
                    'html5' => false,
                    'attr' => [
                        'class' => 'form-control input-inline datepicker',
                        'data-provide' => 'datepicker',
                        'data-date-format' => 'dd-mm-yyyy'
                    ],
                    'translation_domain' => self::TRANSLATION_DOMAIN)
            )->add(
                'save',
                SubmitType::class,
                array(self::LABEL => 'back.ticket.filter.submit',
                    'attr' => array('class' => 'save'),
                    'translation_domain' => self::TRANSLATION_DOMAIN)
            )
            ->setMethod('GET');
    }

    /**
     * Configures the options for this type.
     *
     * @param OptionsResolver $resolver The resolver for the options.
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(
            [
                'csrf_protection' => false
            ]
        );
    }


}
