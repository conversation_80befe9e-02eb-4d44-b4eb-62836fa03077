<?php
namespace Open\BackBundle\Form;


use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class FilterUserType extends AbstractType
{
    const TRANSLATION_DOMAIN = 'AppBundle';
    const CLAZZ = 'class';

    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add(
                'id',
                TextType::class
            )
            ->add(
                'firstname',
                TextType::class
            )
            ->add(
                'lastname',
                TextType::class
            )
            ->add(
                'email',
                TextType::class
            )->add(
                'role',
                ChoiceType::class,
                array(
                    'choices' => array(
                        'back.user.role.all' => 'ALL',
                        'back.user.role.secondary.ROLE_BUYER_BUYER' => 'BUYER',
                        'back.user.role.secondary.ROLE_BUYER_PAYER' => 'PAYER',
                        'back.user.role.secondary.ROLE_BUYER_ADMIN' => 'ACCOUNT_ADMIN',
                        'back.user.role.secondary.ROLE_OPERATOR' => 'ROLE_OPERATOR',
                        'back.user.role.secondary.ROLE_SUPER_ADMIN' => 'ROLE_SUPER_ADMIN',
                        'back.user.role.secondary.ROLE_API' => 'ROLE_API',
                    ),
                    'choice_translation_domain' => 'AppBundle'
                )

            )->add(
                'creationMin',
                DateType::class,
                array('label' => 'back.user.filter.creationMin',
                    'widget' => 'single_text',
                    'format' => 'dd-MM-yyyy',
                    'html5' => false,
                    'attr' => [
                        self::CLAZZ => 'form-control input-inline datepicker',
                        'data-provide' => 'datepicker',
                        'data-date-format' => 'dd-mm-yyyy'
                    ],
                    'translation_domain' => self::TRANSLATION_DOMAIN)
            )->add(
                'creationMax',
                DateType::class,
                array('label' => 'back.user.filter.creationMax',
                    'widget' => 'single_text',
                    'format' => 'dd-MM-yyyy',
                    'html5' => false,
                    'attr' => [
                        self::CLAZZ => 'form-control input-inline datepicker',
                        'data-provide' => 'datepicker',
                        'data-date-format' => 'dd-mm-yyyy'
                    ],
                    'translation_domain' => self::TRANSLATION_DOMAIN)
            )->add(
                'connectionMin',
                DateType::class,
                array('label' => 'back.user.filter.connectionMin',
                    'widget' => 'single_text',
                    'format' => 'dd-MM-yyyy',
                    'html5' => false,
                    'attr' => [
                        self::CLAZZ => 'form-control input-inline datepicker',
                        'data-provide' => 'datepicker',
                        'data-date-format' => 'dd-mm-yyyy'
                    ],
                    'translation_domain' => self::TRANSLATION_DOMAIN)
            )->add(
                'connectionMax',
                DateType::class,
                array('label' => 'back.user.filter.connectionMax',
                    'widget' => 'single_text',
                    'format' => 'dd-MM-yyyy',
                    'html5' => false,
                    'attr' => [
                        self::CLAZZ => 'form-control input-inline datepicker',
                        'data-provide' => 'datepicker',
                        'data-date-format' => 'dd-mm-yyyy'
                    ],
                    'translation_domain' => self::TRANSLATION_DOMAIN)
            )->add(
                'save',
                SubmitType::class,
                array('label' => 'back.user.filter.filter_title',
                    'attr' => array(self::CLAZZ => 'save'),
                    'translation_domain' => self::TRANSLATION_DOMAIN)
            );
    }

    /**
     * @return string
     */
    public function getName()
    {
        return 'user_filter';
    }

    /**
     * Configures the options for this type.
     *
     * @param OptionsResolver $resolver The resolver for the options.
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(
            [
                'csrf_protection' => false
            ]
        );
    }

}
