<?php

namespace Open\BackBundle\Menu;

use Knp\Menu\ItemInterface;
use Knp\Menu\FactoryInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class Builder
{
    private TranslatorInterface $translator;
    private FactoryInterface $factory;

    public function __construct(TranslatorInterface $translator, FactoryInterface $factory)
    {
        $this->translator = $translator;
        $this->factory = $factory;
    }

    /***
     * construction du menu principal de l'admin
     *
     * @param \Knp\Menu\FactoryInterface $factory
     * @param array $options
     *
     * @return \Knp\Menu\ItemInterface
     */
    public function mainMenu(array $options = []): ItemInterface
    {
        $menu = $this->factory->createItem('root');

        $user = $menu->addChild($this->trans('admin_menu.admin_users.label'));

        $user->setAttribute('dropdown', true);
        $user->addChild($this->trans('admin_menu.admin_users.admin_list'), array('route' => 'admin.admin.list', 'routeParameters' => array("qualifier" => "all")));
        $user->addChild($this->trans('admin_menu.admin_users.user_list'), array('route' => 'admin.user.list', 'routeParameters' => array("qualifier" => "all")));
        $user->addChild($this->trans('admin_menu.admin_users.add_user'), array('route' => 'admin.user.add'));

        //technical menu item to match all other user routes
        $user->addChild('', array('extras' => array('routes' => array(array('pattern' => '/^admin\.user.*/')))))->setDisplay(false);


        $company = $menu->addChild($this->trans('admin_menu.companies_menu.label'));
        $company->setAttribute('dropdown', true);
        $company->addChild($this->trans('admin_menu.companies_menu.all_companies'), array('route' => 'admin.company.list', 'routeParameters' => array("qualifier" => "all")));
        $company->addChild($this->trans('admin_menu.companies_menu.costs_centers'), array('route' => 'admin.site.list', 'routeParameters' => array("qualifier" => "all")));
        $company->addChild($this->trans('admin_menu.bafv_requests_menu.label'), array('route' => 'admin.bafv_requests.list', 'routeParameters' => array()));


        $vendor = $menu->addChild($this->trans('admin_menu.merchant_menu.label'));
        $vendor->setAttribute('dropdown', true);
        $vendor->addChild($this->trans('admin_menu.merchant_menu.label'), array('route' => 'admin.merchant.list', 'routeParameters' => array()));


        //technical menu item to match all other user routes
        $company->addChild('', array('extras' => array('routes' => array(array('pattern' => '/^admin\.company.*/')))))->setDisplay(false);

        $pages = $menu->addChild($this->trans('admin_menu.web_content.label'));
        $pages->addChild($this->trans('admin_menu.web_content.list'), array('route' => 'admin.page.list', 'routeParameters' => array()));
        $pages->addChild($this->trans('admin_menu.web_content.add'), array('route' => 'admin.page.add', 'routeParameters' => array()));
        $pages->addChild($this->trans('admin_menu.web_content.popular_offers'), array('route' => 'system.settings.group', 'routeParameters' => array('domain' => 'offers', 'group' => 'popular', 'locale' => 'en')));
        $pages->setAttribute('dropdown', true);

        //technical menu item to match all other user routes
        $pages->addChild('', array('extras' => array('routes' => array(array('pattern' => '/^admin\.page.*/')))))->setDisplay(false);
        $pages->addChild($this->trans('admin_menu.other_menu.slider'), array('route' => 'admin.slider.list', 'routeParameters' => array()));
        $pages->addChild($this->trans('admin_menu.other_menu.logo'), array('route' => 'admin.logo.list', 'routeParameters' => array()));

        $tickets = $menu->addChild($this->trans('admin_menu.messages_menu.label'));
        $tickets->setAttribute('dropdown', true);
        $tickets->addChild($this->trans('admin_menu.messages_menu.list'), array('route' => 'admin.ticket.list'));
        $tickets->addChild($this->trans('admin_menu.messages_menu.resolved'), array('route' => 'admin.ticket.list', 'routeParameters' => array('status' => 'closed')));
        $tickets->addChild($this->trans('admin_menu.messages_menu.add'), array('route' => 'admin.ticket.create'));

        //technical menu item to match all other user routes
        $tickets->addChild('', array('extras' => array('routes' => array(array('pattern' => '/^admin\.ticket.*/')))))->setDisplay(false);

        $others = $menu->addChild($this->trans('admin_menu.other_menu.label'));
        $others->setAttribute('dropdown', true);
        $others->addChild($this->trans('admin_menu.other_menu.notifications'), array('route' => 'admin.notification.list', 'routeParameters' => array()));
        $others->addChild($this->trans('admin_menu.other_menu.term_payment'), array('route' => 'admin.payment_term.list', 'routeParameters' => array('qualifier' => 'all')));
        $others->addChild($this->trans('admin_menu.redirects.label'), array('route' => 'admin.redirect.list', 'routeParameters' => array("qualifier" => "all")));
        $others->addChild($this->trans('admin_menu.other_menu.search_historization.label'), array('route' => 'admin.search_historization.list', 'routeParameters' => array()));
        $others->addChild($this->trans('admin_menu.other_menu.top_mismatch_catalog_references'), array('route' => 'admin.top_mismatch_catalog_references', 'routeParameters' => array()));
        $others->addChild($this->trans('admin_menu.other_menu.automatic_controls'), array('route' => 'admin.monitor', 'routeParameters' => array()));
        $others->addChild($this->trans('admin_menu.other_menu.redis_keys'), array('route' => 'admin.rediskeys', 'routeParameters' => array()));

        $others->addChild('', array('extras' => array('routes' => array(array('pattern' => '/^admin\.notification.*/')))))->setDisplay(false);
        $others->addChild($this->trans('admin_menu.other_menu.payloads'), ['route' => 'admin.payloads.list', 'routeParameters' => []]);
        $others->addChild($this->trans('admin_menu.other_menu.api_doc'), ['route' => 'api_doc', 'routeParameters' => []])->setLinkAttribute('target', 'blank');

        if ($options['isAdmin']) {
            $params = $menu->addChild($this->trans('admin_menu.sys_parameters.label'), array('route' => 'admin.settings.index', 'routeParameters' => array()));
            //technical menu item to match all other user routes
            $params->addChild('', array('extras' => array('routes' => array(array('pattern' => '/^system\.settings.*/')))))->setDisplay(false);
        }

        return $menu;
    }

    /***
     * Juste pour alléger la lecture du code mainMenu
     * N'apporte rien d'autre
     * @param $msg
     *
     * @return string
     */
    private function trans($msg)
    {
        return $this->translator->trans($msg, [], 'AppBundle', 'en');
    }
}
