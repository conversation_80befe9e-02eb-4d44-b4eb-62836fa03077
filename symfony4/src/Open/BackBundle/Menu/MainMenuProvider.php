<?php


namespace Open\BackBundle\Menu;

use Knp\Menu\Provider\MenuProviderInterface;
use Knp\Menu\ItemInterface;

class MainMenuProvider implements MenuProviderInterface
{
    private Builder $builder;

    public function __construct(Builder $builder)
    {
        $this->builder = $builder;
    }

    public function get($name, array $options = []): ItemInterface
    {
        if ('main' === $name) {
            return $this->builder->mainMenu($options);
        }
        throw new \InvalidArgumentException(sprintf('The menu "%s" is not defined.', $name));
    }

    public function has($name, array $options = []): bool
    {
        return 'main' === $name;
    }
}