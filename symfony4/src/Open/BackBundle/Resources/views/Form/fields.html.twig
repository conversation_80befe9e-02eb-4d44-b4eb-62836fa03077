{% block setting_widget %}
    {% set required = '' %}
    {% if form.parent.vars.value.required %}
        {% set required = "required" %}
    {% endif %}
    {% set label = 'system.settings.' ~ form.parent.vars.value.domain ~ '.' ~ form.parent.vars.value.name %}
    <label for="{{ form.vars.id }}" class="control-label">{{ label|trans({}, 'AppBundle') }}</label>
    {% if form.parent.vars.value.valueType == 'string' or form.parent.vars.value.valueType == 'integer' or form.parent.vars.value.valueType == 'float' %}
        <input {{ required }} type="text" name="{{ form.vars.full_name }}" value="{{ form.vars.value }}" id="{{ form.vars.id }}" class="form-control"/>
    {% elseif  form.parent.vars.value.valueType == 'text' or form.parent.vars.value.valueType == 'wysiwyg' %}
        <textarea id="{{ form.vars.id }}" name="{{ form.vars.full_name }}" class="form-control" data-wysiwyg="{{ form.parent.vars.value.valueType == 'wysiwyg' ? 1 : 0 }}">{{ form.vars.value }}</textarea>
    {% endif %}
{% endblock %}

{% block slug_widget %}
    {% set attr = attr|merge({class: (attr.class|default('') ~ ' input-group')|trim}) %}
    {% set attrInput = attr|merge({class: (attr.class|default('') ~ ' form-control')|trim}) %}
    {%- if not valid -%}
        {% set attr = attr|merge({class: (attr.class|default('') ~ ' is-invalid')|trim}) -%}
        {% set attrInput = attr|merge({class: (attrInput.class|default('') ~ ' is-invalid')|trim}) -%}
    {%- endif -%}
    {% apply spaceless %}
        <div class="row">
            <div class="col-md-6 form-group">
                <label class="form-control-label" for="js-slug-edit-input-{{ form.vars.id }}">{{ label| trans({}, 'AppBundle') }}</label>
                <div class="{{ attr.class }}">
                    <div class="input-group-prepend">
                        <span class="input-group-text">{{ url('homepage') }}</span>
                    </div>
                    <input data-original-slug="{{ form.vars.value }}" id="js-slug-edit-input-{{ form.vars.id }}" type="text" class="{{ attrInput.class }}" value="{{ form.vars.value }}" name="{{ form.vars.full_name }}" readonly/>
                    <div class="input-group-append">
                        <span id="js-slug-edit-button-{{ form.vars.id }}" class="input-group-text">
                            <svg class="Icon">
                                <use xlink:href="#pencil"></use>
                            </svg>
                        </span>
                    </div>
                    {{ form_errors(form) }}
                    <label class="error" for="js-slug-edit-input-{{ form.vars.id }}"></label>
                </div>
            </div>
        </div>
    {% endapply %}
{% endblock %}

{% block facet_filter_widget %}
    {% apply spaceless %}
        {% for child in form %}
            <div class="h-line {% if loop.index > 1 %} mt-negative-50 {% endif %}"></div>
            <div class="criteria-block">
                <div id="control-{{ loop.index }}" class="criteria-control">
                    <div class="select-label">
                        {{ form_label(child, null, {'capitalized': true}) }}
                    </div>
                    <div class="criteria-checkbox pb-50">
                        {% set extraChoices = [] %}
                        {% for choiceField in child %}
                            {% set choice %}
                                <div class="facet facetTooltip">
                                    {{ form_widget(choiceField, {'attr':{'onchange': "javascript:formSearchSubmit()"}}) }}
                                    {{ form_label(choiceField) }}
                                </div>
                            {% endset %}
                            {% if loop.index < 6 %}
                                {{ choice }}
                            {% else %}
                                {%  set extraChoices = extraChoices|merge([choice])%}
                            {% endif %}
                        {% endfor %}
                        {% if child.children|length > 5 %}
                            <div class="show-more pointer" onclick="return onClickShowMore(this, {{ extraChoices|json_encode }});">
                                {{ 'search.sidebar.see_more'|trans({}, 'AppBundle') }}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% endfor %}
    {% endapply %}
{% endblock %}
