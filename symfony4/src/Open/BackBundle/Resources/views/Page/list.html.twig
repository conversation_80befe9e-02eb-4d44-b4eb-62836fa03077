{% extends '@OpenBack/base.html.twig' %}
{% trans_default_domain "AppBundle" %}
{% block body %}


    <div class="d-flex export-btn">
        <a href="{{ path('admin.page.add') }}" class="btn btn-sm btn-primary btn-dark" role="button" aria-pressed="true">{{ 'back.page.add'|trans }}</a>
    </div>

    <div class="d-flex justify-content-center">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th scope="col">{{ 'back.commons.id'|trans }}</th>
                    {# sorting of properties based on query components #}
                    <th scope="col">{{ 'back.page.list.title'|trans }}</th>
                    <th scope="col">{{ 'back.page.list.slug'|trans }}</th>
                    <th scope="col">{{ 'back.page.list.author'|trans }}</th>
                    <th scope="col" class="col-date">{{ knp_pagination_sortable(pagination, 'back.page.list.creation'|trans, 'e.createdAt') }}</th>
                    <th scope="col" class="col-status">{{ 'back.page.list.status'|trans }}</th>
                    <th scope="col" class="col-lang">{{ 'back.page.list.lang'|trans }}</th>
                    <th scope="col" class="text-right">{{ 'back.commons.actions'|trans }}</th>
                </tr>
            </thead>
            <tbody>
            {# table body #}
            {% for page in pagination %}
                <tr {% if loop.index is odd %}class="color"{% endif %}>
                    <th scope="row">{{ page.id }}</th>
                    <td>{{ page.title}}</td>
                    <td>/{{ page.slug}}</td>
                    <td>{{ page.author}}</td>
                    <td class="col-date">{{ page.createdAt|format_datetime('short', 'none', locale=locale) }}</td>
                    <td class="col-status">{{ ('back.page.' ~ page.status) | trans }}</td>
                    <td class="col-lang">
                        {% if page.hasLanguage('en') %}
                            <img src="{{ asset('/images/blank.gif') }}" class="flag flag-en" alt="flag" />
                        {% endif %}
                        {% if page.hasLanguage('fr') %}
                            <img src="{{ asset('/images/blank.gif') }}" class="flag flag-fr" alt="flag" />
                        {% endif %}
                        {% if page.hasLanguage('es') %}
                            <img src="{{ asset('/images/blank.gif') }}" class="flag flag-es" alt="flag" />
                        {% endif %}
                        {% if page.hasLanguage('de') %}
                            <img src="{{ asset('/images/blank.gif') }}" class="flag flag-de" alt="flag" />
                        {% endif %}
                        {% if page.hasLanguage('it') %}
                            <img src="{{ asset('/images/blank.gif') }}" class="flag flag-it" alt="flag" />
                        {% endif %}
                        {% if page.hasLanguage('nl') %}
                            <img src="{{ asset('/images/blank.gif') }}" class="flag flag-nl" alt="flag" />
                        {% endif %}
                    </td>
                    <td class="text-right col-action">
                        <a class="action visualisationPage" href="{{ page.slug }}" data-fr="{{ page.hasLanguage('fr') }}" data-en="{{ page.hasLanguage('en') }}" data-es="{{ page.hasLanguage('es') }}" data-de="{{ page.hasLanguage('de') }}" data-it="{{ page.hasLanguage('it') }}">
                            <span title={{ 'back.commons.view'|trans }}>
                                <svg class="Icon" alt={{ 'back.commons.view'|trans }}>
                                    <use xlink:href="#eye"></use>
                                </svg>
                            </span>
                        </a>
                        <a class="action" href="{{ path('admin.page.edit', {'id':page.id}) }}">
                            <span title={{ 'back.commons.edit'|trans }}>
                                <svg class="Icon" alt={{ 'back.commons.edit'|trans }}>
                                    <use xlink:href="#pencil"></use>
                                </svg>
                            </span>
                        </a>
                        <a id="deletePage" class="action" href="{{ path('admin.page.delete', {'id':page.id}) }}">
                            <span title={{ 'back.commons.delete'|trans }}>
                                <svg class="Icon" alt={{ 'back.commons.delete'|trans }}>
                                    <use xlink:href="#icon-cancel"></use>
                                </svg>
                            </span>
                        </a>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>
    {# display navigation #}
    <div class="d-flex justify-content-left">
        <div class="navigation col-md-10">
            {{ knp_pagination_render(pagination) }}
        </div>
        <div class="col-md-2">Total: {{ pagination.getTotalItemCount }}</div>
    </div>


    <div id="modalSelectLocale" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="modalSelectLocale" aria-hidden="true" style="margin-top: 50px;" data-backdrop="false">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" >{{ 'back.page.list.modal.title'|trans }}</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                </div>
                <div class="modal-body">
                    <a id="pageFR" class="selectLocale" href="#"><img src="{{ asset('/images/blank.gif') }}" class="flag flag-fr" alt="flag" /><span>{{ 'node.form.lang.fr'|trans }}</span> </a>
                    <a id="pageEN" class="selectLocale" href="#"><img src="{{ asset('/images/blank.gif') }}" class="flag flag-en" alt="flag" /><span>{{ 'node.form.lang.en'|trans }}</span></a>
                    <a id="pageES" class="selectLocale" href="#"><img src="{{ asset('/images/blank.gif') }}" class="flag flag-es" alt="flag" /><span>{{ 'node.form.lang.es'|trans }}</span></a>
                    <a id="pageDE" class="selectLocale" href="#"><img src="{{ asset('/images/blank.gif') }}" class="flag flag-de" alt="flag" /><span>{{ 'node.form.lang.de'|trans }}</span></a>
                    <a id="pageIT" class="selectLocale" href="#"><img src="{{ asset('/images/blank.gif') }}" class="flag flag-it" alt="flag" /><span>{{ 'node.form.lang.it'|trans }}</span></a>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            $('#deletePage').on('click', function (ev) {
                var href = $(ev.currentTarget).attr('href');

                ev.preventDefault();

                window.UI.Modal.confirm('{{ 'node.form.delete.title'|trans({}, 'AppBundle') }}', '{{ 'node.form.delete.content'|trans({}, 'AppBundle') }}', function () {
                        window.UI.Modal.showLoading();
                        window.location.href = href;
                    },
                    function () {
                        console.log('Confirmation canceled');
                    });

                return false;
            });

            $('.visualisationPage').on('click', function (ev) {
                let target = $(ev.currentTarget);
                let href = target.attr('href');
                ev.preventDefault();

                if(target.data('fr')){
                    $("#pageFR").attr("href", "/fr/"+href);
                }else{
                    $("#pageFR").hide();
                }

                if(target.data('en')){
                    $("#pageEN").attr("href", "/en/"+href);
                }else{
                    $("#pageEN").hide();
                }

                if(target.data('es')){
                    $("#pageES").attr("href", "/es/"+href);
                }else{
                    $("#pageES").hide();
                }

                if(target.data('de')){
                    $("#pageDE").attr("href", "/de/"+href);
                }else{
                    $("#pageDE").hide();
                }

                if(target.data('it')){
                    $("#pageIT").attr("href", "/it/"+href);
                }else{
                    $("#pageIT").hide();
                }
                $('#modalSelectLocale').modal('show');
            });
        });
    </script>

{% endblock %}
