{% extends '@OpenBack/base.html.twig' %}
{% trans_default_domain "AppBundle" %}
{% block title %}
    {{ 'back.redirect.list.header'|trans }}
{% endblock %}

{% block body %}


    <div class="d-flex export-btn">
        <a href="{{ path('admin.redirect.add') }}" class="btn btn-sm btn-primary btn-dark" role="button" aria-pressed="true">{{ 'back.redirect.add'|trans }}</a>
    </div>


    <div class="d-flex justify-content-center">
        <table class="table table-hover">
            <thead>
            <tr>
                <th scope="col">{{ 'back.commons.id'|trans }}</th>
                {# sorting of properties based on query components #}
                <th scope="col">{{ 'back.redirect.list.origin'|trans }}</th>
                <th scope="col">{{ 'back.redirect.list.destination'|trans }}</th>
                <th scope="col">{{ 'back.redirect.list.type'|trans }}</th>
                <th scope="col" class="col-date">{{ knp_pagination_sortable(pagination, 'back.page.list.creation'|trans, 'e.createdAt') }}</th>
                <th scope="col" class="text-right">{{ 'back.commons.actions'|trans }}</th>
            </tr>
            </thead>
            <tbody>
            {# table body #}
            {% for redirect in pagination %}
                <tr {% if loop.index is odd %}class="color"{% endif %}>
                    <th scope="row">{{ redirect.id }}</th>
                    <td>/{{ redirect.origin }}</td>
                    <td>/{{ redirect.destination }}</td>
                    <td>{{ redirect.type}}</td>
                    <td class="col-date">{{ redirect.createdAt|format_datetime('short', 'none', locale=locale) }}</td>
                    <td class="text-right col-action">
                        <a class="action" href="/{{ locale }}/{{ redirect.destination }}">
                            <span title={{ 'back.commons.view'|trans }}>
                                <svg class="Icon" alt={{ 'back.commons.view'|trans }}>
                                    <use xlink:href="#eye"></use>
                                </svg>
                            </span>
                        </a>
                        <a class="action" href="{{ path('admin.redirect.edit', {id: redirect.id}) }}">
                            <span title={{ 'back.commons.delete'|trans }}>
                                <svg class="Icon" alt={{ 'back.commons.edit'|trans }}>
                                    <use xlink:href="#pencil"></use>
                                </svg>
                            </span>
                        </a>

                        <a class="action js-delete-redirect" href="{{ path('admin.redirect.delete', {id: redirect.id}) }}">
                            <span title={{ 'back.commons.delete'|trans }}>
                                <svg class="Icon" alt={{ 'back.commons.delete'|trans }}>
                                    <use xlink:href="#delete"></use>
                                </svg>
                            </span>
                        </a>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>
    {# display navigation #}
    <div class="d-flex justify-content-left">
        <div class="navigation col-md-10">
            {{ knp_pagination_render(pagination) }}
        </div>
        <div class="col-md-2">Total: {{ pagination.getTotalItemCount }}</div>
    </div>
    <script>
      document.addEventListener('DOMContentLoaded', function() {
            $('.js-delete-redirect').on('click', function (ev) {
              var href = $(ev.currentTarget).attr('href');

              ev.preventDefault();

              window.UI.Modal.confirm('{{ 'back.redirect.list.confirm_delete_title'|trans({}, 'AppBundle') }}', '{{ 'back.redirect.list.confirm_delete'|trans({}, 'AppBundle') }}', function () {
                window.UI.Modal.showLoading();
                window.location.href = href;
              },
              function () {
                console.log('Confirmation canceled');
              });

              return false;
            });
      });
    </script>
{% endblock %}
