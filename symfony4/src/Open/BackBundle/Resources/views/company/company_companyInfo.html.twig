{% extends '@OpenBack/company/menu/company_menu.html.twig' %}
{% trans_default_domain 'AppBundle' %}
{% block body %}

    <div id="actionBox" style="margin-bottom: 20px; display:flex;">
        {% if company.enabled %}

            {{ form_start(form_deactivate_company, {'action': path('admin.company.deactivate'),  'attr': {'id': 'deactivateCompany'} }) }}
            {{ form_widget(form_deactivate_company.id) }}
            {{ form_widget(form_deactivate_company.validate, { 'label': 'back.user.form.deactivate' | trans, 'attr': {'class': 'btn btn-danger'} } ) }}
            {{ form_end(form_deactivate_company) }}

        {% else %}

            {{ form_start(form_activate_company, {'action': path('admin.company.activate')}) }}
            {{ form_widget(form_activate_company.id) }}
            {{ form_widget(form_activate_company.validate, { 'label': 'back.user.form.activate' | trans, 'attr': {'class': 'btn btn-info'} } ) }}
            {{ form_end(form_activate_company) }}

            {% if not company.rejected %}

                {{ form_start(form_reject_company, {'action': path('admin.company.reject')}) }}
                {{ form_widget(form_reject_company.id) }}
                {{ form_widget(form_reject_company.validate, { 'label': 'back.user.form.reject' | trans, 'attr': {'class': 'btn btn-danger'} } ) }}
                {{ form_end(form_reject_company) }}

            {% endif %}
        {% endif %}
        <div>
        <a href="{{ path('admin.company.info.edit', { 'id' : company.id})}}"
           class="btn btn-info"
           role="button" aria-pressed="true">{{ 'back.company.companyInfo.edit' | trans }}</a>
        </div>
        {{form_start(form_account_type, {'attr': {'class': 'form-inline'} }) }}
        <div class="form-group ml-2">
            {{ form_label(form_account_type.category) }}
            {{ form_widget(form_account_type.category) }}
        </div>
        {{form_end(form_account_type)}}

    </div>
        {{ 'back.company.companyInfo.info.title' | trans }}
        <table class="table table-bordered">
            <tr>
                <td style="width: 50%">{{ 'back.company.companyInfo.country' | trans }}</td>
                <td style="width: 50%">
                   {% if company.mainAddress %}
                       {{ company.mainAddress.country|trans }}
                   {% endif %}
               </td>
            </tr>
            <tr>>
                <td>{{ 'back.company.companyInfo.info.name' | trans }}</td>
                <td>{{ company.name }}</td>
            </tr>
            <tr>
                <td>{{ 'back.company.companyInfo.info.code' | trans }}</td>
                <td>{{ company.identification }}</td>
            </tr>
            <tr>
                <td>{{ 'back.company.companyInfo.document.title' | trans }}</td>
                <td>
            {% for document in company.documents %}
                <a href="{{ path('document.view', {'id': document.id}) }}">{{ document.filename }}</a><br />
            {% endfor %}
                </td>
            </tr>
            {% if company.endpointUrl is not null %}
            <tr>
                <td>{{ 'back.company.companyInfo.endpointUrl' | trans }}</td>
                <td>{{ company.endpointUrl }}</td>
            </tr>
            {% endif %}
            <tr>
                <td>{{ 'company.form.cgu' | trans }}</td>
                <td>
                    {% if company.cgu %}
                        <svg class="Icon">
                            <use xlink:href="#icon-buyer-tick-green"></use>
                        </svg>
                    {% else %}
                        <svg style="height: 18px" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 352 512"><path fill="#dc3545" d="M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z"></path></svg>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <td>{{ 'company.form.fullAuto' | trans }}</td>
                <td>
                    {% if company.fullAuto %}
                        <svg class="Icon">
                            <use xlink:href="#icon-buyer-tick-green"></use>
                        </svg>
                    {% else %}
                        <svg style="height: 18px" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 352 512"><path fill="#dc3545" d="M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z"></path></svg>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <td>{{ 'company.form.eCatalog' | trans }}</td>
                <td>
                    {% if company.eCatalog %}
                        <svg class="Icon">
                            <use xlink:href="#icon-buyer-tick-green"></use>
                        </svg>
                    {% else %}
                        <svg style="height: 18px" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 352 512"><path fill="#dc3545" d="M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z"></path></svg>
                    {% endif %}
                </td>
            </tr>
        </table>

        <div>
            {{ 'back.company.companyInfo.termpayment.authorization' | trans }}
            <div style="margin: 10px 0 15px">
                {% if not company.termpaymentMoneyTransfertEnabled  %}

                    {{ form_start(form_activate_term_payment, {'action': path('admin.company.generalInfo.activateTermPayment')}) }}
                    {{ form_widget(form_activate_term_payment.id) }}
                    {{ form_widget(form_activate_term_payment.validate, { 'label': 'back.company.companyInfo.termpayment.active' | trans, 'attr': {'class': 'btn btn-info'} } ) }}
                    {{ form_end(form_activate_term_payment) }}

                    {% if company.termpaymentMoneyTransfertPending %}
                        <a class="btn btn-danger" href="#" id="buttonShowModal" name="buttonShowModal" data-company-id={{ company.id }}>
                            {{ 'back.company.companyInfo.termpayment.deny'|trans }}
                        </a>
                    {% endif %}
                {% else %}

                    {{ form_start(form_deactivate_term_payment, {'action': path('admin.company.generalInfo.removeTermPayment')}) }}
                    {{ form_widget(form_deactivate_term_payment.id) }}
                    {{ form_widget(form_deactivate_term_payment.validate, { 'label': 'back.company.companyInfo.termpayment.inactive' | trans, 'attr': {'class': 'btn btn-info'} } ) }}
                    {{ form_end(form_deactivate_term_payment) }}

                {% endif %}
            </div>
            <table class="table table-bordered">
                <tr>
                    <td style="width: 50%">{{ 'back.company.companyInfo.termpayment.title' | trans }}</td>
                    <td>
                        {% if company.termpaymentMoneyTransfertPending %}
                            {{ 'back.company.companyInfo.termpayment.pending' | trans }} {{ company.termpaymentMoneyTransfertRequestDate | date('d/m/Y') }}
                        {% elseif company.termpaymentMoneyTransfertEnabled %}
                            {{ 'back.company.companyInfo.termpayment.enabled' | trans }} {{ company.termpaymentMoneyTransfertAcceptDate | date('d/m/Y') }}
                        {% else %}
                            {{ 'back.company.companyInfo.termpayment.disabled' | trans }}
                        {% endif %}
                    </td>
                </tr>
                {% if company.rejectedReason is not null %}
                    <tr>
                        <td>{{ 'back.company.companyInfo.rejectedReason' | trans }}</td>
                        <td>{{ company.rejectedReason }}</td>
                    </tr>
                {% endif %}

                {% if company.deactivationReason is not null %}
                    <tr>
                        <td>{{ 'back.company.companyInfo.deactivationReason' | trans }}</td>
                        <td>{{ company.deactivationReason }}</td>
                    </tr>
                {% endif %}
            </table>
        </div>


            {{ 'back.company.companyInfo.address' | trans }}
            {% if company.billingAddress %}
                <table class="table table-bordered">
                    <tr>
                        <td>{{ 'back.company.companyInfo.address' | trans }}</td>
                        <td>
                            {% if company.mainAddress %}
                                {{ company.mainAddress.address }}
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td>{{ 'back.company.companyInfo.addressComplement' | trans }}</td>
                        <td>
                            {% if company.mainAddress %}
                                {{ company.mainAddress.address2 }}
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td>{{ 'back.company.companyInfo.zipCode' | trans }}</td>
                        <td>
                            {% if company.mainAddress %}
                                {{ company.mainAddress.zipCode }}
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td>{{ 'back.company.companyInfo.city' | trans }}</td>
                        <td>
                            {% if company.mainAddress %}
                                {{ company.mainAddress.city }}
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td>{{ 'back.company.companyInfo.region' | trans }}</td>
                        <td>
                            {% if company.mainAddress %}
                                {{ company.mainAddress.regionText }}
                            {% endif %}
                        </td>
                    </tr>
                </table>
            {% else %}
                <p> {{ 'back.company.companyInfo.contact.noInformation' | trans }}</p>
            {% endif %}

            {{ 'back.company.companyInfo.contact.billing' | trans }}
            {% if company.billingAddress %}
                <table class="table table-bordered">
                    <tr>
                        <td>{{ 'back.company.companyInfo.billing.service' | trans }}</td>
                        <td>{{ company.billingService }}</td>
                    </tr>
                    <tr>
                        <td>{{ 'back.company.companyInfo.address' | trans }}</td>
                        <td>
                            {% if company.billingAddress %}
                                {{ company.billingAddress.address }}
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td>{{ 'back.company.companyInfo.addressComplement' | trans }}</td>
                        <td>
                            {% if company.billingAddress %}
                                {{ company.billingAddress.address2 }}
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td>{{ 'back.company.companyInfo.zipCode' | trans }}</td>
                        <td>
                            {% if company.billingAddress %}
                                {{ company.billingAddress.zipCode }}
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td>{{ 'back.company.companyInfo.city' | trans }}</td>
                        <td>
                            {% if company.billingAddress %}
                                {{ company.billingAddress.city }}
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td>{{ 'back.company.companyInfo.region' | trans }}</td>
                        <td>
                            {% if company.billingAddress %}
                                {{ company.billingAddress.regionText }}
                            {% endif %}
                        </td>
                    </tr>
                </table>
            {% else %}
                <p> {{ 'back.company.companyInfo.contact.noInformation' | trans }}</p>
            {% endif %}
        </div>

    <div id="modalDenyReason" class="modal Modal" tabindex="-1" role="dialog" aria-labelledby="modalDenyReason" aria-hidden="true" data-backdrop="static">
        <div class="modal-dialog">
            <div class="modal-content Modal-content js-modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" >{{ 'back.company.companyInfo.termpayment.deny'|trans }}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                </div>
                <div class="modal-body">
                    {{ form_start(formModal) }}
                    <div class="form-group">
                        {{ form_row(formModal.reason, {'attr' : {'class' : 'form-control'}}) }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary js-cancel-button" data-dismiss="modal">{{ 'modal.cancel'|trans }}</button>
                    <button id="js-add-lang-button" class="btn btn-info">{{ 'modal.confirm'|trans  }}</button>
                </div>
                {{ form_end(formModal) }}
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            $('#deactivateCompany #id_form_validate').on('click', function (ev) {

                ev.preventDefault();

                window.UI.Modal.confirm('{{ 'back.company.companyInfo.deactivate.title'|trans({}, 'AppBundle') }}', '{{ 'back.company.companyInfo.deactivate.content'|trans({}, 'AppBundle') }}', function () {
                        window.UI.Modal.showLoading();
                        $("#deactivateCompany").submit();
                    },
                    function () {
                        console.log('Confirmation canceled');
                    });

                return false;
            });

            let selectedIndex = null;
            $('#category')
                .on('focus', function () {
                    // Store the current value on focus and on change
                    selectedIndex = this.value;
                }).on('change',function (ev) {
                let parent = $(this);
                console.log(selectedIndex);
                parent.attr('disabled', 'disabled');
                window.UI.Modal.confirm('{{ 'back.user.change_type.title'|trans({}, 'AppBundle') }}', '{{ 'back.user.change_type.content'|trans({}, 'AppBundle') }}',
                    function () {
                        console.log('asd');
                        window.UI.Modal.showLoading();
                        $.ajax({
                            method: 'POST',
                            url: '{{ path('admin.company.generalInfo.updateAccountType') }}',
                            data: {
                                companyId: $('#companyId').val(),
                                categoryId: parent.val()
                            },
                            success: function (response) {
                                window.UI.Modal.hideLoading();
                                parent.removeAttr('disabled');
                                window.UI.Modal.alert(response.message);
                            },
                            error: function (responseJSON) {
                                window.UI.Modal.hideLoading();
                                parent.removeAttr('disabled');
                                window.UI.Modal.alert(responseJSON.responseJSON.message);
                            }
                        });
                    }, function () {
                        parent.removeAttr('disabled');
                        parent.val(selectedIndex);
                    });
            });
        });

        document.addEventListener('DOMContentLoaded', function() {

            $("#buttonShowModal").click(function(e){
                e.preventDefault();
                $("#form_company").val($(this).data('companyId'));
                $("#modalDenyReason").modal("show");
            });
        });
    </script>

{% endblock %}
