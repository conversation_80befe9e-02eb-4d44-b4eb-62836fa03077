{% extends '@OpenBack/base.html.twig' %}
{% form_theme form 'bootstrap_4_layout.html.twig' %}

{% trans_default_domain "AppBundle" %}
{% block body %}
    <div class="container">

    <div style="width:100%; padding: 0 0 20px 5px">
        <a href="{{ path('admin.company.generalInfo', {'id': company.id}) }}" style="display:flex; align-items: center; color: #9600FF">
            <div style="width: 10px; height: 10px; border: solid; border-color: #9600FF; border-width: 0 2px 2px 0;transform: rotate(135deg); margin-right: 5px"></div>
            {{ 'back.back' | trans }}
        </a>
    </div>

    <div class="CompanyForm">
        <div class="card">
            <div class="card-body">
                {{ form_start(form, {'attr':{'id':'js-company-form'}}) }}
                <h2>{{ 'company.form.social'|trans }}</h2>
                {% do form.mainAddress.check.setRendered %}
                {{ form_row(form.mainAddress.country) }}
                {{ form_row(form.name) }}
                {{ form_row(form.category) }}
                {{ form_row(form.fullAuto) }}
                {{ form_row(form.eCatalog) }}

                <div class="CompanyIdenfication">
                    {{ form_row(form.identification) }}
                    {% if company.identification and company.mainAddress and company.mainAddress.country and company.mainAddress.country.id == 1%}
                        <div class="form-group row">
                            <label class="form-control-label col-sm-2" for="company_form_siren" title="company.form.siren">{{ 'company.form.siren'|trans }}</label>
                            <div class="col-sm-10">
                                <input type="text" disabled="disabled" class="form-control" value="{{ company.identification[4:] }}">
                            </div>
                        </div>
                    {% endif %}
                </div>

                <div class="form-row js-attachments-container">
                    {{ form_label(form.documents) }}
                    <ul class="attachment-files js-attachment-files">
                        <li class="file--warning">{{ 'ticket.common.nofiles'|trans }}</li>
                    </ul>
                    {{ form_widget(form.documents) }}
                    <button type="button" class="Button Button--upload js-file-reset-button button--reset">
                        <svg class="Icon"><use xlink:href="#icon-trash"/></svg>
                    </button>
                </div>

                <h2>{{ 'company.form.address'|trans }}</h2>
                {{ form_row(form.mainAddress.address) }}
                {{ form_row(form.mainAddress.address2) }}
                {{ form_row(form.mainAddress.zipCode) }}
                {{ form_row(form.mainAddress.city) }}
                {% if form.mainAddress.region is defined %}
                    {{ form_row(form.mainAddress.region) }}
                {% elseif form.mainAddress.regionText is defined %}
                    {{ form_row(form.mainAddress.regionText) }}
                {% endif %}

                <h2>{{ 'company.form.billing_address.address'|trans }}</h2>
                {{ form_row(form.billingAddress.check,{ 'label' : 'company.form.check' }) }}
                <div id="js-billing-address" >
                    {% do form.billingAddress.country.setRendered %}
                    {{  form_row(form.billingService) }}
                    {{ form_row(form.billingAddress.address) }}
                    {{ form_row(form.billingAddress.address2) }}
                    {{ form_row(form.billingAddress.zipCode) }}
                    {{ form_row(form.billingAddress.city) }}
                    {% if form.billingAddress.region is defined %}
                        {{ form_row(form.billingAddress.region) }}
                    {% elseif form.billingAddress.regionText is defined %}
                        {{ form_row(form.billingAddress.regionText) }}
                    {% endif %}
                </div>
                <h2>{{ 'company.form.middleware'|trans }}</h2>
                {{ form_row(form.endpointUrl) }}
            </div>
        </div>

        <br>
        <div class="form-row Buttons">
            <div class="col-md-12">
                {{ form_row(form.save, {'attr':{'class':'btn-primary'}}) }}
            </div>
        </div>

        {{ form_end(form) }}

        <script type="text/javascript">
            'use strict';

            var _updateList = function () {
                var $filesDiv = $(this).parents('.js-attachments-container').find('.js-attachment-files');

                if (this.files.length > 0) {
                    $filesDiv.html('');

                    for (var i = 0 ; i < this.files.length ; i++) {
                        var $li = $('<li>' + this.files[i].name  + '</li>');

                        $filesDiv.append($li);
                    }
                } else {
                    $filesDiv.html('<li class="file--warning">{{ 'ticket.common.nofiles'|trans }}</li>');
                }
            };

            $('.js-file-reset-button').on('click', function () {
                var $button = $(this);
                var $parent = $button.parent();
                var $input = $parent.find('input[type=file]');
                var $filesDiv = $(this).parents('.js-attachments-container').find('.js-attachment-files');


                $input.wrap('<form>').closest('form').get(0).reset();
                $input.unwrap();

                $filesDiv.html('<li class="file--warning">{{ 'ticket.common.nofiles'|trans }}</li>');

            });

            document.addEventListener('DOMContentLoaded', function() {
                let $form = $('#js-company-form');


                // EO Sort country list
                $('input[type=file]').on('change', _updateList);

                // Initialize the custom company identification validation
                window.UI.CompanyIdentification.init(
                    '#company_form_mainAddress_country'
                );

                // Initialize country selection
                window.UI.CountrySelector.init(
                    '#company_form_mainAddress_country',
                    '#company_form_identification'

                );


                UI.OpenDiv.createCheckAction({
                    "divId": "#js-billing-address",
                    "checkId": "#company_form_billingAddress_check"
                });

                UI.OpenDiv.filterRegion({
                    "countrySelector": "#company_form_mainAddress_country",
                    "regionSelector": "#company_form_mainAddress_region"
                });
                UI.OpenDiv.filterRegion({
                    "countrySelector": "#company_form_mainAddress_country",
                    "regionSelector": "#company_form_billingAddress_region"
                });

                {{ form_jquery_validation(form) }}


                // Tweak validation of the sites checkbox (1 site checked required unless role is admin)
                $('input[name="company_form[identification]"]').rules(
                    'add',
                    {
                        'company-identification' : true,
                        'messages' : {
                            'company-identification': '{{ 'form.company.ident_number.invalid'|trans({}, 'validators') }}'
                        }
                    }
                );


                /*UI.AutoComplete.createAutoComplete({
                 "path": "{{ path('city_autocomplete') }}",
                 "countryId": "#company_form_mainAddress_country",
                 "cityId": "#company_form_mainAddress_city",
                 "zipcodeId": "#company_form_mainAddress_zipCode",
                 "cityField": true
                 });

                 UI.AutoComplete.createAutoComplete({
                 "path": "{{ path('city_autocomplete') }}",
                 "countryId": "#company_form_billingAddress_country",
                 "cityId": "#company_form_billingAddress_city",
                 "zipcodeId": "#company_form_billingAddress_zipCode",
                 "cityField": true
                 });

                 // Auto complete zipcode
                 UI.AutoComplete.createAutoComplete({
                 "path": "{{ path('city_autocomplete') }}",
                 "countryId": "#company_form_mainAddress_country",
                 "cityId": "#company_form_mainAddress_zipCode",
                 "zipcodeId": "#company_form_mainAddress_city",
                 "cityField": false
                 });
                 UI.AutoComplete.createAutoComplete({
                 "path": "{{ path('city_autocomplete') }}",
                 "countryId": "#company_form_billingAddress_country",
                 "cityId": "#company_form_billingAddress_zipCode",
                 "zipcodeId": "#company_form_billingAddress_city",
                 "cityField": false
                 });*/


                // Show loading spinner on save/submit if the form is valid
                $form.on('submit', function () {
                    if($form.data('validator').valid()) {
                        UI.Modal.showLoading();
                    }
                });



            });
        </script>
    </div>
{% endblock %}
