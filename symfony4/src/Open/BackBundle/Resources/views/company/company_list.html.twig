{% extends '@OpenBack/base.html.twig' %}
{% trans_default_domain "AppBundle" %}
{% form_theme form 'bootstrap_4_layout.html.twig' %}
{% block body %}

    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.7.1/css/bootstrap-datepicker.css" rel="stylesheet" type="text/css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.7.1/js/bootstrap-datepicker.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.7.1/locales/bootstrap-datepicker.fr.min.js"></script>


    <div>
        <h4>{{ 'back.company.filter_title' | trans }}</h4>
    </div>

    <div class="form-container company-filter">

        {{ form_start(form, {'class': 'myclass', 'attr': {'class': 'form-inline'}}) }}



        <div class="input-group col-md-4">
            {{ form_label(form.creationMin) }}
            {{ form_errors(form.creationMin) }}
            {{ form_widget(form.creationMin, {'attr' : {'class' : 'resetable_input col-md-3 datepicker'}}) }}

            {{ form_label(form.creationMax) }}
            {{ form_errors(form.creationMax) }}
            {{ form_widget(form.creationMax, {'attr' : {'class' : 'resetable_input col-md-3 datepicker'}}) }}
        </div>

        <div class="input-group col-md-3">
            {{ form_widget(form.save, {'attr' : {'class' : 'btn-info'}}) }}
            <button id="clearForm" class="btn btn-outline-dark" type="button">{{ 'back.company.filter_clear' | trans }}</button>
        </div>
    </div>

    <div class="d-flex export-btn">
        <a href="#" id="exportBtn" class="btn btn-sm btn-primary btn-dark" role="button" aria-pressed="true">{{ 'back.commons.export_csv'|trans }}</a>
    </div>

    <div class="d-flex justify-content-center">
        <table class="table table-hover">
            <thead>
                <tr>
                    {# sorting of properties based on query components #}
                    <th>{{ 'back.commons.id'|trans }}</th>
                    <th>{{ knp_pagination_sortable(pagination, 'back.company.list.name'|trans, 'e.name') }}</th>
                    <th>{{ knp_pagination_sortable(pagination, 'back.company.list.identification'|trans, 'e.identification') }}</th>
                    <th>{{ knp_pagination_sortable(pagination, 'back.company.list.city'|trans, 'a.city') }}</th>
                    <th>{{ knp_pagination_sortable(pagination, 'back.company.list.country'|trans, 'a.country') }}</th>
                    <th>{{ 'back.company.list.user_type'|trans }}</th>
                    <th>{{ 'back.company.list.costCenters'|trans }}</th>
                    <th>{{ 'back.company.list.users'|trans }}</th>

                    <th>{{ knp_pagination_sortable(pagination, 'back.company.list.created'|trans, 'e.createdAt') }}</th>
                    <th>{{ 'back.company.list.status'|trans }}</th>
                    <th>{{ 'back.commons.actions'|trans }}</th>
                </tr>
                <tr>
                    <th>{{ form_widget(form.id, {'attr': {'style': 'width:50px'}}) }}</th>
                    <th>{{ form_widget(form.name) }}</th>
                    <th>{{ form_widget(form.identification) }}</th>
                    <th>{{ form_widget(form.city) }}</th>
                    <th class="min-width-small">{{ form_widget(form.country, {'attr' : {'class' : 'filter_select'}}) }}</th>
                    <th class="min-width-medium">{{ form_widget(form.category, {'attr' : {'class' : 'filter_select'}}) }}</th>
                  <!--  <th></th>-->
                    <th></th>

                    <th></th>
                    <th></th>


                    {% if qualifier_val == 'all' %}
                        <th class="min-width-medium">{{ form_widget(form.status, {'attr' : {'class' : 'filter_select'}}) }}</th>
                    {% else %}
                        {% do form.status.setRendered %}
                        <th></th>
                    {% endif %}
                    <th></th>

                    <th></th>
                    <input type="submit" style="display: none"></input>
                    {{ form_end(form) }}
                </tr>
            </thead>
            <tbody>
            {% for company in pagination %}
                <tr {% if loop.index is odd %}class="color"{% endif %}>
                    <td>{{ company.id }}</td>
                    <td>{{ company.name }}</td>
                    <td>{{ company.identification }}</td>
                    <td>{{ company.mainAddress.city }}</td>
                    <td>{{ company.mainAddress.country | trans }}</td>
                    {% if company.category is null %}
                        <td></td>
                    {% else %}
                        <td>{{ ('category.' ~ company.category.label) | trans }}</td>
                    {% endif %}

                    <td><a href="{{ path('admin.company.sites', {'id':company.id}) }}">{{ company.sites | length }}</a></td>
                    <td><a href="{{ path('admin.company.users', {'id':company.id}) }}">{{ company.enabled_users | length }}</a></td>
                    {% if company.createdAt is null %}
                        <td></td>
                    {% else %}
                        <td>{{company.createdAt|format_datetime('short', 'none', locale=locale) }}</td>
                    {% endif %}

                    <td>
                        {{ ('back.company.list.status_company.' ~ company.status) | trans  }}
                    </td>
                    <td class="text-right col-action">
                        <a class="action" href="{{ path('admin.company.generalInfo', {'id':company.id}) }}">
                            <span title={{ 'back.commons.view'|trans }}>
                                <svg class="Icon" >
                                    <use xlink:href="#eye"></use>
                                </svg>
                            </span>
                        </a>

                        <a class="action" href="{{ path('admin.company.user.add', {'id':company.id}) }}">
                            <span title="{{ 'back.company.list.add.user'|trans }}">
                                <svg class="Icon" >
                                    <use xlink:href="#icon-add-user"></use>
                                </svg>
                            </span>
                        </a>

                        <a class="action" href="#">
                            <span id="buttonShowModal{{ company.id }}" name="buttonShowModal" data-company-id={{ company.id }} title="{{ 'back.site.add'|trans }}">
                                <svg class="Icon" >
                                    <use xlink:href="#icon-add-site"></use>
                                </svg>
                            </span>
                        </a>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>

    <div id="modalNewSite" class="modal Modal" tabindex="-1" role="dialog" aria-labelledby="modalNewSite" aria-hidden="true" data-backdrop="static">
        <div class="modal-dialog">
            <div class="modal-content Modal-content js-modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" >{{ 'site.form.add_modal'|trans }}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                </div>
                <div class="modal-body">
                    {{ form_start(formModal) }}
                    <div class="form-group">
                        {{ form_row(formModal.name, {'attr' : {'class' : 'form-control'}}) }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary js-cancel-button" data-dismiss="modal">{{ 'modal.cancel'|trans }}</button>
                    <button id="js-add-lang-button" class="btn btn-info">{{ 'node.form.lang.button'|trans  }}</button>
                </div>
                {{ form_end(formModal) }}
            </div>
        </div>
    </div>
    {# display navigation #}
    <div class="d-flex justify-content-left">
        <div class="navigation col-md-10">
            {{ knp_pagination_render(pagination) }}
        </div>
        <div class="col-md-2">Total: {{ pagination.getTotalItemCount }}</div>
    </div>
    <script type="application/javascript">
      document.addEventListener('DOMContentLoaded', function() {
          $('.datepicker').datepicker({
              language: 'fr'
          });

          let selectCountryText = $("#filter_company_country option:selected").text();
          let selectCategoryText = $("#filter_company_category option:selected").text();
          let selectStatusText = $("#filter_company_status option:selected").text();

          $("#filter_company_country").html($('#filter_company_country option').sort(function(x, y) {
              if($(x).val() === "")
                return 0;
              else
                return $(x).text().toUpperCase() < $(y).text().toUpperCase() ? -1 : 1;
          }));
          $("#filter_company_category").html($('#filter_company_category option').sort(function(x, y) {
              return $(x).text().toUpperCase() < $(y).text().toUpperCase() ? -1 : 1;
          }));
          $("#filter_company_status").html($('#filter_company_status option').sort(function(x, y) {
              return $(x).text().toUpperCase() < $(y).text().toUpperCase() ? -1 : 1;
          }));


        $(".filter_select").change(function() {
            $('form[name="filter_company"]').submit();
        });

        $("#clearForm").click(function() {
          $(".resetable_input").val("");
            $('form[name="filter_company"]').submit();
        });

        $("span[name='buttonShowModal']").click(function(e){
            e.preventDefault();
            $("#form_company").val($(this).data('companyId'));
            $("#modalNewSite").modal("show");
        });

        $("#exportBtn").click(function() {
            document.filter_company.action= '{{ path('admin.company.export', {'qualifier': qualifier_val}) }}';
            $('form[name="filter_company"]').submit();
            document.filter_company.action= '';
        });

        // Sort countries select list
        $("#filter_company_country option").filter(function() {
            return $(this).text() === selectCountryText;
        }).prop('selected', true);
        $("#filter_company_category option").filter(function() {
            return $(this).text() === selectCategoryText;
        }).prop('selected', true);
        $("#filter_company_status option").filter(function() {
            return $(this).text() === selectStatusText;
        }).prop('selected', true);
      });

    </script>
{% endblock %}
