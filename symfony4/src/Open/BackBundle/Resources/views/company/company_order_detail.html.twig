{% extends '@OpenBack/company/menu/company_menu.html.twig' %}
{% trans_default_domain 'AppBundle' %}
{% set locale = app.request.locale %}

{% block title %} {{ 'orders.detail.title' | trans }}{% endblock %}
{% block stylesheets %}
    <link href="{{ asset('css/page-default-v1.css') }}" rel="stylesheet" media="screen">
{% endblock %}
{% block body %}
    <div class="Page-cart-inner Page-order-detail">
        <div class="previous-block">
            <a class="previous-page" href="{{ path('admin.company.orders', { 'id' : company.id})}}"><i class="arrow left"></i>{{ 'orders.detail.go_back'|trans|upper }}</a>
        </div>

        <div class="detail-block">

            <div class="Page-content">
                <div class="Page-body">
                    {% for merchant in orders %}
                        <div class="order-status">
                            <span class="title">{{ 'orders.list.block.status'|trans([], 'AppBundle') }}&nbsp;</span>
                            <span class="value">{{ ('orders.status.status_'~order.status ) |trans([], 'AppBundle') }}</span>
                        </div>
                        <div class="merchant-bloc-actions">
                            <h2 class="merchant-title">{{ merchant.merchant.name }}</h2>

                        </div>
                        <table class="table">
                            <thead class="underline">
                            <tr>
                                <th class="label-detail">{{ 'cart.table_label.product_detail'|trans }}</th>
                                <th>{{ 'cart.table_label.product_name'|trans }}</th>
                                <th>{{ 'cart.table_label.unit_price'|trans }}</th>
                                <th>{{ 'cart.table_label.quantity'|trans }}</th>
                                <th>{{ 'cart.table_label.total_price'|trans }}</th>
                            </tr>
                            </thead>
                            <tbody>
                            {% for item in merchant.items %}
                                <tr>
                                    <td class="underline">
                                        <div class="product">
                                            {% if item.name is not empty %}
                                                <a href="{{ path('front.offer.detail', {'productName' : item.name, 'ref': item.offer_id})|replace({'%2F': '%252F'}) }}"></a>
                                            {% else %}
                                                <a href="{{ path('front.offer.detail.short', {'ref': item.offer_id}) }}"></a>
                                            {% endif %}

                                            <div class="img-container">
                                                {% if item.item_image_url is not empty %}
                                                    <img src="{{ item.item_image_url }}" width="128" height="128"/>
                                                {% else %}
                                                    <img src="/images/no_image_available.svg?v1'"/>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td class="underline">
                                        <h5 class="name">{{ item.name }}</h5>
                                    </td>
                                    <td class="underline">
                                        <p class="mobile-center">
                                            {{ item.price | price(locale)}}
                                            {{ item.currency.code|upper }}
                                        </p>
                                    </td>
                                    <td class="underline">
                                        <p>{{ item.quantity }}</p>
                                    </td>
                                    <td class="underline">

                                        <p class="item-total-price mobile-center">
                                                {{ (item.price * item.quantity) | price(locale)}}
                                            {{ item.currency.code|upper }}
                                        </p>
                                    </td>
                                </tr>
                            {% endfor %}
                            {% if merchant.items|length > 1 %}
                                <tr class="resume-table subtotal">
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td>{{ 'cart.table_label.subtotal'|trans }}</td>
                                    <td class="total-without-vat">
                                        {{ merchant.amount | price(locale)}}
                                        {{ order.currency|upper }}
                                    </td>
                                </tr>

                                {% for vat,total in merchant.subTotalVat %}
                                    <tr class="resume-table taxes">
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td>
                                            <p class="label-taxes label">{{ 'cart.table_label.taxes'|trans }}&nbsp;</p>
                                            <p class="vat-percent">{{ vat | number_format(2, '.')}}%</p>
                                        </td>
                                        <td>
                                            {{ total | price(locale)}}
                                            {{ order.currency|upper }}
                                        </td>
                                    </tr>
                                {% endfor %}

                                <tr class="resume-table subtotal-vat">
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td>{{ 'cart.table_label.subtotal_vat'|trans }}</td>
                                    <td class="total-cart">
                                            {{ merchant.amountVatIncluded | price(locale)}}
                                        {{ order.currency|upper }}
                                    </td>
                                </tr>
                            {% endif %}
                            </tbody>
                        </table>
                        <div class="between"></div>
                    {% endfor %}
                </div>
                <div class="row">
                    <div class="col-sm-4"></div>
                    <div class="col-sm-4"></div>
                    <div class="col-sm-4">
                        <table id="table-merchant-total" class="table">

                            <tbody>
                            <tr class="resume-table subtotal">
                                <td></td>
                                <td></td>
                                <td class="label">{{ 'cart.table_label.total'|trans }}</td>
                                <td class="total-without-vat">
                                    {{ order.amount | price(locale)}}
                                    {{ order.currency|upper }}
                                </td>
                            </tr>
                            {% for vat, total in order.subTotalVat %}
                                <tr class="total-table-taxes resume-table taxes">
                                    <td></td>
                                    <td></td>
                                    <td class="label">
                                        <p class="label-taxes label">{{ 'cart.table_label.taxes'|trans }} <span class="vat-percent">{{ vat | number_format(2, '.')}}%</span></p>
                                    </td>
                                    <td class="vat-value">
                                        {{ total | price(locale)}}
                                        {{ order.currency|upper }}
                                    </td>
                                </tr>
                            {% endfor %}
                            <tr  id="subtotal-vat-total" class="resume-table subtotal-vat">
                                <td></td>
                                <td></td>
                                <td class="label">{{ 'cart.table_label.total_vat'|trans }}</td>
                                <td class="total-cart">
                                    {{ order.amountVatIncluded | price(locale)}}
                                    {{ order.currency|upper }}
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
