{% trans_default_domain 'AppBundle' %}
{% set locale = app.request.locale %}

<tr>
    <td>
        {{ order.id }}
    </td>
    <td>
        {{ order.createdOn|format_datetime('short', 'none', locale=locale) }}
    </td>
    <td>
        {{ order.idNumber }}
    </td>
    <td>
        {{ ('back.company.order.status.'~order.statusFront)|trans }}
    </td>
    <td>
        {{ order.shippingAddress.name }}
    </td>
    <td>
        {% if locale == 'en' %}
            {{ order.price | number_format('2', '.', ',') }}
        {% else %}
            {{ order.price | number_format('2', ',', ' ')}}
        {% endif %}
        {{ order.currency }}
    </td>
    <td>
        <a href="{{ path('admin.company.order.export', {'orderId': order.id,'companyId':companyId,}) }}">{{ 'back.company.order.export'|trans }}</a>
    </td>
</tr>
