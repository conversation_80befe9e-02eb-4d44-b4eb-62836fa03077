{% extends '@OpenBack/base.html.twig' %}
{% trans_default_domain "AppBundle" %}
{% block body %}

    {{ form_start(form_accept_paymentTerm , {'action': path('admin.payment_term.accept'), 'attr': {'id': 'acceptTermPayment'}}) }}
    {{ form_widget(form_accept_paymentTerm.id) }}
    {{ form_widget(form_accept_paymentTerm.validate, { 'label': 'back.user.form.activate' | trans, 'attr': {'class': 'btn btn-info', 'style': 'display:none;'} } ) }}
    {{ form_end(form_accept_paymentTerm) }}


    <h2>{{ 'back.index_page.unread_messages'| trans }}</h2>
    {% if newTicket.getTotalItemCount > 0 %}
        <div class="d-flex justify-content-center">
            <table class="table table-hover">
                <thead>
                    <th>{{ 'ticket.list.number'|trans }}</th>
                    <th>{{ 'ticket.list.sujet'|trans }}</th>
                    <th>{{ 'ticket.list.author'|trans }}</th>
                    <th>{{ 'ticket.list.main_contact'|trans }}</th>
                    <th>{{ 'ticket.list.lastAt'|trans }}</th>
                    <th>{{ 'ticket.list.createdAt'|trans }}</th>
                    <th>{{ 'ticket.list.status'|trans }}</th>
                    <th>{{ 'ticket.list.nb_messages'|trans }}</th>
                    <th>{{ 'ticket.list.actions'|trans }}</th>
                </thead>
                <tbody>
                {% for ticket in newTicket %}
                    <tr>
                        <td class="col--id">
                            <a href="{{ path('admin.ticket.edit', {'id':ticket.ticketNumber}) }}" title="{{ ticket.ticketNumber }}">{{ ticket.ticketNumber|slice(-5,5) }}</a>
                        </td>
                        <td class="col--left">{{ ticket.subject }}</td>
                        <td>
                            {% if ticket.author is null %}
                                {% set author = ticket.anonymousFirstName ~ ' ' ~ ticket.anonymousLastName %}
                            {% else %}
                                {% set author = ticket.author.name %}
                            {% endif %}
                            {{ author }} {% if ticket.createdByAdmin == true %} {{ 'ticket.edit.operator' | trans }} {% endif %}
                        </td>
                        <td>
                            {% if ticket.company is not null %}
                                {% if ticket.company.mainContact is not null %}
                                    {{ ticket.company.mainContact.email }}
                                {% endif %}
                            {% endif %}
                        </td>
                        <td>{{ ticket.lastMessage.createdAt | format_datetime('short', 'none', locale=locale) }}</td>
                        <td>{{ ticket.createdAt | format_datetime('short', 'none', locale=locale) }}</td>
                        <td>
                            {{ ('ticket.status.' ~ ticket.statusAsString)|trans }}
                        </td>
                        <td>
                            {{ ticket.messages|length }}
                        </td>
                        <td>
                            <a class="action" href="{{ path('admin.ticket.edit', {'id':ticket.ticketNumber}) }}">
                            <span title={{ 'back.commons.view'|trans }}>
                                <svg class="Icon" alt={{ 'back.commons.view'|trans }}>
                                    <use xlink:href="#eye"></use>
                                </svg>
                            </span>
                            </a>
                        </td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
        <a href="{{ path( 'admin.ticket.list') }}">{{ 'back.index_page.see_all' | trans }}</a>
    {% else %}
        <div class="d-flex justify-content-left">
            <span>{{ 'ticket.list.empty'| trans }}</span>
        </div>
    {% endif %}

    <h2>{{ 'back.index_page.companies_to_validate'| trans }}</h2>
    {% if acceptableCompany.getTotalItemCount > 0 %}
        <div class="d-flex justify-content-center">
            <table class="table table-hover">
                <thead>
                    <th>{{ 'back.commons.id'|trans }}</th>
                    <th>{{ 'back.company.list.name'|trans }}</th>
                    <th>{{ 'back.company.list.identification'|trans }}</th>
                    <th>{{ 'back.company.list.country'|trans }}</th>
                    <th>{{ 'back.company.list.category'|trans }}</th>
                    <th>{{ 'back.company.list.costCenters'|trans }}</th>
                    <th>{{ 'back.company.list.users'|trans }}</th>
                    <th>{{ 'back.company.list.created'|trans }}</th>
                    <th>{{ 'back.company.list.status'|trans }}</th>
                    <th>{{ 'back.commons.actions'|trans }}</th>
                </thead>
                <tbody>
                {% for company in acceptableCompany %}
                    <tr>
                        <td>{{ company.id }}</td>
                        <td>{{ company.name }}</td>
                        <td>{{ company.identification }}</td>
                        <td>{{ company.mainAddress.country | trans }}</td>
                        {% if company.category is null %}
                            <td></td>
                        {% else %}
                            <td>{{ ('category.' ~ company.category.label) | trans }}</td>
                        {% endif %}

                        <td><a href="{{ path('admin.company.sites', {'id':company.id}) }}">{{ company.sites | length }}</a></td>
                        <td><a href="{{ path('admin.company.users', {'id':company.id}) }}">{{ company.users | length }}</a></td>
                        {% if company.createdAt is null %}
                            <td></td>
                        {% else %}
                            <td>{{company.createdAt|format_datetime('short', 'none', locale=locale) }}</td>
                        {% endif %}
                        <td>
                            {{ ('back.company.list.status_company.' ~ company.status) | trans  }}
                        </td>
                        <td class="text-right col-action">
                            <a class="action" href="{{ path('admin.company.generalInfo', {'id':company.id}) }}">
                            <span title={{ 'back.commons.view'|trans }}>
                                <svg class="Icon" >
                                    <use xlink:href="#eye"></use>
                                </svg>
                            </span>
                            </a>
                        </td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
        <a href="{{ path('admin.company.list', {'qualifier':'pending'}) }}"> {{ 'back.index_page.see_all' | trans }}</a>
    {% else %}
        <div class="d-flex justify-content-left">
            <span>{{ 'back.company.list.empty'| trans }}</span>
        </div>
    {% endif %}

    <h2>{{ 'back.index_page.companies_term_payment_request_to_validate'| trans }}</h2>
    {% if companyWithTermPaymentRequest.getTotalItemCount > 0 %}
        <div class="d-flex justify-content-center">
            <table class="table table-hover">
                <thead>
                <tr>
                    {# sorting of properties based on query components #}
                    <th>{{ 'back.commons.id'|trans }}</th>
                    <th>{{ 'back.company.list.name'|trans }}</th>
                    <th>{{ 'back.company.list.identification'|trans }}</th>
                    <th>{{ 'back.company.list.city'|trans }}</th>
                    <th>{{ 'back.company.list.country'|trans }}</th>
                    <th>{{ 'back.company.list.termpayment_moneytransfert_enabled'|trans }}</th>
                    <th>{{ 'back.company.list.termpayment_moneytransfert_pending'|trans }}</th>
                    <th>{{ 'back.company.list.termpayment_moneytransfert_date_requested'|trans }}</th>
                    <th>{{ 'back.company.list.termpayment_moneytransfert_date_accepted'|trans }}</th>
                    <th>{{ 'back.commons.actions'|trans }}</th>
                </tr>
                </thead>
                <tbody>
                {% for company in companyWithTermPaymentRequest %}
                    <tr {% if loop.index is odd %}class="color"{% endif %}>
                        <td>{{ company.id }}</td>
                        <td>{{ company.name }}</td>
                        <td>{{ company.identification }}</td>
                        <td>{{ company.mainAddress.city }}</td>
                        <td>{{ company.mainAddress.country | trans }}</td>
                        <td>{{ company.termpaymentMoneyTransfertEnabled ? 'Yes' : 'No' }}</td>
                        <td>{{ company.termpaymentMoneyTransfertPending ? 'Yes' : 'No' }}</td>
                        <td title="{{ company.termpaymentMoneyTransfertRequestDate | format_datetime('short', 'none', locale=locale)  }}">
                            {%  if company.termpaymentMoneyTransfertRequestDate %}
                                {{ company.termpaymentMoneyTransfertRequestDate | format_datetime('short', 'none', locale=locale)  }}
                            {% else %}
                                N/A
                            {%  endif %}
                        </td>
                        {%  if company.termpaymentMoneyTransfertAcceptDate %}
                        <td title="{{ company.termpaymentMoneyTransfertAcceptDate | format_datetime('short', 'none', locale=locale)  }}">
                            {{ company.termpaymentMoneyTransfertAcceptDate | format_datetime('short', 'none', locale=locale) }}
                            {%  else %}
                        <td>
                            N/A
                            {%  endif %}
                        </td>
                        <td class="text-right col-action">
                            <a class="action acceptTermPayment" data-id="{{ company.id }}">
                            <span title="{{ 'back.company.list.termpayment_moneytransfert_accept'|trans }}">
                                <svg class="Icon" >
                                    <use xlink:href="#icon-buyer-tick-black"></use>
                                </svg>
                            </span>
                            </a>
                            <a class="action" href="#">
                                <span id="buttonShowModal{{ company.id }}" name="buttonShowModal" data-company-id={{ company.id }} title="{{ 'back.company.list.termpayment_moneytransfert_deny'|trans }}">
                                    <svg class="Icon" >
                                        <use xlink:href="#icon-cancel"></use>
                                    </svg>
                                </span>
                            </a>
                        </td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
        <a href="{{ path( 'admin.payment_term.list', {'qualifier' : 'pending'}) }}">{{ 'back.index_page.see_all' | trans }}</a>
    {% else %}
        <div class="d-flex justify-content-left">
            <span>{{ 'back.company.term_payment_empty'| trans }}</span>
        </div>
    {% endif %}


    <div id="modalDenyReason" class="modal Modal" tabindex="-1" role="dialog" aria-labelledby="modalDenyReason" aria-hidden="true" data-backdrop="static">
        <div class="modal-dialog">
            <div class="modal-content Modal-content js-modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" >{{ 'back.company.companyInfo.termpayment.deny'|trans }}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                </div>
                <div class="modal-body">
                    {{ form_start(formModal) }}
                    <div class="form-group">
                        {{ form_row(formModal.reason, {'attr' : {'class' : 'form-control'}}) }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary js-cancel-button" data-dismiss="modal">{{ 'modal.cancel'|trans }}</button>
                    <button id="js-add-lang-button" class="btn btn-info">{{ 'modal.confirm'|trans  }}</button>
                </div>
                {{ form_end(formModal) }}
            </div>
        </div>
    </div>

    <script type="application/javascript">
        document.addEventListener('DOMContentLoaded', function() {

            $(".acceptTermPayment").click(function(){
                $("#id_form_id").val($(this).data('id'));
                $("#acceptTermPayment").submit();
            });

            $("span[name='buttonShowModal']").click(function(e){
                e.preventDefault();
                $("#form_company").val($(this).data('companyId'));
                $("#modalDenyReason").modal("show");
            });
        });
    </script>

{% endblock %}
