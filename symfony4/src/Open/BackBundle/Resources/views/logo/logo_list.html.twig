{% extends '@OpenBack/base.html.twig' %}
{% trans_default_domain "AppBundle" %}

{% block title %}
    {{ 'back.slider.list.header'|trans }}
{% endblock %}

{% block body %}

    {{ form_start(form_change_slider_status , {'attr': {'id': 'changeStatus'}}) }}
    {{ form_widget(form_change_slider_status.id) }}
    {{ form_widget(form_change_slider_status.validate, { 'label': 'back.user.form.activate' | trans, 'attr': {'class': 'btn btn-info', 'style': 'display:none;'} } ) }}
    {{ form_end(form_change_slider_status) }}

    <div class="d-flex" style="margin-bottom: 10px">
        <a href="{{ path('admin.logo.add') }}" class="btn btn-dark">{{ 'back.logo.commons.add' | trans }}</a>
    </div>
    <div class="d-flex justify-content-center">
        <table class="table table-hover">
            <thead>
            <tr>
                <th scope="col">{{ 'back.commons.id'|trans }}</th>
                <th scope="col" style="text-align: center">{{ 'back.slider.commons.order'|trans }}</th>
                <th scope="col">{{ 'back.logo.commons.title'|trans }}</th>
                <th scope="col">{{ 'back.logo.commons.backgroundImage'|trans }}</th>
                <th scope="col">{{ 'back.logo.commons.status' | trans }}</th>
                <th scope="col" class="col-date">{{ knp_pagination_sortable(pagination, 'back.logo.list.createdAt'|trans, 'e.createdAt') }}</th>
                <th scope="col" class="col-date">{{ knp_pagination_sortable(pagination, 'back.logo.list.updatedAt'|trans, 'e.updatedAt') }}</th>
                <th scope="col" class="text-right">{{ 'back.logo.list.actions'|trans }}</th>
            </tr>
            </thead>
            <tbody>
            {# table body #}
            {% for logoItem in pagination %}
                <tr {% if loop.index is odd %}class="color"{% endif %}>
                    <td style="font-weight: bold">{{ logoItem.id }}</td>
                    <td style="text-align: center">
                        {% if logoItem.orderNode is defined and logoItem.orderNode is not null %}
                            {{ logoItem.orderNode }}
                        {% else %}
                            0
                        {% endif %}
                    </td>
                    <td>{{ logoItem.slug}}</td>
                    <td>
                        {% if logoItem.backgroundImage %}
                            <img src="{{ path('front.get.image', {'id': logoItem.backgroundImage.id}) }}" width="100"/>
                        {% else %}
                            N/A
                        {% endif %}
                    </td>
                    <td>{{ ('back.logo.status.'~logoItem.status) | trans }}</td>
                    <td class="col-date">{{ logoItem.createdAt|format_datetime('short', 'none', locale=locale) }}</td>
                    <td class="col-date">{{ logoItem.updatedAt|format_datetime('short', 'none', locale=locale) }}</td>

                    <td class="text-right col-action">
                        {% if logoItem.status == 'published' %}
                            <a class="action changeSliderStatus" data-id="{{ logoItem.id }}" data-status="disable">
                                <span title={{ 'back.logo.commons.lock'|trans }}>
                                    <svg class="Icon" alt={{ 'back.logo.commons.lock'|trans }}>
                                        <use xlink:href="#icon-lock"></use>
                                    </svg>
                                </span>
                            </a>
                        {% else %}
                            <a class="action changeSliderStatus" data-id="{{ logoItem.id }}" data-status="enable">
                                <span title={{ 'back.logo.commons.published'|trans }}>
                                    <svg class="Icon" alt={{ 'back.logo.commons.published'|trans }}>
                                        <use xlink:href="#icon-play"></use>
                                    </svg>
                                </span>
                            </a>
                        {% endif %}
                        <a class="action" href="{{ path('admin.logo.edit', {'id':logoItem.id}) }}">
                            <span title={{ 'back.commons.edit'|trans }}>
                                <svg class="Icon" alt={{ 'back.commons.edit'|trans }}>
                                    <use xlink:href="#pencil"></use>
                                </svg>
                            </span>
                        </a>
                        <a class="action" href="{{ path('admin.logo.delete', {'id':logoItem.id}) }}">
                            <span title={{ 'back.commons.delete'|trans }}>
                                <svg class="Icon" alt={{ 'back.commons.delete'|trans }}>
                                    <use xlink:href="#icon-trash"></use>
                                </svg>
                            </span>
                        </a>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>

    {# display navigation #}
    <div class="d-flex justify-content-left">
        <div class="navigation col-md-10">
            {{ knp_pagination_render(pagination) }}
        </div>
        <div class="col-md-2">Total: {{ pagination.getTotalItemCount }}</div>
    </div>


    <script type="application/javascript">
        document.addEventListener('DOMContentLoaded', function() {

            $(".changeSliderStatus").click(function(){

                var action = null;
                if($(this).data('status') === 'enable'){
                    action = "{{ path('admin.logo.status', {'status': 'enable'} )}}";
                }

                if($(this).data('status') === 'disable'){
                    action = "{{ path('admin.logo.status', {'status': 'disable'} )}}";
                }

                if(action !== null){
                    $('#changeStatus').attr('action', action);
                }

                $("#id_form_id").val($(this).data('id'));
                $("#changeStatus").submit();
            });

            function changeSliderStatus(id, status){

                let action = null;
                if(status === 'enable'){
                    action = "{{ path('admin.logo.status', {'status': 'enable'} )}}";
                }

                if(status === 'disable'){
                    action = "{{ path('admin.logo.status', {'status': 'disable'} )}}";
                }

                if(action !== null){
                    $('#changeStatus').attr('action', action);
                }

                $("#id_form_id").val(id);
                $("#changeStatus").submit();
            }
        });
    </script>

{% endblock %}

