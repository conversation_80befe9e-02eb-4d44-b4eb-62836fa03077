{% extends '@OpenBack/base.html.twig' %}
{% trans_default_domain "AppBundle" %}

{% block title %}
    {{ 'back.notification.list.header'|trans }}
{% endblock %}

{% block body %}

    <div class="justify-content-center">
        <table class="table table-hover">
            <thead>
            <tr>
                <th scope="col">{{ 'back.commons.id'|trans }}</th>
                {# sorting of properties based on query components #}
                <th scope="col">{{ 'back.notification.list.slug'|trans }}</th>
                <th scope="col">{{ 'back.notification.list.title'|trans }}</th>
                <th scope="col" class="col-date">{{ knp_pagination_sortable(pagination, 'back.notification.list.creation'|trans, 'e.createdAt') }}</th>
                <th scope="col" class="col-lang">{{ 'back.notification.list.lang'|trans }}</th>
                <th scope="col" class="text-right">{{ 'back.commons.actions'|trans }}</th>
            </tr>
            </thead>
            <tbody>
            {# table body #}
            {% for notification_full in pagination %}
                <tr {% if loop.index is odd %}class="color"{% endif %}>
                    {% set notification = notification_full[0] %}
                    <th scope="row">{{ notification.id }}</th>
                    <td>{{ notification.slug}}</td>
                    <td>{{ notification.title}}</td>

                    <td class="col-date">{{ notification.createdAt|format_datetime('short', 'none', locale=locale) }}</td>

                    <td class="col-lang">
                        {% if notification.hasLanguage('en') %}
                            <img src="{{ asset('/images/blank.gif') }}" class="flag flag-en" alt="flag" />
                        {% endif %}
                        {% if notification.hasLanguage('fr') %}
                            <img src="{{ asset('/images/blank.gif') }}" class="flag flag-fr" alt="flag" />
                        {% endif %}
                        {% if notification.hasLanguage('es') %}
                            <img src="{{ asset('/images/blank.gif') }}" class="flag flag-es" alt="flag" />
                        {% endif %}
                        {% if notification.hasLanguage('de') %}
                            <img src="{{ asset('/images/blank.gif') }}" class="flag flag-de" alt="flag" />
                        {% endif %}
                        {% if notification.hasLanguage('it') %}
                            <img src="{{ asset('/images/blank.gif') }}" class="flag flag-it" alt="flag" />
                        {% endif %}
                        {% if notification.hasLanguage('nl') %}
                            <img src="{{ asset('/images/blank.gif') }}" class="flag flag-nl" alt="flag" />
                        {% endif %}
                    </td>

                    <td class="text-right col-action">
                         {% if notification_full.active is defined and notification_full.active %}
                            <a class="action" href="{{ path('admin.notification.switchTemplateActivation', {'id':notification.id}) }}">
                                <span title="{{ 'back.notification.list.disable'|trans }}">
                                    <svg class="Icon">
                                        <use xlink:href="#icon-buyer-tick-green"></use>
                                    </svg>
                                </span>
                            </a>
                         {% else %}
                            <a class="action" href="{{ path('admin.notification.switchTemplateActivation', {'id':notification.id}) }}">
                                <span title="{{ 'back.notification.list.enable'|trans }}">
                                    <svg class="Icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 352 512">
                                        <path fill="#dc3545" d="M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z"></path>
                                    </svg>
                                </span>
                            </a>
                         {% endif %}
                        <a class="action" href="{{ path('admin.notification.edit', {'id':notification.id}) }}">
                            <span title={{ 'back.commons.edit'|trans }}>
                                <svg class="Icon" alt={{ 'back.commons.edit'|trans }}>
                                    <use xlink:href="#pencil"></use>
                                </svg>
                            </span>
                        </a>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>
    {# display navigation #}
    <div class="d-flex justify-content-left">
        <div class="navigation col-md-10">
            {{ knp_pagination_render(pagination) }}
        </div>
        <div class="col-md-2">Total: {{ pagination.getTotalItemCount }}</div>
    </div>

{% endblock %}
