{% extends '@OpenBack/base.html.twig' %}
{% trans_default_domain "AppBundle" %}

{% block title %}
    {{ 'back.payloads.list.header' | trans }}
{% endblock %}

{% block body %}
    <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />

    <div class="justify-content-center">
        <table class="table table-hover">
            <thead>
            <tr>
                <th scope="col">{{ 'back.commons.id'|trans }}</th>
                <th scope="col">{{ 'back.payloads.list.identifier' | trans }}</th>
                <th scope="col">{{ 'back.payloads.list.company_name' | trans }}</th>
                <th scope="col">{{ 'back.payloads.list.payload_id' | trans }}</th>
                <th scope="col">{{ 'back.payloads.list.type' | trans }}</th>
                <th scope="col" class="col-date">
                    {{ knp_pagination_sortable(payloads, 'back.payloads.list.creation' | trans, 'e.createdAt') }}
                </th>
                <th scope="col" class="col-lang">{{ 'back.payloads.list.status' | trans }}</th>
                <th scope="col" class="text-right">{{ 'back.commons.actions'|trans }}</th>
            </tr>
            {{ form_start(form) }}
                <th></th>
                <th>{{ form_widget(form.identifier) }}</th>
                <th>{{ form_widget(form.companyName) }}</th>
                <th>{{ form_widget(form.payloadId) }}</th>
                <th>{{ form_widget(form.type, {'attr' : {'class' : 'filter_select'}}) }}</th>
                <th>
                    <div class="mx-3 p-0 d-flex flex-column" style="width: 210px;">
                        {{ form_widget(form.date, {'attr' : {'class' : 'form-control daterange'}}) }}
                    </div>
                </th>
                <th></th>
                <th></th>
                <input type="submit" style="opacity:0; position: fixed; left: -9999px;">
            {{ form_end(form) }}
            </thead>
            <tbody>
            {# table body #}
            {% for payload in payloads %}
                {% include '@OpenBack/payload/payload-content-modal.html.twig' with {identifier: payload.identifier, content: payload.sentContent} %}
                <tr class="{{  loop.index is odd ? 'color' : '' }}">
                    <td>{{ payload.id}}</td>
                    <td>{{ payload.identifier}}</td>
                    <td>{{ payload.userType }}</td>
                    <td>{{ payload.entityId }}</td>
                    <td>{{ ('back.payloads.types.' ~ payload.type) | lower | trans }}</td>
                    <td class="col-date">{{ payload.createdAt|format_datetime('short', 'short', app.request.locale) }}</td>
                    <td>{{ ('back.payloads.statuses.' ~ payload.status) | lower | trans }}</td>
                    <td class="text-center col-action">
                         <span class="action visualisationPage"
                               title="{{ 'back.commons.view'|trans }}"
                               data-toggle="modal"
                               data-target="#modal-{{ payload.identifier }}"
                         >
                            <svg class="Icon pointer">
                                <use xlink:href="#eye"></use>
                            </svg>
                         </span>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>
    {# display navigation #}
    <div class="d-flex justify-content-left">
        <div class="navigation col-md-10">
            {{ knp_pagination_render(payloads) }}
        </div>
        <div class="col-md-2">Total: {{ payloads.getTotalItemCount }}</div>
    </div>
    <script type="application/javascript">
        jQuery(document).ready(function() {

            $(".filter_select").change(function() {
                $("form").submit();
            });
            const $daterange = $(".daterange")

            $daterange.daterangepicker({
                opens: 'left',
                autoUpdateInput: false,
                locale: {
                    format: 'DD/MM/YYYY',
                    cancelLabel: 'Clear'
                }
            });
            $daterange.on('apply.daterangepicker', function(ev, picker) {
                $(this).val(picker.startDate.format('DD-MM-YYYY') + ' - ' + picker.endDate.format('DD-MM-YYYY'));
                $('form').submit();
            });
            $daterange.on('cancel.daterangepicker', function() {
                $daterange.val('');
                $('form').submit();
            });
        });
    </script>
{% endblock %}
