{% extends '@OpenBack/base.html.twig' %}
{% trans_default_domain "AppBundle" %}

{% block title %}
    {{ 'back.slider.list.header'|trans }}
{% endblock %}

{% block body %}

    {{ form_start(form_change_slider_status , {'attr': {'id': 'changeStatus'}}) }}
    {{ form_widget(form_change_slider_status.id) }}
    {{ form_widget(form_change_slider_status.validate, { 'label': 'back.user.form.activate' | trans, 'attr': {'class': 'btn btn-info', 'style': 'display:none;'} } ) }}
    {{ form_end(form_change_slider_status) }}

    <div class="d-flex" style="margin-bottom: 10px">
        <a href="{{ path('admin.slider.add') }}" class="btn btn-dark">{{ 'back.slider.commons.add' | trans }}</a>
    </div>
    <div class="d-flex justify-content-center">
        <table class="table table-hover">
            <thead>
            <tr>
                <th scope="col">{{ 'back.commons.id'|trans }}</th>
                <th scope="col" style="text-align: center">{{ 'back.slider.commons.order'|trans }}</th>
                <th scope="col">{{ 'back.slider.commons.title'|trans }}</th>
                <th scope="col">{{ 'back.slider.commons.link'|trans }}</th>
                <th scope="col">{{ 'back.slider.commons.backgroundImage'|trans }}</th>
                <th scope="col">{{ 'back.slider.commons.status' | trans }}</th>
                <th scope="col" class="col-date">{{ knp_pagination_sortable(pagination, 'back.slider.list.createdAt'|trans, 'e.createdAt') }}</th>
                <th scope="col" class="col-date">{{ knp_pagination_sortable(pagination, 'back.slider.list.updatedAt'|trans, 'e.updatedAt') }}</th>
                <th scope="col" class="col-lang">{{ 'back.slider.list.lang'|trans }}</th>
                <th scope="col" class="text-right">{{ 'back.slider.list.actions'|trans }}</th>
            </tr>
            </thead>
            <tbody>
            {# table body #}
            {% for sliderItem in pagination %}
                <tr {% if loop.index is odd %}class="color"{% endif %}>
                    <td style="font-weight: bold">{{ sliderItem.id }}</td>
                    <td style="text-align: center">
                        {% if sliderItem.orderNode is defined and sliderItem.orderNode is not null %}
                            {{ sliderItem.orderNode }}
                        {% else %}
                            0
                        {% endif %}
                    </td>
                    <td>{{ sliderItem.slug}}</td>
                    <td>
                        {% if sliderItem.getLink != 'N/A' and sliderItem.getLink is not null and sliderItem.getLink is not empty %}
                            {% set link = sliderItem.getLink|trim('/') %}
                            {% if sliderItem.getLinkText and link %}
                                <a target="_blank" href="{{ url('homepage') }}{{ link }}">
                                    {{ url('homepage') }}{{ link }}
                                </a>
                            {% else %}
                                {% if sliderItem.isExternalLinkType %}
                                    {% set link = sliderItem.getLinkExternal %}
                                    <a target="_blank" href="{{ url(link) }}">{{ sliderItem.getLinkText }}</a>
                                {% else %}
                                    {% set link = "#" %}
                                    <a href="{{ link }}">N/A</a>
                                {% endif %}
                            {% endif %}
                        {% else %}
                            {% set link = sliderItem.getLinkExternal %}
                            {% if sliderItem.isExternalLinkType and link %}
                                <a target="_blank" href="{{ url(link) }}">{{ sliderItem.getLinkText }}</a>
                            {% else %}
                                {% set link = "#" %}
                                <a href="{{ link }}">N/A</a>
                            {% endif %}
                        {% endif %}
                    </td>
                    <td>
                        {% if sliderItem.backgroundImage %}
                            <img src="{{ path('front.get.image', {'id': sliderItem.backgroundImage.id}) }}" width="100" height="100" border="1 px black"/>
                        {% else %}
                            N/A
                        {% endif %}
                    </td>
                    <td>{{ ('back.slider.status.'~sliderItem.status) | trans }}</td>
                    <td class="col-date">{{ sliderItem.createdAt|format_datetime('short', 'none', locale=locale) }}</td>
                    <td class="col-date">{{ sliderItem.updatedAt|format_datetime('short', 'none', locale=locale) }}</td>
                    <td class="col-lang">
                        {% if sliderItem.hasLanguage('en') %}
                            <img src="{{ asset('/images/blank.gif') }}" class="flag flag-en" alt="flag" />
                        {% endif %}
                        {% if sliderItem.hasLanguage('fr') %}
                            <img src="{{ asset('/images/blank.gif') }}" class="flag flag-fr" alt="flag" />
                        {% endif %}
                        {% if sliderItem.hasLanguage('es') %}
                            <img src="{{ asset('/images/blank.gif') }}" class="flag flag-es" alt="flag" />
                        {% endif %}
                        {% if sliderItem.hasLanguage('de') %}
                            <img src="{{ asset('/images/blank.gif') }}" class="flag flag-de" alt="flag" />
                        {% endif %}
                        {% if sliderItem.hasLanguage('it') %}
                            <img src="{{ asset('/images/blank.gif') }}" class="flag flag-it" alt="flag" />
                        {% endif %}
                        {% if sliderItem.hasLanguage('nl') %}
                            <img src="{{ asset('/images/blank.gif') }}" class="flag flag-nl" alt="flag" />
                        {% endif %}
                    </td>
                    <td class="text-right col-action">
                        {% if sliderItem.status == 'published' %}
                            <a class="action changeSliderStatus" data-id="{{ sliderItem.id }}" data-status="disable">
                                <span title={{ 'back.slider.commons.lock'|trans }}>
                                    <svg class="Icon" alt={{ 'back.slider.commons.lock'|trans }}>
                                        <use xlink:href="#icon-lock"></use>
                                    </svg>
                                </span>
                            </a>
                        {% else %}
                            <a class="action changeSliderStatus" data-id="{{ sliderItem.id }}" data-status="enable">
                                <span title={{ 'back.slider.commons.published'|trans }}>
                                    <svg class="Icon" alt={{ 'back.slider.commons.published'|trans }}>
                                        <use xlink:href="#icon-play"></use>
                                    </svg>
                                </span>
                            </a>
                        {% endif %}
                        <a class="action" href="{{ path('admin.slider.edit', {'id':sliderItem.id}) }}">
                            <span title={{ 'back.commons.edit'|trans }}>
                                <svg class="Icon" alt={{ 'back.commons.edit'|trans }}>
                                    <use xlink:href="#pencil"></use>
                                </svg>
                            </span>
                        </a>
                        <a class="action" href="{{ path('admin.slider.delete', {'id':sliderItem.id}) }}">
                            <span title={{ 'back.commons.delete'|trans }}>
                                <svg class="Icon" alt={{ 'back.commons.delete'|trans }}>
                                    <use xlink:href="#icon-trash"></use>
                                </svg>
                            </span>
                        </a>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>

    {# display navigation #}
    <div class="d-flex justify-content-left">
        <div class="navigation col-md-10">
            {{ knp_pagination_render(pagination) }}
        </div>
        <div class="col-md-2">Total: {{ pagination.getTotalItemCount }}</div>
    </div>


    <script type="application/javascript">
        document.addEventListener('DOMContentLoaded', function() {

            $(".changeSliderStatus").click(function(){

                var action = null;
                if($(this).data('status') === 'enable'){
                    action = "{{ path('admin.slider.status', {'status': 'enable'} )}}";
                }

                if($(this).data('status') === 'disable'){
                    action = "{{ path('admin.slider.status', {'status': 'disable'} )}}";
                }

                if(action !== null){
                    $('#changeStatus').attr('action', action);
                }

                $("#id_form_id").val($(this).data('id'));
                $("#changeStatus").submit();
            });

            function changeSliderStatus(id, status){

                let action = null;
                if(status === 'enable'){
                    action = "{{ path('admin.slider.status', {'status': 'enable'} )}}";
                }

                if(status === 'disable'){
                    action = "{{ path('admin.slider.status', {'status': 'disable'} )}}";
                }

                if(action !== null){
                    $('#changeStatus').attr('action', action);
                }

                $("#id_form_id").val(id);
                $("#changeStatus").submit();
            }
        });
    </script>

{% endblock %}

