{% extends '@OpenBack/base.html.twig' %}
{% trans_default_domain "AppBundle" %}
{% form_theme form 'bootstrap_4_layout.html.twig' %}

{% block body %}
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.7.1/css/bootstrap-datepicker.css" rel="stylesheet" type="text/css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.7.1/js/bootstrap-datepicker.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.7.1/locales/bootstrap-datepicker.fr.min.js"></script>

    <div>
        <h4>{{ 'back.user.filter.filter_title' | trans }}</h4>
    </div>

    <div class="form-container company-filter">

        {{ form_start(form, {'class': 'myclass', 'attr': {'class': 'form-inline'}}) }}


        <div class="input-group col-md-4">
            {{ form_label(form.creationMin) }}
            {{ form_errors(form.creationMin) }}
            {{ form_widget(form.creationMin, {'attr' : {'class' : 'resetable_input col-md-3 datepicker'}}) }}

            {{ form_label(form.creationMax) }}
            {{ form_errors(form.creationMax) }}
            {{ form_widget(form.creationMax, {'attr' : {'class' : 'resetable_input col-md-3 datepicker'}}) }}
        </div>
        <div class="input-group col-md-4">
            {{ form_label(form.connectionMin) }}
            {{ form_errors(form.connectionMin) }}
            {{ form_widget(form.connectionMin, {'attr' : {'class' : 'resetable_input col-md-3 datepicker'}}) }}

            {{ form_label(form.connectionMax) }}
            {{ form_errors(form.connectionMax) }}
            {{ form_widget(form.connectionMax, {'attr' : {'class' : 'resetable_input col-md-3 datepicker'}}) }}
        </div>

        <div class="input-group col-md-3">
            {{ form_widget(form.save, {'attr' : {'class' : 'btn-info'}}) }}
            <button id="clearForm" class="btn btn-outline-dark" type="button">{{ 'back.user.filter.filter_clear' | trans }}</button>
        </div>
    </div>

<div class="d-flex export-btn">
    <a href="{{ path('admin.user.export', {'qualifier': qualifier_val}) }}" class="btn btn-sm btn-primary btn-dark" role="button" aria-pressed="true">{{ 'back.commons.export_csv'|trans }}</a>

    <a href="{{ path('admin.user.add') }}" class="btn btn-sm btn-primary btn-dark" role="button" aria-pressed="true" style="margin-left: 20px;">{{ 'user.form.add'|trans }}</a>
</div>

<div class="d-flex justify-content-center">
    <table class="table table-hover">
        <thead>
        <tr>
            {# sorting of properties based on query components #}
            <th scope="col">{{ knp_pagination_sortable(pagination, 'back.commons.id'|trans, 'e.id') }}</th>
            <th scope="col">{{ knp_pagination_sortable(pagination, 'back.user.list.creation'|trans, 'e.createdAt') }}</th>
            <th scope="col">{{ knp_pagination_sortable(pagination, 'back.user.list.email'|trans, 'e.email') }}</th>
            <th scope="col">{{ knp_pagination_sortable(pagination, 'back.user.list.firstname'|trans, 'e.firstname') }}</th>
            <th scope="col">{{ knp_pagination_sortable(pagination, 'back.user.list.lastname'|trans, 'e.lastname') }}</th>
            <th scope="col">{{ knp_pagination_sortable(pagination, 'back.user.list.role'|trans, 'e.roles') }}</th>
            <th scope="col">{{ knp_pagination_sortable(pagination, 'back.user.list.company'|trans, 'c.name') }}</th>
            <th scope="col">{{ knp_pagination_sortable(pagination, 'back.user.list.lastLogin'|trans, 'e.lastLogin') }}</th>
            <th scope="col">{{ knp_pagination_sortable(pagination, 'back.user.list.status'|trans, 'e.enabled') }}</th>
            <th scope="col">{{ 'back.commons.actions'|trans }}</th>
        </tr>
            {{ form_start(form) }}
            {#{{ form_widget(form.save) }}#}
            {#{%  do  form.save.setRendered %}#}
            <th>{{ form_widget(form.id, {'attr': {'style': 'width:50px'}}) }}</th>
            <th></th>
            <th>{{ form_widget(form.email) }}</th>
            <th>{{ form_widget(form.firstname, {'attr': {'style': 'width:150px'}})  }}</th>
            <th>{{ form_widget(form.lastname, {'attr': {'style': 'width:150px'}}) }}</th>
            <th>{{ form_widget(form.role, {'attr' : {'class' : 'filter_select'}})  }}</th> <!-- role -->
            <th>{{ form_widget(form.company) }}</th>
            <th></th>
            <th>{{ form_widget(form.status, {'attr' : {'class' : 'filter_select'}})  }}</th>
            <input type="submit" style="opacity:0; position: fixed; left: -9999px;"></input>
            {{ form_end(form) }}
        </thead>
        <tbody>
        {# table body #}
        {% for user in pagination %}
            <tr {% if loop.index is odd %}class="color"{% endif %}>
                <th scope="row">{{ user.id }}</th>
                <td>{{ user.createdAt|format_datetime('short', 'none', locale=locale) }}</td>
                <td>{{ user.email}}</td>
                <td>{{ user.firstname}}</td>
                <td>{{ user.lastname}}</td>
                <td>{{ ('back.user.role.secondary.' ~ (user.roles | first)) | trans }}</td>
                <td>{{ user.company }}</td>
                <td>
                    {% if user.lastLogin %}
                        {{ user.lastLogin|format_datetime('short', 'none', locale=locale) }}
                    {% endif %}
                </td>
                <td>
                    {% if user.enabled %}
                        {{ 'back.user.list.activeStatus'|trans }}
                    {% else %}
                        {{ 'back.user.list.inactiveStatus'|trans }}
                    {% endif %}
                </td>
                <td class="text-center">
                    <a class="action" href="{{ path('admin.user.info', {'id':user.id}) }}">
                        <span title="{{ 'back.commons.edit'|trans }}">
                            <svg class="Icon">
                                <use xlink:href="#eye" />
                            </svg>
                        </span>
                    </a>
                    <a class="action" href="{{ path('front.company.info', {'_piggy':user.email}) }}">
                        <span title="{{ 'back.commons.piggy'|trans }}">
                            <svg class="Icon">
                                <use xlink:href="#icon-users" />
                            </svg>
                        </span>
                    </a>
                </td>
            </tr>
        {% endfor %}
        </tbody>
    </table>
</div>
{# display navigation #}
    <div class="d-flex justify-content-left">
        <div class="navigation col-md-10">
            {{ knp_pagination_render(pagination) }}
        </div>
        <div class="col-md-2">Total: {{ pagination.getTotalItemCount }}</div>
    </div>

    <script type="application/javascript">
        document.addEventListener('DOMContentLoaded', function() {
            $(".filter_select").change(function() {
                $("form").submit();
            });
            $("#clearForm").click(function() {
                $(".resetable_input").val("");
                $('form[name="filter_admin"]').submit();
            });
        });

    </script>


{% endblock %}
