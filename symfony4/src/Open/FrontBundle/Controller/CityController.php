<?php
/**
 * Created by Php<PERSON>torm.
 * User: AQU04740
 * Date: 27/03/2017
 * Time: 10:17
 */

namespace Open\FrontBundle\Controller;

use AppBundle\Entity\ZipCode;
use AppBundle\Repository\CityRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Doctrine\Persistence\ManagerRegistry;

/**
 * City controller.
 */
#[\Symfony\Component\Routing\Attribute\Route(path: '/city')]
class CityController extends AbstractController
{
    protected ManagerRegistry $doctrine;

    public function __construct(ManagerRegistry $doctrine)
    {
        $this->doctrine = $doctrine;
    }
    /**
     * @param Request $request
     * @return Response|null
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/autocomplete', name: 'city_autocomplete', methods: ['POST'])]
    public function autocompleteAction(Request $request)
    {
        $content = $request->getContent();
        $data = json_decode($content, true);
        $term = $data['term'];
        $country = $data['country'];

        $em = $this->doctrine->getManager();

        /** @var CityRepository $cityRepository */
        $cityRepository = $em->getRepository(ZipCode::class);
        $entities = $cityRepository->findByCityLike($term, $country);


        return $this->render('@OpenFront/city/json.html.twig', ['results' => $entities]);
    }

    /**
     * @param null $name
     * @return Response
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/get/{name}', name: 'city_get')]
    public function getCityAction($name = null)
    {
        /** @var CityRepository $cityRepository */
        $cityRepository = $this->doctrine->getRepository(ZipCode::class);
        $city = $cityRepository->findByCity($name);
        if ($city && $city[0]) {
            return new Response($city[0]->getCity());
        }
        return new Response();


    }
}
