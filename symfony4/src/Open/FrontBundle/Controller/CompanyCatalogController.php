<?php

namespace Open\FrontBundle\Controller;

use App<PERSON><PERSON>le\Controller\MkoController;
use AppBundle\Entity\Company;
use AppBundle\Entity\User;
use AppBundle\Services\CompanyCatalogService;
use AppBundle\Services\MessengerProgressionService;
use Open\FrontBundle\Form\CompanyCatalogForm;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Contracts\Translation\TranslatorInterface;

class CompanyCatalogController  extends MkoController
{
    /**
     * @param Request $request
     * @param TranslatorInterface $translator
     * @param CompanyCatalogService $companyCatalogService
     * @return mixed
     * @throws \Doctrine\ORM\ORMException
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company_catalog', name: 'front.company_catalog')]
    public function indexAction(
        Request $request,
        TranslatorInterface $translator,
        CompanyCatalogService $companyCatalogService,
        MessengerProgressionService $messengerProgressionService
    )
    {
        $companyCatalog = $this->createForm(CompanyCatalogForm::class);
        $companyCatalog->handleRequest($request);
        $user = $this->getUser();
        if (! $user instanceof User){
            Throw new \Exception("User can not be null");
        }
        $company = $user->getCompany();
        if(!$company instanceof Company){
            Throw new \Exception("Company can not be null");
        }
        $messageInProgress = $messengerProgressionService->hasMessageInProgress(MessengerProgressionService::CATALOG_MESSENGER,$company->getId());
        if($companyCatalog->isSubmitted() && $companyCatalog->isValid() && !$messageInProgress) {

            /** @var File $file */
            $file = $companyCatalog->get('upload')->getData();
            $extension = $file->guessExtension();
            if($extension == 'txt' || $extension == 'csv' || $extension == null) {
                $companyCatalogService->importCompanyCatalogFromCsvFile($this->getUser()->getCompany(), $file->getPathname());
            } else {
                $this->addFlash(
                    'error',
                    $translator->trans('company_catalog.wrong_file_format', [], self::TRANSLATION_DOMAIN)
                );
            }
            $companyCatalog = $this->createForm(CompanyCatalogForm::class);
        }

        return $this->render('@OpenFront/company_catalog/index.html.twig', [
            'formUpload' => $companyCatalog->createView(),
            'hasCatalog' => $companyCatalogService->hasData(),
            'matchingReferences' => $companyCatalogService->totalMatchingReferences(),
            'mismatchingReferences' => $companyCatalogService->totalMismatchingReferences(),
            'importInProgress' => $messageInProgress,
        ]);
    }

    /**
     * @param CompanyCatalogService $companyCatalogService
     * @return mixed
     * @throws \Unirest\Exception
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company_catalog/delete', name: 'front.company_catalog.delete')]
    public function deleteCatalogAction(CompanyCatalogService $companyCatalogService)
    {
        $companyCatalogService->delete();
        return $this->redirectToRoute('front.company_catalog');
    }

    /**
     * @param CompanyCatalogService $companyCatalogService
     * @return StreamedResponse
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company_catalog/export', name: 'front.company_catalog.export')]
    public function exportCatalogAction(CompanyCatalogService $companyCatalogService)
    {
        return new StreamedResponse(
            [$companyCatalogService, 'export'],
            Response::HTTP_OK,
            [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="catalog.csv"',
            ]
        );
    }

    /**
     * @param CompanyCatalogService $companyCatalogService
     * @return StreamedResponse
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company_catalog/export/mismatching', name: 'front.company_catalog.export.mismatching')]
    public function exportMismatchingCatalogAction(CompanyCatalogService $companyCatalogService)
    {
        return new StreamedResponse(
            [$companyCatalogService, 'exportMismatching'],
            Response::HTTP_OK,
            [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="mismatching-catalog.csv"',
            ]
        );
    }

    /**
     * @param CompanyCatalogService $companyCatalogService
     * @return StreamedResponse
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company_catalog/export/matching', name: 'front.company_catalog.export.matching')]
    public function exportMatchingCatalogAction(CompanyCatalogService $companyCatalogService)
    {
        return new StreamedResponse(
            [$companyCatalogService, 'exportMatching'],
            Response::HTTP_OK,
            [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="matching-catalog.csv"',
            ]
        );
    }


    /**
     * @param MessengerProgressionService $messengerProgressionService
     * @return JsonResponse
     * @throws \Unirest\Exception
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company_catalog/import_checker', name: 'front.company_catalog.import_checker')]
    public function importCheckerAction(MessengerProgressionService $messengerProgressionService)
    {
        $user = $this->getUser();
        if (! $user instanceof User){
            Throw new \Exception("User can not be null");
        }
        $company = $user->getCompany();
        if(!$company instanceof Company){
            Throw new \Exception("Company can not be null");
        }
        $progression = $messengerProgressionService->getProgression(MessengerProgressionService::CATALOG_MESSENGER, $company->getId());
        $totalImported = 0;
        if($progression !== null){
            $totalImported = $progression[MessengerProgressionService::CURRENT_ITEM];
        }
        $status = ($messengerProgressionService->hasMessageInProgress(MessengerProgressionService::CATALOG_MESSENGER, $company->getId())) ? 'running' : 'finished';

        return new JsonResponse(
            (new class($totalImported, $status){
                public $totalImported;
                public $status;

                public function __construct(int $totalImported, string $status)
                {
                    $this->totalImported = $totalImported;
                    $this->status = $status;
                }
            })
        );
    }
}
