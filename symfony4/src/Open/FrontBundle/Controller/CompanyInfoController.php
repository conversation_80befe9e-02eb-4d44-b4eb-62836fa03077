<?php

namespace Open\FrontBundle\Controller;

use AppBundle\Controller\MkoController;
use AppBundle\Entity\Address;
use AppBundle\Entity\CheckableZipCodeTrait;
use AppBundle\Entity\Company;
use AppBundle\Entity\Document;
use AppBundle\Entity\Node;
use AppBundle\Entity\NodeContent;
use AppBundle\Entity\User;
use AppBundle\Form\CompanyInfoForm;
use AppBundle\Repository\AddressRepository;
use AppBundle\Repository\NodeContentRepository;
use AppBundle\Repository\NodeRepository;
use AppBundle\Services\MailService;
use AppBundle\Services\SecurityService;
use AppBundle\Services\SiteService;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\Session;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Contracts\Translation\TranslatorInterface;

class CompanyInfoController extends MkoController
{

    use CheckableZipCodeTrait;

    const TRANSLATION_DOMAIN = 'AppBundle';
    const GLOBALE = "global";
    const FILES = "files";
    const NOT_AUTHORIZED = "you are not authorized to perform this action";

    /**
     * @param Request $request
     * @param TranslatorInterface $translator
     * @param SecurityService $securityService
     * @param MailService $mailService
     * @param SessionInterface $session
     * @param RouterInterface $router
     * @param SiteService $siteService
     * @param LoggerInterface $logger
     * @return Response|null
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/info', name: 'front.company.info')]
    public function editAction(
        Request $request,
        TranslatorInterface $translator,
        SecurityService $securityService,
        MailService $mailService,
        SessionInterface $session,
        RouterInterface $router,
        SiteService $siteService,
        LoggerInterface $logger
    )
    {

        /** @var User $user */
        $user = $this->getUser();
        $company= $user->getCompany();
        $em = $this->doctrine->getManager();

        // overwrite registration success flash message if exists for buyer registration only
        if( !$session instanceof Session){
            return null;
        }

        // delete billingAddress if same than main address
        if ($this->getCompany()->getBillingAddress() === $this->getCompany()->getMainAddress()) {
            $this->getCompany()->setBillingAddress(null);
            $em->flush();
        }

        $flashBag = $session->getFlashBag();
        if ($flashBag->has('success')) {
            // find and replace buyer registration complete message
            $successMessages = $flashBag->get('success');
            $messageToReplace = $translator->trans('registration.flash.user_created',[], 'FOSUserBundle');
            $messageReplacement = $translator->trans('company.form.info.company_registration', [], self::TRANSLATION_DOMAIN);
            $successMessages = array_map(
                function(string $message) use ($messageToReplace, $messageReplacement) : string{
                    if ($message === $messageToReplace) {
                        return $messageReplacement;
                    }

                    return $message;
                }, $successMessages
            );
            $flashBag->set('success', $successMessages);
        }

        if (!$company) {
            return $this->redirectToRoute('fos_user_profile_show');
        }

        $cguContent = null;
        $slug = $_ENV['CGU_SLUG'];
        /** @var NodeRepository $nodeRepository */
        $nodeRepository = $em->getRepository(Node::class);
        /** @var Node $node */
        $node = $nodeRepository->findBySlug($slug);
        if($node != null){
            /** @var NodeContentRepository $nodeContentRepository */
            $nodeContentRepository = $em->getRepository(NodeContent::class);
            $cguContent = $nodeContentRepository->findPageBySlugAndLanguage($slug, $request->getLocale());
        }

        $options = array(
            'method' => 'patch'
        );

        $disabled = false;
        $options['disabled'] = false;

        // Role autre que responsable de compte => lecture seule
        if (!$this->isGranted(self::ROLE_BUYER_ADMIN)) {
            $options['disabled'] = true;
            $disabled = true;
        }

        $form = $this->createForm(
            CompanyInfoForm::class,
            $company,
            $options
        );

        $cgu = $company->getCgu();

      $this->setCheck($form,$company->getBillingAddress(), "billingAddress");


        if ($disabled) {
            $form->remove('save');
        }

        $databaseCompanyMainAddress = null;
        $databaseCompanyBillingAddress = null;
        if ($this->getCompany() !== null) {
            if ($this->getCompany()->getMainAddress() !== null) {
                $databaseCompanyMainAddress = clone $this->getCompany()->getMainAddress();
            }
            if ($this->getCompany()->getBillingAddress() !== null) {
                $databaseCompanyBillingAddress = clone $this->getCompany()->getBillingAddress();
            }
        }
        $databaseCompany = clone $this->getCompany()->getMainAddress();
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $error[] = $this->checkZipCode($form, 'mainAddress', $form->get('mainAddress')->get('country')->getData()->getCode());
            if($form->get("billingAddress")->get('check')->getData()===true){
                $error[] = $this->checkZipCode($form, 'billingAddress',$form->get('mainAddress')->get('country')->getData()->getCode());
            }
            if(!in_array(true,$error)) {

                /**
                 * security checks
                 */
                if ($this->getUser()->getCompany()->getId() !== $company->getId()){
                    throw $this->createAccessDeniedException($translator->trans(self::ACTION_DENIED_EXCEPTION));
                }

                // Role autre que responsable de compte => lecture seule
                if (!$this->isGranted(self::ROLE_BUYER_ADMIN)) {
                    throw $this->createAccessDeniedException($translator->trans(self::ACTION_DENIED_EXCEPTION));
                }

                /** @var Company $company */
                $company = $form->getData();
                if (!$company->getCgu()) {
                    $company->setCgu($cgu);
                }
                $company = $this->cleanCompanyAddress($form, $company);

                if ($company->getBillingAddress() && $company->getMainAddress()) {
                    $company->getBillingAddress()->setCountry($company->getMainAddress()->getCountry());
                }

                if ($databaseCompanyBillingAddress !== null) {
                    if ($company->getBillingAddress() !== null) {
                        if (!$databaseCompanyBillingAddress->isEqual($company->getBillingAddress())) {
                            if ($company->getBillingAddress()->getIzbergAddressId() !== null) {
                                $oldAddress = $company->getBillingAddress();
                                $address = new Address();
                                $address->createFromAddress($company->getBillingAddress());
                                $oldAddress->createFromAddress($databaseCompanyBillingAddress);
                                $company->setBillingAddress($address);
                            }
                        }
                    } else if ($company->getMainAddress() !== null) {
                        $this->rewriteMainAdress($databaseCompanyMainAddress, $company);
                    }
                } else if ($databaseCompanyMainAddress !== null) {
                    if ($company->getMainAddress() !== null) {
                        $this->rewriteMainAdress($databaseCompanyMainAddress, $company);
                    }
                }

                // Address info must not be updated and set to null
                // also we put back the good info using the clone of the database before the update
                if ($databaseCompanyBillingAddress !== null && $company->getBillingAddress() === null) {
                    /** @var AddressRepository $addressRepository */
                    $addressRepository = $this->doctrine->getManager()->getRepository(Address::class);
                    /** @var Address $addressToReset */
                    $addressToReset = $addressRepository->find($databaseCompanyBillingAddress->getId());
                    $addressToReset->createFromAddress($databaseCompanyBillingAddress);
                }

                $logger
                    ->info(
                        $translator->trans('log.company.save'),
                        LogUtil::buildContext([
                            LogUtil::EVENT_NAME => EventNameEnum::COMPANY_SAVE,
                            LogUtil::USER_NAME => $user->getUsername(),
                            'id' => $company->getId(),
                            'status' => $company->getStatus()
                        ])
                    );
                try {
                  $em->merge($company);
                  $em->flush();

                  $session->getFlashBag()->clear();
                  $this->addFlash('success', $translator->trans('company.form.update.success',array(), self::TRANSLATION_DOMAIN));

                  if (!$company->canAccessSites()){
                    if (!$company->getCgu()){
                      $url = $router->generate('front.company.cgu');
                      $this->addFlash('error', $translator->trans('company.form.info.cgu_not_accepted',array('%link%'=>$url), self::TRANSLATION_DOMAIN));
                    } else {
                        $siteService->ensureHasAtLeastOneSiteAttached($company);

                        // On boarding STEP 2 done
                        // send email to operator
                        $operators = $securityService->getOperators();
                        foreach ($operators as $operator) {
                            /**
                             * @var User $operator
                             */
                            $template = MailService::COMPANY_PENDING_VALIDATION;
                            if ($operator->isEnabled()) {
                                $mailService->sendEmailMessage(
                                    $template,
                                    $operator->getLocale(),
                                    $operator->getEmail(),
                                    [
                                        'companyName' => $company->getName(),
                                        'companyId' => $company->getId(),
                                        'firstName' => $user->getFirstname(),
                                        'lastName' => $user->getLastname(),
                                        'email' => $user->getEmail(),
                                        'link' => $this->container->get('router')->generate('admin.company.generalInfo',['id'=>$company->getId()],UrlGeneratorInterface::ABSOLUTE_URL)
                                    ],
                                    null,
                                    null);
                            }
                        }
                    }
                    return $this->redirectToRoute('front.company.info');
                  }
                } catch (\Exception $e) {
                    $this->addFlash('error', $translator->trans('company.form.update.error',array(), self::TRANSLATION_DOMAIN));
                }
            }
        }

        return $this->render(
            '@OpenFront/company/company_info.html.twig',
            array(
              'form' => $form->createView(),
              'company' => $company,
              'tab_active' => 2,
              'isDisabled' => $disabled,
              'cgu' => $company->getCgu(),
              'cguContent' => $cguContent,
              'sizeMax' => Document::SIZE_CONSTRAINT,
              'typeError' => $translator->trans('document.upload.mime', array('%contrainte%' => implode(", ", Document::MIME_CONSTRAINT)), self::TRANSLATION_DOMAIN),
              'sizeError' => $translator->trans('document.upload.size', array('%contrainte%' => Document::SIZE_CONSTRAINT / (1024 * 1024)), self::TRANSLATION_DOMAIN)
            ));
    }

    private function setCheck ($form, $contact, string $contactId)
    {
        if ($contact != null) {
            $form->get($contactId)->get('check')->setData(true);
        }
    }

    /**
     * @param Company $company
     * @return Company
     */
    private function cleanCompanyAddress($form, $company)
    {
        if ($form->get("billingAddress")->get('check')->getData() === false) {
            $company->setBillingAddress(null);
        }
        return $company;
    }

    private function rewriteMainAdress(Address $databaseCompanyMainAddress, Company $company): void
    {
        if (!$databaseCompanyMainAddress->isEqual($company->getMainAddress())) {
            if ($company->getMainAddress()->getIzbergAddressId() !== null) {
                $oldAddress = $company->getMainAddress();
                $address = new Address();
                $address->createFromAddress($company->getMainAddress());
                $oldAddress->createFromAddress($databaseCompanyMainAddress);
                $company->setMainAddress($address);
            }
        }
    }
}
