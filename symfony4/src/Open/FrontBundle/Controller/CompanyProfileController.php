<?php

namespace Open\FrontBundle\Controller;

use App<PERSON><PERSON>le\Controller\MkoController;
use AppBundle\Entity\User;
use AppBundle\Form\CompanyProfileForm;
use AppBundle\Services\LanguageService;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

class CompanyProfileController extends MkoController
{

    const TRANSLATION_DOMAIN = 'AppBundle';
    const GLOBALE = "global";
    const FILES = "files";
    const NOT_AUTHORIZED = "you are not authorized to perform this action";

    /**
     * @param Request $request
     * @param TranslatorInterface $translator
     * @param LanguageService $languageService
     * @param LoggerInterface $logger
     * @return mixed
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/profile', name: 'front.company.profile')]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/profile', name: 'fos_user_profile_show')]
    public function editAction(Request $request, TranslatorInterface $translator, LanguageService $languageService, LoggerInterface $logger)
    {
        $user = $this->getUser();

        $mail = $user->getEmail();

        // After a reset password for an operator or an admin => redirect to admin
        if($user->hasRole('ROLE_OPERATOR') || $user->hasRole('ROLE_SUPER_ADMIN')){
            return $this->redirectToRoute('admin.dashboard');
        }

        $company = $this->getCompany();
        $em = $this->doctrine->getManager();

        $options = [
            'validation_groups' =>['Default'],
            'method' => 'patch',
            'languageService' => $languageService,
        ];


        $form = $this->createForm(
            CompanyProfileForm::class,
            $user,
            $options
        );


        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            /** @var User $user */
            $user = $form->getData();

            $user->setEmail($mail);
            $user->setEmailCanonical($mail);
            $logger
                ->info(
                    $translator->trans('log.profile.save'),
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::PROFILE_SAVE,
                        LogUtil::USER_NAME => $user->getUsername(),
                        'id' => $user->getId()
                    ])
                );

            try {
                $em->merge($user);
                $em->flush();
                $this->addFlash('success', $translator->trans('profile.form.update.success', array(), 'AppBundle'));
                return $this->redirectToRoute('front.company.profile');
            } catch (\Exception $e) {
                $this->addFlash('error', $translator->trans('company.form.update.error', array(), 'AppBundle'));
            }

        }

        return $this->render(
            '@OpenFront/company/company_profile.html.twig',
            array(
                'form' => $form->createView(),
                'company_id' => $user->getId(),
                'company'=> $company,
                'tab_active' => 1
            ));
    }
}
