<?php

namespace Open\FrontBundle\Controller;

use AppBundle\Controller\MkoController;
use AppB<PERSON>le\Entity\User;
use AppBundle\Model\Cart\Cart;
use AppBundle\Model\Cart\CartItem;
use AppBundle\Model\Cart\CartMerchant;
use AppBundle\Model\DetailedOffer;
use AppBundle\Services\CartService;
use AppBundle\Services\ComparisonSheetService;
use AppBundle\Services\OfferService;
use AppBundle\Services\SecurityService;
use AppBundle\Services\SpecificPriceService;
use Mpdf\Config\ConfigVariables;
use Mpdf\Config\FontVariables;
use Mpdf\Mpdf;
use Open\IzbergBundle\Api\ApiException;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

class ComparisonSheetController extends MkoController
{
    private const QUERY_PARAM_ITEM_ID = 'itemId';

    /**
     * @param Request $request
     * @param int $offerId
     * @param TranslatorInterface $translator
     * @param ComparisonSheetService $comparisonSheetService
     * @param int $comparisonSheetMaxItem
     * @return RedirectResponse
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/comparisonSheet/add/{offerId}', name: 'comparisonSheet.add')]
    public function addToComparisonSheetAction(
        Request $request,
        int $offerId,
        TranslatorInterface $translator,
        ComparisonSheetService $comparisonSheetService,
        int $comparisonSheetMaxItem
    )
    {
        $responseOptions = [];
        $this->denyAccessUnlessGranted('IS_AUTHENTICATED_REMEMBERED', null, 'Unable to access this page');
        $currency = $request->query->get('currency');

        $user = $this->getUser();
        $resultAddOfferToComparisonSheet = $comparisonSheetService->addOfferToComparisonSheet($user, $offerId, $currency);

        if ($resultAddOfferToComparisonSheet === 0 ) {
            $this->addFlash('success', $translator->trans('comparaisonSheet.add.success', [], self::TRANSLATION_DOMAIN));
        }

        if($resultAddOfferToComparisonSheet === 1){
            $this->addFlash('error', $translator->trans('comparaisonSheet.add.maxItemError',['%maxItem%' => $comparisonSheetMaxItem], self::TRANSLATION_DOMAIN));
        }

        if($resultAddOfferToComparisonSheet === 2) {
            $this->addFlash('error', $translator->trans('comparaisonSheet.add.itemAlreadyexist',[], self::TRANSLATION_DOMAIN));
        }

        if($resultAddOfferToComparisonSheet === 3){
            $this->addFlash('error', $translator->trans('comparaisonSheet.add.error', [], self::TRANSLATION_DOMAIN));
        }

        if($resultAddOfferToComparisonSheet > 0) {
            $responseOptions = ['comparatorError' => true];
        }

        $referer = $request->headers->get('referer', $this->generateUrl('homepage'));
        return new RedirectResponse($referer, 302, $responseOptions);
    }

    /**
     * @param SpecificPriceService $specificPriceService
     * @param SecurityService $securityService
     * @param CartService $cartService
     * @param OfferService $offerService
     * @return Response
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/comparisonSheet/', name: 'comparisonSheet.info')]
    public function getComparisonSheetAction(
        SpecificPriceService $specificPriceService,
        SecurityService $securityService,
        CartService $cartService,
        OfferService $offerService
    )
    {
        $em = $this->doctrine->getManager();
        $this->denyAccessUnlessGranted('IS_AUTHENTICATED_REMEMBERED', null, 'Unable to access this page');

        /**
         * @var User $user;
         */
        $user = $this->getUser();
        $cart = [];
        if($user->getComparisonSheetEURId() != null ) {
            $cart[] = $cartService->findCart($user, $user->getComparisonSheetEURId());
        }

        if($user->getComparisonSheetUSDId() != null) {
            $cart[] = $cartService->findCart($user, $user->getComparisonSheetUSDId());
        }

        $items = $this->buildItems($offerService, $cart);

        //form with submit and comment field
        $form = $this->buildSheetForm();

        if(!$securityService->isAnonymous()){

            // Update the number of items in the user's comparison sheet
            $user->setItemInComparisonSheet(count($items));
            $em->merge($user);
            $em->flush();

            $items = $specificPriceService->injectSpecificPriceComparisonSheet($this->getUser(), $items);
        }

        return $this->render('@OpenFront/comparison_sheet/comparison_sheet.html.twig', [
            'cart' => $items,
            'form' => $form->createView()
        ]);
    }

    private function buildItems(OfferService $offerService, array $cart)
    {
        $items = [];
        // Ajout des seuils et prix spécifiques pour les afficher dans le comparateur
        /** @var Cart $ca */
        foreach($cart as $ca){
            /** @var CartMerchant $merchant */
            foreach ($ca->getMerchants() as $merchant){
                /** @var CartItem $cartItem */

                foreach($merchant->getItems() as $cartItem) {
                    $it = [];
                    /**
                     * @var DetailedOffer $offer
                     */
                    $offer = $offerService->findDetailedOfferById($cartItem->getOfferId(), $this->getUser()->getCompany());

                    $it['limited'] = $offer->getOffer()->isLimited();

                    if(!$offer->getOffer()->isLimited()){
                        $it['prices'] = $offer->getOffer()->getPrices();
                        $it['unitPrice'] = $cartItem->getUnitPrice();
                        $it['thresholds'] = $offer->getOffer()->getThresholds();
                    }

                    $it['manufacturer'] = $offer->getOffer()->getManufacturerName();
                    $it['manufacturer_reference']= $offer->getOffer()->getManufacturerRef();
                    $it['merchant'] = $merchant->getName();
                    $it['merchant_reference'] = $offer->getOffer()->getSellerRef();
                    $it['incoterm_country'] = $offer->getOffer()->getIncotermCountry();
                    $it['merchant_id'] = $offer->getOffer()->getMerchant()->getId();
                    $it['incoterm'] = $offer->getOffer()->getIncoterm();
                    $it['offer_id'] = $offer->getOffer()->getIzbergReference();
                    $it['name'] = $offer->getOffer()->getOfferTitle();
                    $it['id'] = $cartItem->getId();
                    $it['image_url'] = $cartItem->getImageUrl();
                    $it['currency'] = $ca->getCurrency();
                    $it['product_id'] = $offer->getOffer()->getProductId();
                    $it['delivery_time'] =  $offer->getOffer()->getDeliveryTime();
                    $it['frame_contract'] =  $offer->getOffer()->getFrameContract();
                    $it['priceValidityDate'] =  $offer->getOffer()->getPriceValidityDate();
                    $it['realStock'] =  $offer->getOffer()->getRealStock();
                    $it['warrantyPeriod'] =  $offer->getOffer()->getWarrantyPeriod();

                    $items[] = $it;
                }
            }
        }

        return $items;
    }

    /**
     * @param Request $request
     * @param CartService $cartService
     * @return Response
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/comparisonSheet/remove', name: 'comparisonSheet.remove_item', methods: ['DELETE'])]
    public function removeItemFromCart(
        Request $request,
        CartService $cartService
    ) {

        $this->denyAccessUnlessGranted('IS_AUTHENTICATED_REMEMBERED', null, 'Unable to access this page');

        /**
         * @var \AppBundle\Entity\User $user
         */
        $user = $this->getUser();
        $em = $this->doctrine->getManager();

        try {
            if ($request->isXmlHttpRequest() && $request->request->has(self::QUERY_PARAM_ITEM_ID)) {
                $cartItemId = $request->request->get(self::QUERY_PARAM_ITEM_ID);
                $cartService->removeItem($cartItemId);
                $user->setItemInComparisonSheet($user->getItemInComparisonSheet() -1);
                $em->merge($user);
                $em->flush();
            }
        }
        catch(ApiException $e) {
            return new Response('KO', 500);
        }

        return new Response('', 200);
    }

    /**
     * @param Request $request
     * @param SpecificPriceService $specificPriceService
     * @param SecurityService $securityService
     * @param CartService $cartService
     * @param OfferService $offerService
     * @return RedirectResponse
     * @throws \Mpdf\MpdfException
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/comparisonSheet/export/', name: 'comparisonSheet.export', methods: ['POST'])]
    public function exportComparisonSheetAction(
        Request $request,
        SpecificPriceService $specificPriceService,
        SecurityService $securityService,
        CartService $cartService,
        OfferService $offerService
    ){

        $this->denyAccessUnlessGranted('IS_AUTHENTICATED_REMEMBERED', null, 'Unable to access this page');

        $form = $this->buildSheetForm();
        $form->handleRequest($request);

        $comment = $form->getData()["comment"];

        /**
         * @var User $user;
         */
        $user = $this->getUser();
        $cart = [];
        if($user->getComparisonSheetEURId() != null ) {
            $cart[] = $cartService->findCart($user, $user->getComparisonSheetEURId());
        }

        if($user->getComparisonSheetUSDId() != null) {
            $cart[] = $cartService->findCart($user, $user->getComparisonSheetUSDId());
        }

        $items = $this->buildItems($offerService, $cart);

        if(!$securityService->isAnonymous()){
            $items = $specificPriceService->injectSpecificPriceComparisonSheet($this->getUser(), $items);
        }

        $html =  $this->renderView('@OpenFront/comparison_sheet/comparison_sheet_pdf.html.twig', [
            'cart' => $items,
            'user' => $user,
            'comment' => $comment
        ]);

        $defaultConfig = (new ConfigVariables())->getDefaults();
        $fontDirs = $defaultConfig['fontDir'];

        $defaultFontConfig = (new FontVariables())->getDefaults();
        $fontData = $defaultFontConfig['fontdata'];


        $customFontDir = $this->getParameter('kernel.project_dir').'/public/fonts/Open_Sans/';

        $mpdf = new Mpdf([
            'default_font_size' => 8,
            'fontDir' => array_merge($fontDirs, [
                $customFontDir,
            ]),
            'fontdata' => $fontData + [
                    'OpenSans' => [
                        'R' => 'OpenSans-Regular.ttf',
                        'I' => 'OpenSans-Italic.ttf',
                    ]
                ],
            'default_font' => 'OpenSans',
            'margin_top' => 0,
            'margin_right' => 0,
            'margin_bottom' => 0,
            'margin_left' => 0,
            'tempDir' => '/tmp',
        ]);
        $mpdf->WriteHTML($html);
        $mpdf->output('comparisonSheet.pdf', 'D');

        return $this->redirectToRoute('comparisonSheet.info');
    }

    /**
     * @return \Symfony\Component\Form\FormInterface
     */
    private function buildSheetForm(){
        //form with submit + comment
        $options = array('translation_domain' => 'AppBundle');
        return $this->createFormBuilder(null, $options)
            ->setAction($this->generateUrl('comparisonSheet.export'))
            ->setMethod('POST')
            ->add('comment', TextareaType::class, array(
                'attr' => array(
                    "class" => "SheetComment",
                    "maxlength" => "500"
                ),
                'label' => false
            ))
            ->add('submit', SubmitType::class, array(
                "attr" => array (
                    "value" => "submit",
                    'class' => "Button"
                ),
                'label' => 'comparaisonSheet.export'
            ))
            ->getForm();

    }
}
