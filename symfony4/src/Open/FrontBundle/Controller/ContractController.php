<?php

namespace Open\FrontBundle\Controller;

use AppBundle\Controller\MkoController;
use AppBundle\Entity\Node;
use AppBundle\Entity\NodeContent;
use Doctrine\ORM\EntityManager;
use Open\FrontBundle\Form\ContractForm;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Contracts\Translation\TranslatorInterface;

class ContractController extends MkoController
{

    const TRANSLATION_DOMAIN = 'AppBundle';
    const GLOBALE = "global";
    const FILES = "files";
    const NOT_AUTHORIZED = "you are not authorized to perform this action";

    /**
     * @param Request $request
     *
     *
     * @param TranslatorInterface $translator
     * @return RedirectResponse|Response|null
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/cgu', name: 'front.company.cgu')]
    public function getAction(Request $request, TranslatorInterface $translator)
    {
        $company= $this->getCompany();

        /** @var EntityManager $em */
        $em = $this->doctrine->getManager();

        $content = null;

        $slug = $_ENV['CGU_SLUG'];

       /** @var Node $node */
		$node = $em->getRepository(Node::class)->findBySlug($slug);
        if($node != null){
            $content = $em->getRepository(NodeContent::class)->findPageBySlugAndLanguage($slug, $request->getLocale());
        }

        // If CGU already accepted then just show the page
        if ($company->getCgu()) {
			return $this->render('@OpenFront/company/cguAccepted.html.twig',
                [
                    'content' => $content,
                    'customShowMenu' => true,
                    'company' => $company,
                ]);
		}


        $em = $this->doctrine->getManager();

        $options = array();

        $form = $this->createForm(
            ContractForm::class,
            $company,
            $options
        );

        $form->handleRequest($request);

        if ($form->isSubmitted()) {

            $this->logger->info(
                "cgs has been accepted",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::CGU_ACCEPTED,
                    LogUtil::USER_NAME => $this->getUsername(),
                    'CompanyId' => $company->getId(),
                    'user' => $this->getUsername(),
                ])
            );

            $company->setCgu(true);
            try {
                $em->persist($company);
                $em->flush();
                return $this->redirectToRoute('front.company.info');
            } catch (\Exception $e) {
                $this->addFlash('error', $translator->trans('cgu.error',array(), self::TRANSLATION_DOMAIN));

            }
        }

        //render list of users
        return $this->render('@OpenFront/company/cguForm.html.twig',
            array(
                'form' => $form->createView(),
                'company' => $company,
                'tab_active' => 4,
                'content' => $content
            ));
    }

    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/contract/accept', name: 'front.company.contract.accept')]
    public function editAction(Request $request)
    {

        $company= $this->getCompany();

        //render list of users
        return $this->render('@OpenFront/companyDocument/contract.html.twig',
            array(
                'company' => $company,
                'tab_active' => 4
            ));
    }
}
