<?php

namespace Open\FrontBundle\Controller;

use AppBundle\Controller\MkoController;
use AppBundle\Services\CreditNoteService;
use Symfony\Component\Routing\Annotation\Route;

class CreditNoteController extends MkoController
{
    /**
     * @param                   $creditNoteId
     * @param CreditNoteService $creditNoteService
     * @return \Symfony\Component\HttpFoundation\RedirectResponse
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/credit-note/{creditNoteId}/pdf', name: 'front.credit_note.pdf')]
    public function creditNoteUrl($creditNoteId, CreditNoteService $creditNoteService)
    {
        return $this->redirect($creditNoteService->fetchCreditNotePdfUrl($creditNoteId));
    }
}
