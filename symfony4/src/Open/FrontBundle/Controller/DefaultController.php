<?php

namespace Open\FrontBundle\Controller;

use AppBundle\Entity\Node;

use AppBundle\Entity\NodeContent;
use AppBundle\Entity\Redirect;
use AppBundle\Exception\InvalidSettingException;
use AppBundle\Services\BafvService;
use AppBundle\Services\OfferService;
use AppBundle\Services\SecurityService;
use AppBundle\Services\SpecificPriceService;
use AppBundle\Services\UserBddService;
use Doctrine\ORM\EntityManager;
use AppBundle\Controller\MkoController;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ManagerRegistry;
use Open\FrontBundle\Form\ContactMerchantToAskPriceForm;
use Open\IzbergBundle\Api\ApiException;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

class DefaultController extends MkoController
{

    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager, ManagerRegistry $doctrine)
    {
        parent::__construct($doctrine);
        $this->entityManager = $entityManager;
    }

    /**
     * @param Request $request
     * @param OfferService $offerService
     * @param SecurityService $securityService
     * @param SpecificPriceService $specificPriceService
     *
     * @param TranslatorInterface $translator
     * @param BafvService $bafvService
     *
     * @return Response|null
     * @throws InvalidSettingException
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/', name: 'homepage')]
    public function indexAction(
        Request $request,
        OfferService $offerService,
        SecurityService $securityService,
        SpecificPriceService $specificPriceService,
        TranslatorInterface $translator,
        BafvService $bafvService
    )
    {
        $popularOffers = $offerService->getPopularOffersIds();
        $offers = [];
        if (count($popularOffers) > 0) {
            foreach ($popularOffers as $popularOffer) {
                try {
                    $offer = $offerService->findOfferById($popularOffer);
                    if($offer){
                        $offers[] = $offer;
                    }
                }catch(ApiException $e){
                    $this->logger->error(
                        "unable to display best offer with id ".$popularOffer." because it doesn't exist",
                        LogUtil::buildContext([
                            LogUtil::EVENT_NAME => EventNameEnum::TECHNICAL_ERROR,
                        ])
                    );
                }
            }
        }

        if(!$securityService->isAnonymous() && !$securityService->isAdmin($this->getUser())) {
            $offers = $specificPriceService->bulkUpdateOfferSpecificPrices($this->getUser()->getCompany(), $offers);
        }

        $locale = $request->getLocale();

        /** @var EntityManager $em */
        $em = $this->entityManager;
        $nodes = $em->getRepository(Node::class)->findSlidesHomepage();

        $logos = $em->getRepository(Node::class)->findLogosHomepage();
        $logosSlides = [];

        /** @var Node $logo */
        foreach ($logos as $logo) {
            if($img = $logo->getBackgroundImage()) {
                $url = '';
                if($content = $logo->getContent($locale)) {
                    $url = $content->getLinkExternal();
                }
                $logosSlides[] = ['img' => $img->getId(), 'url' => $url];
            }
        }

        $slides = [];

        /** @var Node $node */
        foreach ($nodes as $node) {
            $content = $node->getContent($locale);
            if ($content !== null) {
                $title = ($node->getTitleVisible()) ? $content->getTitle() : '';
                $date=new \DateTime("now");
                $today=$date->format('Y-m-d');
                if ($node->getBackgroundImage()){
                    if ($node->getPublishedAt() !== null and $node->getEndedAt() !== null) {
                        if ($node->getPublishedAt()->format('Y-m-d') <= $today and $node->getEndedAt()->format('Y-m-d') >= $today) {
                            $isExternal = false;

                            if ($content->isExternalLinkType()) {
                                $external=$content->getLinkExternal();
                                $url = trim($external, '/');
                                $isExternal = true;
                            }
                            else {
                                if ($content->getLink() !== null) {
                                    $url = '/' . trim($this->generateUrl('homepage') . $content->getLink(), '/');
                                }
                                else {
                                    $url = $this->generateUrl('homepage');
                                }
                            }

                            $slides[] =
                                [
                                    'image' => $node->getBackgroundImage()->getId(),
                                    'title' => $title,
                                    'text' => $content->getBody(),
                                    'url' => $url,
                                    'isExternal' => $isExternal,
                                    'button' => ['name' => $content->getLinkText()]
                                ];
                        }
                    }
                }
            }
        }

        // default slides
        $emptySlides = false;
        if (empty($slides)) {
            $emptySlides = true;
            $slides = array(
                array(
                    'image' => 'slide-1.jpg',
                    'title' => '',
                    'text' => '',
                    'url' => '/',
                    'isExternal' => false
                )
            );
        }

        $user = $this->getUser();

        $bafv = $bafvService->fetchBafvRequestDetails($user, $offers);

        $contactMerchantToAskPriceForm = $this->createForm(ContactMerchantToAskPriceForm::class, null, [
                'translator' =>$translator,
            ])->createView();

        $contactMerchantToAskPriceForNoPriceOfferForm = $this->createForm(ContactMerchantToAskPriceForm::class, null, [
            'translator' =>$translator,
            //'content' => $translator->trans('offer_detail.ask_vendor_no_price_offer_message_content', [], MkoController::TRANSLATION_DOMAIN),
            'content' => str_replace(array('\r\n', '\r', '\n'), "\n", $translator->trans('offer_detail.ask_vendor_no_price_offer_message_content', [], MkoController::TRANSLATION_DOMAIN)),
        ])->createView();

        return $this->render('@OpenFront/default/index.html.twig',
			[
			    'empty_slides' => $emptySlides,
			    'slides' => $slides,
                'offers' => $offers,
                'contactMerchantToAskPriceForm' => $contactMerchantToAskPriceForm,
                'contactMerchantToAskPriceForNoPriceOfferForm' => $contactMerchantToAskPriceForNoPriceOfferForm,
                'bafv' => $bafv,
                'is_homepage' => true,
                'logos' => $logosSlides
			]
		);
    }


	/**
	 * @param $template
	 * @return string
	 */
	private function getTemplateName($template)
	{
		$tpl = '@OpenFront/content/';

		switch ($template) {
			case "fullwidthproducts":
				$tpl .= "full-width-with-products";
				break;
			case "fullwidthproductsfaq":
				$tpl .= "full-width-with-products-and-faq";
				break;
			case "fullwidthfaq":
				$tpl .= "full-width-with-faq";
				break;
			case "defaultproducts":
				$tpl .= "default-with-products";
				break;
			case "defaultproductsfaq":
				$tpl .= "default-with-products-and-faq";
				break;
			case "defaultfaq":
				$tpl .= "default-with-faq";
				break;
			case "fullwidth":
				$tpl .= "full-width";
				break;
			default:
				$tpl .= "default";
				break;
		}

		return $tpl . '.html.twig';
	}


    /**
     * Action that will catch left over from other controllers
     * @param Request $request
     * @param $slug
     * @param TranslatorInterface $translator
     * @return \Symfony\Component\HttpFoundation\RedirectResponse|\Symfony\Component\HttpFoundation\Response
     */
	public function catchAllAction(Request $request, $slug, TranslatorInterface $translator)
	{
		return $this->getNodeContent($request, $slug, $translator);
	}

    /**
     * Action that will catch left over from other controllers
     * @param Request $request
     * @param $slug
     * @param TranslatorInterface $translator
     * @return \Symfony\Component\HttpFoundation\RedirectResponse|\Symfony\Component\HttpFoundation\Response
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/slider/view/{slug}', name: 'slider.view')]
    public function viewStaticPageAction(Request $request, $slug, TranslatorInterface $translator)
    {
        return $this->getNodeContent($request, $slug, $translator);
    }


    private function getNodeContent(Request $request, $slug, TranslatorInterface $translator){
        /** @var EntityManager $em */
        $em = $this->entityManager;

        if(!$this->isGranted('ROLE_OPERATOR') || !$this->isGranted('ROLE_SUPER_ADMIN')){
            // Filter by published nodes
            $em->getFilters()->enable('published_node');
        }

        $lang_filter = $em->getFilters()->enable('node_language');

        $lang_filter->setParameter('lang', $request->getLocale());


        /** @var Node $node */
        // Try to find a node that match the url
        $node = $em->getRepository(Node::class)->findBySlug($slug);

        // If no content matching the slug then throw a 404
        if ($node === null) {
            $redirect = $em->getRepository(Redirect::class)->findByOrigin($slug);

            if ($redirect == null) {
                throw $this->createNotFoundException($translator->trans(MkoController::NOT_FOUND_EXCEPTION). ' ('.$slug.')');
            }

            $destination = $redirect->getDestination();

            $destination = $request->getLocale() .'/' . $destination;

            // if not an external url then prefix with slash
            if (!filter_var($destination, FILTER_VALIDATE_URL)) {
                $destination = '/' . $destination;
            }


            return $this->redirect($destination, $redirect->getType());
        }


        $content = $node->getContent();

        // If content not found in requested language then throw a 404
        if (!$content[0]) {
            throw $this->createNotFoundException($translator->trans(MkoController::NOT_FOUND_EXCEPTION));
        }

        $offers = array();

        // Basic switch for different node types
        switch ($node->getType()) {
            case 'page':

                $tpl = $this->getTemplateName($node->getTemplate());

                $content = $em->getRepository(NodeContent::class)->findPageBySlugAndLanguage($slug, $request->getLocale());
                return $this->render(
                    $tpl,
                    array(
                        'node' => $content,
                        'pageType' => 'static',
                        'offers' => $offers
                    ));
            default:
                throw $this->createNotFoundException($translator->trans(MkoController::NOT_FOUND_EXCEPTION));
        }
    }
}
