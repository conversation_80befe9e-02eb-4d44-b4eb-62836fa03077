<?php

namespace Open\FrontBundle\Controller;

use App<PERSON><PERSON>le\Entity\BddFile;
use AppB<PERSON>le\Repository\BddFileRepository;
use AppBundle\Repository\PurchaseRequestRepository;
use Doctrine\ORM\NonUniqueResultException;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class FileDownloadController extends AbstractController
{
    /**
     * get the attachment of the ticket
     * @param string $token the identifier of the message attachment id
     * @param BddFileRepository $bddFileRepository
     * @return mixed
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/download/{token}', name: 'file.download')]
    public function indexAction(string $token, BddFileRepository $bddFileRepository)
    {
        /** @var BddFile $bddFile */
        $bddFile = $bddFileRepository->findOneBy(['token' => $token]);

        if (!$bddFile) {
            throw $this->createNotFoundException('Unable to find File.');
        }

        $file = stream_get_contents($bddFile->getData());

        return new Response(
            $file,
            200,
            array(
                'Content-Type' => $bddFile->getType(),
                'Content-Disposition' => 'attachment; filename="' . $bddFile->getName() . '"',
                'Content-Length' => $bddFile->getSize()
            )
        );
    }
}
