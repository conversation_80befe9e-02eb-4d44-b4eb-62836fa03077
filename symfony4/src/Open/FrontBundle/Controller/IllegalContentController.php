<?php

namespace Open\FrontBundle\Controller;

use App<PERSON><PERSON>le\Controller\MkoController;
use AppBundle\Entity\User;
use AppBundle\Services\MailService;
use AppBundle\Services\SecurityService;
use Open\FrontBundle\Form\IllegalContentForm;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Contracts\Translation\TranslatorInterface;

class IllegalContentController extends MkoController
{

    /**
     * @param Request $request
     * @param MailService $mailService
     * @param TranslatorInterface $translator
     * @param SecurityService $securityService
     * @param string $domain
     * @return \Symfony\Component\HttpFoundation\Response
     * @throws \AppBundle\Exception\MailException
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/illegal_content/create/', name: 'illegal_content.create')]
    public function createIllegalContentAction(
        Request $request,
        MailService $mailService,
        TranslatorInterface $translator,
        SecurityService $securityService,
        string $domain
    ){

        $form = $this->createForm(IllegalContentForm::class);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $realUrl = $domain;
            if(strpos($form->getData()['url'], $realUrl) !== false){
                /** @var User $operator */
                foreach ($securityService->getOperators() as $operator) {
                    if ($operator->isEnabled()) {
                        $mailService->sendEmailMessage(MailService::ILLEGAL_CONTENT_CREATED, "en", $operator->getEmail(),
                            [
                                MailService::FIRST_NAME_VAR => $operator->getFirstname(),
                                MailService::LAST_NAME_VAR => $operator->getLastname(),
                                "reporterFirstName" => $this->getUser()->getFirstname(),
                                "reporterLastName" => $this->getUser()->getLastname(),
                                "reporterCompanyName" => $this->getUser()->getCompany()->getName(),
                                "content" => $form->getData()['content'],
                                "url" => $form->getData()['url']]);
                    }
                }

                $this
                    ->addFlash(
                        'success',
                        $translator->trans('illegal_content.form.ok', array(), self::TRANSLATION_DOMAIN)
                    );
                return $this->redirect($form->getData()['url']);

            }else{
                $this
                    ->addFlash(
                        'error',
                        $translator->trans('illegal_content.form.ko', array(), self::TRANSLATION_DOMAIN)
                    );
            }
        }

        return $this->render(
            '@OpenFront/illegalContent/illegalContent.html.twig',
            array(
                'form' => $form->createView(),
                'url' => $request->headers->get('referer')
            ));
    }
}
