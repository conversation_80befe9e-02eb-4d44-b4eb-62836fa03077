<?php

namespace Open\FrontBundle\Controller;

use AppBundle\Controller\MkoController;
use AppBundle\Entity\Company;
use AppBundle\Entity\User;
use AppBundle\Repository\InvoiceRepository;
use AppBundle\Serializer\Encoder\CsvEncoder;
use AppBundle\Services\CreditNoteService;
use AppBundle\Services\InvoiceService;
use AppBundle\Services\OrderService;
use DateTimeImmutable;
use Doctrine\Common\Annotations\AnnotationReader;
use Exception;
use Open\FrontBundle\Form\InvoiceForm;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\PropertyInfo\Extractor\PhpDocExtractor;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Serializer\Mapping\Factory\ClassMetadataFactory;
use Symfony\Component\Serializer\Mapping\Loader\AnnotationLoader;
use Symfony\Component\Serializer\Mapping\Loader\AttributeLoader;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Serializer\Normalizer\ArrayDenormalizer;
use Symfony\Component\Serializer\Normalizer\DateTimeNormalizer;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;
use Symfony\Component\Serializer\Serializer;
use Symfony\Contracts\Translation\TranslatorInterface;

class InvoiceController extends MkoController
{
    /**
     * @param $request
     * @return Response
     * @throws Exception
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/invoices', name: 'front.invoice.list')]
    public function invoiceListAction(
        Request $request,
        InvoiceService $invoiceService
    )
    {
        /** @var User $user */
        $user = $this->getUser();

        $invoicesTab = [
            [
                'title' => 'invoice.list.tab.not_paid',
                'type' => 'CUSTOM_LIST',
                'subType' => 'NOT_PAID_INVOICE',
                'route' => 'front.invoice.list',
                'query' => ['activeTab' => 1, 'page' => 1],
            ],
            [
                'title' => 'invoice.list.tab.paid',
                'type' => 'CUSTOM_LIST',
                'subType' => 'PAID_INVOICE',
                'route' => 'front.invoice.list',
                'query' => ['activeTab' => 2, 'page' => 1],
            ],
        ];

        $activeTab = $request->query->getInt('activeTab', 1);
        $activeTab = (in_array($activeTab - 1, array_keys($invoicesTab))) ? $activeTab : 1;
        $activeInvoiceTab = $invoicesTab[$activeTab - 1];

        $page = $request->query->getInt('page', 1);
        $page = ($page <= 0) ? 1 : $page;
        $limit = 10;

        $paymentStatus = 'paid';

        if ($activeInvoiceTab['subType'] === 'NOT_PAID_INVOICE') {
            $paymentStatus = 'not_paid';
        }

        $paginationOptions = [
            'pageParameterName' => 'page',
            'active' => 'page'
        ];

        $search = null;
        $form = $this->createForm(InvoiceForm::class);
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $search = $form->getData()['invoice'];
            $page = 1;
        }

        $invoices = $invoiceService->paginateUserInvoicesByPaymentStatus($user, $paymentStatus, $search, $page, $limit, $paginationOptions);

        $totalToPayEUR = $invoiceService->totalToPayEUR($user);
        $dueDateTotalEUR = $invoiceService->dueDateTotalToPayEUR($user);
        $toPayThisMonthEUR = $invoiceService->toPayThisMonthEUR($user);
        $latePaymentEUR = $invoiceService->latePaymentEUR($user);

        $totalToPayUSD = $invoiceService->totalToPayUSD($user);
        $dueDateTotalUSD = $invoiceService->dueDateTotalToPayEUR($user);
        $toPayThisMonthUSD = $invoiceService->toPayThisMonthUSD($user);
        $latePaymentUSD = $invoiceService->latePaymentUSD($user);

        return $this->render('@OpenFront/invoice/invoices_list.html.twig',
            [
                'invoicesTab' => $invoicesTab,
                'activeInvoiceTab' => $invoicesTab[$activeTab - 1],
                'activeTab' => $activeTab,
                'invoices' => $invoices,
                'totalToPayEUR' => $totalToPayEUR,
                'dueDateTotalEUR' => $dueDateTotalEUR,
                'toPayThisMonthEUR' => $toPayThisMonthEUR,
                'latePaymentEUR' => $latePaymentEUR,
                'totalToPayUSD' => $totalToPayUSD,
                'dueDateTotalUSD' => $dueDateTotalUSD,
                'toPayThisMonthUSD' => $toPayThisMonthUSD,
                'latePaymentUSD' => $latePaymentUSD,
                'customShowMenu' => false,
                'locale' => $request->getLocale(),
                'form' => $form->createView()
            ]
        );
    }

    /**
     * @param $invoiceId
     * @param $hasPdfFile = null
     * @param InvoiceService $invoiceService
     * @return RedirectResponse
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/invoice/{invoiceId}/pdf/{hasPdfFile}', name: 'front.invoice.pdf', defaults: ['hasPdfFile' => false])]
    public function invoiceUrlAction(
        $invoiceId,
        $hasPdfFile,
        InvoiceService $invoiceService
    )
    {
        return $this->redirect($invoiceService->fetchInvoicePdfUrl($invoiceId, $hasPdfFile));
    }

    /**
     * @param $creditNoteId
     * @param CreditNoteService $creditNoteService
     * @return RedirectResponse
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/creditNote/{creditNoteId}/pdf', name: 'front.creditNote.pdf')]
    public function creditNoteUrlAction(
        $creditNoteId,
        CreditNoteService $creditNoteService
    )
    {
        return $this->redirect($creditNoteService->fetchCreditNotePdfUrl($creditNoteId));
    }

    /**
     * @param Request $request
     * @param  $merchantOrderId
     * @param OrderService $orderService
     * @return Response
     * @throws Exception
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/merchantOrder/invoice/{merchantOrderId}/', name: 'front.merchantOrder.invoices')]
    public function getMerchantOrderInvoicesAction(
        Request $request,
        $merchantOrderId,
        OrderService $orderService
    )
    {
        $paymentSummary = $orderService->paymentProcessSummary($merchantOrderId);

        return $this->render('@OpenFront/invoice/invoice_detail.html.twig',
            [
                'locale' => $request->getLocale(),
                'now' => new DateTimeImmutable(),
                'paymentSummary' => $paymentSummary,
            ]
        );
    }

    /**
     * @param Request $request
     * @param TranslatorInterface $translator
     * @param InvoiceRepository $invoiceRepository
     * @return Response
     * @throws Exception
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/invoice/export/', name: 'front.invoice.export')]
    public function exportCompanyInvoiceAction(
        Request $request,
        TranslatorInterface $translator,
        InvoiceRepository $invoiceRepository
    ): Response
    {
        $classMetadataFactory = new ClassMetadataFactory(new AttributeLoader());
        $objectNormalizer = new ObjectNormalizer($classMetadataFactory, null, null, new PhpDocExtractor());
        $objectNormalizer->setIgnoredAttributes(['company']);
        $arrayDeNormalizer = new ArrayDenormalizer();
        $dateTimeNormalizer = new DateTimeNormalizer([DateTimeNormalizer::FORMAT_KEY=>'d/m/Y H:i:s']);
        $csvEncoder = new CsvEncoder();
        $serializer = new Serializer([$arrayDeNormalizer, $dateTimeNormalizer, $objectNormalizer], [$csvEncoder]);

        /**
         * @var Company $company
         */
        $company = $this->getCompany();

        !empty($from = $request->get('dateFrom_submit')) ? $fromDate = new DateTimeImmutable($from) : $fromDate = null;
        !empty($to = $request->get('dateTo_submit')) ? $toDate = new DateTimeImmutable($to) : $toDate = null;

        $invoices = $invoiceRepository->findByCompany($company, $fromDate, $toDate);

        $headers = array_map(
            function (string $translationKey) use ($translator) {
                return $translator->trans($translationKey, [], self::TRANSLATION_DOMAIN);
            },
            [
                'invoice.export.invoice_number',
                'invoice.export.order_number',
                'invoice.export.vendor_name',
                'invoice.export.invoice_creation_date',
                'invoice.export.order_creation_date',
                'invoice.export.invoice_amount',
                'invoice.export.payment_status',
            ]
        );

        $properties = [
            'numberId',
            'numOrder',
            'issuerName',
            'createdOn',
            'orderCreatedOn',
            'totalAmountWithTaxes',
            'paymentStatus'
        ];

        $content = utf8_decode($serializer->serialize(
            $invoices,
            'csv',
            [
                AbstractNormalizer::ATTRIBUTES => $properties,
                CsvEncoder::HEADERS_KEY => $headers,
                CsvEncoder::DELIMITER_KEY => ';',
            ]
        ));

        return new Response($content, 200, array(
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="invoices.csv"'
        ));
    }
}
