<?php

namespace Open\FrontBundle\Controller;

use AppB<PERSON>le\Controller\MkoController;
use AppBundle\Services\KycService;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class MerchantController extends MkoController
{
    /**
     * @param $merchantId
     * @param KycService $kycService
     * @return \Symfony\Component\HttpFoundation\RedirectResponse|Response
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/vendor/{merchantId}/general-sales-condition', name: 'front.merchant.general_sales_condition')]
    public function generalSalesConditionAction($merchantId, KycService $kycService)
    {
        $url = $kycService->fetchMerchantGeneralSalesConditionUrl($merchantId);

        if (!$url) {
            return new Response('', Response::HTTP_NOT_FOUND);
        }

        return $this->redirect($url);
    }
}
