<?php

namespace Open\FrontBundle\Controller;

use App<PERSON><PERSON><PERSON>\Controller\MkoController;
use AppB<PERSON>le\Entity\User;
use AppBundle\Services\CountryService;
use AppBundle\Services\MerchantService;
use <PERSON>deboer\Vatin\Validator;
use FOS\UserBundle\Form\Factory\FactoryInterface;
use FOS\UserBundle\Model\UserManagerInterface;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Symfony\Component\Form\FormError;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

class MerchantRegistrationController extends MkoController
{

    const TRANSLATION_DOMAIN = 'AppBundle';

    /**
     * @Route("/vendor/register", name="front.merchant.registration")
     * @return Response
     */
    public function registerAction()
    {
        // Redirect all merchant registration attempts to buyer registration
        return $this->redirectToRoute('fos_user_registration_register', [], 301);
    }

    /**
     * @Route("/vendor/register/success/", name="front.merchant.registration_success")
     * @return Response
     */
    public function registerSuccessAction()
    {
        // Show success view
        return $this->render(
            '@OpenFront/merchant/register_success.html.twig',
            [
            ]
        );
    }

    /**
     * DISABLED: Merchant registration is now consolidated through buyer registration
     * Original method kept for reference but not routed
     * @param Request $request
     * @param TranslatorInterface $translator
     * @param UserManagerInterface $userManager
     * @param FactoryInterface $registrationFormFactory
     * @param MerchantService $merchantService
     * @param CountryService $countryService
     * @param Validator $vatValidator
     *
     *
     * @return Response|null
     */
    public function registerActionDisabled(
        Request $request,
        TranslatorInterface $translator,
        UserManagerInterface $userManager,
        FactoryInterface $registrationFormFactory,
        MerchantService $merchantService,
        CountryService $countryService,
        Validator $vatValidator
    )
    {
        /**
         * @var User $user
         */
        $user = $userManager->createUser();
        /**
         * @psalm-suppress TooManyArguments
         */
        $form = $registrationFormFactory->createForm(
            [
                'merchant' => true,
                'validation_groups' => ['merchant_registration']
            ]
        );

        $form->setData($user);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $tvaChecked = true;
            $country = $countryService->getCountryById(intval($form->get('country')->getViewData()));
            if ($country !== null && $country->isInEU()){
                $this->logger->info(
                    "check TVA Number validity",
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::TVA_CHECK,
                        LogUtil::USER_NAME => $this->getUsername(),
                        "tva_num" => $form->get("identification")->getViewData()
                    ])
                );

                try {
                    if (!$vatValidator->isValid($form->get("identification")->getViewData(), true)) {
                        $this->logger->error(
                            "Invalid TVA Number",
                            LogUtil::buildContext([
                                LogUtil::EVENT_NAME => EventNameEnum::TVA_CHECK,
                                LogUtil::USER_NAME => $this->getUsername(),
                                "tva_num" => $form->get("identification")->getViewData(),
                            ])
                        );
                        $form->get("identification")->addError(new FormError($translator->trans('form.company.ident_number.unknown', [], 'validators')));
                    }
                }catch (\Exception $e){
                    //we don't want to block the process here: only add a log
                    $this->logger->error(
                        "unable to check TVA Number",
                        LogUtil::buildContext([
                            LogUtil::EVENT_NAME => EventNameEnum::TVA_CHECK,
                            LogUtil::USER_NAME => $this->getUsername(),
                            "tva_num" =>  $form->get("identification")->getViewData(),
                            "reason" => $e->getMessage()
                        ])
                    );

                    $tvaChecked = false;

                }
            }

            if ($form->isValid()) {
                $merchantService->registerMerchant($form->all(), $tvaChecked);

                // Redirect to success page
                return $this->redirectToRoute(
                    'front.merchant.registration_success',
                    []
                );
            }
        }

        return $this->render(
            '@FOSUser/Registration/register.html.twig',
            [
                'form' => $form->createView(),
                'merchant' => true,
            ]
        );
    }
}
