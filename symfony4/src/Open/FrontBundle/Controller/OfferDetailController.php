<?php

namespace Open\FrontBundle\Controller;

use AppBundle\Controller\MkoController;
use AppBundle\Entity\BafvRequest;
use AppBundle\Entity\Company;
use AppBundle\Entity\ThreadParentMessage;
use AppBundle\Entity\User;
use AppBundle\Model\DetailedOffer;
use AppBundle\Model\Offer as OfferModel;
use AppBundle\Repository\BafvRequestRepository;
use AppBundle\Services\AlstomCustomAttributes;
use AppBundle\Services\BafvService;
use AppBundle\Services\CompanyCatalogService;
use AppBundle\Services\KycService;
use AppBundle\Services\MailService;
use AppBundle\Services\MerchantService;
use AppBundle\Services\OfferPDFGenerator;
use AppBundle\Services\OfferService;
use AppBundle\Services\OrderPDFGenerator;
use AppBundle\Services\WishListService;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Mpdf\Output\Destination;
use Open\FrontBundle\Form\ContactMerchantToAskPriceForm;
use Open\IzbergBundle\Api\MerchantApi;
use Open\IzbergBundle\Api\MessageApi;
use Open\LogBundle\Utils\LogUtil;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Constraints\Callback;
use Symfony\Component\Validator\Constraints\Count;
use Symfony\Component\Validator\Constraints\NotBlank;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Open\LogBundle\Utils\EventNameEnum;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class OfferDetailController extends MkoController
{
    const QUERY_PARAM_OFFER_REF = "ref";

    const IN_STOCK = "in_stock";
    const REFILL = "refill";
    const OUT_OF_ORDER = "out_of_order";
    private TranslatorInterface $translator;

    /**
     * @param Request $request
     * @param $ref
     * @param WishListService $wishListService
     * @param KycService $kycService
     * @param OfferService $offerService
     * @param MerchantService $merchantService
     * @param AlstomCustomAttributes $alstomCustomAttributes
     * @param MerchantApi $merchantApi
     * @param CompanyCatalogService $companyCatalogService
     * @param TranslatorInterface $translator
     * @param BafvService $bafvService
     *
     * @return Response
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: 'offer/detail/{ref}', name: 'front.offer.detail.short')]
    #[\Symfony\Component\Routing\Attribute\Route(path: 'offer/detail/{ref}/{productName}', name: 'front.offer.detail')]
    public function offerDetailAction(
        Request $request,
        $ref,
        WishListService $wishListService,
        KycService $kycService,
        OfferService $offerService,
        MerchantService $merchantService,
        AlstomCustomAttributes $alstomCustomAttributes,
        MerchantApi $merchantApi,
        CompanyCatalogService $companyCatalogService,
        TranslatorInterface $translator,
        BafvService $bafvService,
        FormFactoryInterface $formFactory
    )
    {
        $this->translator = $translator;
        /** @var User $user */
        $user = $this->getUser();

        $company = $this->getCompany();
        $offerRef = $ref;

        if (empty($offerRef)) {
            throw $this->createNotFoundException("query parameter ref is mandatory");
        }

        if (!is_numeric($offerRef)) {
            throw $this->createNotFoundException("query parameter ref must be an integer");
        }
        $offerRef = intval($offerRef);


        /** @var DetailedOffer|null $offer */
        $offer = $offerService->findDetailedOfferById($offerRef, $company);

        if (!$offer instanceof DetailedOffer){
            throw $this->createNotFoundException("no offer with id ".$offerRef. " was found");
        }

        if (!$offer->getOffer()->isAllowedForUser($user)) {
            return new Response('', Response::HTTP_FORBIDDEN);
        }

        /**
         * we need to fetch the merchant
         * @var \AppBundle\Model\Merchant $merchant
         */
        $merchant = $merchantService->findMerchantById($offer->getOffer()->getMerchant()->getId());

        $merchantMinimumOrderAmountName = $alstomCustomAttributes->getMinimumOrderAmount();
        $merchantMinimumOrderAmount = $merchantApi->getMerchantCustomAttribute($merchant->getId(), $merchantMinimumOrderAmountName);

        $merchantDescription = array(
            'title' => $merchant->getName(),
            'icon' => $merchant->getLogo(),
            'description' => $merchant->getLongDescription(),
            'minimum_order_amount' => $merchantMinimumOrderAmount
        );

        if($kycService->merchantHasGeneralSalesCondition($merchant)){
            $merchantDescription['cgv'] = $this->generateUrl(
                'front.merchant.general_sales_condition',
                ['merchantId' => $merchant->getId()]
            );
        }

        $informationTab = [
            ['title' => 'product.description', 'type' => 'TEXT', 'data' => $offer->getOffer()->getShortDescription()],
            ['title' => 'product.technical_detail', 'type' => 'GRID', 'data' => $offer->getTechnicalAttributes()],
            ['title' => 'product.logistics_informations', 'type' => 'GRID', 'data' => $offer->getLogisticAttributes()],
            ['title' => 'product.about_seller', 'type' => 'TEXT_AND_IMAGE', 'data' => $merchantDescription],
        ];

        $contactMerchantForm = $this->contactMerchantForm($offer->getOffer());

        $buyerCatalogRef = $companyCatalogService->findBuyerReference($this->getUser(), $offerService->findCatalogReferences($offerRef));
        $buyerCatalogReferenceForm = $this->buyerCatalogReferenceForm($this->getUser(), $offer->getOffer()->getManufacturerRef(), $buyerCatalogRef);

        if ($buyerCatalogReferenceForm) {
            $buyerCatalogReferenceForm->handleRequest($request);

            if($buyerCatalogReferenceForm->isSubmitted() && $buyerCatalogReferenceForm->isValid()) {

                if ($buyerCatalogReferenceForm->get('cancel')->isClicked()) {
                    // reset form data to use buyerReference in the form field
                    $buyerCatalogReferenceForm = $this->buyerCatalogReferenceForm($this->getUser(), $offer->getOffer()->getManufacturerRef(), $buyerCatalogRef);
                }

                if ($buyerCatalogReferenceForm->get('delete')->isClicked()) {
                    $companyCatalogService->deleteBuyerCatalogReference(
                        $this->getUser(),
                        $buyerCatalogReferenceForm->get('manufacturerReference')->getData()
                    );

                    // reset form data to remove previous buyerReference in the form field
                    $buyerCatalogRef = null;
                    $buyerCatalogReferenceForm = $this->buyerCatalogReferenceForm($this->getUser(), $offer->getOffer()->getManufacturerRef(), $buyerCatalogRef);
                }

                if ($buyerCatalogReferenceForm->get('save')->isClicked()) {
                    $buyerCatalogRef = $companyCatalogService->saveBuyerCatalogReference(
                        $this->getUser(),
                        $buyerCatalogReferenceForm->get('manufacturerReference')->getData(),
                        $buyerCatalogReferenceForm->get('buyerReference')->getData()
                    );
                }
            }
        }

        $bafv = $bafvService->fetchBafvRequestDetails($user, [$offer->getOffer()]);

        $contactMerchantToAskPriceForm = $formFactory->createNamed(
            'contact_merchant_to_ask_price_form',
            ContactMerchantToAskPriceForm::class,
            null,
            [
                'merchantId' => $offer->getOffer()->getMerchant()->getId(),
                'merchantName' => $offer->getOffer()->getMerchant()->getName(),
                'offerRef' => 'Message sent from product page: ' . $offer->getOffer()->getOfferTitle() .' under the reference number: '. $offer->getOffer()->getSellerRef(),
                'translator' => $translator,
            ]
        );

        $contactMerchantToAskPriceForNoPriceOfferForm = $formFactory->createNamed(
            'contact_merchant_to_ask_price_for_no_price_offer_form',
            ContactMerchantToAskPriceForm::class,
            null,
            [
                'merchantId' => $offer->getOffer()->getMerchant()->getId(),
                'merchantName' => $offer->getOffer()->getMerchant()->getName(),
                'offerRef' => 'Message sent from product page: ' . $offer->getOffer()->getOfferTitle() .' under the reference number: '. $offer->getOffer()->getSellerRef(),
                'translator' => $translator,
                //'content' => $translator->trans('offer_detail.ask_vendor_no_price_offer_message_content', [], MkoController::TRANSLATION_DOMAIN),
                'content' => str_replace(array('\r\n', '\r', '\n'), "\n", $translator->trans('offer_detail.ask_vendor_no_price_offer_message_content', [], MkoController::TRANSLATION_DOMAIN)),
            ]
        );

        // check for incoterm validity
        $country = 'France';
        try {
            if ($user) {
                $mainAddressCountry = $user->getCompany()->getMainAddress()->getCountry();
                if($mainAddressCountry) {
                    $country = trim($user->getCompany()->getMainAddress()->getCountry()->getIzbFcaCountry());
                }
            }
        } catch(\Exception $e) {
            $this->logger->error(
                "Error while get user fca country: ".$e->getMessage(),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::TECHNICAL_ERROR,
                    LogUtil::USER_NAME => $user,
                    "exception" => $e->getTraceAsString()
                ])
            );
        }

        $offerModel = $offer->getOffer();
        // if incoterm DAP country different than buyer's country
        if($offerModel->getIncoterm() == 'DAP' && $offerModel->getIncotermCountry() != $country) {
            $offerModel->setIncotermValid(false);
        }
        $offer->setOffer($offerModel);
        if (!empty($offer->getOffer()) && !empty($merchantDescription['cgv'])) {
            $offer->getOffer()->cgv = $merchantDescription['cgv'];
        }

        return $this->render(
            '@OpenFront/offer_detail/index.html.twig',
            [
                'offer' => $offer,
                'myCatalogRef' => $buyerCatalogRef,
                'myCatalogReferenceForm' => ($buyerCatalogReferenceForm) ? $buyerCatalogReferenceForm->createView() : null,
                'contactMerchantForm' => $contactMerchantForm->createView(),
                'contactMerchantToAskPriceForm' => $contactMerchantToAskPriceForm->createView(),
                'contactMerchantToAskPriceForNoPriceOfferForm' => $contactMerchantToAskPriceForNoPriceOfferForm->createView(), // todo duplicate this one
                'informationTab' => $informationTab,
                'previousUrl' => $request->query->all()['previousURL'],
                'companyStep' => empty($company) ? null : $company->getStep(),
                'wishLists' => ($user) ? $wishListService->getUserWishList($user->getId(), $offer->getOffer()->getCurrency()) : [],
                'breadcrumb' => $offerService->getBreadcrumb(),
                'bafv' => $bafv
            ]
        );
    }

    private function contactMerchantForm (?OfferModel $offer = null)
    {
        $action = null;
        $offerRef = null;

        if ($offer) {
            $merchantId = $offer->getMerchant()->getId();
            $action = $this->generateUrl('front.contact.merchant', array('merchantId' => $merchantId));
            $offerRef = 'Message sent from product page: ' . $offer->getOfferTitle() .' under the reference number: '. $offer->getSellerRef();
        }
        /** @var User $user */
        $user = $this->getUser();

        $buyer = ($user) ? $user->getCompany()->getName() : 'none';

        $formbuilder = $this->createFormBuilder()
            ->add('object',
                ChoiceType::class,
                array(
                    'translation_domain' => 'AppBundle',
                    'label' => 'contactMerchant.form.object',
                    'choices' => [
                        '' => '',
                        $this->translator->trans('contactMerchant.message.object.technical', ['%buyer%'=>$buyer], MkoController::TRANSLATION_DOMAIN) => 'technical',
                        $this->translator->trans('contactMerchant.message.object.quotation', ['%buyer%'=>$buyer], MkoController::TRANSLATION_DOMAIN) => 'quotation',
                        $this->translator->trans('contactMerchant.message.object.feedback', ['%buyer%'=>$buyer], MkoController::TRANSLATION_DOMAIN) => 'feedback'
                    ],
                    'mapped' => false,
                    'constraints' => [new NotBlank()],
                    'attr' => [
                        'required' => 'required',
                    ],
                )
            )
            ->add('message',
                TextareaType::class,
                array(
                    'label' => 'contactMerchant.form.message',
                    "attr" => array (
                        "class" => "full_width"
                    ),
                    'translation_domain' => self::TRANSLATION_DOMAIN
                )
            )
            ->add('ref', HiddenType::class, ['data' => $offerRef])
            ->add(
                'attachments',
                FileType::class,
                [
                    'multiple' => true,
                    'constraints' => [
                        new Count(['min' => 0, 'max' => 3]),
                        new Callback(['callback' => function($files, ExecutionContextInterface $executionContext) {
                            $sizeLimit = 5 * 1024 * 1024; // in MB
                            /** @var UploadedFile $file */
                            foreach($files as $file) {
                                if ($file->getSize() > $sizeLimit) {
                                    $executionContext->addViolation('');
                                }

                                if (!in_array($file->getMimeType(), ["application/pdf","image/jpeg", "image/gif", "image/png", "image/tiff"])) {
                                    $executionContext->addViolation('');
                                }
                            }
                        }])
                    ]
                ]
            )
            ->add(
                'save',
                SubmitType::class,
                array(
                    "attr" => array (
                        "value" => "save",
                        "class" => "Button button_margin"
                    ),
                    'label' => 'contactMerchant.form.save',
                    'translation_domain' => self::TRANSLATION_DOMAIN
                )
            )
            ->setMethod('POST');

        if ($action) {
            $formbuilder->setAction($action);
        }

        return $formbuilder->getForm();
    }

    private function buyerCatalogReferenceForm(?User $user, string $manufacturerReference, $buyerReference)
    {
        if (!$user || !$user->getCompany() || empty($manufacturerReference)) {
            return null;
        }

        return  $this->createFormBuilder()
            ->add('companyId',
                HiddenType::class,
                [
                    'data' => $user->getCompany()->getId(),
                ]
            )
            ->add('manufacturerReference',
                HiddenType::class,
                [
                    'data' => $manufacturerReference,
                ]
            )
            ->add('buyerReference',
                TextType::class,
                [
                    'label' => 'company_catalog.buyer_reference',
                    'translation_domain' => self::TRANSLATION_DOMAIN,
                    'data' => $buyerReference,
                    'constraints' => [new NotBlank()]
                ]
            )
            ->add(
                'save',
                SubmitType::class,
                array(
                    "attr" => array (
                        "value" => "save",
                        "class" => "Button button_margin"
                    ),
                    'label' => 'company_catalog.save',
                    'translation_domain' => self::TRANSLATION_DOMAIN
                )
            )
            ->add(
                'cancel',
                SubmitType::class,
                array(
                    "attr" => array (
                        "value" => "cancel",
                        "class" => "Button button_margin"
                    ),
                    'label' => 'company_catalog.cancel',
                    'translation_domain' => self::TRANSLATION_DOMAIN
                )
            )
            ->add(
                'delete',
                SubmitType::class,
                array(
                    "attr" => array (
                        "value" => "delete",
                        "class" => "Button button_margin"
                    ),
                    'label' => 'company_catalog.delete_reference',
                    'translation_domain' => self::TRANSLATION_DOMAIN
                )
            )
            ->setMethod('POST')
            ->getForm();
    }

    /**
     * @IsGranted("IS_AUTHENTICATED_REMEMBERED")
     * @param Request             $request
     * @param MailService         $mailService
     * @param TranslatorInterface $translator
     * @param MessageApi          $messageApi
     * @param BafvService         $bafvService
     * @return RedirectResponse
     */
    #[IsGranted(new Expression('is_granted("ROLE_OPERATOR") or is_granted("ROLE_SUPER_ADMIN")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: 'offer/askprice', name: 'front.askprice', methods: ['POST'])]
    public function askPriceAction(
        Request $request,
        MailService $mailService,
        TranslatorInterface  $translator,
        MessageApi $messageApi,
        BafvService $bafvService
    )
    {
        $user  = $this->getUser();

        $form = $this->createForm(
            ContactMerchantToAskPriceForm::class,
            null,
            [
                'translator' =>$translator,
            ]
        );

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $askForPrice = ($form->getData()['askForPrice'] === 'yes');
            $merchantId = $form->getData()['merchantId'];
            $merchantName = $form->getData()['merchantName'];
            $message = $form->getData()['message'];
            if ($askForPrice) {
                /** @var Company $company */
                $company = $user->getCompany();

                $message .= "\n\n".$translator->trans('offer_detail.from_company', array(), self::TRANSLATION_DOMAIN) . $company->getName();
                $message .= "\n\n".$translator->trans('offer_detail.company_code', array(), self::TRANSLATION_DOMAIN) . $company->getIdentification();

                $object =  $translator->trans('offer_detail.ask_vendor', array(), self::TRANSLATION_DOMAIN);

                /** @var BafvRequestRepository $bafvRequestRepository */
                $bafvRequestRepository = $this->doctrine->getRepository(BafvRequest::class);

                if(!$bafvService->hasBafvRequest($user, $merchantId)){
                    $dateFormat = "d/m/Y H:i:s";
                    $createdAt = new \DateTime();
                    $bafvRequest = (new BafvRequest())
                        ->setCompany($company)
                        ->setUser($user)
                        ->setMerchantId($merchantId)
                        ->setMerchantName($merchantName)
                        ->setCreatedAt(new \DateTime());
                    $bafvRequestRepository->save($bafvRequest);

                    $mailService->sendEmailToOperators(
                        MailService::OPERATOR_NEW_BAFV_REQUEST,
                        [
                            'createdAt' => $createdAt->format($dateFormat),
                            'buyerCompanyName' => $company->getName(),
                            'vendorCompanyName' => $merchantName,
                        ]
                    );
                }

            } else {
                $userName = ($user)? $user->getFirstName() .' '. $user->getLastName() : 'anonymous';
                $message .= "\n\n".$userName;
                $message .= "\n".$form->getData()['ref'];
                $object = $form->getData()['object'];
            }

            $response = $messageApi->contactMerchant(
                $object,
                $message,
                $this->getUser()->getCompany()->getIzbergUserId(),
                $merchantId
            );

            $status = $response->getStatusCode();

            if($status == 201){
                // Add success message
                $this
                    ->addFlash(
                        'success',
                        $translator->trans('contactMerchant.form.success', array(), self::TRANSLATION_DOMAIN)
                    );

                return $this->redirect($request->server->get('HTTP_REFERER'));
            }
        }

        // Add error message
        $this
            ->addFlash(
                'error',
                $translator->trans('contactMerchant.form.error', array(), self::TRANSLATION_DOMAIN)
            );

        return $this->redirect($request->server->get('HTTP_REFERER'));
    }


    /**
     * @IsGranted("IS_AUTHENTICATED_REMEMBERED")
     * @param Request $request
     * @param TranslatorInterface $translator
     * @param MessageApi $messageApi
     * @param FormFactoryInterface $formFactory
     * @return RedirectResponse
     */
    #[IsGranted(new Expression('is_granted("ROLE_OPERATOR") or is_granted("ROLE_SUPER_ADMIN")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: 'offer/askprice-nopriceoffer', name: 'front.askprice_for_nopriceoffer', methods: ['POST'])]
    public function askPriceForNoPriceOfferAction(
        Request $request,
        TranslatorInterface  $translator,
        MessageApi $messageApi,
        FormFactoryInterface $formFactory
    ): RedirectResponse {
        $user  = $this->getUser();

        $form = $formFactory->createNamed(
            'contact_merchant_to_ask_price_for_no_price_offer_form',
            ContactMerchantToAskPriceForm::class,
            null,
            [
                'translator' => $translator,
            ]
        );

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {

            $askForPrice = ($form->getData()['askForPrice'] === 'yes');
            $merchantId = $form->getData()['merchantId'];
            $message = $form->getData()['message'];
            $reference = $form->getData()['ref'];

            if ($askForPrice) {
                /** @var Company $company */
                $company = $user->getCompany();

                $message .= "\n\n" . $translator->trans('offer_detail.from_company', array(), self::TRANSLATION_DOMAIN) . $company->getName();
                $message .= "\n\n" . $translator->trans('offer_detail.company_code', array(), self::TRANSLATION_DOMAIN) . $company->getIdentification();

                $object = $translator->trans('offer_detail.ask_vendor_price_reference', array('%reference%' => $reference), self::TRANSLATION_DOMAIN);

                $response = $messageApi->contactMerchant(
                    $object,
                    $message,
                    $this->getUser()->getCompany()->getIzbergUserId(),
                    $merchantId
                );

                $status = $response->getStatusCode();

                if($status == 201){
                    $content = json_decode($response->getBody()->getContents(), true);
                    $izberg_id = $content['id']??'';
                    $this->logger->info("thread_parent_message izberg_id: $izberg_id");

                    if(!empty($izberg_id)) {
                        $threadParentMessageRepository = $this->doctrine->getRepository(ThreadParentMessage::class);
                        $threadParentMessage = new ThreadParentMessage();
                        $threadParentMessage->setIzbergId($izberg_id);
                        $threadParentMessage->setFromEmail($this->getUser()->getId());
                        $threadParentMessageRepository->add($threadParentMessage, true);
                    }

                    // Add success message
                    $this
                        ->addFlash(
                            'success',
                            $translator->trans('contactMerchant.form.success', array(), self::TRANSLATION_DOMAIN)
                        );
                    return $this->redirect($request->server->get('HTTP_REFERER'));
                }
            }
        }

        // Add error message
        $this
            ->addFlash(
                'error',
                $translator->trans('contactMerchant.form.error', array(), self::TRANSLATION_DOMAIN)
            );

        return $this->redirect($request->server->get('HTTP_REFERER'));
    }

    /**
     * @IsGranted("IS_AUTHENTICATED_REMEMBERED")
     * @param Request             $request
     * @param                     $merchantId
     * @param MessageApi          $messageApi
     * @param MerchantService     $merchantService
     * @param TranslatorInterface $translator
     * @param MailService         $mailService
     * @param BafvService          $bafvService
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_OPERATOR") or is_granted("ROLE_SUPER_ADMIN")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: 'offer/contact/vendor/{merchantId}', name: 'front.contact.merchant')]
    public function contactMerchantAction(
        Request $request,
        $merchantId,
        MessageApi $messageApi,
        MerchantService $merchantService,
        TranslatorInterface $translator,
        MailService $mailService,
        BafvService $bafvService
    )
    {
        $this->translator = $translator;
        $form_bafv = $request->request->get('contact_merchant_to_ask_price_form');
        $askForPrice = false;
        if ($form_bafv) {
            $askForPrice = (bool)$form_bafv['askForPrice'];
        }
        $attachments = [];
        $status = null;

        $this->logger->info("thread_parent_message start");

        if($askForPrice){
            /** @var Company $company */
            $user  = $this->getUser();
            $company = $user->getCompany();
            $form_bafv['message'] .= "\n\n".$translator->trans('offer_detail.from_company', array(), self::TRANSLATION_DOMAIN) . $company->getName();
            $form_bafv['message'] .= "\n\n".$translator->trans('offer_detail.company_code', array(), self::TRANSLATION_DOMAIN) . $company->getIdentification();
            $object =  $translator->trans('offer_detail.ask_vendor', array(), self::TRANSLATION_DOMAIN);

            if(!$bafvService->hasBafvRequest($user, $merchantId)){
                $dateFormat = "d/m/Y H:i:s";
                $merchant = $merchantService->findMerchantById($merchantId);
                $createdAt = new \DateTime();
                /** @var BafvRequestRepository $bafvRequestRepository */
                $bafvRequestRepository = $this->doctrine->getRepository(BafvRequest::class);
                $bafvRequest = (new BafvRequest())
                    ->setCompany($company)
                    ->setUser($user)
                    ->setMerchantId($merchantId)
                    ->setMerchantName($merchant->getName())
                    ->setCreatedAt($createdAt->format($dateFormat));
                $bafvRequestRepository->save($bafvRequest);

                $mailService->sendEmailToOperators(
                    MailService::OPERATOR_NEW_BAFV_REQUEST,
                    [
                        'createdAt' => $createdAt->format($dateFormat),
                        'buyerCompanyName' => $company->getName(),
                        'vendorCompanyName' => $merchant->getName(),
                    ]
                );
            }

            $message = $form_bafv['message'];

            $response = $messageApi->contactMerchant(
                $object,
                $message,
                $this->getUser()->getCompany()->getIzbergUserId(),
                $merchantId,
                ...$attachments
            );

        }else{

            $contactMerchantForm = $this->contactMerchantForm();

            $contactMerchantForm->handleRequest($request);
            if ($contactMerchantForm->isSubmitted() && $contactMerchantForm->isValid()) {
                /** @var User $user */
                $user = $this->getUser();
                $buyer = $user->getCompany()->getName();

                $message = sprintf(
                    "%s\n\n%s\n%s",
                    $contactMerchantForm->get('message')->getData(),
                    $buyer,
                    $contactMerchantForm->get('ref')->getData()
                );
                $object = $contactMerchantForm->get('object')->getData();
                $attachments = $contactMerchantForm->get('attachments')->getData();

                $response = $messageApi->contactMerchant(
                    $translator->trans('contactMerchant.message.object.' . $object, ['%buyer%' => $buyer], self::TRANSLATION_DOMAIN),
                    $message,
                    $this->getUser()->getCompany()->getIzbergUserId(),
                    $merchantId,
                    ...$attachments
                );



            }
        }

        $this->logger->info("thread_parent_message status: $status");

        $status = $response->getStatusCode();

        if( $status == 201 ){

            $content = json_decode($response->getBody()->getContents(), true);
            $izberg_id = $content['id']??'';
            $this->logger->info("thread_parent_message izberg_id: $izberg_id");

            if(!empty($izberg_id)) {
                $threadParentMessageRepository = $this->doctrine->getRepository(ThreadParentMessage::class);
                $threadParentMessage = new ThreadParentMessage();
                $threadParentMessage->setIzbergId($izberg_id);
                $threadParentMessage->setFromEmail($this->getUser()->getId());
                $threadParentMessageRepository->add($threadParentMessage, true);
            }

            // Add success message
            $this
                ->addFlash(
                    'success',
                    $translator->trans('contactMerchant.form.success', array(), self::TRANSLATION_DOMAIN)
                );
        } else {
            // add an error message
            $this
                ->addFlash(
                    'error',
                    $translator->trans('contactMerchant.form.error', array(), self::TRANSLATION_DOMAIN)
                );
        }

        return $this->redirect($request->server->get('HTTP_REFERER'));
    }

    /**
     * @param Request $request
     * @param $offerRef
     * @param $quantity
     * @param $total
     * @param OfferService $offerService
     * @param MerchantApi $merchantApi
     * @param OfferPDFGenerator $offerPDFGenerator
     *
     * @return Response
     * @throws NonUniqueResultException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws \Mpdf\MpdfException
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: 'offer/proforma/{offerRef}', name: 'front.offer.proforma')]
    #[\Symfony\Component\Routing\Attribute\Route(path: 'offer/proforma/{offerRef}/{quantity}/{total}', name: 'front.offer.proforma_full')]
    public function offerProformaAction(
        Request $request,
                $offerRef,
        OfferService $offerService,
        MerchantApi $merchantApi,
        OfferPDFGenerator $offerPDFGenerator,
        $quantity = null,
        $total = null
    ): Response {
        $user = $this->getUser();
        $company = $this->getCompany();

        if (empty($offerRef)) {
            throw $this->createNotFoundException("query parameter ref is mandatory");
        }

        if (!is_numeric($offerRef)) {
            throw $this->createNotFoundException("query parameter ref must be an integer");
        }
        $offerRef = intval($offerRef);

        /** @var DetailedOffer|null $offer */
        $offer = $offerService->findDetailedOfferById($offerRef, $company);

        if (!$offer instanceof DetailedOffer){
            throw $this->createNotFoundException("no offer with id ".$offerRef. " was found");
        }

        if (!$offer->getOffer()->isAllowedForUser($user) || $offer->getOffer()->isLimited()) {
            return new Response('', Response::HTTP_FORBIDDEN);
        }

        $merchant = $merchantApi->getMerchant($offer->getOffer()->getMerchant()->getId());
        $customAttr = $merchantApi->getMerchantAllCustomAttributes($offer->getOffer()->getMerchant()->getId());
        $merchantCompany = $merchantApi->getMerchantCompany($offer->getOffer()->getMerchant()->getId());

        $pdf = $offerPDFGenerator->computeOfferProformaPDF($offer, $merchant, $customAttr, $merchantCompany, $company, $quantity, $total, $request->getLocale());
        $pdf->output('proforma-' . $offerRef . '.pdf', Destination::INLINE);

        return $this->redirectToRoute('front.offer.detail.short', ['ref' => $offerRef]);
    }
}
