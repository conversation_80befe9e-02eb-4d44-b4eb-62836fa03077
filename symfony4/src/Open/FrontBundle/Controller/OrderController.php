<?php

namespace Open\FrontBundle\Controller;

use AppBundle\Controller\MkoController;
use AppBundle\Entity\ThreadParentMessage;
use AppBundle\Exception\UnexpectedValueException;
use AppBundle\Form\DisputeForm;
use AppBundle\Model\View\OrderDetail\OrderFactory;
use AppBundle\Services\CartService;
use AppBundle\Services\CartService\Response\Error\OfferError;
use AppBundle\Services\CsvOrderService;
use AppBundle\Services\OfferService;
use AppBundle\Services\OrderPDFGenerator;
use AppBundle\Services\OrderService;
use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Mpdf\Output\Destination;
use Open\FrontBundle\Form\OrderForm;
use Open\FrontBundle\Helpers\CartItemHelper;
use Open\IzbergBundle\Api\MessageApi;
use Open\IzbergBundle\Api\OrderApi;
use Open\IzbergBundle\Api\ProductOfferApi;
use Open\IzbergBundle\Model\MerchantOrder;
use Open\IzbergBundle\Model\OrderItem;
use Open\IzbergBundle\Service\AttributeService;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\Form\SubmitButton;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Contracts\Translation\TranslatorInterface;

class OrderController extends MkoController
{
    /**
     * @param Request $request
     * @param OrderService $orderService
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/orders/', name: 'front.orders.list')]
    public function userOrdersAction(
        Request $request,
        OrderService $orderService
    )
    {
        $page = $request->query->getInt('page', 1);
        $activeTab = $request->query->getInt('activeTab', 1);
        $activeTab = $activeTab ? $activeTab : 1;
        $limit = 10;
        $paginationOptions = [
            'pageParameterName' => 'page',
            'active' => 'page'
        ];
        $statusesTab = [
            1 => [ \AppBundle\Entity\Order::STATUS_RUNNING, \AppBundle\Entity\Order::STATUS_PENDING_CREATION ],
            2 => [ \AppBundle\Entity\Order::STATUS_PAST ],
            3 => [ \AppBundle\Entity\Order::STATUS_CANCELLED ],
        ];
        $statuses = $statusesTab[$activeTab];

        $company = $this->getCompany();
        $user = $this->getUser();

        $search = null;
        $form = $this->createForm(OrderForm::class);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $search = $form->getData()['order'];
            $page = 1;
        }

        $paginatedOrders = $orderService->paginateUserOrderBySearchAndStatus(
            $user,
            $search,
            $statuses,
            $page,
            $limit,
            $paginationOptions
        );

        $ordersTab = [
            [
                'title' => 'orders.list.tab.running',
                'type' => 'CUSTOM_LIST',
                'subType' => 'RUNNING_ORDERS',
                'route' => 'front.orders.list',
                'query' => ['activeTab' => 1, 'page' => 1] + $request->query->all(),
            ],
            [
                'title' => 'orders.list.tab.past',
                'type' => 'CUSTOM_LIST',
                'subType' => 'PAST_ORDERS',
                'route' => 'front.orders.list',
                'query' => ['activeTab' => 2, 'page' => 1] + $request->query->all(),
            ],
            [
                'title' => 'orders.list.tab.cancelled',
                'type' => 'CUSTOM_LIST',
                'subType' => 'CANCELLED_ORDERS',
                'route' => 'front.orders.list',
                'query' => ['activeTab' => 3, 'page' => 1] + $request->query->all(),
            ],
        ];

        return $this->render(
            '@OpenFront/order/orders_list.html.twig',
            [
                'companyId' =>  $company->getId(),
                'ordersTab' => $ordersTab,
                'activeTab' => $activeTab,
                'orders' => $paginatedOrders,
                'customShowMenu' => false,
                'firstExpectedDate' => null,
                'form' => $form->createView()
            ]
        );
    }

    /**
     * @param Request $request
     * @param  $orderId
     * @param OrderService $orderService
     * @param OrderFactory $orderFactory
     * @return Response
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/order/{orderId}', name: 'front.order.detail')]
    public function getOrderAction(
        Request $request,
        $orderId,
        OrderService $orderService,
        OrderFactory $orderFactory,
        AttributeService $attributeService,
        CartService $cartService,
    )
    {
        $user = $this->getUser();
        $izbergOrder = $orderService->fetchOrderById($orderId);

        $order = $orderFactory->build($izbergOrder, $this->getCompany(), $request->getLocale());

        $formValidationNumber = $this->getValidationNumberForm($order->getValidationNumber());
        $formValidationNumber->handleRequest($request);
        if( $formValidationNumber->isSubmitted() && $formValidationNumber->isValid()){
            $validationNumber = $formValidationNumber->get('validationNumber')->getData();
	          $accountingEmail = null;

						$cartId = $izbergOrder->getCart()->getId() ?? $izbergOrder->getArchivedCartId();

						if($cartId) {
							$cartDB = $cartService->getCartDb($izbergOrder->getCart()->getId() ?? $izbergOrder->getArchivedCartId());
							$accountingEmail = $cartDB?->getAccountingEmail();
						}

            /**
             * @var SubmitButton $deleteButton
             */
            $deleteButton = $formValidationNumber->get("delete");

            /**
             * @var SubmitButton $saveButton
             */
            $saveButton = $formValidationNumber->get("save");

            if ($deleteButton->isClicked() || $saveButton->isClicked()) {
                foreach ($order->getMerchantOrders() as $merchantOrderToUpdate) {
                    $attributeService->updateMerchantOrderAttributes(
                        $merchantOrderToUpdate->getId(),
                        [
                            'ZZB-Internal-Buyer-Validation-ID' => $validationNumber,
                            'ZZE_Accountant_Email' => $accountingEmail,
                        ]
                    );
                }
            }
            $orderService->syncSingleOrder($izbergOrder);
            $order->setValidationNumber($validationNumber);
        }

        $documentNames = [];
        foreach ($order->getDocumentFileUploaded() as $attrKey => $fileUrl) {
            $documentNames[$attrKey] = $attributeService->getAttributeNameByKey($attrKey, $request->getLocale());
        }
        $order->setDocumentFileUploadedNames($documentNames);

        // set quantity demand or on stock
        foreach ($order->getMerchantOrders() as $merchant) {
            CartItemHelper::setQuantityDemandOrOnStock($merchant, "order");
        }

        return $this->render('@OpenFront/order/order_detail.html.twig', [
            'order' => $order,
            'companyId' => $this->getCompany()->getId(),
            'locale' => $request->getLocale(),
            'formValidationNumber' => $formValidationNumber->createView()
        ]);
    }


    /**
     * build validation number form
     * @param $validationNumber
     * @return FormInterface
     */
    private function getValidationNumberForm($validationNumber):FormInterface{

        return $this->createFormBuilder()
            ->add('validationNumber', TextType::class,
                array('label' => 'orders.list.block.validation_number_title',
                    'data' => $validationNumber,
                    'translation_domain' => self::TRANSLATION_DOMAIN))
        ->add(
            'save',
            SubmitType::class,
            array(
                "attr" => array (
                    "value" => "save",
                    "class" => "Button button_margin"
                ),
                'label' => 'generic.save',
                'translation_domain' => self::TRANSLATION_DOMAIN
            )
        )
            ->add(
                'cancel',
                SubmitType::class,
                array(
                    "attr" => array (
                        "value" => "cancel",
                        "class" => "Button button_margin"
                    ),
                    'label' => 'generic.cancel',
                    'translation_domain' => self::TRANSLATION_DOMAIN
                )
            )
            ->add(
                'delete',
                SubmitType::class,
                array(
                    "attr" => array (
                        "value" => "delete",
                        "class" => "Button button_margin"
                    ),
                    'label' => 'generic.delete',
                    'translation_domain' => self::TRANSLATION_DOMAIN
                )
            )
            ->setMethod('POST')
            ->addEventListener(FormEvents::PRE_SUBMIT, function(FormEvent $event){
                $data = $event->getData();
                if (array_key_exists('delete', $data)){
                    $data['validationNumber'] = null;
                    $event->setData($data);
                }
            })
            ->getForm();
    }

    /**
     * @param Request $request
     * @param  $orderId
     * @param $companyId
     * @param OrderPDFGenerator $orderPDFGenerator
     * @return Response
     * @throws \Mpdf\MpdfException
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/order/export/{companyId}/{orderId}', name: 'front.order.export')]
    public function exportOrder(
        Request $request,
        $orderId,
        $companyId,
        OrderPDFGenerator $orderPDFGenerator
    )
    {
        $pdf = $orderPDFGenerator->computeOrderPDF($orderId, $companyId, $request->getLocale());
        $pdf->output('order.pdf', Destination::DOWNLOAD);

        return $this->redirectToRoute('front.orders.list');
    }

    /**
     * @param Request $request
     * @param int $merchantOrderId
     * @param OrderPDFGenerator $orderPDFGenerator
     * @return \Symfony\Component\HttpFoundation\RedirectResponse
     * @throws \Mpdf\MpdfException
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/merchant-order/export/{merchantOrderId}', name: 'front.merchant_order.export')]
    public function exportMerchantOrder(
        Request $request,
        int $merchantOrderId,
        OrderPDFGenerator $orderPDFGenerator
    )
    {
        $pdf = $orderPDFGenerator->computeMerchantOrderPDF($merchantOrderId, $request->getLocale());
        $pdf->output('merchant-order.pdf', Destination::DOWNLOAD);

        return $this->redirectToRoute('front.orders.list'); // return to the http referer ? or order details page ?
    }

    /**
     * @param Request $request
     * @param  $orderId
     * @param OrderService $orderService
     * @param TranslatorInterface $translator
     * @param CartService $cartService
     * @return Response
     * @throws \Exception
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/order/newCart/{orderId}', name: 'front.order.newCart')]
    public function orderToNewCart(
        Request $request,
        $orderId,
        OrderService $orderService,
        TranslatorInterface $translator,
        CartService $cartService
    )
    {
        $order = $orderService->findOrder($orderId);
        $serviceResponse = $cartService->addOrderItemsToNewUserCart($order, $this->getUser());

        if ($serviceResponse->hasErrors()) {
            foreach ($serviceResponse->getErrors() as $error) {
                if ($error instanceof OfferError) {

                    if ($error->isErrorStatus()) {
                        $this->addFlash(
                            'error',
                            $translator->trans(
                                'orders.createCart.errorStatus',
                                [ '%reference%'=>$error->getOffer()->getOfferTitle() ],
                                self::TRANSLATION_DOMAIN
                            )
                        );
                    }

                    if ($error->isErrorMoq()) {
                        $this->addFlash(
                            'error',
                            $translator->trans(
                                'orders.createCart.errorMoq',
                                [ '%reference%'=> $error->getOffer()->getOfferTitle() ],
                                self::TRANSLATION_DOMAIN
                            )
                        );
                    }

                    if ($error->isErrorStock()) {
                        $this->addFlash(
                            'error',
                            $translator->trans(
                                'orders.createCart.errorStock',
                                ['%reference%'=>$error->getOffer()->getOfferTitle()],
                                self::TRANSLATION_DOMAIN
                            )
                        );
                    }

                    if ($error->isErrorPrice()) {
                        $this->addFlash(
                            'error',
                            $translator->trans(
                                'orders.createCart.errorPrice',
                                [ '%reference%' => $error->getOffer()->getOfferTitle()],
                                self::TRANSLATION_DOMAIN
                            )
                        );
                    }

                    if ($error->isErrorNoPrice()) {
                        $this->addFlash(
                            'error',
                            $translator->trans(
                                'orders.createCart.errorNoPrice',
                                [ '%reference%' => $error->getOffer()->getOfferTitle() ],
                                self::TRANSLATION_DOMAIN
                            )
                        );
                    }
                }
            }
        }

        if($serviceResponse->getTotal()){
            return $this->redirectToRoute('cart.details.before_buy', ['currencyOrCartId' => $order->getCurrency()]);
        }

        return $this->redirect($request->server->get('HTTP_REFERER'));
    }

    /**
     * @param Request $request
     * @param                     $merchantOrderId
     * @param TranslatorInterface $translator
     * @param OrderService $orderService
     * @param OrderApi $orderApi
     * @param MessageApi $messageApi
     * @param OfferService $offerService
     * @param ProductOfferApi $productOfferApi
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/merchantOrder/dispute/{merchantOrderId}/create', name: 'front.merchantOrder.dispute.create')]
    public function createDisputeAction(
        Request $request,
        $merchantOrderId,
        TranslatorInterface $translator,
        OrderService $orderService,
        OrderApi $orderApi,
        MessageApi $messageApi,
        OfferService $offerService,
        ProductOfferApi $productOfferApi
    ){
        /** @var MerchantOrder $merchantOrder */
        $merchantOrder = $orderService->fetchMerchantsOrderByOrderId($merchantOrderId);
        if (!$merchantOrder) {
            throw new UnexpectedValueException(sprintf('Cannot find merchant order with id %s', $merchantOrderId));
        }

        // remove shipping offers from merchant order items
        $merchantOrderItems = array_filter(
            array_map(
                function(OrderItem $orderItem) {
                    return (preg_match('/^shipment-[0-9]+$/', $orderItem->getOfferExternalId())) ? null : $orderItem;
                },
                $merchantOrder->getItems()->toArray()
            )
        );
        $merchantOrder->setItems(new ArrayCollection($merchantOrderItems));

        /** @var OrderItem $item */
        foreach ($merchantOrder->getItems() as $item){

            $product = $productOfferApi->getProduct($item->getProductId());
            $item->setProduct($product);
            $orderItem = $orderApi->getOrderItemById($item->getId());

            if(!empty($orderItem->delivery_dates)) {
                $item->setDelayDelivery(($orderItem->delivery_dates[0]->expected_delivery_date));
            }

            $offer = $offerService->findOfferById($item->getOfferId());
            $item->setName($offer->getOfferTitle());
            $item->setSellerRef($offer->getSellerRef());
        }
        $subject = 'Dispute - Order '.$merchantOrder->getOrder()->getIdNumber().' - ' . date('d/m/Y h:i A');

        $form = $this->createForm(DisputeForm::class);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $id = $messageApi->disputeMerchantOrder($subject, $data['message'], $this->getUSer()->getCompany()->getIzbergUserId(), $merchantOrder->getMerchant()->getId() , $merchantOrderId);
            if($id != null){
                $threadParentMessageRepository = $this->doctrine->getRepository(ThreadParentMessage::class);
                $threadParentMessage = new ThreadParentMessage();
                $threadParentMessage->setIzbergId($id);
                $threadParentMessage->setFromEmail($this->getUser()->getId());
                $threadParentMessageRepository->add($threadParentMessage, true);

                $this
                    ->addFlash(
                        'success',
                        $translator->trans('dispute.create.ok', array(), self::TRANSLATION_DOMAIN)
                    );
                return $this->redirectToRoute('izberg.ticket.edit', array('id' => $id));
            }else{
                $this->logger->error(
                    "Unable to create dispute",
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::TECHNICAL_ERROR,
                        LogUtil::USER_NAME => $this->getUSer()->getUsername(),
                    ])
                );
                $this
                    ->addFlash(
                        'error',
                        $translator->trans('dispute.create.ko', array(), self::TRANSLATION_DOMAIN)
                    );
            }
        }

        return $this->render('@OpenFront/order/create_dispute.html.twig',
            ['form' => $form->createView(),
             'merchantOrder' => $merchantOrder,
             'subject' => $subject   ]);
    }

    /**
     * @param  $merchantOrderId
     * @param OrderService $orderService
     * @param MessageApi $messageApi
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/merchantOrder/dispute/{merchantOrderId}/list', name: 'front.merchantOrder.dispute.list')]
    public function listDisputesOfMerchantOrderAction(
        $merchantOrderId,
        OrderService $orderService,
        MessageApi $messageApi
    ){
        $merchantOrder = $orderService->fetchMerchantsOrderByOrderId($merchantOrderId);
        if (!$merchantOrder) {
            throw new UnexpectedValueException(sprintf('Cannot find merchant order with id %s', $merchantOrderId));
        }

        $disputes = $messageApi->getDisputesByMerchantOrderId($merchantOrder->getId());

        return $this->render('@OpenFront/order/disputes.html.twig',
            ['disputes' => $disputes->objects]);
    }

    /**
     * @param CsvOrderService $csvOrderService
     * @param Request $request
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/orders/export/', name: 'front.orders.export')]
    public function exportCompanyOrderAction(
        CsvOrderService $csvOrderService,
        Request $request
    ): StreamedResponse
    {
        $company = $this->getCompany();

        if (null === $company) {
            throw new BadRequestHttpException('Company details are missing');
        }

        $companyId = $company->getId();

        $fromDate = (!empty($from = $request->get('dateFrom_submit'))) ? new DateTimeImmutable($from) : null;
        $toDate = !empty($to = $request->get('dateTo_submit')) ? new DateTimeImmutable($to) : null;

        return new StreamedResponse(
            function () use ($csvOrderService, $companyId, $fromDate, $toDate) {
                $csvOrderService->exportOrders($companyId, $fromDate, $toDate);
            },
            Response::HTTP_OK,
            [
                'Content-Encoding' => 'UTF-16',
                'Content-Type' => 'application/CSV; charset=UTF-16',
                'Content-Disposition' => 'attachment; filename="orders.csv"',
            ]
        );
    }

    /**
     * @param Request $request
     * @param string $orderMerchantId
     * @param OrderService $orderService
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/odrer/merchantOrder/{orderMerchantId}', name: 'order_details_update_merchant_order_internal_id', methods: ['POST'])]
    public function updateBuyerInternalOrderIdAction(
        Request $request,
        string $orderMerchantId,
        OrderService $orderService
    )
    {
        if($this->isCsrfTokenValid('form',  $request->get('_token', null))) {
            $value = $request->get('value', null);
            if($value !== null) {
                $orderService->updateBuyerOrderInternalId(intval($orderMerchantId), $value);
                return $this->redirect($request->headers->get('referer'));
            }
        }
        return new Response('Form Error', Response::HTTP_BAD_REQUEST);
    }

    /**
     * @param Request $request
     * @param int $orderItemId
     * @param OrderService $orderService
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/odrer/item/{orderItemId}', name: 'order.details.update_item_extra_info', methods: ['POST'])]
    public function updateOrderItemExtraInfoAction(
        Request $request,
        int $orderItemId,
        OrderService $orderService
    )
    {
        $form = $this->createFormBuilder(null, ['csrf_protection' => true])
            ->add('field', TextType::class, ['required' => true, 'constraints' => new NotBlank()])
            ->add('value', TextType::class)
            ->getForm()
        ;
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $field = $form->getData()['field'];
            $value = $form->getData()['value'];

            $orderService->updateMerchantOrderItemExtraInfo($orderItemId, $field, $value);

            return $this->redirect($request->headers->get('referer'));
        }
        return new Response('Form Error', Response::HTTP_BAD_REQUEST);
    }

    /**
     * @param  $orderId
     * @param OrderService $orderService
     * @param OrderApi $orderApi ,
     * @return RedirectResponse
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/order/{orderId}/document/uploaded', name: 'front.order.document.uploaded')]
    public function getDocumentUploadedAction(
        $orderId,
        OrderService $orderService,
        OrderApi $orderApi,
    )
    {
        $izbergOrder = $orderService->fetchOrderById($orderId);
        $merchantOrder = $izbergOrder->getMerchantOrders()->first();

        if (empty($merchantOrder->getId())) {
            return $this->redirect($this->generateUrl('front.order.detail', ['orderId' => $orderId]));
        }

        $merchantOrder = $orderApi->fetchMerchantOrderById($merchantOrder->getId());
        $attributes = $merchantOrder->getAttributes();
        $files = $orderService->buildDocumentFileUploaded($attributes);
        $result = $orderService->getDocumentFileUploaded($files);

        if (!$result) {
            return $this->redirect($this->generateUrl('front.order.detail', ['orderId' => $orderId]));
        }

        return new RedirectResponse($result);
    }

    /**
     * @param  $orderId
     * @param OrderService $orderService
     * @param OrderApi $orderApi ,
     * @return RedirectResponse
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/order/{orderId}/{attrKey}/singleDocument/uploaded', name: 'front.order.singledocument.uploaded')]
    public function getSingleDocumentUploadedAction(
        $orderId,
        $attrKey,
        OrderService $orderService,
        OrderApi $orderApi,
        AttributeService $attributeService,
        Request $request
    )
    {
        $izbergOrder = $orderService->fetchOrderById($orderId);
        $merchantOrder = $izbergOrder->getMerchantOrders()->first();

        if (empty($merchantOrder->getId())) {
            return $this->redirect($this->generateUrl('front.order.detail', ['orderId' => $orderId]));
        }

        $merchantOrder = $orderApi->fetchMerchantOrderById($merchantOrder->getId());
        $attributes = $merchantOrder->getAttributes();

        if (!empty($attributes[$attrKey])) {
            $attributes = [
                $attrKey => $attributes[$attrKey]
            ];

            if ($files = $orderService->buildDocumentFileUploaded($attributes)) {
                return new RedirectResponse(array_values($files)[0]);
            }
        }

        return $this->redirect($this->generateUrl('front.order.detail', ['orderId' => $orderId]));
    }
}
