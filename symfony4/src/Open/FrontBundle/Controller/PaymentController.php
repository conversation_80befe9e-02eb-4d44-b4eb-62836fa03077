<?php

namespace Open\FrontBundle\Controller;

use AppBundle\Controller\MkoController;
use AppBundle\Entity\Cart;
use AppBundle\Entity\Company;
use AppBundle\Entity\User;
use AppBundle\Factory\CartFactory;
use AppBundle\Model\Cart\CartMerchant;
use AppBundle\Model\Order\MerchantOrder;
use AppBundle\Model\PaymentModes;
use AppBundle\Model\Transaction;
use AppBundle\Services\CartService;
use AppBundle\Services\JobService;
use AppBundle\Services\MerchantOrderService;
use AppBundle\Services\OrderService;
use AppBundle\Services\PaymentService;
use AppBundle\Services\WPSService;
use Doctrine\Persistence\ManagerRegistry;
use Open\FrontBundle\Form\PaymentModeSelectForm;
use Open\IzbergBundle\Api\OrderApi;
use Open\IzbergBundle\Model\OrderPayment;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Contracts\Translation\TranslatorInterface;

class PaymentController extends MkoController
{
    private const CART_ID = "cartId";
    public const CODE_QUALIFIER_MERCHANT_ORDER = "merchantOrder/";
    public const JOB_QUEUE_PAYMENT_TERM = 'payment_term';
    public const JOB_QUEUE_PAYMENT_BANK_TRANSFER = 'payment_bank_transfer';

    public function __construct(ManagerRegistry $doctrine, private readonly int $creditCardTimeout = 10)
    {
        parent::__construct($doctrine);
    }

    /**
     * get the payments modes elements that is needed to display the form in the view
     * @param Request $request
     * @param \AppBundle\Model\Cart\Cart $cart
     *
     * @param PaymentService $paymentService
     * @return array
     */
    protected function initPaymentModesForm(
        Request $request,
        \AppBundle\Model\Cart\Cart $cart,
        PaymentService $paymentService
    )
    {
        $user = $this->getUser();

        /**
         * Load Company
         * @var Company $company
         */
        $company = $user->getCompany();
        if ($company === null){
            $this->logger->error(
                "Unable checkout cart ". $cart->getId() . ": current user has no company",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::TECHNICAL_ERROR,
                    LogUtil::USER_NAME => $this->getUsername(),
                    self::CART_ID => $cart->getId(),
                ])
            );
            throw new AccessDeniedException("Unable to load cart with id ". $cart->getId() . " for current user");
        }

        // Load payment modes
        $paymentModes = new PaymentModes();
        $paymentModes->setPreCreditCart($company->getPrepaymentCreditcardEnabled());
        $paymentModes->setPreWireTransfer($company->getPrepaymentMoneyTransfertEnabled());

        $options = array(
            PaymentModeSelectForm::PAYMENT_PRE_CARD => $paymentModes->isPreCreditCart(),
            PaymentModeSelectForm::PAYMENT_PRE_WIRE => $paymentModes->isPreWireTransfer(),
            'merchantShippings' => array_map(
                function(CartMerchant $cartMerchant) {
                    return ['id' => $cartMerchant->getId()];
                },
                $cart->getMerchants()
            )
        );

        if ($company->getTermpaymentMoneyTransfertEnabled()){
            $paymentModes->setTimePayment(true);
            $options[PaymentModeSelectForm::PAYMENT_TERM] = true;
            $term = $paymentService->fetchPaymentTerm($request->getLocale());
            $paymentModes->setTimePaymentModeIzbergId($term->getId());
        }

        //build form
        $form = $this->createForm(PaymentModeSelectForm::class, null, $options);

        //returning elements that need to be included in the view
        return
            [
                'form' => $form,
                'viewParams' => [
                    'paymentModes' => $paymentModes,
                    'form' => $form->createView()
                ]
            ];
    }

    protected function handlePaymentRequest(
        \AppBundle\Model\Cart\Cart $cart,
        CartService $cartService,
        WPSService $wpsService,
        OrderService $orderService,
        MerchantOrderService $merchantOrderService,
        TranslatorInterface $translator,
        JobService $jobService,
        ?string $validationNumber = null,
        ?string $accountingEmail = null,
    ): Response
    {
        $order = null;
        $user = $this->getUser();

        $this->logger->info(
            'PaymentController::handlePaymentRequest',
            LogUtil::buildContext([
                'cartID' => $cart->getId(),
                'cartPaymentMode' => $cart->getPaymentMode()
            ])
        );

        if ($cart->getPaymentMode() === PaymentModeSelectForm::PAYMENT_PRE_CARD) {
            return $this->prePaymentWithCreditCard(
                $cart,
                $this->getUser(),
                $validationNumber,
                $cartService,
                $wpsService,
                $orderService,
                $merchantOrderService,
                $translator,
                $accountingEmail
            );
        }

        if ($cart->getPaymentMode() === PaymentModeSelectForm::PAYMENT_PRE_WIRE) {
            return $this->prePaymentWithWireTransfer(
                $cart,
                $this->getUser(),
                $validationNumber,
                $jobService,
                $cartService,
                $orderService,
                $translator,
                $accountingEmail
            );
        }

        if ($cart->getPaymentMode() === PaymentModeSelectForm::PAYMENT_TERM) {
            return $this->termPayment(
                $cart,
                $this->getUser(),
                $validationNumber,
                $jobService,
                $cartService,
                $orderService,
                $translator,
                $accountingEmail
            );
        }

        return $this->errorPaymentProcess(
            $user,
            $cart,
            new \Exception('Cannot find payment method'),
            $cartService,
            $translator
        );
    }

    /**
     * @param             $orderId
     * @param OrderApi $orderApi
     * @param CartFactory $cartFactory
     *
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_ADMIN")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/payment/creditCart/checkPayment/{orderId}', name: 'payment.process_pre_payment_cc.check_payment')]
    public function creditCartPaymentAction(
        $orderId,
        OrderApi $orderApi,
        CartFactory $cartFactory
    )
    {
        $order = $orderApi->fetchOrder($orderId);
        $cart = $cartFactory->buildCart($order->getCart()->getId(), $this->getCompany());

        $hasShipping = $cart->hasShippableItems();


        $this->logger->info(
            'user is back to the site from Webhelp payment by credit card page',
            LogUtil::buildContext([
                LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_PROCESS,
                LogUtil::USER_NAME => $this->getUsername(),
                "orderId" => $order->getId(),
            ])
        );

        return $this->render(
            '@OpenFront/payment/process_pre_payment_cc.twig',
            [
                "orderId" => $orderId,
                "order" => $order,
                "hasShipping" => $hasShipping,
            ]
        );
    }

    /**
     * @param $orderId
     * @param OrderService $orderService
     * @param CartService $cartService
     * @param OrderApi $orderApi
     * @return JsonResponse
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_ADMIN")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/payment/creditCart/checkAuthorization/{orderId}', name: 'front.check.cc')]
    public function checkCreditCartPaymentAction(
        $orderId,
        OrderService $orderService,
        CartService $cartService,
        OrderApi $orderApi
    )
    {
        $creditCardTimeout = intval($_ENV['CREDIT_CART_TIMEOUT']);
        ini_set('max_execution_time', strval($creditCardTimeout + 30));

        //we fetch the order until we have one of the following status:
        // - status = 60 => payment authorized, return ok
        // - status = 61 => payment pending authorization, return pending
        // - status = 2000 => order is cancelled, return ko
        try {
            $start = time();
            $order = $orderApi->fetchOrder($orderId);
            $timeout = $creditCardTimeout * count($order->getMerchantOrders());

            while (
                time() - $start < $timeout
                && !in_array(
                    $order->getPayment()->getStatus(),
                    [
                        OrderPayment::STATUS_PAYMENT_PENDING_AUTHORIZATION,
                        OrderPayment::STATUS_PAYMENT_AUTHORIZED,
                        OrderPayment::STATUS_PAYMENT_CANCELLED,
                    ]
                )
            ) {
                //avoid spamming izberg api...
                sleep(3);
                $order = $orderApi->fetchOrder($orderId);
            }

            if ( $order->getPayment()->getStatus() == OrderPayment::STATUS_PAYMENT_AUTHORIZED ) {
                $cartService->updateCartDbStatus($order->getCart()->getId(), Cart::STATUS_DONE);
                $cartService->clearUserCurrentCarts($this->getUser(), $order->getCart()->getId(), $order->getCurrency());
                return new JsonResponse(["status" => "ok", "currency" => $order->getCurrency()]);
            }

            if ( $order->getPayment()->getStatus() == OrderPayment::STATUS_PAYMENT_PENDING_AUTHORIZATION ) {
                return new JsonResponse(["status" => "pending", "currency" => $order->getCurrency()]);
            }

            if ( $order->getPayment()->getStatus() == OrderPayment::STATUS_PAYMENT_CANCELLED) {
                return new JsonResponse(["status" => "ko", "currency" => $order->getCurrency()]);
            }

            //here we auto cancel the order
            $orderService->cancelOrderById($order->getId());

            //also add a log to inform that we have no response from WPS
            $this->logger->error(
                "order has not been authorized within the time limit",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_GENERAL_ERROR,
                    LogUtil::USER_NAME => $this->getUsername(),
                    "orderId" => $orderId
                ])
            );

            return new JsonResponse(["status" => "ko"]);
        }catch (\Exception $e){

            $this->logger->error(
                "An error occurred while checking the status of the order",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_GENERAL_ERROR,
                    LogUtil::USER_NAME => $this->getUsername(),
                    "orderId" => $orderId
                ])
            );

            return new JsonResponse(["status" => "technical"]);
        }
    }

    private function prePaymentWithCreditCard(
        \AppBundle\Model\Cart\Cart $cart,
        User $user,
        ?string $validationNumber,
        CartService $cartService,
        WPSService $wpsService,
        OrderService $orderService,
        MerchantOrderService $merchantOrderService,
        TranslatorInterface $translator,
        ?string $accountingEmail,
    ): Response
    {

        $this->logger->info(
            'PaymentController::prePaymentWithCreditCard',
            LogUtil::buildContext([
                'cartID' => $cart->getId(),
            ])
        );

        $company = $user->getCompany();

        $cartService->updateCartInfoBeforePayment(
            $cart,
            $user
        );
        $orderService->createTemporaryOrderFromCart($cart, $user);
        $order = $orderService->createOrderFromCart($cart, $user, $validationNumber, $accountingEmail);

        $merchantOrderIds = array_map(
            function(MerchantOrder $merchantOrder) {
                return $merchantOrder->getId();
            },
            $order->getMerchantOrders()
        );

        // create merchant order pdf
        foreach($merchantOrderIds as $merchantOrderId) {
            $merchantOrderService->uploadMerchantOrderPdfToMerchant($merchantOrderId);
        }

        try {
            $transaction = $wpsService->createTransaction(
                $order,
                $company,
                $this->generateUrl(
                    'payment.process_pre_payment_cc.check_payment',
                    [
                        'orderId' => $order->getIzbergId()
                    ],
                    UrlGeneratorInterface::ABSOLUTE_URL
                )
            );

            if ($transaction->isRefused()){
                $this->logger->error(
                    "unable to create transaction",
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_GENERAL_ERROR,
                        LogUtil::USER_NAME => $user->getUsername(),
                        "transactionStatus" => Transaction::STATUS_REFUSED,
                        "transactionReason" => $transaction->getReason(),
                    ])
                );

                $this->addFlash(
                    'error',
                    $translator->trans('payment.select_mode.error.cc', [], self::TRANSLATION_DOMAIN)
                );

                return $this->redirectToRoute("homepage");
            }
        } catch(\Exception $exception) {
            return $this->errorPaymentProcess(
                $user,
                $cart,
                $exception,
                $cartService,
                $translator
            );
        }

        //Redirect user to WPS page
        $this->logger->info(
            "redirecting user",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_PROCESS,
                LogUtil::USER_NAME => $this->getUsername(),
                "redirectUrl" => $transaction->getRedirectionCardUrl(),
                "transactionStatus" => $transaction->getStatus(),
                "transactionReason" => $transaction->getReason(),
                "orderId" => $order->getIzbergId(),
            ])
        );

        $cartService->clearUserCurrentCarts($user, $cart->getId());

        return $this->redirect($transaction->getRedirectionCardUrl());
    }

    private function prePaymentWithWireTransfer(
        \AppBundle\Model\Cart\Cart $cart,
        User $user,
        ?string $validationNumber,
        JobService $jobService,
        CartService $cartService,
        OrderService $orderService,
        TranslatorInterface $translator,
        ?string $accountingEmail,
    ): Response
    {
        $hasShipping = $cart->hasShippableItems();

        try {
            $jobService->paymentBankTransfer($cart->getId(), $user->getId(), $validationNumber, $accountingEmail);

            $orderService->createTemporaryOrderFromCart($cart, $user);
            $cartService->clearUserCurrentCarts($user, $cart->getId(), $cart->getCurrency());

        } catch(\Exception $exception) {
            return $this->errorPaymentProcess(
                $user,
                $cart,
                $exception,
                $cartService,
                $translator
            );
        }

        return $this->render(
            '@OpenFront/payment/process_pre_payment_wire_pending.twig',
            [
                'hasShipping' => $hasShipping,
            ]
        );
    }

    /**
     * @param \AppBundle\Model\Cart\Cart $cart
     * @param User $user
     * @param string|null $validationNumber
     * @param JobService $jobService
     * @param CartService $cartService
     * @param OrderService $orderService
     * @param TranslatorInterface $translator
     * @return Response
     */
    private function termPayment(
        \AppBundle\Model\Cart\Cart $cart,
        User $user,
        ?string $validationNumber = null,
        JobService $jobService,
        CartService $cartService,
        OrderService $orderService,
        TranslatorInterface $translator,
        ?string $accountingEmail,
    ): Response
    {

        $hasShipping = $cart->hasShippableItems();

        try {
            $jobService->paymentTerm($cart->getId(), $user->getId(), $validationNumber, $accountingEmail);
            $orderService->createTemporaryOrderFromCart($cart, $user);
            $cartService->clearUserCurrentCarts($user, $cart->getId(), $cart->getCurrency());

        } catch(\Exception $exception) {
            return $this->errorPaymentProcess($user, $cart, $exception, $cartService, $translator);
        }

        return $this->render(
            '@OpenFront/payment/process_time_payment_pending.twig',
            [
                'hasShipping' => $hasShipping
            ]
        );
    }

    private function errorPaymentProcess(
        User $user,
        \AppBundle\Model\Cart\Cart $cart,
        \Exception $exception,
        CartService $cartService,
        TranslatorInterface $translator
    )
    {
        $this->logger->error(
            $exception->getMessage(),
            LogUtil::buildContext([
                LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_PROCESS,
                LogUtil::USER_NAME => $this->getUsername(),
            ])
        );

        $newCart = $cartService->revertCart($user, $cart);

        //add a flash message to inform user that an error occurred
        $this->addFlash('error', $translator->trans('payment.error', [], self::TRANSLATION_DOMAIN));

        //redirect user to the new cart
        return $this->redirectToRoute(
            'cart.details.before_buy',
            [
                'currencyOrCartId' => $newCart->getId(),
                '_fragment' => 'newCart'
            ]
        );
    }
}
