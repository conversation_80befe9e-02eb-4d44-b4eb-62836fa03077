<?php
/**
 * Created by PhpStorm.
 * User: LRO16285
 * Date: 12/04/2018
 * Time: 09:27
 */

namespace Open\FrontBundle\Controller;

use AppBundle\Controller\MkoController;
use AppBundle\Entity\Company;
use AppBundle\Entity\Document;
use AppBundle\Services\CompanyService;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\HttpFoundation\Request;
use AppBundle\Form\CompanyPaymentModesForm;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Contracts\Translation\TranslatorInterface;

class PaymentModeController extends MkoController
{

    const SUCCESS = 'success';
    const ERROR = 'error';

    /**
     * @param Request $request
     *
     * @param TranslatorInterface $translator
     * @param CompanyService $companyService
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/payment_modes', name: 'front.company.paymentmodes')]
    public function editAction(
        Request $request,
        TranslatorInterface $translator,
        CompanyService $companyService
    )
    {
        /** @var Company $company */
        $company = $this->getCompany();

        if (!$company->canAccessPaymentModes()) {
            throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_COMPANY_NOT_VALID));
        }

        $disabled = true;
        $roles = $this->getUser()->getRoles();
        if (in_array('ROLE_BUYER_ADMIN', $roles)) {
            $disabled = false;
        }

        $options = array(
            'validation_groups' => ['Default'],
            'payment_term_is_pending' => $company->getTermpaymentMoneyTransfertPending(),
            'payment_term_is_enabled' => $company->getTermpaymentMoneyTransfertEnabled(),
            'disabled' => $disabled,
        );

        // Les prépaiements sont toujours actifs
        $company->setPrepaymentCreditcardEnabled(true);
        $company->setPrepaymentMoneyTransfertEnabled(true);

        $form = $this->createForm(
            CompanyPaymentModesForm::class,
            $company,
            $options
        );

        if ($disabled) {
            $form->remove('submit');
        }

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid() && !$disabled) {
            $companyDataInDB = $companyService->getDataFromDB($company);

            try {
                if ((!$companyDataInDB['termpaymentMoneyTransfertEnabled'])) {
                    $companyService->askForTermPayment($company);
                }
                $companyService->save($company);
                $this
                    ->addFlash(
                        self::SUCCESS,
                        $translator->trans('payment_mode.saved', array(),
                            self::TRANSLATION_DOMAIN)
                    );
                $request->getSession()->set(self::SESSION_ACCOUNT_CREATION, false);

            } catch (\Exception $e) {
                $this
                    ->addFlash(
                        self::ERROR,
                        $translator->trans('payment_mode.ask_for_term_error', array(),
                            self::TRANSLATION_DOMAIN)
                    );
            }
            return $this->redirectToRoute("front.company.paymentmodes");
        }

        return $this->render(
            '@OpenFront/company/company_paymentmodes.html.twig',
            [
                'form' => $form->createView(),
                'company' => $company,
                'disabled' => $disabled,
                'sizeMax' => Document::SIZE_CONSTRAINT,
                'typeError' => $translator->trans('document.upload.mime', array('%contrainte%' => implode(", ", Document::MIME_CONSTRAINT)), self::TRANSLATION_DOMAIN),
                'sizeError' => $translator->trans('document.upload.size', array('%contrainte%' => Document::SIZE_CONSTRAINT / (1024 * 1024)), self::TRANSLATION_DOMAIN),
            ]
        );
    }
}
