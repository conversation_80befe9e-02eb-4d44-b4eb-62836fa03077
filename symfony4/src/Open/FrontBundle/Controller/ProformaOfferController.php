<?php

declare(strict_types=1);

namespace Open\FrontBundle\Controller;

use App<PERSON><PERSON>le\Controller\MkoController;
use AppBundle\Entity\Address;
use AppBundle\Entity\User;
use AppBundle\Model\DetailedOffer;
use AppBundle\Services\CartService;
use AppBundle\Services\OfferPDFGenerator;
use AppBundle\Services\OfferService;
use Mpdf\Output\Destination;
use Open\FrontBundle\ProformaPdf\Builder\ProformaPdfBuilderInterface;
use Open\FrontBundle\ProformaPdf\MerchantData;
use Open\IzbergBundle\Api\MerchantApi;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

final class ProformaOfferController extends MkoController
{
    #[\Symfony\Component\Routing\Attribute\Route(path: 'cart/proforma/{cartId}/offers', name: 'front.cart.offers.proforma')]
    public function proformaOffersByCart(
        int $cartId,
        CartService $cartService,
        OfferPDFGenerator $offerPDFGenerator,
        Request $request,
        ProformaPdfBuilderInterface $proformaPdfBuilder
    ): Response {
        /** @var User $user */
        $user = $this->getUser();

        $billingAddress = $this->createBillingAddress($request);

        if (!$user) {
            return $this->redirectToRoute('homepage');
        }

        $company = $this->getCompany();

        $cartModel = $cartService->findCart(user: $user, cartId: $cartId);

        $pdfData = $proformaPdfBuilder->create(
            cart: $cartModel,
            company: $company
        );

        $pdf = $offerPDFGenerator->computeOffersProformaPDF(
            pdfData: (array) $pdfData,
            userCompany: $company,
            locale: $request->getLocale(),
            billingAddress: $billingAddress
        );

        $pdf->output(sprintf('proforma-cart-%d.pdf', $cartId), Destination::INLINE);

        return $this->redirectToRoute('cart.details.before_buy', ['currencyOrCartId' => $cartId]);
    }

    private function createBillingAddress(Request $request): Address {
        // To use address filled in form not the one saved in database
        $billingAddressMain = $request->get('billingAddressMain');
        $billingAddressComplement = $request->get('billingAddressComplement');
        $billingAddressZipCode = $request->get('billingAddressZipCode');
        $billingAddressCity = $request->get('billingAddressCity');
        $billingAddressArea = $request->get('billingAddressArea');

        $billingAddress = new Address();
        $billingAddress->setAddress($billingAddressMain);
        $billingAddress->setAddress2($billingAddressComplement);
        $billingAddress->setZipCode($billingAddressZipCode);
        $billingAddress->setCity($billingAddressCity);
        $billingAddress->setRegionText($billingAddressArea);

        return $billingAddress;
    }
}
