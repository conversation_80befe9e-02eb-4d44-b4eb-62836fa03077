<?php

namespace Open\FrontBundle\Controller;

use AppBundle\Controller\MkoController;
use AppBundle\Entity\BddFile;
use AppBundle\Entity\PurchaseRequest;
use AppBundle\Entity\PurchaseRequestItem;
use AppBundle\Entity\SearchResult;
use AppBundle\Entity\User;
use AppBundle\Exception\PurchaseRequestException;
use AppBundle\Model\View\PurchaseRequest\PurchaseRequestFactory;
use AppBundle\Repository\PurchaseRequestItemRepository;
use AppBundle\Repository\PurchaseRequestRepository;
use AppBundle\Services\MailService;
use AppBundle\Services\PurchaseRequestService;
use Doctrine\Common\Annotations\AnnotationReader;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\DependencyInjection\Container;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\PropertyInfo\Extractor\PhpDocExtractor;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Router;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Serializer\Encoder\JsonEncoder;
use Symfony\Component\Serializer\Mapping\Factory\ClassMetadataFactory;
use Symfony\Component\Serializer\Mapping\Loader\AttributeLoader;
use Symfony\Component\Serializer\Normalizer\ArrayDenormalizer;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;
use Symfony\Component\Serializer\Serializer;

class PurchaseRequestController extends MkoController
{
    /**
     * @param Request $request
     * @param PurchaseRequestRepository $purchaseRequestRepository
     * @param PurchaseRequestFactory $purchaseRequestFactory
     * @param PurchaseRequestService $purchaseRequestService
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/purchase-request', name: 'purchase_request.details', methods: ['GET'])]
    public function purchaseRequestAction(
        PurchaseRequestRepository $purchaseRequestRepository,
        PurchaseRequestFactory $purchaseRequestFactory,
        PurchaseRequestService $purchaseRequestService
    ): Response
    {
        $user = $this->getUser();
        $csvMapping = json_encode($purchaseRequestService->csvMappingGenerator());
        $foundCsvMapping = json_encode($purchaseRequestService->foundCsvMappingGenerator());

        $classMetadataFactory = new ClassMetadataFactory(new AttributeLoader());
        $objectNormalizer = new ObjectNormalizer($classMetadataFactory, null, null, new PhpDocExtractor());
        $arrayDeNormalizer = new ArrayDenormalizer();
        $jsonEncoder = new JsonEncoder();
        $serializer = new Serializer([$arrayDeNormalizer, $objectNormalizer], [$jsonEncoder]);

        $purchaseRequest = $purchaseRequestRepository->findOneBy(['user' => $user, 'status' => PurchaseRequest::STATUS_INITIALISED]);
        $purchaseRequestViewModel = $purchaseRequestFactory->build($purchaseRequest);

        return $this->render(
            '@OpenFront/purchaseRequest/purchase_request_details.html.twig',
            [
                'csvMapping' => $csvMapping,
                'foundCsvMapping' => $foundCsvMapping,
                'purchaseRequestViewModel' => $serializer->serialize($purchaseRequestViewModel, 'json'),
            ]
        );
    }

    /**
     * entry point of purchase request creation
     *
     * Parse CSV file from uploaded file
     *
     * @param Request $request
     * @param PurchaseRequestService $purchaseRequestService
     * @return Response
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/purchase-request/import', name: 'purchase_request.import', methods: ['POST'])]
    public function uploadPurchaseRequestAction(
        Request $request,
        PurchaseRequestService $purchaseRequestService
    ): Response
    {
        /** @var User $user */
        $user = $this->getUser();

        /** @var UploadedFile $file */
        $file = $request->files->get('files')[0];
        $classMetadataFactory = new ClassMetadataFactory(new AnnotationLoader(new AnnotationReader()));
        $objectNormalizer = new ObjectNormalizer($classMetadataFactory, null, null, new PhpDocExtractor());
        $objectNormalizer->setIgnoredAttributes(['user', 'purchaseRequest']);
        $arrayDeNormalizer = new ArrayDenormalizer();
        $jsonEncoder = new JsonEncoder();
        $serializer = new Serializer([$arrayDeNormalizer, $objectNormalizer], [$jsonEncoder]);

        try {
            $purchaseRequest = $purchaseRequestService->uploadPurchaseRequestFromCsv($user, $file);
        } catch (PurchaseRequestException $exception) {
            return new JsonResponse(['message' => $exception->getMessage()], Response::HTTP_BAD_REQUEST);
        }

        return new JsonResponse($serializer->serialize($purchaseRequest, 'json'), 200, [], true);
    }

    /**
     * entry point of purchase request creation
     *
     * Parse CSV file from uploaded file
     *
     * @param Request $request
     * @param PurchaseRequestRepository $purchaseRequestRepository
     * @param RouterInterface $router
     * @param MailService $mailService
     * @return Response
     * @throws \AppBundle\Exception\MailException
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/purchase-request/send', name: 'purchase_request.send', methods: ['POST'])]
    public function sendCsvPurchaseRequestAction(
        Request $request,
        PurchaseRequestRepository $purchaseRequestRepository,
        RouterInterface $router,
        MailService $mailService,
        array $purchaseRequestEmailAddress
    ): Response
    {
        /** @var User $user */
        $user = $this->getUser();

        $purchaseRequest = $purchaseRequestRepository->findOneBy(['user' => $user, 'status' => PurchaseRequest::STATUS_INITIALISED]);

        $csv = $request->get('csv');
        $totalRef = $request->get('totalRef');
        $totalNotFoundRef = $request->get('totalNotFoundRef');

        if (!is_null($purchaseRequest->getNotFoundItemsFile())) {
            return new JsonResponse(json_encode(['response' => 'not send']), 200, [], true);
        }

        $bddFile = (new BddFile())
            ->setName('purchase-request-not-found-' . date('Y-m-d') . '-' . $user->getCompany()->getIzbergUserId() . '-' . $purchaseRequest->getId() . '.csv')
            ->setType('text/plain')
            ->setSize(mb_strlen($csv, 'utf8'))
            ->setData($csv)
            ->setCreatedAt(new \DateTime());

        $purchaseRequest->setNotFoundItemsFile($bddFile);
        $purchaseRequestRepository->save($purchaseRequest);

        $linkImportFile = '';
        $linkNotFoundFile = $router->generate(
            "file.download",
            ["token" => $bddFile->getToken()],
            Router::ABSOLUTE_URL
        );

        if ($purchaseRequest->getImportFile() && $purchaseRequest->getImportFile()->getToken()) {
            $linkImportFile = $router->generate(
                "file.download",
                ["token" => $purchaseRequest->getImportFile()->getToken()],
                Router::ABSOLUTE_URL
            );
        }

        $date = date("Y-m-d");
        if ($purchaseRequest->getCreatedAt()) {
            $date = $purchaseRequest->getCreatedAt()->format("Y-m-d");
        }

        $mailService->sendEmailMessage(MailService::PURCHASE_REQUEST_SEND_NOT_FOUND, "en", $purchaseRequestEmailAddress,
            [
                'company' => $user->getCompany()->getName(),
                'date' => $date,
                'totalRef' => $totalRef,
                'totalNotFoundRef' => $totalNotFoundRef,
                'link_import_file' => $linkImportFile,
                'link_not_found_file' => $linkNotFoundFile,
            ],null,null
        );

        return new JsonResponse(json_encode($purchaseRequest), 200, [], true);
    }

    /**
     * @param Request $request
     * @param PurchaseRequestService $purchaseRequestService
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/purchase-request/clear', name: 'purchase_request.clear', methods: ['POST'])]
    public function clearPurchaseRequestAction(
        Request $request,
        PurchaseRequestService $purchaseRequestService
    )
    {
        $token = $request->get('token');

        if (!$this->isCsrfTokenValid('clear', $token)) {
            return new JsonResponse(['message' => 'error'], Response::HTTP_BAD_REQUEST);
        }

        $purchaseRequestService->clearOnGoingUserPurchaseRequest($this->getUser());

        return new JsonResponse(['message' => 'Purchase request successfully cleared']);
    }

    /**
     * @param Request $request
     * @param $purchaseRequestItemId
     * @param PurchaseRequestService $purchaseRequestService
     * @param PurchaseRequestItemRepository $purchaseRequestItemRepository
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/purchase-request/search-offer/{purchaseRequestItemId}', name: 'purchase_request.search_offer', methods: ['GET'])]
    public function fetchPurchaseRequestItemOffersAction(
        Request $request,
        $purchaseRequestItemId,
        PurchaseRequestService $purchaseRequestService,
        PurchaseRequestItemRepository $purchaseRequestItemRepository
    ): Response
    {
        $page = $request->get('page', 1);

        $classMetadataFactory = new ClassMetadataFactory(new AnnotationLoader(new AnnotationReader()));
        $objectNormalizer = new ObjectNormalizer($classMetadataFactory, null, null, new PhpDocExtractor());
        $objectNormalizer->setIgnoredAttributes(['regions', 'user']); // otherwise circular reference when serializing the offer
        $arrayDeNormalizer = new ArrayDenormalizer();
        $jsonEncoder = new JsonEncoder();
        $serializer = new Serializer([$arrayDeNormalizer, $objectNormalizer], [$jsonEncoder]);

        $defaultResponse = new JsonResponse($serializer->serialize((new SearchResult()), 'json'), 200, [], true);

        /** @var PurchaseRequestItem $purchaseRequestItem */
        $purchaseRequestItem = $purchaseRequestItemRepository->find($purchaseRequestItemId);
        if (!$purchaseRequestItem) {
            return $defaultResponse;
        }

        if ($purchaseRequestItem->getPurchaseRequest()->getUser() !== $this->getUser()) {
            return $defaultResponse;
        }

        $searchResult = $purchaseRequestService->searchOffers($purchaseRequestItem, $request->getLocale(), $page);

        return new JsonResponse($serializer->serialize($searchResult, 'json'), 200, [], true);
    }

    /**
     *
     * @param Request $request
     * @param int $purchaseRequestItemId
     * @param PurchaseRequestService $purchaseRequestService
     * @param PurchaseRequestItemRepository $purchaseRequestItemRepository
     * @return JsonResponse
     * @throws \Doctrine\ORM\NonUniqueResultException
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/purchase-request/add-to-cart/{purchaseRequestItemId}', name: 'purchase_request.add_to_cart', methods: ['POST'])]
    public function addPurchaseRequestItemToCartAction(
        Request $request,
        int $purchaseRequestItemId,
        PurchaseRequestService $purchaseRequestService,
        PurchaseRequestItemRepository $purchaseRequestItemRepository
    )
    {
        $token = $request->get('token');
        $offerId = $request->get('offerId');
        $quantity = $request->get('quantity');

        if (!$this->isCsrfTokenValid('addToCart', $token)) {
            return new JsonResponse(['message' => 'error'], Response::HTTP_BAD_REQUEST);
        }

        // save into purchaseRequestItem offerId
        $purchaseRequestItem = $purchaseRequestItemRepository->find($purchaseRequestItemId);
        if (!$purchaseRequestItem) {
            return new JsonResponse(null, Response::HTTP_NOT_FOUND);
        }

        if ($purchaseRequestItem->getPurchaseRequest()->getUser() !== $this->getUser()) {
            return new JsonResponse(null, Response::HTTP_FORBIDDEN);
        }

        try {
            $purchaseRequestService->addToUserCart($this->getUser(), $purchaseRequestItem, $offerId, $quantity);
        } catch(PurchaseRequestException $exception) {
            return new JsonResponse(['message' => $exception->getMessage()], Response::HTTP_BAD_REQUEST);
        }

        $count = [
            'EUR' => $this->getUser()->getItemInCartEUR(),
            'USD' => $this->getUser()->getItemInCartUSD(),
        ];

        return new JsonResponse($count, Response::HTTP_OK);
    }

    /**
     *
     * @param Request $request
     * @param int $purchaseRequestItemId
     * @param PurchaseRequestService $purchaseRequestService
     * @param PurchaseRequestItemRepository $purchaseRequestItemRepository
     * @return JsonResponse
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/purchase-request/remove-from-cart/{purchaseRequestItemId}', name: 'purchase_request.remove_from_cart', methods: ['POST'])]
    public function removePurchaseRequestItemFromCartAction(
        Request $request,
        int $purchaseRequestItemId,
        PurchaseRequestService $purchaseRequestService,
        PurchaseRequestItemRepository $purchaseRequestItemRepository
    )
    {
        $token = $request->get('token');

        if (!$this->isCsrfTokenValid('removeFromCart', $token)) {
            return new JsonResponse(['message' => 'error'], Response::HTTP_BAD_REQUEST);
        }

        /** @var PurchaseRequestItem $purchaseRequestItem */
        $purchaseRequestItem = $purchaseRequestItemRepository->find($purchaseRequestItemId);
        if (!$purchaseRequestItem) {
            return new JsonResponse(null, Response::HTTP_NOT_FOUND);
        }

        if ($purchaseRequestItem->getPurchaseRequest()->getUser() !== $this->getUser()) {
            return new JsonResponse(null, Response::HTTP_FORBIDDEN);
        }

        try {
            $purchaseRequestService->removeFromUserCart($this->getUser(), $purchaseRequestItem);
        } catch(PurchaseRequestException $exception) {
            return new JsonResponse(['message' => $exception->getMessage()], Response::HTTP_BAD_REQUEST);
        }

        $count = [
            'EUR' => $this->getUser()->getItemInCartEUR(),
            'USD' => $this->getUser()->getItemInCartUSD(),
        ];

        return new JsonResponse($count, Response::HTTP_OK);
    }

    /**
     *
     * @param Request $request
     * @param int $purchaseRequestItemId
     * @param PurchaseRequestService $purchaseRequestService
     * @param PurchaseRequestItemRepository $purchaseRequestItemRepository
     * @return JsonResponse
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/purchase-request/remove-item/{purchaseRequestItemId}', name: 'purchase_request.remove_item', methods: ['POST'])]
    public function removePurchaseRequestItemAction(
        Request $request,
        int $purchaseRequestItemId,
        PurchaseRequestService $purchaseRequestService,
        PurchaseRequestItemRepository $purchaseRequestItemRepository
    )
    {
        $token = $request->get('token');

        if (!$this->isCsrfTokenValid('removeItem', $token)) {
            return new JsonResponse(['message' => 'error'], Response::HTTP_BAD_REQUEST);
        }

        /** @var PurchaseRequestItem $purchaseRequestItem */
        $purchaseRequestItem = $purchaseRequestItemRepository->find($purchaseRequestItemId);
        if (!$purchaseRequestItem) {
            return new JsonResponse(null, Response::HTTP_NOT_FOUND);
        }

        if ($purchaseRequestItem->getPurchaseRequest()->getUser() !== $this->getUser()) {
            return new JsonResponse(null, Response::HTTP_FORBIDDEN);
        }

        try {
            $purchaseRequestService->removeItem($this->getUser(), $purchaseRequestItem);
        } catch(PurchaseRequestException $exception) {
            return new JsonResponse(['message' => $exception->getMessage()], Response::HTTP_BAD_REQUEST);
        }

        $count = [
            'EUR' => $this->getUser()->getItemInCartEUR(),
            'USD' => $this->getUser()->getItemInCartUSD(),
        ];

        return new JsonResponse($count, Response::HTTP_OK);
    }
}
