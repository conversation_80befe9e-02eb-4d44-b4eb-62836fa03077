<?php

namespace Open\FrontBundle\Controller;

use App<PERSON><PERSON>le\Controller\MkoController;
use AppBundle\Services\CompanyCatalogService;
use Open\FrontBundle\Form\SearchBarForm;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class SearchBarController extends AbstractController
{
    /**
     * @param Request $request
     * @param CompanyCatalogService $companyCatalogService
     * @return \Symfony\Component\HttpFoundation\Response
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/search_bar', name: 'front.searchbar.search')]
    public function formAction(
        Request $request,
        CompanyCatalogService $companyCatalogService
    )
    {
        $user = $this->getUser();

        $isHomePage = ($request->get('homepage') === true);
        $isMobileSearch = ($request->get('mobile-search') === true);
        $searchText = $request->get('text', '');
        $searchType = $request->get('searchType', null);

        $form = $this->createForm(
            SearchBarForm::class,
            [
                'text' => $searchText,
                'isMobile' => $isMobileSearch,
                'homepage' => $isHomePage,
            ],
            [
                'enableCatalogSearch' => (!!$user && $companyCatalogService->hasData()),
                'enableProductCompatibilitySearch' => ($searchType === SearchBarForm::SEARCH_TYPE_IN_PRODUCT_COMPATIBILITY),
                'csrf_protection' => false,
            ]
        );

        return $this->render('@OpenFront/component/search-bar.html.twig', array(
            'formSearchBar' => $form->createView(),
            'homepage' => $isHomePage,
            'isMobileSearch' => $isMobileSearch,
            'user' => $user,
            'hasCatalog' => (!!$user && $companyCatalogService->hasData())
        ));
    }
}
