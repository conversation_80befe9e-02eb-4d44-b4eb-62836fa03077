<?php

namespace Open\FrontBundle\Controller;

use AppBundle\Controller\MkoController;
use AppBundle\Entity\CompanyCatalog;
use AppBundle\Entity\User;
use AppBundle\Model\AutocompleteOfferResponse;
use AppBundle\Repository\CompanyCatalogRepository;
use AppBundle\Services\BafvService;
use AppBundle\Services\CompanyCatalogService;
use AppBundle\Services\SearchService;
use AppBundle\Services\SecurityService;
use AppBundle\Services\SpecificPriceService;
use AppBundle\Services\UserBddService;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Open\FrontBundle\Form\ContactMerchantToAskPriceForm;
use Open\FrontBundle\Form\SearchBarForm;
use Open\FrontBundle\Form\SearchFormFactory;
use Open\IzbergBundle\Algolia\AlgoliaService;
use Open\IzbergBundle\Service\CategoryService;
use Open\TicketBundle\Entity\Ticket;
use Open\TicketBundle\Entity\TicketMessage;
use Open\TicketBundle\Form\TicketWaylfType;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

class SearchOfferController extends MkoController
{
    private const DEFAULT_HITS_PER_PAGE = 9;
    private const DEFAULT_DISPLAY = 'card';

    /**
     * @param Request                  $request
     * @param CompanyCatalogRepository $companyCatalogRepository
     *
     * @param SearchService            $searchService
     * @return Response
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/search/autocomplete/', name: 'front.search.autocomplete')]
    public function autocompleteAction(Request $request, CompanyCatalogRepository $companyCatalogRepository, SearchService $searchService): Response
    {
        $user = $this->getUser();
        $companyId = ($user && $user->getCompany()) ? $user->getCompany()->getId() : null;
        $text = $request->query->get('text');
        $searchType = $request->query->get(
            'searchType',
            (!!$user) ? SearchBarForm::SEARCH_TYPE_IN_MARKETPLACE : SearchBarForm::SEARCH_TYPE_IN_MARKETPLACE_ANONYMOUS
        );

        if ($searchType === SearchBarForm::SEARCH_TYPE_IN_CATALOG && $companyId) {
            return $this->autocompleteCatalogSearch($companyId, $text, $companyCatalogRepository);
        }

        return $this->autoCompleteMarketPlaceSearch((string)$text, $request->getLocale(), $searchService);
    }

    /**
     *
     * @param Request $request
     * @param SearchFormFactory $searchFormFactory
     * @param SearchService $searchService
     * @param CompanyCatalogService $companyCatalogService
     * @param SecurityService $securityService
     * @param SpecificPriceService $specificPriceService
     * @param TranslatorInterface $translator
     * @param CategoryService $categoryService
     * @param UserBddService $userBddService
     * @param BafvService $bafvService
     * @param FormFactoryInterface $formFactory
     * @return Response
     * @throws ORMException
     * @throws OptimisticLockException
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/search/', name: 'front.search')]
    public function searchAction(
        Request $request,
        SearchFormFactory $searchFormFactory,
        SearchService $searchService,
        CompanyCatalogService $companyCatalogService,
        SecurityService $securityService,
        SpecificPriceService $specificPriceService,
        TranslatorInterface $translator,
        CategoryService $categoryService,
        UserBddService $userBddService,
        BafvService $bafvService,
        FormFactoryInterface $formFactory,
        SessionInterface $session
    ): Response
    {
        $user = $this->getUser();

        $text = '';
        $searchType = (!!$user) ? SearchBarForm::SEARCH_TYPE_IN_MARKETPLACE : SearchBarForm::SEARCH_TYPE_IN_MARKETPLACE_ANONYMOUS;
        $searchType = $request->query->get('searchType', $searchType);
        $category = '';
        $facetFilters = [];
        $filters = [];
        $page = 0;
        $sortBy = AlgoliaService::RANKING_BY_RELEVANCE;
        $hitsPerPage = $session->get('hitsPerPageOffers',self::DEFAULT_HITS_PER_PAGE);


        $adv_mobile = $request->query->get('adv_mobile', false);
        $display = $request->query->get('display', $session->get('display',self::DEFAULT_DISPLAY));

        $searchBarForm = $this->createForm(
            SearchBarForm::class,
            null,
            [
                'enableCatalogSearch' => (!!$user && $companyCatalogService->hasData()),
                'enableProductCompatibilitySearch' => ($searchType === SearchBarForm::SEARCH_TYPE_IN_PRODUCT_COMPATIBILITY),
                'csrf_protection' => false,
            ]
        );
        $searchForm = $searchFormFactory->build((!!$user), $hitsPerPage);

        $searchBarForm->handleRequest($request);
        $searchForm->handleRequest($request);
        if ($searchBarForm->isSubmitted() && $searchBarForm->isValid()) {
            $text = $searchBarForm->get('text')->getData() ?? $text;
            $searchType = $request->get('searchType',  $searchType);
        } else {
            $text = $searchForm->get('text')->getData() ?? '';
            $category = $searchForm->get('category')->getData() ?? $category;
            $searchType = $searchForm->get('searchType')->getData() ?? $searchType;
            $facetFilters = array_merge($request->get('commonFacetFilters', []), $request->get('specificFacetFilters', []));
            $filters =  json_decode($searchForm->get('filters')->getData(),boolval(JSON_OBJECT_AS_ARRAY)) ?? $filters;
            $hitsPerPage = $searchForm->get('hitsPerPage')->getData() ?? $hitsPerPage;
            $session->set('hitsPerPageOffers', $hitsPerPage);
            $session->set('display',$display);
            $page = $searchForm->get('page')->getData() ?? $page;
            $sortBy = $request->get('sortBy', $sortBy);
        }

        $searchResult = $searchService->search(
            $user,
            $text,
            $category,
            $searchType,
            $facetFilters,
            $filters,
            $hitsPerPage,
            $page,
            $sortBy,
            $request->getLocale()
        );

        $offers = $searchResult->getOffers();

        if(!$securityService->isAnonymous() && !$securityService->isAdmin($this->getUser())){
            $offers = $specificPriceService->bulkUpdateOfferSpecificPrices($this->getUser()->getCompany(), $offers);
        }

        $searchForm = $searchFormFactory->build((!!$user), $hitsPerPage, $searchResult);
        $searchForm->handleRequest($request);

        $upDepartment = ($searchResult->getCategory() !== null) ? 'up' : '';
        $showDepartment = ($searchResult->getCategory() !== null) ? 'show' : '';
        $departments = $searchService->fetchDepartmentsFromSearchResults($searchResult);

        $showRefine = $searchResult->hasFacets() ? 'show' : '';
        $upRefine = $searchResult->hasFacets() ? 'up' : '';

        $activeSearchFacets = $searchService->buildActiveSearchFacets($searchResult, $request);

        $bafv = $bafvService->fetchBafvRequestDetails($user, $offers);

        $contactMerchantToAskPriceForm = $formFactory->createNamed(
            'contact_merchant_to_ask_price_form',
            ContactMerchantToAskPriceForm::class,
            null,
            ['translator' => $translator]
        )->createView();

        $contactMerchantToAskPriceForNoPriceOfferForm = $formFactory->createNamed(
            'contact_merchant_to_ask_price_for_no_price_offer_form',
            ContactMerchantToAskPriceForm::class,
            null,
            [
                'translator' =>$translator,
                //'content' => $translator->trans('offer_detail.ask_vendor_no_price_offer_message_content', [], MkoController::TRANSLATION_DOMAIN),
                'content' => str_replace(array('\r\n', '\r', '\n'), "\n", $translator->trans('offer_detail.ask_vendor_no_price_offer_message_content', [], MkoController::TRANSLATION_DOMAIN)),
            ]
        )->createView();

        // What Are You Looking For form
        $ticketMessage = new TicketMessage();
        $createBycustomer = true; //!$this->isOperator() && !$this->isAdmin();
        $ticket = new Ticket($createBycustomer);
        $ticket->addMessage($ticketMessage);

        $isAnonymous = (!$user instanceof User);

        $form_options = [];
        if ($isAnonymous) {
            $form_options['validation_groups'] = ["Default", "TicketAnonymous"];

            $form_options['captcha_enabled'] = $this->getParameter('captcha_enabled');
            $form_options['captcha_secret'] = $this->getParameter('captcha_secret');
        }

        $formContactWAYLF = $this->createForm(
            TicketWaylfType::class,
            $ticket,
            $form_options
        );

        $template = '@OpenFront/search/index.html.twig';

        if ($searchResult->total() == 0) {
            $template = '@OpenFront/search/no-result.html.twig';
        }

        return $this->render(
            $template,
            [
                'show_department' => $showDepartment,
                'up_department' => $upDepartment,
                'show_refine' => $showRefine,
                'up_refine' => $upRefine,
                'formSearchBar' => $searchBarForm->createView(),
                'formSearch' => $searchForm->createView(),
                'formContactWAYLF' => $formContactWAYLF->createView(), // What Are You Looking For form
                'departments' => $departments,
                'nbTotal' => $searchResult->total(),
                'query' => $text,
                'activeSearchFacets' => $activeSearchFacets,
                'searchInCatalog' => ($searchType === SearchBarForm::SEARCH_TYPE_IN_CATALOG),
                'searchInCompatibleProducts' => ($searchType === SearchBarForm::SEARCH_TYPE_IN_PRODUCT_COMPATIBILITY),
                'offers' => $offers,
                'bafv' => $bafv,
                'contactMerchantToAskPriceForm' => $contactMerchantToAskPriceForm,
                'contactMerchantToAskPriceForNoPriceOfferForm' => $contactMerchantToAskPriceForNoPriceOfferForm,
                'pagination' => [
                    'current_page' => $searchResult->currentPage(),
                    'pages' => $searchResult->pages()
                ],
                'retour_dept' => ($searchResult->getCategory()) ? $searchResult->getParentCategory() ?? '' : null,
                'retour_dept_name' => ($searchResult->getCategory()) ? $categoryService->getCategorieName($searchResult->getCategory()->getId()) : null,
                'isAnonymous' => $isAnonymous,
                'user' => $user,
                'category' => $searchResult->getCategory(),
                'hitsPerPage' => $hitsPerPage,
                'adv_mobile' => $adv_mobile,
                'display' => $display,
            ]
        );
    }

    private function autocompleteCatalogSearch(
        int $companyId,
        string $text,
        CompanyCatalogRepository $companyCatalogRepository
    ): JsonResponse
    {
        $mainOfferTitle = '';
        $mainOfferCategory = '';
        $mainOfferCategoryName = '';
        $offerTitles = [];

        $references = $companyCatalogRepository->findByCompanyIdAndReference(
            $companyId,
            $text,
            false
        );

        if (count($references)) {
            $references = array_map(function(CompanyCatalog $reference) {
                return $reference->getRef();
            }, $references);

            $mainOfferTitle = array_shift($references);
            $offerTitles = $references;
        }

        return new JsonResponse(new AutocompleteOfferResponse($mainOfferTitle, $mainOfferCategory, $mainOfferCategoryName, $offerTitles));
    }

    private function autoCompleteMarketPlaceSearch(
        string $text,
        string $locale,
        SearchService $searchService
    ): JsonResponse
    {
        $queries = $searchService->findQuerySuggestionFrom($text, $locale);

        return new JsonResponse($queries);
    }
}
