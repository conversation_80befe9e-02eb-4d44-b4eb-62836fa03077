<?php

namespace Open\FrontBundle\Controller;

use Api\Domain\CostCenter\Message\CostCenterPayloadMessage;
use AppBundle\Controller\MkoController;
use AppBundle\Entity\Address;
use AppBundle\Entity\Company;
use AppBundle\Entity\Middleware\CostCenterPayload;
use AppBundle\Entity\ShippingPoint;
use AppBundle\Form\Type\ShippingPointType;
use AppBundle\Services\AddressService;
use AppBundle\Services\ShippingPointService;
use AppBundle\Services\SiteService;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Contracts\Translation\TranslatorInterface;

class ShippingPointController extends MkoController
{
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/site/{site_id}/shipping_point/{id}/edit', name: 'front.shipping_point.edit')]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/site/{site_id}/shipping_point/create', name: 'front.shipping_point.create')]
    public function editAction(
        Request $request,
        SiteService $siteService,
        ShippingPointService $shippingPointService,
        AddressService $addressService,
        TranslatorInterface $translator,
        MessageBusInterface $messageBus,
        $site_id,
        $id = null
    ) {
        /** @var Company $company */
        $company = $this->getCompany();

        if (!$company->canAccessSites()) {
            throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_COMPANY_NOT_VALID));
        }

        /** @var ShippingPoint $shippingPoint */
        $shippingPoint = ($id) ? $shippingPointService->get($id) : new ShippingPoint();

        $databaseAddress = null;
        if ($shippingPoint->getAddress() !== null) {
            /** @var Address $databaseAddress */
            $databaseAddress = clone $addressService->get($shippingPoint->getAddress()->getId());
        }
        /////////////////////////////////////////////////////////
        //security checks
        /////////////////////////////////////////////////////////

        //check site access
        $site = $siteService->get($site_id);
        if ($site !== null && $this->getUser()->getCompany()->getId() !== $site->getCompany()->getId()) {
            $this->logger->error(
                "user try to access a site that he doesn't own. Move to state ACCESS_DENIED",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::SECURITY_EVENT,
                    LogUtil::USER_NAME => $this->getUsername(),
                    "requestedSiteId" => $site_id
                ])
            );
            throw $this->createAccessDeniedException($translator->trans(self::ACTION_DENIED_EXCEPTION));
        }

        //check shipping point access
        if ($id && $shippingPoint->getSite()->getCompany()->getId() !== $this->getUser()->getCompany()->getId()) {
            $this->logger->error(
                "user try to access a shipping point that he doesn't own. Move to state ACCESS_DENIED",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::SECURITY_EVENT,
                    LogUtil::USER_NAME => $this->getUsername(),
                    "requestedShippingPointId" => $id,
                ])
            );
            throw $this->createAccessDeniedException($translator->trans(self::ACTION_DENIED_EXCEPTION));
        }

        $form = $this->createForm(
            ShippingPointType::class,
            $shippingPoint
        );

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $shippingPoint->setSite($site);

            // force address country = MainAdress coutry de la company
            if ($shippingPoint->getAddress()) {
                $shippingPoint->getAddress()->setCountry($company->getMainAddress()->getCountry());
            }

            // modify IZb adress if exist
            if ($shippingPoint->getAddress()->getIzbergAddressId() !== null) {
                $address = $shippingPoint->getAddress();
                if ($databaseAddress !== null && !$address->isEqual($databaseAddress)) {
                    $oldAddress = $address;
                    $newAddress = new Address();
                    $newAddress->createFromAddress($address);
                    $oldAddress->createFromAddress($databaseAddress);
                    $shippingPoint->setAddress($newAddress);
                }
            }

            // Save it
            $entityManager = $this->doctrine->getManager();
            $entityManager->persist($shippingPoint);
            $entityManager->flush();

            return $this->redirectToRoute('front.company.sites');
        }

        $templateVariables = [
            'form' => $form->createView(),
            'company' => $company,
            'shippingPoint' => $shippingPoint,
            'siteId' => $site_id,
            'locale' => $request->getLocale()
        ];

        if ($id) {
            $templateVariables['shippingId'] = $id;
        }

        return $this->render(
            '@OpenFront/shipping_point/index.html.twig',
            $templateVariables
        );
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/shipping_point/{id}/delete', name: 'front.shipping_point.delete')]
    public function deleteAction(
        ShippingPointService $shippingPointService,
        SiteService $siteService,
        MessageBusInterface $messageBus,
        $id = 0
    ): RedirectResponse
    {
        $shippingPoint = $shippingPointService->get($id);

        if (!$shippingPoint) {
            return $this->redirectToRoute('front.company.sites');
        }

        /** @var Company $company */
        $company = $this->getCompany();
        $shippingSiteId = $shippingPoint->getSite()->getId();

        // On n'a pas de droit sur ce shipping point
        if (!($company->getId() == $shippingPoint->getSite()->getCompany()->getId())) {
            return $this->redirectToRoute('front.company.sites');
        }

        // Ici on est OK
        $shippingPointService->delete($shippingPoint);

        $siteService->ensureHasAtLeastOneSiteAttached($company);

        return $this->redirectToRoute('front.company.sites');
    }
}
