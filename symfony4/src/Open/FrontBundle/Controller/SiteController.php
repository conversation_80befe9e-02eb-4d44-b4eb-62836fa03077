<?php

namespace Open\FrontBundle\Controller;

use Api\Domain\CostCenter\Message\CostCenterPayloadMessage;
use Api\Domain\CostCenter\Message\DeleteCostCenterPayloadMessage;
use AppBundle\Controller\MkoController;
use AppBundle\Entity\Address;
use AppBundle\Entity\Company;
use AppBundle\Entity\Document;
use AppBundle\Entity\Middleware\CostCenterPayload;
use AppBundle\Entity\ShippingPoint;
use AppBundle\Entity\Site;
use AppBundle\Entity\User;
use AppBundle\Form\SiteForm;
use AppBundle\Services\SiteService;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Contracts\Translation\TranslatorInterface;

class SiteController extends MkoController
{
    const REPO_SITE = \AppBundle\Entity\Site::class;

    const GLOBALE = "global";

    const FILES = "files";

    const NOT_AUTHORIZED = "you are not authorized to perform this action";

    const COMPANY = 'company';

    const TABACTIVE = 'tab_active';

    const SUCCESS = 'success';

    const ERROR = 'error';


    /**
     * @param SiteService $siteService
     *
     * @return Response|null
     * @throws ORMException
     * @throws OptimisticLockException
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/sites', name: 'front.company.sites')]
    public function getList(SiteService $siteService, TranslatorInterface $translator)
    {

        /** @var Company $company  */
        $company = $this->getCompany();
        $hasApiUser = $company->hasUserApi();

        $user = $this->getUser();

        if (!$company->canAccessSites()) {
            throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_COMPANY_NOT_VALID));
        }

        $siteService->ensureHasAtLeastOneSiteAttached($company);

        $site = new Site();
        $form = $this->createForm(SiteForm::class, $site, [
            "isCreation" => true,
            "companyId" => $company->getId(),
            'action' => $this->generateUrl('front.site.new'),
            'method' => 'POST',
            'hasUserApi' => $hasApiUser,
        ])->remove('save');

        $action = true;

        // Role autre que responsable de compte => lecture seule
        if (!$this->isGranted(self::ROLE_BUYER_ADMIN)) {
            $action = false;
        }
        /** @var Site $site */
        foreach ($company->getSites() as $site) {
            $users = $siteService->getUsersOfOneSite($site->getId());
            $site->setUsers($users);
        }


        return $this->render(
            '@OpenFront/site/list.html.twig',
            array(
                'form' => $form->createView(),
                'sites' => $company->getSites(),
                'current_user' => $user,
                self::COMPANY => $company,
                self::TABACTIVE => 3,
                'step' => 2,
                'action' => $action
            ));
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/site/new', name: 'front.site.new')]
    public function intermediateSaveAction(
        Request $request,
        TranslatorInterface $translator,
        MessageBusInterface $messageBus
    ): RedirectResponse
    {

        /** @var User $user */
        $user = $this->getUser();

        /** @var EntityManager $em */
        $em = $this->doctrine->getManager();

        /** @var Company $company */
        $company = $user->getCompany();
        $hasUserApi = $company->hasUserApi();


        if (!$company->canAccessSites()) {
            throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_COMPANY_NOT_VALID));
        }

        $site = new Site();
        $form = $this->createForm(SiteForm::class, $site, [
            'isCreation' => true,
            'companyId' => $company->getId(),
            'action' => $this->generateUrl('front.site.new'),
            'method' => 'POST',
            'hasUserApi' => $hasUserApi,
        ])->remove('save');

        $form->handleRequest($request);

        if ($form->isSubmitted() && !$form->isValid()) {
            $this->addFlash(
                self::ERROR,
                $translator->trans('site.form.error', [], self::TRANSLATION_DOMAIN)
            );
        }

        if ($form->isSubmitted() && $form->isValid()) {
            // assign company of the current user
            $site->setCompany($company);

            // persist
            $em->persist($site);
            $em->flush();

            // Add success message to session
            $this->addFlash(
                self::SUCCESS,
                $translator->trans('site.form.created', array(),
                    self::TRANSLATION_DOMAIN)
            );
        }

        // Redirect to newly created site
        return $this->redirectToRoute('front.company.sites',
            array('id' => $site->getId()));
    }

    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/site/{id}/edit', name: 'front.site.edit')]
    public function editAction(
        Request $request,
        TranslatorInterface $translator,
        MessageBusInterface $messageBus,
        $id = 0
    ): Response
    {
        /** @var User $user */
        $user = $this->getUser();

        /** @var EntityManager $em */
        $em = $this->doctrine->getManager();

        /** @var Company $company */
        $company = $this->getCompany();

        if (!$company->canAccessSites()) {
            throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_COMPANY_NOT_VALID));
        }

        /** @var Site $site */
        $site = $em->getRepository(self::REPO_SITE)->find($id);

        //security check
        if ($site == null || $site->getCompany()->getId() != $company->getId()) {
            throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_EXCEPTION));
        }

        $shippingPoints = new ArrayCollection();
        foreach ($site->getShippingPoints() as $shippingPoint) {
            $shippingPoints->add($shippingPoint);
        }


        // All users for this site except logged in user
        $siteUsers = [];

        /** @var User $other_user */
        foreach ($company->getUsers() as $other_user) {
            $siteUsers[] = $other_user;
        }

        /** @var  Address */
        $address = $company->getMainAddress();
        $validation_group = array("Default");
        if ($company->getMainAddress()->getCountry()->getCode() == "france") {
            $validation_group[] = "france";
        }

        $options = array(
            "companyId" => $company->getId(),
            "country" => $address->getCountry()->getId(),
            'hasUserApi' => $company->hasUserApi(),
            "validation_groups" => $validation_group,
        );

        $disabled = false;

        // Role autre que responsable de compte => lecture seule
        if (!$this->isGranted(self::ROLE_BUYER_ADMIN)) {
            $options['disabled'] = true;
            $disabled = true;
        }

        $form = $this->createForm(
            SiteForm::class,
            $site,
            $options
        );

        if ($disabled) {
            $form->remove('save');
            $form->remove('submit');
        }

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            {
                /**
                 * security checks
                 */
                if ($this->getUser()->getCompany()->getId() !== $site->getCompany()->getId()) {
                    throw $this->createAccessDeniedException($translator->trans(self::ACTION_DENIED_EXCEPTION));
                }

                // Role autre que responsable de compte => lecture seule
                if (!$this->isGranted(self::ROLE_BUYER_ADMIN)) {
                    throw $this->createAccessDeniedException($translator->trans(self::ACTION_DENIED_EXCEPTION));
                }

                try {
                    $this->logger->info(
                        $translator->trans('log.site.submit'),
                        LogUtil::buildContext([
                            LogUtil::EVENT_NAME => EventNameEnum::SITE_SUBMIT,
                            LogUtil::USER_NAME => $user->getUsername(),
                            'id' => $user->getId()
                        ])
                    );

                    foreach ($shippingPoints as $shippingPoint) {
                        if (false === $site->getShippingPoints()->contains($shippingPoint)) {
                            /** @var ShippingPoint $shippingPoint */
                            $s = $shippingPoint->getSite();
                            /** @var Site $s */
                            $s->removeShippingPoint($shippingPoint);

                            $em->remove($shippingPoint);
                        }
                    }

                    $this->addFlash(self::SUCCESS,
                        $translator->trans('back.site.modification.ok', array(),
                            self::TRANSLATION_DOMAIN));

                    $site->setCompany($company);

                    $em->merge($site);
                    $em->flush();

                } catch (\Exception $e) {
                    $this->logger
                        ->error("error while saving site: " . $e->getMessage(),
                            LogUtil::buildContext([
                                LogUtil::EVENT_NAME => EventNameEnum::TECHNICAL_ERROR,
                                LogUtil::USER_NAME => $this->getUser()->getUsername(),
                                'site_id' => $site->getId(),
                                'exception' => $e,
                            ]));
                    $this->addFlash(self::ERROR,
                        $translator->trans('back.site.modification.ko', array(),
                            self::TRANSLATION_DOMAIN));
                }

                return $this->redirectToRoute('front.company.sites');
            }
        }

        // Render view
        return $this->render(
            '@OpenFront/site/edit.html.twig',
            array(
                'form' => $form->createView(),
                'site_id' => $id,
                self::COMPANY => $company,
                'sites' => $company->getSites(),
                self::TABACTIVE => 3,
                'isDisabled' => $disabled,
                'sizeMax' => Document::SIZE_CONSTRAINT,
                'typeError' => $translator->trans('document.upload.mime',
                    array('%contrainte%' => implode(", ", Document::MIME_CONSTRAINT)),
                    self::TRANSLATION_DOMAIN),
                'sizeError' => $translator->trans('document.upload.size',
                    array('%contrainte%' => Document::SIZE_CONSTRAINT / (1024 * 1024)),
                    self::TRANSLATION_DOMAIN),
                'submitError' => $translator->trans('site.form.submitError', array(),
                    self::TRANSLATION_DOMAIN)
            )
        );
    }

    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/site/{id}/deactivate', name: 'front.site.deactivate')]
    public function deactivateSite(
        SiteService $siteService,
        TranslatorInterface $translator,
        MessageBusInterface $messageBus,
        $id
    ): RedirectResponse
    {
        if (!$siteService->canDelete($id)) {
            $this->addFlash(self::ERROR, $translator->trans('site.list.deactivation.users_exist', array(), self::TRANSLATION_DOMAIN));
            return $this->redirectToRoute('front.company.sites');
        }

        try {
            /** @var SiteService $site_service */
            $siteService->delete($id);

            $siteService->ensureHasAtLeastOneSiteAttached($this->getCompany());


            $this->addFlash(self::SUCCESS, $translator->trans('site.list.deactivation.ok', array(), self::TRANSLATION_DOMAIN));
        } catch (\Exception $e) {

            $this->addFlash(self::ERROR, $translator->trans('site.list.deactivation.ko', array(), self::TRANSLATION_DOMAIN));
        }

        return $this->redirectToRoute('front.company.sites');
    }
}
