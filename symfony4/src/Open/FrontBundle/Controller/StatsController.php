<?php

namespace Open\FrontBundle\Controller;

use App<PERSON><PERSON>le\Controller\MkoController;
use AppBundle\Services\OrderService;
use AppBundle\Services\SecurityService;
use AppBundle\Services\SiteService;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

class StatsController extends MkoController
{
    /**
     * @param Request         $request
     * @param SecurityService $securityService
     * @param SiteService     $siteService
     *
     * @param OrderService    $orderService
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/dashboard/stats', name: 'dashboard.stats')]
    public function getStatsAction(Request $request, SecurityService $securityService, SiteService $siteService, OrderService $orderService)
    {

        $user = $this->getUser();
        $company = $this->getCompany();

        $sites = [];

        // Find sites
        if(!is_null($company)) {
            //if user is buyer admin, he can all sites
            if ($securityService->isAdminCompany($user)) {
                $sites = $siteService->getSitesWithAtLeastOneAddressByCompany($user->getCompany()->getId());
            } else {
                $sites = $siteService->getUserSitesWithAtLeastOneAddress($user);
            }
        }


        $years = [];

        $yearsDB = $orderService->getYears($company->getId());
        foreach($yearsDB as $yearDB){
            $years[] = $yearDB['year'];
        }

        if(count($years) == 0){
            $years[] = date("Y");
        }

        return $this->render(
            '@OpenFront/stats/stats.html.twig', [
                'sites' => $sites,
                'years' => $years,
                'locale' => $request->getLocale()
            ]
        );
    }

    /**
     * @param Request      $request
     * @param OrderService $orderService
     * @return mixed
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/stats/orderAmount', name: 'stats.orderAmount')]
    public function getOrderAmountEURAction(Request $request, OrderService $orderService){

        $cumul = $request->query->get('cumul');
        $siteId = $request->query->get('siteId');
        $year = $request->query->get('year');
        $locale = $request->query->get('locale');
        $currency = $request->query->get('currency');

        $company = $this->getCompany();

        $orderStats = $orderService->getStats($company->getId(), $currency, $year, $siteId !== "" ? $siteId : null);

        $orderAmountByMonthLabel = [];
        $orderAmountByMonthValue = [];

        // Remplissage pour les mois passés sans valeurs
        $i = 1;
        if($year === date('Y')){
            while($i <= date('m')) {
                $orderAmountByMonthLabel[] = $this->convertMonth($i, $locale);
                $orderAmountByMonthValue[] = 0;
                $i++;
            }
        }else{
            while($i <= 12) {
                $orderAmountByMonthLabel[] = $this->convertMonth($i, $locale);
                $orderAmountByMonthValue[] = 0;
                $i++;
            }
        }


        foreach ($orderStats as $orderStat) {
            $month = intval($orderStat['month'] - 1);
            $orderAmountByMonthValue[$month] = $orderStat['amount'];
        }

        $data = [];
        $data['label'] = $orderAmountByMonthLabel;

        if($cumul === 'yes'){
            $offerOnlineByMonthValueCumul = [];
            $total = 0;
            foreach ($orderAmountByMonthValue as $t) {
                $total = $total +$t;
                $offerOnlineByMonthValueCumul[]= $total;
            }
            $data['value'] = $offerOnlineByMonthValueCumul;
        }else{
            $data['value'] = $orderAmountByMonthValue;
        }

        return new JsonResponse($data);
    }

    private function convertMonth($month, $locale){

        if($locale == 'fr'){
            $months = ['', 'Jan', 'Fev', 'Mars', 'Avr', 'Mai', 'Juin', 'Juil', 'Aout', 'Sept', 'Oct', 'Nov', 'Dec'];
        }else{
            $months = ['', 'Jan', 'Feb', 'March', 'Apr', 'May', 'June', 'July', 'Aug', 'Sept', 'Oct', 'Nov', 'Dec'];
        }

        return $months[intval($month)];
    }
}

