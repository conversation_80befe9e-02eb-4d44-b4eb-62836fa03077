<?php

namespace Open\FrontBundle\Controller;


use AppBundle\Controller\MkoController;
use AppBundle\Entity\User;
use AppBundle\Entity\Company;
use AppBundle\Repository\CompanyRepository;
use Doctrine\ORM\EntityManager;
use FOS\UserBundle\Event\FormEvent;
use FOS\UserBundle\Model\UserManagerInterface;
use Open\FrontBundle\Event\RegistrationFormEvent;
use Open\FrontBundle\Form\UserForm;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Contracts\Translation\TranslatorInterface;


class UserController extends MkoController
{
    const TRANSLATION_DOMAIN = 'AppBundle';
    const USER_BUNDLE = \AppBundle\Entity\User::class;
    const SUCCESS = 'success';

	/**
	 * create a fake generated password
	 * @return string the generated fake password
	 */
	private function createFakePassword()
	{
		// This array must match regexp in AppBundle\Validator\Constraints\SecurePasswordValidator #line 34
		$schars = array('!', '@', '#', '$', '%', '^', '*', '_', '-');

		$password = substr(md5(uniqid(strval(rand()), true)), 0, 15);

		if (strtolower($password) === $password) {
			$password .= chr(rand(65, 90));
		}

		return $password . $schars[array_rand($schars)];
	}

    /**
     * get number of enabled users to redirect to user creation form or the user list
     * @param $users
     * @return int the generated fake password
     */
	private function getNumberOfEnabledUsers($users) :int
    {
	    $number = 0;

        foreach($users as $user) {
            if ($user->isEnabled() && $user->getId() != $this->getUser()->getId()) {
                $number ++;
            }
        }

        return $number;
    }

    /**
     * get users sorted by role
     * @param $users
     * @return array of sorted users
     */
    private function getSortedUsers($users) {
        $admin = $buyer = $requestor = [];

        foreach ($users as $user) {
            if ($userRoles = $user->getRoles()) {
                if ($userRoles[0] == self::ROLE_BUYER_ADMIN) {
                    $admin[] = $user;
                } else if ($userRoles[0] == self::ROLE_BUYER_PAYER) {
                    $buyer[] = $user;
                } else {
                    $requestor[] = $user;
                }
            }
        }

        return array_merge($admin, $buyer, $requestor);
    }

    /**
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/users', name: 'front.company.users')]
    public function getList(TranslatorInterface $translator)
    {
        $action = true;

        // Role autre que responsable de compte => lecture seule
        if (!$this->isGranted(self::ROLE_BUYER_ADMIN)) {
            $action = false;
        }

        $company= $this->getCompany();

        if(!$company->canAccessUsers()){
            throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_COMPANY_NOT_VALID));
        }

        $users = $this->getSortedUsers(
            $company->getUsers()->filter(fn (User $user) => ($user->isEnabled() && $user->isBuyerApi() === false))
        );
        return $this->render(
            '@OpenFront/user/list.html.twig',
            array(
                'Users' => $users,
                'user_id' => $this->getUser()->getId(),
                'company' => $company,
                'step' => 2,
                'tab_active' => 4,
                'action' => $action
            ));
    }

    /**
  * @param Request                       $request
  * @param UserManagerInterface          $userManager
  * @param EventDispatcherInterface      $eventDispatcher
  * @param int                           $id the identifier of the user to edit. If 0, create a new user
  * @param bool                          $ajax
  *
  * @return mixed
  */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
 #[\Symfony\Component\Routing\Attribute\Route(path: '/company/user/new', name: 'front.user.new')]
 #[\Symfony\Component\Routing\Attribute\Route(path: '/company/user/{id}/edit', name: 'front.user.edit')]
 public function userAction(Request $request, UserManagerInterface $userManager, EventDispatcherInterface $eventDispatcher, TranslatorInterface $translator, $id = 0, $ajax = false)
	{
	    $ajax = $request->query->getBoolean('ajax', $ajax);

        /** @var EntityManager $em */
        $em = $this->doctrine->getManager();

        /**
         * security Checks
         */

        $this->securityCheck($id);

		/**
		 * END of security checks
		 */

        /** @var Company $company */
        $company = $this->getUser()->getCompany();

        if(!$company->canAccessUsers()){
            throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_COMPANY_NOT_VALID));
        }

        // if this is a user creation
		if ($id === 0) {

            // Role autre que responsable de compte => lecture seule
            if (!$this->isGranted(self::ROLE_BUYER_ADMIN)) {
                throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_EXCEPTION));
            }

            /** @var User $user */
            $user = $userManager->createUser();
            $user->setEnabled(true);
            $password = $this->createFakePassword();
            $user->setPassword($password);
            $user->setPlainPassword($password);
            $user->setUserName($password);
            $user->setLocale($this->getUser()->getLocale());
        // if this is a user edition
		} else {
		    $user = $em->getRepository(self::USER_BUNDLE)->find($id);
			if ($user->getRoles() != null && !empty($user->getRoles())) {
                $user->setRole($user->getRoles()[0]);
			}
		}

		//if user was not found
		if (!$user) {
			throw $this->createNotFoundException($translator->trans(self::NOT_FOUND_EXCEPTION));
		}

		$disabled = false;

        // Role autre que responsable de compte => lecture seule
        if (!$this->isGranted(self::ROLE_BUYER_ADMIN)) {
            $disabled = true;
        }

        $groups = ['user', 'Default', 'user_registration', 'user_sites'];

        /** @var  FormInterface $form */
		$form = $this->createForm(
		    UserForm::class,
            $user,
            [
                'is_new' => $user->getId() == 0,
                'company_id' => $company->getId(),
                'disabled' => $disabled,
                'validation_groups' => $groups,
            ]
        );

		// Add a submit button with styles
        if(!$disabled){
            $form->add(
                'save',
                SubmitType::class,
                array(
                    'label' => ($id == 0) ? 'form.user.save_new' : 'form.user.save_edit',
                    'attr' => array(
                        'class' => 'button_margin'
                    )
                )
            );
        }

		$form->handleRequest($request);

		if ($form->isSubmitted() && $form->isValid()) {

		    //in case of user creation, check if email already exists
            if ($id === 0 && $userManager->findUserByUsernameOrEmail($user->getEmail()) !== null){
                $form->get('email')->addError(new FormError($translator->trans('fos_user.email.already_used',[], 'validators')));
            }
            else {
                $role = $user->getRole();

                /**
                 * security checks
                 */
                if ($this->getUser()->getCompany()->getId() !== $this->getCompany()->getId()) {
                    throw $this->createAccessDeniedException($translator->trans(self::ACTION_DENIED_EXCEPTION));
                }


                // Role autre que responsable de compte => lecture seule
                if (!$this->isGranted(self::ROLE_BUYER_ADMIN)) {
                    throw $this->createAccessDeniedException($translator->trans(self::ACTION_DENIED_EXCEPTION));
                }

                if ($id === 0) {
                    $username = uniqid('user_');
                    $user->setUsername($username);
                    $user->setUsernameCanonical($username);
                    $event = new FormEvent($form, $request);
                    $event->getForm()->getData()->setCompany($this->getUser()->getCompany());
                    $eventDispatcher->dispatch($event);
                }

                // This will break if we add other roles to a user
                $user->setRoles([$role]);

                // Buyer admins do not have site associated
                if ($role == self::ROLE_BUYER_ADMIN) {
                    $user->setSites(null);
                }

                //set the same company as the owner
                $user->setCompany($this->getUser()->getCompany());

                $userManager->updateUser($user);

                if ($id > 0) {
                    $this->addFlash(self::SUCCESS, $translator->trans('form.user.success_update', [], 'AppBundle'));
                } else {
                    $this->addFlash(self::SUCCESS, $translator->trans('form.user.success_new', [], 'AppBundle'));
                }

                if ($ajax) {
                    $response = new Response();
                    $response->setContent('success');
                    return $response;
                }

                return $this->redirectToRoute('front.company.users');
            }
		}

		// Render view
		return $this->render(
            ($ajax) ? '@OpenFront/user/ajax/index.html.twig' : '@OpenFront/user/index.html.twig',
			array(
				'form' => $form->createView(),
				'company'=> $company,
				'user_id' => $id,
                'tab_active' => 4,
				'admin_role' => self::ROLE_BUYER_ADMIN,
                'isDisabled' => $disabled
			)
		);
	}

    /**
     * @param                     $id
     * @param TranslatorInterface $translator
     *
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/company/users/{id}/deactivate', name: 'front.user.deactivate')]
    public function deactivateUser($id, TranslatorInterface $translator){

        $this->securityCheck($id);

        $em = $this->doctrine->getManager();

        $this->logger->info(
            "user has been disabled",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME => EventNameEnum::USER_DISABLE,
                LogUtil::USER_NAME => $this->getUser()->getUsername(),
                "USER_ID" => $id,
            ])
        );

        /** @var User $user */
        $user = $em->getRepository(self::USER_BUNDLE)->find($id);

        $user->setEnabled(false);
        $user->setDisabledAt(new \DateTime());

        try {
            $em->merge($user);
            $em->flush();
            $this->addFlash(self::SUCCESS, $translator->trans('back.user.form.deactivation.ok',array(), self::TRANSLATION_DOMAIN));
        } catch (\Exception $e) {
            $this->addFlash('error', $translator->trans('back.user.form.deactivation.ko',array(), self::TRANSLATION_DOMAIN));
        }

        return $this->redirectToRoute('front.company.users');
    }


    /**
     * @param int $id the identifier of the user
     * @throws AccessDeniedException
     */
    private function securityCheck($id)
    {
        /** @var User $user */
        $user = $this->getUser();

        /** @var EntityManager $em */
        $em = $this->doctrine->getManager();

        if ($id !== 0) {
            /** @var CompanyRepository $companyRepository */
            $companyRepository = $em->getRepository(Company::class);
            if ($companyRepository->isUserExistsInCompany($user->getCompany()->getId(), $id) == null || $user->getId() == $id) {
                throw $this->createAccessDeniedException("you are not authorized to perform this action");
            }
        }
    }
}
