<?php

namespace Open\FrontBundle\Controller;

use App<PERSON><PERSON>le\Controller\MkoController;
use AppBundle\Entity\WishListItem;
use AppBundle\Model\DetailedOffer;
use AppBundle\Services\CartService;
use AppBundle\Services\OfferService;
use AppBundle\Services\SpecificPriceService;
use AppBundle\Services\WishListService;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Contracts\Translation\TranslatorInterface;

class WishListController extends MkoController
{
    /**
     * @param Request             $request
     * @param WishListService     $wishListService
     *
     * @param CartService         $cartService
     *
     * @param TranslatorInterface $translator
     * @return JsonResponse|Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/wishlist/save-from-cart', name: 'wishlist.save_from_cart', methods: 'post')]
    public function saveWishListFromCart(Request $request, WishListService $wishListService, CartService $cartService, TranslatorInterface $translator)
    {
        $user = $this->getUser();

        $wishListForm = $this->createFormBuilder(null, ['csrf_protection' => false,])
            ->add(
                'wishListId',
                HiddenType::class
            )
            ->add(
                'name',
                TextType::class
            )
            ->add(
                'cartId',
                HiddenType::class
            )
            ->add(
                'currency',
                HiddenType::class
            )
            ->getForm();

        $wishListForm->handleRequest($request);
        if ($wishListForm->isSubmitted() && $wishListForm->isValid()) {
            $formData = $wishListForm->getData();
            $cart = $cartService->findCart($user, $formData['cartId']);

            if ($formData['wishListId']) {
                $wishList = $wishListService->overwriteWishList($user, $formData['wishListId'], WishListService::offersFromCart($cart));
            } else {
                $wishList = $wishListService->createWishList($user, $formData['name'], $formData['currency'], WishListService::offersFromCart($cart));
            }

            if (!$wishList) {
                $this->addFlash('error', $translator->trans('wishlist.save_error',[], 'AppBundle'));
                return new Response('', 404);
            }

            $this->addFlash('success', $translator->trans('wishlist.save_success',[], 'AppBundle'));

            return new JsonResponse(['id' => $wishList->getId()]);
        }

        return new Response();
    }

    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/wishlist/save-offer-quantity', name: 'wishlist.save_offer_quantity', methods: 'post')]
    public function saveWishListOfferQuantity(Request $request, WishListService $wishListService, TranslatorInterface $translator)
    {
        $wishListForm = $this->createFormBuilder(null, ['csrf_protection' => false,])
            ->add(
                'wishListId',
                HiddenType::class
            )
            ->add(
                'offerId',
                HiddenType::class
            )
            ->add(
                'quantity',
                HiddenType::class
            )
            ->add(
                'name',
                HiddenType::class
            )
            ->add(
                'currency',
                HiddenType::class
            )
            ->getForm();


        $wishListForm->handleRequest($request);

        if ($wishListForm->isSubmitted() && $wishListForm->isValid()) {
            $formData = $wishListForm->getData();

            //security check
            if (!$wishListService->userCanAccessWishListId($this->getUser(), $formData['wishListId'])) {
                throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_EXCEPTION));
            }

            $wishList = $wishListService->addOfferQuantityToWishList(
                $this->getUser(),
                $formData['offerId'],
                $formData['quantity'],
                $formData['wishListId'],
                $formData['name'],
                $formData['currency']
            );

            if (!$wishList) {
                return new Response('', 404);
            }

            return new JsonResponse(['id' => $wishList->getId()]);
        }

        return new Response('', 400);
    }

    /**
     * @param Request         $request
     * @param WishListService $wishListService
     * @return JsonResponse|Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/wishlist/create', name: 'wishlist.create', methods: 'post')]
    public function createWishList(Request $request, WishListService $wishListService)
    {
        $user = $this->getUser();

        $wishListForm = $this->createFormBuilder(null, ['csrf_protection' => false,])
            ->add(
                'name',
                TextType::class
            )
            ->add(
                'currency',
                HiddenType::class
            )
            ->getForm();


        $wishListForm->handleRequest($request);

        if ($wishListForm->isSubmitted() && $wishListForm->isValid()) {
            $formData = $wishListForm->getData();
            $wishList = $wishListService->createWishList($user, $formData['name'], $formData['currency'], []);

            return new JsonResponse([
                'id' => $wishList->getId(),
                'name' => $wishList->getName(),
            ]);
        }

        return new Response();
    }

    /**
     * @param Request             $request
     * @param WishListService     $wishListService
     *
     * @param TranslatorInterface $translator
     * @return JsonResponse
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/wishlist/delete', name: 'wishlist.delete', methods: ['POST', 'DELETE'])]
    public function deleteWishlist(Request $request, WishListService $wishListService, TranslatorInterface $translator):JsonResponse
    {
        $wishListId = $request->request->get('wishListId');

        //security check
        if (!$wishListService->userCanAccessWishListId($this->getUser(), $wishListId)) {
            throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_EXCEPTION));
        }

        $wishListService->deleteWishList($wishListId);

        return new JsonResponse();
    }

    /**
     * @param Request             $request
     * @param WishListService     $wishListService
     *
     * @param TranslatorInterface $translator
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/wishlist/item/delete', name: 'wishlist.item.delete', methods: ['POST', 'DELETE'])]
    public function deleteWishlistItem(Request $request, WishListService $wishListService, TranslatorInterface $translator)
    {

        $itemId = $request->request->get('itemId');

        //security check
        if (!$wishListService->userCanAccessWishListItemId($this->getUser(), $itemId)) {
            throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_EXCEPTION));
        }

        $wishListService->deleteWishListItem($itemId);

        return new Response();
    }

    /**
     * @param Request              $request
     * @param WishListService      $wishListService
     *
     * @param OfferService         $offerService
     *
     * @param SpecificPriceService $specificPriceService
     *
     * @param TranslatorInterface  $translator
     * @return mixed
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/wishlist/item/update', name: 'wishlist.item.update', methods: ['POST'])]
    public function updateWishlistItem(Request $request, WishListService $wishListService, OfferService $offerService, SpecificPriceService $specificPriceService, TranslatorInterface $translator)
    {

        $itemId = $request->request->get('itemId');
        $offerId = $request->request->get('offerId');
        $quantity = $request->request->get('quantity');

        //security check
        if (!$wishListService->userCanAccessWishListItemId($this->getUser(), $itemId)) {
            throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_EXCEPTION));
        }

        try{
            /**
             * @var DetailedOffer $offer
             */
            $offer = $offerService->findDetailedOfferById($offerId, $this->getCompany());

            if($offer->getOffer()->getBatchSize() != null && $quantity % $offer->getOffer()->getBatchSize() > 0) {
                $response = new Response('', 500);
            }else{
                $offer = $specificPriceService->updateOfferSpecificPrices($this->getUser()->getCompany(), $offer->getOffer());

                $thresholds = $offer->getThresholds();
                $price = $offer->getPrice($offer->getCurrency());

                foreach ($thresholds as $key => $t){
                    if($quantity >= $key){
                        $price = $t;
                    }
                }
                $wishListService->updateWishListItem($itemId, $quantity);

                $response = new Response($price, 200);
            }
        } catch (\Exception $e){
            $response = new Response('', 500);
        }

        return $response;
    }

    /**
     * @param WishListService $wishListService
     * @return Response|null
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/wishlist/', name: 'front.wishlist')]
    public function getUserWishList(WishListService $wishListService)
    {
        $userWishList = $wishListService->getUserWishList($this->getUser()->getId());

        return $this->render(
            '@OpenFront/wishList/wish_list.html.twig',
            [
                'list' =>  $userWishList,
            ]
        );
    }

    /**
     * @param                      $wishlistId
     * @param WishListService      $wishListService
     * @param OfferService         $offerService
     *
     * @param SpecificPriceService $specificPriceService
     * @param TranslatorInterface  $translator
     * @return Response|null
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/wishlist/{wishlistId}', name: 'front.wishlist.details')]
    public function getWishList($wishlistId, WishListService $wishListService, OfferService $offerService, SpecificPriceService $specificPriceService, TranslatorInterface $translator)
    {
        $wishlistValid = true;

        //security check
        if (!$wishListService->userCanAccessWishListId($this->getUser(), $wishlistId)) {
            throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_EXCEPTION));
        }

        $wishlist = $wishListService->getWishList($wishlistId);
        $items = $wishlist->getItems();
        $offers = [];

        $totalPrice = 0;
        /** @var WishListItem $item */
        foreach($items as $item) {
            $offer = $offerService->findDetailedOfferById(intval($item->getOfferId()), $this->getCompany())->getOffer();
            $price = $specificPriceService->specificPrice($this->getCompany(), $offer, $item->getQuantity());

            if ($offer->hasNoPrice()) {
                $wishlistValid = false;
            }

            $item->setOffer($offer);
            $item->setPrice($price);

            $totalPrice = $totalPrice + ($item->getPrice() * $item->getQuantity());

            $offers[] = $item;
        }

        return $this->render(
            '@OpenFront/wishList/wish_list_details.html.twig',
            [
                'wishlist' => $wishlist,
                'offers' => $offers,
                'totalPrice' => $totalPrice,
                'wishlistValid' => $wishlistValid,
            ]
        );
    }

    /**
     * @param                 $wishlistId
     * @param WishListService $wishListService
     * @param CartService     $cartService
     *
     * @return Response
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/wishlist/charge/{wishlistId}', name: 'front.wishlist.charge')]
    public function chargeToCart($wishlistId, WishListService $wishListService, CartService $cartService, TranslatorInterface $translator)
    {
        //security check
        if (!$wishListService->userCanAccessWishListId($this->getUser(), $wishlistId)) {
            throw $this->createAccessDeniedException($translator->trans(self::ACCESS_DENIED_EXCEPTION));
        }

        $wishList = $wishListService->getWishList($wishlistId);
        $serviceResponse = $cartService->addWishListItemsToUserCart($wishList, $this->getUser());

        if ($serviceResponse->hasErrors()) {
            $serviceResponse->handleErrors(
                function (CartService\Response\Error\OfferError $error) use ($translator){
                    if ($error->isErrorStatus()) {
                        $this->addFlash(
                            'error',
                            $translator->trans(
                                'orders.createCart.errorStatus',
                                [ '%reference%'=>$error->getOffer()->getOfferTitle() ],
                                self::TRANSLATION_DOMAIN
                            )
                        );
                    }

                    if ($error->isErrorMoq()) {
                        $this->addFlash(
                            'error',
                            $translator->trans(
                                'orders.createCart.errorMoq',
                                [ '%reference%'=> $error->getOffer()->getOfferTitle() ],
                                self::TRANSLATION_DOMAIN
                            )
                        );
                    }

                    if ($error->isErrorStock()) {
                        $this->addFlash(
                            'error',
                            $translator->trans(
                                'orders.createCart.errorStock',
                                ['%reference%'=>$error->getOffer()->getOfferTitle()],
                                self::TRANSLATION_DOMAIN
                            )
                        );
                    }

                    if ($error->isErrorPrice()) {
                        $this->addFlash(
                            'error',
                            $translator->trans(
                                'orders.createCart.errorPrice',
                                [ '%reference%' => $error->getOffer()->getOfferTitle()],
                                self::TRANSLATION_DOMAIN
                            )
                        );
                    }
                },
                array_filter(
                    $serviceResponse->getErrors(),
                    function($error) {
                        return ($error instanceof CartService\Response\Error\OfferError);
                    }
                )
            );
        }

        if($serviceResponse->getTotal() != 0){
            return new Response($serviceResponse->getTotal(), 200);
        }

        return new Response('', 304);
    }
}
