<?php

namespace Open\FrontBundle\EventSubscriber;


use FOS\UserBundle\Event\GetResponseNullableUserEvent;
use FOS\UserBundle\FOSUserEvents;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class EmailResetSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;

    public static function getSubscribedEvents()
    {
        // Catch login actions to trigger logs actions
        return array(
            FOSUserEvents::RESETTING_SEND_EMAIL_INITIALIZE => 'onResettingEmailInitialize'
        );
    }

    public function onResettingEmailInitialize(GetResponseNullableUserEvent $event)
    {
        if (null === $event->getUser()){
            $this->logger->info(
                "anonymous user has requested email reset for an email that doesn't exist",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::SECURITY_EVENT,
                    "email" => $event->getRequest()->request->get("username")
                ])
            );
        }

    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
