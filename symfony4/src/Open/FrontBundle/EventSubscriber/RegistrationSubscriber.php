<?php


namespace Open\FrontBundle\EventSubscriber;

use AppBundle\Entity\Company;
use AppBundle\Entity\User;
use AppBundle\Services\CompanyService;
use AppBundle\Services\MailService;
use AppBundle\Services\SecurityService;
use Doctrine\ORM\ORMException;
use FOS\UserBundle\Event\FilterUserResponseEvent;
use FOS\UserBundle\Event\FormEvent;
use FOS\UserBundle\Event\UserEvent;
use FOS\UserBundle\FOSUserEvents;
use FOS\UserBundle\Model\UserManagerInterface;
use FOS\UserBundle\Util\TokenGeneratorInterface;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Session\Session;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class RegistrationSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    const FRONT_USER_FORM = 'front_user_form';

    private MailService $mailer;
    private RouterInterface $router;

    private UserManagerInterface $userManager;

    private TokenGeneratorInterface $tokenGenerator;

    private SessionInterface $session;
    private CompanyService $companyService;
    private RequestStack $requestStack;
    private TranslatorInterface $translator;
    private LoggerInterface $logger;
    private SecurityService $securityService;

    /**
     * RegistrationSubscriber constructor.
     * @param MailService $mailer
     * @param UserManagerInterface $user_manager
     * @param RouterInterface $router
     * @param TokenGeneratorInterface $tokenGenerator
     * @param CompanyService $companyService
     * @param RequestStack $requestStack
     * @param TranslatorInterface $translator
     * @param SecurityService $securityService
     */

    public function __construct(MailService $mailer,
                                UserManagerInterface $user_manager,
                                RouterInterface $router,
                                TokenGeneratorInterface $tokenGenerator,
                                CompanyService $companyService,
                                RequestStack $requestStack,
                                TranslatorInterface $translator,
                                SecurityService $securityService
    )
    {
        $this->mailer = $mailer;
        $this->userManager = $user_manager;
        $this->router = $router;
        $this->tokenGenerator = $tokenGenerator;
        $this->companyService = $companyService;
        $this->requestStack = $requestStack;
        $this->translator = $translator;
        $this->securityService = $securityService;
    }


    public static function getSubscribedEvents(): array
    {
        return array(
            FOSUserEVents::REGISTRATION_INITIALIZE => 'onRegistrationInitialize',
            FOSUserEvents::REGISTRATION_SUCCESS => 'onRegistrationSuccess',
            FOSUserEvents::REGISTRATION_CONFIRMED => 'onRegistrationConfirmed',
            FormEvent::class => 'onRegistrationSuccess'
        );
    }


    public function onRegistrationSuccess(FormEvent $event)
    {
        $this->session = $this->requestStack->getSession();
        /** @var User $user */
        $user = $event->getForm()->getData();

        if (null === $user->getConfirmationToken()) {
            $user->setConfirmationToken($this->tokenGenerator->generateToken());
        }

        // Find users preferred language from the browser
        $preferedLanguage = $this->requestStack->getCurrentRequest()->getPreferredLanguage(['en', 'fr', 'es', 'it', 'de', 'nl']);

        // Define user locale from the browser
        $user->setLocale($this->session->get('_locale', $preferedLanguage));

        $formData = $event->getForm()->all();
        if ($event->getForm()->getName() == self::FRONT_USER_FORM || $event->getForm()->getName() == 'user_form') {
            // Needed for the reset form to work
            $user->setPasswordRequestedAt(new \DateTime());

            // Send user email with reset link even the user is already activated (so we know the email is valid)
            $this->mailer->sendUserConfirmationEmailMessage($user);
        } else {

            $user->setRoles(
                array(
                    'ROLE_BUYER_ADMIN'
                )
            );

            // Generate URL
            $url = $this->router->generate('front.login');

            // Redirect the user to his/her profile page
            $event->setResponse(new RedirectResponse($url));

            /////////////////////////////////////////////////////////
            //create company
            /////////////////////////////////////////////////////////


            /** @var Company $company */
            try {
                $company = $this
                    ->companyService
                    ->createCompany(
                        $formData['raisonSociale']->getViewData(),
                        $formData['identification']->getViewData(),
                        intval($formData['country']->getViewData()),
                        $user->getEmail(),
                        $user->getFirstname(),
                        $user->getLastname(),
                        $user->getMainPhoneNumber(),
                        $user->getOptionalPhoneNumber()
                    );

                // Assign created company to user
                $user->setCompany($company);

                $user->setEnabled(true);

                if ($user->getRoles()) {
                    // Send email to buyer
                    $this->mailer->sendEmailMessage(MailService::BUYER_REGISTRATION_CONFIRMED, $user->getLocale(), $user->getEmail(), array(
                        MailService::FIRST_NAME_VAR => $user->getFirstname(),
                        MailService::LAST_NAME_VAR => $user->getLastname(),
                        "companyName" => $user->getCompany()->getName()

                    ));
                    // Send email to operator
                    $url = $this->router->generate('admin.company.generalInfo', array("id" => $company->getId()), UrlGeneratorInterface::ABSOLUTE_URL);
                    /** @var User $operator */
                    foreach ($this->securityService->getOperators() as $operator) {
                        $this->mailer->sendEmailMessage(MailService::OPERATOR_ACCOUNT_CREATED, "en", $operator->getEmail(), [
                            MailService::FIRST_NAME_VAR => $operator->getFirstname(),
                            MailService::LAST_NAME_VAR => $operator->getLastname(),
                            "accountCreatedFirstName" => $user->getFirstname(),
                            "accountCreatedLastName" => $user->getLastname(),
                            "accountCreatedEmail" => $user->getEmail(),
                            "accountCreatedCompanyName" => $formData['raisonSociale']->getViewData(),
                            "url" => $url
                        ]);
                    }


                } else {
                    $this->logger->info(
                        "unable to notify buyer and operator for account creation: User has no role",
                        LogUtil::buildContext([
                            LogUtil::EVENT_NAME => EventNameEnum::EMAIL_ERROR,
                            LogUtil::USER_NAME => $user->getUsername(),
                        ])
                    );
                }

                $this->session->set('fos_user_send_confirmation_email/email', $user->getEmail());

            } catch (ORMException $e) {
                $this->logger->error(
                    "Unable to create user company on registration: " . $e->getMessage(),
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::TECHNICAL_ERROR,
                        LogUtil::USER_NAME => $user->getUsername(),
                    ])
                );

                // translate the message
                $message = $this->translator->trans('registration.error.technical', array(), 'AppBundle');
                $currentSession = $this->session;
                /**
                 * @var Session $currentSession
                 */
                $currentSession->getFlashBag()->add('error', $message);

                $url = $this->router->generate('front.login');
                $event->setResponse(new RedirectResponse($url));
            }
        }
    }

    public function onRegistrationConfirmed(FilterUserResponseEvent $event)
    {
        /** @var User $user */
        $user = $event->getUser();

        // Flag the Email as confirmed
        $user->setEmailConfirmed(true);

        // Persist change
        $this->userManager->updateUser($user);
    }

    /**
     * Generate a username before validating the registration form
     * @param UserEvent $event
     */
    public function onRegistrationInitialize(UserEvent $event)
    {
        if ($event->getRequest()->getMethod() != 'POST') {
            return;
        }

        $username = uniqid("user_");

        /** @var User $user */
        $user = $event->getUser();

        // Set username and temp password
        $user->setUsername($username);
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }


}
