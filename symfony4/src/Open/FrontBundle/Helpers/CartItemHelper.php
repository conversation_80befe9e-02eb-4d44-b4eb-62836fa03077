<?php

namespace Open\FrontBundle\Helpers;

class CartItemHelper
{
    public static function setQuantityDemandOrOnStock($merchant, $from): void
    {
        $merchantItems = $merchant->getItems();

        foreach ($merchantItems ?? [] as $product) {
            if ($from === "order") {
                $realStockOffer = $product->getRealStock();
            } elseif ($from === "cart") {
                $realStockOffer = $product->getOffer()->getRealStock();
            }
            $qtyProduct = $product->getQuantity();

            $product->qty_greater_real_stock = false;
            $product->quantity_demand = $product->quantity_on_stock = null;
            if (!empty($realStockOffer) && $qtyProduct > $realStockOffer) {
                $product->qty_greater_real_stock = true;
                $product->quantity_demand = $qtyProduct - $realStockOffer;
                $product->quantity_on_stock = $qtyProduct - $product->quantity_demand;
            }
        }
    }

}
