services:
    _defaults:
        autowire: true
        autoconfigure: true
        public: false
        bind:
            int $comparisonSheetMaxItem: '%env(int:MAX_ITEM_COMPARISON)%'
            array $ignoredCategories: '%env(json:IGNORED_CATEGORIES_ID)%'
            array $purchaseRequestEmailAddress: '%env(json:PURCHASE_REQUEST_EMAIL_ADDRESS)%'
            string $domain: '%env(DOMAIN)%'

    # controllers are imported separately to make sure services can be injected
    # as action arguments even if you don't extend any base controller class
    Open\FrontBundle\Controller\:
        resource: '../../Controller/'
        tags: [ 'controller.service_arguments' ]

    # Provide repository to fetch notification entities
    AppBundle\Repository\CompanyRepository:
        class: AppBundle\Repository\CompanyRepository
        factory: [ '@doctrine.orm.default_entity_manager', getRepository ]
        arguments: [ 'AppBundle\Entity\Company' ]

    # Provide repository to fetch notification entities
    AppBundle\Repository\ThreadParentMessageRepository:
        class: AppBundle\Repository\ThreadParentMessageRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\ThreadParentMessage' ]

    # Provide repository to fetch notification entities
    AppBundle\Repository\ThreadMessageRepository:
        class: AppBundle\Repository\ThreadMessageRepository
        factory: [ "@doctrine.orm.default_entity_manager", getRepository ]
        arguments: [ 'AppBundle\Entity\ThreadMessage' ]

    # Provide repository to fetch notification entities
    AppBundle\Repository\DocumentRepository:
        class: AppBundle\Repository\DocumentRepository
        factory: [ '@doctrine.orm.default_entity_manager', getRepository ]
        arguments: [ 'AppBundle\Entity\Document' ]


    # Provide an event handler for registration events
    Open\FrontBundle\EventSubscriber\RegistrationSubscriber:
        class: Open\FrontBundle\EventSubscriber\RegistrationSubscriber
        autowire: true
        tags:
            - { name: kernel.event_subscriber }

    Open\FrontBundle\EventSubscriber\OfferSubscriber:
        class: Open\FrontBundle\EventSubscriber\OfferSubscriber
        arguments:
            - '@Open\IzbergBundle\Service\RedisService'
        tags:
            - { name: kernel.event_subscriber }

    #provide a form factory for user
    FOS\UserBundle\Form\Factory\FormFactory:
        class: FOS\UserBundle\Form\Factory\FormFactory
        arguments: [ '@form.factory', 'front_user_form', 'Open\FrontBundle\Form\UserForm', [ 'user', 'Default', 'Registration' ] ]

        # Provide an override of the FOS user registration form
    Open\FrontBundle\Form\UserForm:
        class: Open\FrontBundle\Form\UserForm
        arguments: [ '%fos_user.model.user.class%' ]
        tags:
            - { name: form.type , alias: front_user_registration }


    # Provide repository to fetch notification entities
    AppBundle\Repository\MenuRepository:
        class: AppBundle\Repository\MenuRepository
        factory: [ '@doctrine.orm.entity_manager', getRepository ]
        arguments: [ 'AppBundle\Entity\Menu' ]

    Open\FrontBundle\Form\SearchFormFactory:
        class: 'Open\FrontBundle\Form\SearchFormFactory'
        arguments:
            - '@form.factory'
            - '@Open\IzbergBundle\Service\AttributeService'

    Open\FrontBundle\ProformaPdf\Builder\ProformaPdfBuilderInterface:
        class: Open\FrontBundle\ProformaPdf\Builder\ProformaPdfBuilder
