'use strict';

import '../../../../../../node_modules/normalize.css/normalize.css';
import '../../../../../../node_modules/lightbox2/dist/css/lightbox.css';
import '../../../../../../node_modules/jquery-ui-dist/jquery-ui.css';
import '../../../../../../node_modules/jquery-timepicker/jquery.timepicker.css';
import '../../../../../../node_modules/slider-pro/dist/css/slider-pro.css';
import '../scss/app.scss';

import $ from 'jquery';
import 'jquery-ui-dist/jquery-ui';
import "blueimp-file-upload/js/vendor/jquery.ui.widget.js";
import 'blueimp-file-upload/js/jquery.fileupload';
import './libs/picker';
import './libs/picker.date';

import './libs/modernizr-custom.js';

require('jquery-validation')($);
import 'bootstrap';
import 'bootstrap-select';

global.$ = $; // only for legacy code
global.jQuery = $;

let CookiesHeader = require('./modules/CookiesHeader.js');
const csvExport = require("jsonexport/dist");

window.csvExport = csvExport;

$(function () {
    $('.selectpicker').selectpicker({container: 'body'});
});

window.forceDownload = function(content, filename, type) {
    const blob = new Blob([content], { type: type });
    if (navigator.msSaveBlob) { // IE 10+
        navigator.msSaveBlob(blob, filename);
    } else {
        const link = document.createElement("a");
        if (link.download !== undefined) { // feature detection
            // Browsers that support HTML5 download attribute
            var url = URL.createObjectURL(blob);
            link.setAttribute("href", url);
            link.setAttribute("download", filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }
}

window.UI =  {
    FormSubmit: require('../../../../../AppBundle/Resources/private/js/modules/FormSubmit'),
    CompanyIdentification: require('../../../../../AppBundle/Resources/private/js/modules/CompanyIdentification.js'), // import Company Identity validation module
    CountrySelector: require('../../../../../AppBundle/Resources/private/js/modules/CountrySelector.js'),
    AutoComplete: require('../../../../../AppBundle/Resources/private/js/modules/AutoComplete.js'), // autoComplete for city
    Modal: require('../../../../../AppBundle/Resources/private/js/modules/Modal.js'),
    OpenDiv: require('./modules/OpenDiv.js'),
    Select: require('./modules/Select.js'),
    Translator: require('../../../../../AppBundle/Resources/private/js/modules/Translator.js'),
    UploadDocument: require('../../../../../AppBundle/Resources/private/js/modules/UploadDocument.js'),
    ReCaptcha: require('./modules/ReCaptcha.js'),
    Utils: require('./modules/Utils.js'),
    BrowserDetect: require('./modules/BrowserDetect'),
    Dropdown: require('./modules/Dropdown'),
    TabInfos: require('./modules/TabInfos'),
    Footer: require('./modules/Footer'),
    Message: require('./modules/Message')
};

document.addEventListener('DOMContentLoaded', function () {
  console.log('[Front] DOM Loaded');

  let $document = $(document);
  let $html = $(document.body.parentNode);
  let $menuPanel = $('#js-menu-panel');
  let $searchPanel = $('#js-search-panel');
  let $hamburgerButton = $('#js-hamburger-button');
  let $searchButton = $('#js-search-button');

  let hideMenu = function () {
    $html.removeClass('menu--isActive');
    $menuPanel.removeClass('is-active');
  };

  setTimeout(window.UI.Utils.initInputs, 100);

  jQuery.validator.setDefaults({
    focusCleanup: true,
    focusInvalid: false,
    errorElement: "small",
    errorPlacement: function(error, element) {
      let $el = $(element);

      if ($el.is(':checkbox')) {
        $el.parents('.js-checkbox-container').find('.js-cb-errors').html(error);
      } else {
        error.insertAfter(element);
      }


    },
    onfocusout: function (el) {
      if (el.value.length > 0) {
        el.classList.add('has-text');
      } else {
        el.classList.remove('has-text');
      }
      return true;
    },
    //    errorClass: 'invalid-feedback',
    submitHandler : function (form) {
      //console.log(form);
      //return false;
      return window.UI.FormSubmit.onSubmit(form);
    }
  });

  // Handle click on messages close buttons (delegate)
  $('.js-close-message-button').on('click', function(ev) {
    const $parent = $(ev.currentTarget).parent();
    $parent.fadeOut('fast', function () {
      $parent.remove();
    });
  });

  // close opened selects when clicking on the other elemens of the page
  $document.click(function(ev) {
    let $target = $(ev.target);

    // Close all opened select when clicking anywhere on the page except a select itself
    if ($target.parents('.js-select-wrapper').length <= 0) {
      $('.js-select-wrapper').each(function (idx, el) {
          let $el = $(el);
        if ($el.find('.js-select-wrapper-box').is(":visible")) {
          $el.find('.js-select-placeholder').click();
        }
      });
    }
  });

  // hide hamburger
  $('main.container').on('click', hideMenu);
  $('#js-close-menu-button').on('click',hideMenu);


  // Handle clicks on the hamburger button
  $hamburgerButton.on('click', function () {
    $html.addClass('menu--isActive');
    $menuPanel.addClass('is-active');
  });

  // Switch locale
  $('.js-locale-button').on('click', function (ev) {
    ev.preventDefault();
    let locale = $(ev.currentTarget).data('locale');
    let actualLocale = window.location.href;
    let newURL;

    if (actualLocale.indexOf('/fr/') !== -1) {
        newURL = actualLocale.replace('/fr/', '/' + locale + '/');
    } else if (actualLocale.indexOf('/en/') !== -1) {
        newURL = actualLocale.replace('/en/', '/' + locale + '/');
    } else if (actualLocale.indexOf('/es/') !== -1) {
        newURL = actualLocale.replace('/es/', '/' + locale + '/');
    } else if (actualLocale.indexOf('/de/') !== -1) {
        newURL = actualLocale.replace('/de/', '/' + locale + '/');
    } else if (actualLocale.indexOf('/it/') !== -1) {
        newURL = actualLocale.replace('/it/', '/' + locale + '/');
    } else if (actualLocale.indexOf('/nl/') !== -1) {
        newURL = actualLocale.replace('/nl/', '/' + locale + '/');
    }

    window.location.href = newURL;
  });

  // Prevent clicks on the links available only if company is valid
  $('.js-menu-not-active').on('click', function (ev) {
    ev.preventDefault();
    window.UI.Modal.alert(document.body.dataset.menuLockedMessage);
    return false;
  });

  // Prevent clicks on fake offers bid button when user is logged in
  if (document.body.dataset.isLoggedIn != 0) {
    $('.js-fake-bid-button').on('click', function (ev) {
      ev.preventDefault();
      window.UI.Modal.alert(document.body.dataset.fakeButtonMsg);
      return false;
    });
  }

    // show Cookiea bar at the bottom if needed
    CookiesHeader.init();

    $('.datepicker').pickadate({
        format: 'dd-mm-yyyy',
        formatSubmit: 'yyyy-mm-dd',
        today: '',
        clear: 'Clear',
        close: '',
        selectYears: true,
        selectMonths: true,
    });
});
