@import "../variables";

.Footer {
  background: $ALSTOM_DARK_PURPLE;
  display: flex;
  flex-direction: column;
  -ms-flex-align : center;

  .container-logo {
    padding: 15px 0 0 45px;
    .footer-logo {
      max-width: 170px;
    }
  }

  .footer-pair-section {
    display: flex;
  }

  ul {
    display: flex;
    flex-flow: row wrap;
    padding-left: 0;
    list-style-type: none;
  }

  li {
    .footer-section {
      display: flex;
      flex-direction: column;

      text-align: left;
      padding: 15px 0;
      margin: 0 25px;

      .section-title {
        color: $WHITE;
        font-size: 1rem;
        margin: 0;
        padding-bottom: 10px;
        white-space: nowrap;
      }

      .footer-link {
        margin: 5px 0;
        text-decoration: none;
        font-weight: 300;
        font-size: 0.9375rem;
        color: $WHITE;
      }

      .footer-link:hover {
        text-decoration: $WHITE underline;
      }

      .section-content {
        display: flex;
        flex-direction: column;
      }

      .section-column {
        display: flex;
        flex-direction: column;
        margin-right: 20px;
        margin-top: 0.5rem;
      }

      .footer-link {
        white-space: nowrap;
      }

      &.Footer-spacer {
        flex-grow: 1;
        display: none;
      }
    }

    .icons-section {
      display: flex;
      flex-direction: row;
      width: 242px;

      .icon-row {
        @include breakpoint(min-width 1400px) {
          display: flex;
          flex-direction: row;
        }
        @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
          display: flex;
          flex-direction: row;
        }
      }

      a {
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: rgba($WHITE, 0.1);
        width: 48px;
        height: 48px;
        margin: 5px;
      }
      a > svg {
        fill: $ALSTOM_GREY;
      }

      a:hover {
        background-color: $PRIMARY_COLOR;

        & > svg {
          fill: $WHITE;
        }
      }

      a > .icons-section-img {
        height: 48px;
      }
    }

    .newsletter {
      .label {
        display: flex;
        flex-direction: row;
        .Icon {
          width: 16px;
          height: 16px;
        }
      }
    }
    .newsletter h4{
      text-transform: uppercase;
      color: $BLACK;
      letter-spacing: 2px;
      font-size: 14px;
    }
    .input-email {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      background-color: $WHITE;
      border: 1px solid $APPLI_GREY7;
      height: 40px;
      min-width: 200px;
      input {
        flex-grow: 1;
        all: initial;
        height: 40px;

        font: inherit;
        font-style: italic;
        font-size: 12px;
        padding-left: 5px;
      }

      i {
        width: 10px;
        height: 10px;
        margin-right: 10px;
      }
    }

    &.visit-section {
      .footer-link {
        color: $ALSTOM_GREY;

        &:hover {
          text-decoration: none;
        }
      }
    }
  }

  .additional-content {
    font-size: 1rem;

    p {
      color: $ALSTOM_GREY;
      font-weight: 300;
    }

    .company {
      color: $WHITE;
      font-weight: 400;
    }
  }



  @include breakpoint($BREAKPOINT_DESKTOP) {

    .footer-logo {
      margin-left: 50px;
    }

    .additional-content {
      display: flex;
      justify-content: space-between;
      margin: 20px auto;
      width: 100%;
      max-width: 1200px;
      padding:0 45px;
    }
    ul {
      display: flex;
      justify-content: space-between;
      margin: 0 auto;
    }

    li {
      display: inline;
      width: calc(100% / 3);
      padding: 10px 25px;

      &.Footer-spacer {
        display: inline;
      }
    }

    .background-image {
      display: flex;
      align-items: center;
      position: absolute;
      right: 0;
      height: $FOOTER_HEIGHT;
      width: 300px;
      z-index: 1;

      .background-container {
        height: 651px;

        svg {
          height: 651px;
          width: 300px;
          opacity: 0.1;
        }
      }
    }

    .content {
      max-width: 1200px;
      margin: 80px auto 50px;
      z-index: 10;
      .h-line {
        margin: 15px 0 20px;
        background-color: $WHITE;
        opacity: 0.1;
      }
    }
  }

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {

    ul {
      width: 100%;
      flex-direction: column;

      .container-logo {
        justify-content: center;
        display: flex;
        padding: 1.5em 0;
        margin-top: 20px;
      }

      .title-container {
        display: flex;
        justify-content: space-between;
        .cross {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 100%;
          margin: 8px;
          .horizontal {
            height: 1px;
            width: 20px;
            background-color: $WHITE;
          }
          .vertical {
            position: absolute;
            height: 20px;
            width: 1px;
            background-color: $WHITE;
          }
        }
      }

      li {
        .footer-section {
          border-bottom: 1px rgba($WHITE, 0.1) solid;
          padding: 25px 0;
          .footer-link {
            font-size: 1rem;
          }

          .section-title {
            padding-bottom: 0;
          }

          .section-column {
            margin-top: 0;
          }

          &.no-border-mobile {
            border: 0;
            padding: 10px 0;
          }
        }

        .icons-section {
          width: 100%;
          a {
            display: flex;
            justify-content: center;
            flex-grow: 1;
            margin: 2px;

            &:first-child {
              margin-left: 0;
            }

            &:last-child {
              margin-right: 0;
            }
          }
          .Icon {
            width: 48px;
            height: 48px;
          }

          .icon-row {
            width: 50%;
          }
        }
      }
    }
    .input-email {
      display: flex;
      align-items: center;
      height: 48px !important;
    }

    .hide-mobile {
      display: none !important;
    }

    .additional-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 40px;
      padding-top: 10px;

      .company {
        color: $ALSTOM_GREY;
        font-weight: 300;
      }
    }
  }

  @media print {
    background-color: $ALSTOM_DARK_PURPLE;
    -webkit-print-color-adjust: exact;
  }
}


@include breakpoint($BREAKPOINT_DESKTOP) {
  .Footer, .page-wrap:after {
    height: $FOOTER_HEIGHT;
  }
}

@include breakpoint($BREAKPOINT_NOT_DESKTOP) {
  .inverse-mobile {
    display: flex;
    flex-direction: column-reverse !important;
    margin-bottom: 0;
  }
}
