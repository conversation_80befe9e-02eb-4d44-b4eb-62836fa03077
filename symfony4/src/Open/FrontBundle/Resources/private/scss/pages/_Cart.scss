[class*="Page--cart_"] {
  main.container {
    margin: 0 !important;
    max-width: none !important;
    padding-left: 0;
    padding-right: 0;
    background-color: $WHITE;
  }


  .validation-number {
    padding-right: 10px;
    input {
      height: 65px;
      margin: 0 0 !important;
    }
  }

  .checkout-success {
    max-width: 1200px;
    margin: 50px auto;
  }

  .page-wrap {
    min-height: 0 !important;
  }

  .Page-inner {
    padding-top: 0 !important;
  }

  .empty-cart {
    text-align: center;

    h1 {
      font-size: 1.875rem;
      margin-top: 75px;
    }

    p {
      max-width: 770px;
      font-size: 1rem;
      color: $ALSTOM_DARK_GREY;
      margin: 20px auto;
    }

    a {
      font-size: 1rem;
      text-decoration: underline;
    }

    .cart-empty-img {
      width: 830px;
      height: auto;
      margin: 25px auto 60px;
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      margin-top: -10px;
      padding: 60px 20px;
      background-color: $ALSTOM_GREY_BACKGROUND;

      h1 {
        margin-top: 0;
      }
    }
  }
}

.header-seller, .label-seller {
  color: $BLACK;
  font-size: 1.25rem;
  font-weight: 600;
  text-align: left;
  word-break: normal;

  &.tablet-only {
    width: 80%;
    margin: 0 auto;
  }

  .Icon {
    width: 17px;
    height: 17px;
    fill: $SECONDARY_COLOR;
    margin: 0;
  }

  .nb-products {
    color: $PRIMARY_COLOR;
  }
}

.select-container {
  background-color: $ALSTOM_GREY_BACKGROUND;
  border: 1px solid $ALSTOM_GREY;
  padding: 10px;
  font-weight: 600;

  input[type='text'] {
    width: 300px;
    background-color: $ALSTOM_GREY_BACKGROUND;
  }
}

.Page-cart-inner {
  width: 100%;
  padding-bottom: 0;
  @include breakpoint($BREAKPOINT_DESKTOP) {
    .empty {
      margin: 0 15% !important;
    }
  }

  .cart-header {
    display: flex;
    width: 80%;
    margin: 40px auto 20px auto;
    justify-content: space-between;
    align-items: center;

    .cart-clear {
      padding-top: 0.6rem;
      padding-left: 0.6rem;
    }

    .cart-title {
      display: flex;

      .currency {
        color: $BLACK;
      }
    }

    .cart-header-right {
      display: flex;
      flex-direction: row;

      .shipping-dpa {
        font-weight: 700;
      }

      a {
        margin: 0 10px;
        text-decoration: none;

        i {
          border-color: $WHITE;
          margin-right: 10px;
        }

        &:last-child {
          margin-right: 0;
        }

        button {
          padding: 20px;
        }

        .btn-assign-anchor {
          max-width: 350px;
        }
      }
    }


    #items-count {
      display: flex;
      font-weight: 600;

      h2 {
        color: $ALSTOM_DARK_GREY;
        font-weight: 600;
      }
    }

    .select-container {
      background-color: transparent;
      border: 0;

      input:disabled {
        border-bottom: 1px solid $ALSTOM_LIGHT_GREY;
      }

      input:not([type="file"]) {
        box-shadow: none;
      }
    }

    @include breakpoint(max-width 1150px) {
      flex-direction: column;
      align-items: flex-start;

      .cart-header-right {
        width: 100%;
        justify-content: space-between;

        a {
          flex-grow: 1;
          margin: 0;

          button {
            width: 100%;
            margin: 0;
          }

          .btn-middle {
            width: 90%;
            margin: auto;
          }
        }
      }
    }
  }

  .row {

    .fca-incoterm {
      width: 80%;
      margin: 0 auto;
      display: flex;
      flex-direction: row;
      align-items: baseline;

      .warning {
        font-size: 1.4em;
        color: $ERROR;
      }

      .fca-text {
        color: $ERROR;
      }
    }
  }

  .seller-table {
    width: 80%;
    margin: 40px auto 20px auto;
    background-color: transparent;
    border: none;
    box-shadow: none;

    .warning-fca {
      text-transform: uppercase;
      color: $PRIMARY_COLOR;
      letter-spacing: initial;
    }

    .tooltip {
      z-index: 2;
    }

    .modified {
      font-weight: bold;
      color: $APPLI_PURPLE;
    }

    p {
      margin: 0;
      max-width: 95%;
    }

    tr {
      background-color: transparent;
      vertical-align: inherit;
    }

    tr.invalid {
      background-color: $ERROR_TRANSPARENT;
    }

    thead {
      th {
        text-align: center;
        color: $ALSTOM_GREY;
        font-size: 14px;
        font-weight: 400;
        background-color: transparent;
        border: none;
        padding: 5px 0 10px;
      }

      th:nth-child(1) {
        width: 35%;
      }

      th:nth-child(2) {
        width: 20%;
      }

      th:nth-child(3) {
        width: 15%;
      }

      th:nth-child(4) {
        width: 10%;
      }

      th:nth-child(5) {
        width: 15%;
      }

      th:nth-child(6) {
        width: 5%;
      }

      th:nth-child(1).header-seller {
        width: 100%;
      }
    }

    tbody {
      td {
        text-align: center;
        color: $ALSTOM_DARK_GREY;
        background-color: transparent;
        font-size: 14px;
        border: none;
        vertical-align: middle;
        padding: 10px 0;

        .product {
          position: relative;
          display: flex;
          align-items: center;
          text-align: left;
          min-height: 100px;
          margin: 5px 0;

          a {
            position: absolute;
            top: 0;
            left: 0;
            height: 50%;
            width: 100%;
            z-index: 1;
          }

          h5 {
            font-weight: 600;
          }

          .img-container {
            max-width: 100px;
            width: 100px;
            height: 100px;
            overflow: hidden;
            display: flex;
            align-items: center;

            img {
              position: relative;
              width: auto;
              height: auto;
              max-height: 100px;
              left: 50%;
              transform: translateX(-50%);
            }
          }

          .detail-content {
            width: 280px;
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: flex-start;
            font-weight: 600;
            margin-left: 20px;

            .name {
              color: $BLACK;
              font-size: 16px;
              margin: 0;
              max-width: 300px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              font-weight: bold;
            }

            .incoterm, .sellerRef, .buyerRef {
              font-size: 11px;
              margin: 0;
              letter-spacing: 2px;
            }

            .incoterm {
              color: $ALSTOM_GREY;
              text-transform: uppercase;
            }

            .sellerRef, .buyerRef {
              color: $ALSTOM_DARK_GREY;
            }

            .update-icon {
              width: 15px;
              height: 15px;
              display: inline-block;
              background-repeat: no-repeat;
              background-size: 100% 100%;
              margin-left: 10px;
              cursor: pointer;
            }

            .order-item-status {
              background-color: $PRIMARY_COLOR;
              color: $WHITE;
              font-size: 10px;
              border-radius: 20px;
              padding: 1px 10px;
              font-weight: 300;
              text-transform: uppercase;
              margin-top: 5px;
            }
          }

        }

        input[type=number] {
          border: 1px solid $ALSTOM_LIGHT_GREY;
          padding: 10px 0;
          width: 120px;
          height: 40px;
          color: $ALSTOM_DARK_GREY;
          font-weight: 400;
          text-align: center;

          &::-webkit-inner-spin-button,
          &::-webkit-outer-spin-button {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            margin: 0;
          }

          &:focus {
            color: $PRIMARY_COLOR;
          }
        }
      }
    }

    .resume-table {
      td {
        padding: 20px 0 5px;
        white-space: nowrap;
        text-align: right;
      }

      .taxes-label {
        text-align: left;
      }
    }

    .subtotal-title {
      text-align: left !important;
      font-size: 1.5rem !important;

      span {
        color: #000 !important;
        font-weight: bold;
      }
    }

    .subtotal-vat {
      font-weight: bold;

      td {
        color: $BLACK;
        font-weight: 600;
      }
    }

    .label-detail {
      text-align: left;
    }

    .label-date {
      min-width: 110px;
    }

    .label-price {
      min-width: 150px;
      padding-right: 30px;
      text-align: right;

      @include breakpoint($BREAKPOINT_MOBILE_MAX) {
        padding-right: 0;
      }
    }

    .underline {
      border-bottom: 1px solid $ALSTOM_LIGHT_GREY;
    }

    .trash, .refresh {
      width: 16px;
      height: 16px;
    }

    .not-allowed {
      cursor: not-allowed;
    }

    .taxes-label-cell {
      display: flex;

      p {
        margin: 0;
      }
    }

    .shipping-cart-detail {
      .select-wrapper__placeholder {
        border-color: $APPLI_PURPLE;
      }

      .name {
        color: $APPLI_PURPLE !important;
      }

      .shipping-selector {
        display: flex;

        .product {
          flex: 7;
        }

        .selector-container {
          flex: 5;
          margin: 0 35px;
        }
      }
    }
  }

  .table-total {
    thead th:nth-child(3) {
      width: 10%;
    }
  }

  .total-container {
    background-color: $ALSTOM_GREY_BACKGROUND;
    margin-top: 20px;
    padding-bottom: 40px;

    @include breakpoint($BREAKPOINT_MOBILE_MAX) {
      padding-bottom: 10px;
    }

    .table-total {
      thead {
        th {
          padding-top: 20px;
        }
      }

      tr {
        border: none;
      }

      td {
        font-weight: 600;
        white-space: nowrap;
      }
    }

    .upperline {
      border-top: $PRIMARY_COLOR 2px solid;
    }

    .button-container {
      width: 80%;
      margin: 40px auto 20px auto;
      display: flex;
      justify-content: space-between;

      a:hover {
        text-decoration: none;
      }

      div {
        display: flex;

        button {
          padding: 10px 10px;
          margin: 0 15px;

          i {
            margin: 0 10px;
            border-color: $WHITE;
          }

          &:hover i {
            border-color: $ALSTOM_GREY;
          }
        }
      }
    }
  }

  .historic-table {
    background-color: $ALSTOM_GREY_BACKGROUND;
    padding: 200px 0 90px;
    width: 100%;

    .historic-top {
      width: 80%;
      height: 65px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;

      @include breakpoint($BREAKPOINT_MOBILE_MAX) {
        width: 90%;
        align-items: flex-start;
        .button-block {
          width: 100%;
          margin: 10px 0;

          button {
            width: 100%;
          }
        }
      }
    }

    .title {
      color: $BLACK;
      font-weight: 700;
      margin: 10px 0 0;
    }

    .grid {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      width: 80%;
      margin: 0 auto;
      padding-top: 10px;

      .column-1,
      .column-2 {
        width: 49%;
        margin-bottom: 20px;

        @include breakpoint($BREAKPOINT_MOBILE_MAX) {
          width: 100%;
        }
      }
    }
  }

  .historic-block {
    display: flex;
    flex-direction: column;
    padding: 20px;
    background-color: $WHITE;
    border: $ALSTOM_LIGHT_GREY 1px solid;
    box-shadow: 5px 5px 30px $ALSTOM_LIGHT_GREY;
    height: 100%;

    .row {
      display: flex;
      flex-direction: row;
      align-items: flex-end;
      margin: 5px 0;

      .comment-id {
        width: 2em;
        height: 2em;
        background-color: $PRIMARY_COLOR;
        color: $WHITE;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 1em;
        margin-right: 10px;
      }

      .title {
        font-weight: 600;
        color: $BLACK;
      }

      .value {
        color: $ALSTOM_DARK_GREY;
        font-weight: 400;
        margin: 0 10px;
      }

      &.user-assigned, &.user-who {
        .value {
          font-weight: 400;
          color: $ALSTOM_GREY;
          letter-spacing: 1px;
        }
      }

      .comment .value {
        font-weight: 400;
      }
    }
  }

  @include breakpoint($BREAKPOINT_TABLET) {

    .hidden-desktop {
      display: none !important;
    }
  }

  @include breakpoint($BREAKPOINT_MOBILE_MAX) {

    .hidden-mobile {
      display: none !important;
    }

    padding: 0 !important;

    .row {

      .fca-incoterm {
        flex-direction: column;
      }
    }

    .cart-header {
      display: flex;
      flex-direction: column;
      margin: 0 auto;
      justify-content: space-between;
      align-items: flex-start;
      width: 90%;
      margin-bottom: 30px;


      .select-container {
        width: 100%;
        padding: 10px 0;

        .select-wrapper {
          width: auto;
        }

        .js-select-placeholder {
          max-width: 250px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }

    .cart-title {
      * {
        white-space: nowrap;
      }

      margin: 0;
    }

    .seller-table {
      width: 90%;
      margin: 0 auto 50px;

      thead {
        th:nth-child(1) {
          margin: 0.6rem 0;
        }

        th:nth-child(n+2) {
          display: none;
        }

        .header-seller {
          display: flex;
          justify-content: space-between;
          margin: 0.6rem 0;
          width: 100% !important;

          .label-seller .Icon {
            width: 15px;
            height: 15px;

          }

          .arrow {
            width: 8px;
            height: 8px;

            &.up {
              margin-top: 9px;
            }

            &.down {
              margin-top: 7px;
            }
          }
        }
      }

      tbody {
        tr {
          display: flex;
          flex-direction: column;
          border-top: 1px solid $ALSTOM_LIGHT_GREY;

          td {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            font-size: 1rem;
            margin: 0;

            p {
              margin: 0;
            }

            .mobile-center {
              width: 130px;
              text-align: center;
              font-size: 1.0625rem;
              font-weight: 300;
            }

            input[type=number] {
              font-size: 1.0625rem;
              width: 130px;
              height: 45px;
              box-shadow: none;
              font-size: 1.0625rem;
              font-weight: 300;
              margin: 0;
            }

            .product {
              min-height: 0;
              margin: 0;

              .detail-content {
                .name {
                  font-size: 1.0625rem;
                }

                .incoterm {
                  font-size: 0.9rem;
                  font-weight: bold;
                  letter-spacing: 2px;
                }
              }

              .img-container {
                width: 60px;
                height: 60px;

                img {
                  width: auto;
                  height: 60px;
                }
              }
            }

            .mobile-label {
              color: $ALSTOM_DARK_GREY;
              font-weight: 300;
              font-size: 1.0625rem;
              margin: 0;
              text-align: left;
            }
          }
        }

        .resume-table {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          border: none;
          margin: 0;

          td {
            color: $ALSTOM_DARK_GREY;
            font-weight: 300;
            font-size: 1.0625rem;
            margin: 0 !important;
            margin: 0 !important;
          }
        }

        .subtotal-vat {
          td {
            color: $BLACK;
            font-weight: 600;
            text-transform: uppercase;
          }
        }

        .subtotal {
          padding-top: 1rem;
          border-top: 1px solid $ALSTOM_LIGHT_GREY;
        }
      }

      .trash {
        margin-right: 0.5rem;
      }

      .underline {
        border: none;
        padding-top: 20px;
      }

      .mobile-align {
        margin-left: 80px;
      }
    }


    .total-container {

      tr.resume-table.subtotal,
      tr.total-table-taxes {
        border: none;

        td {
          font-weight: 600;
        }
      }


      .button-container {
        display: block !important;
        margin: -30px auto 0;
        width: 90%;

        div {
          display: flex;
          flex-direction: column;

          button {
            padding: 0.8rem 0.8rem;
            margin: 0.5rem 0;
            width: 100%;
            white-space: nowrap;

            .arrow {
              display: none;
            }
          }
        }
      }

      @include breakpoint($BREAKPOINT_TABLET) {
        .button-container {
          margin: 20px 15% 20px 15%;
          display: flex;
          justify-content: space-between;

          a:hover {
            text-decoration: none;
          }

          div {
            display: flex;
            flex-direction: row;

            button {
              padding: 10px 10px;
              margin: 0 15px;

              i {
                margin: 0 10px;
                border-color: $WHITE;
              }

              &:hover i {
                border-color: $ALSTOM_GREY;
              }

            }
          }
        }
      }
    }
    .historic-table {
      background-color: $ALSTOM_GREY_BACKGROUND;
      padding: 60px 0 120px;

      .historic-top {
        flex-direction: column;
        height: auto;
      }

      .row .date {
        flex-direction: row;
        margin-top: 10px;
      }

      .row.user-who .value,
      .row.user-assigned .value {
        width: 100%;
      }

      .grid {
        display: block;
        width: 90%;
        margin: 0 auto;

        .column-1,
        .column-2 {
          margin: 0;
        }
      }
    }

    .historic-block {
      margin: 1em 0;

      .row {
        display: flex;
        flex-direction: column;
        align-items: flex-start;


        .comment-id {
          width: 2em;
          height: 2em;
          background-color: $PRIMARY_COLOR;
          color: $WHITE;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 1em;
          margin-right: 10px;
        }

        .title {
          font-weight: 600;
          color: $BLACK;
          font-size: 0.8rem;
          margin: 0.5rem 0;
        }

        .value {
          color: $ALSTOM_DARK_GREY;
          font-weight: 400;
          font-size: 0.8rem;
          margin: 0 auto;
        }

        &.user-assigned, &.user-who {
          .value {
            font-weight: 600;
            color: $ALSTOM_GREY;
          }
        }

        .comment .value {
          font-weight: 400;
        }
      }
    }
  }
}

.Modal--assign, .Modal--reject {
  .Modal-body {
    padding: 0 20px;

    .select-container {
      border: 0;
      padding: 0;

      .select-wrapper__placeholder {
        margin: 0;
        height: 100%;
        line-height: 58px;
      }

      .select-wrapper__box {
        height: unset;
        min-height: 50px;
        max-height: 200px;
      }
    }

    .select-wrapper {
      margin-right: 30px;
      margin-bottom: 20px;
      padding: 0;
    }

    button {
      margin: 1rem auto 0 auto;
    }

    .message-container {
      margin: 15px 0 30px;

      textarea {
        border-color: $ALSTOM_LIGHT_GREY;
        padding: 10px;
      }
    }
  }
}

[class*="Page--cart_pending_list"] {
  main.container {
    background-color: $ALSTOM_GREY_BACKGROUND;

    @include breakpoint($BREAKPOINT_DESKTOP) {
      padding: 160px 15px 0 15px;
    }
  }

  @include breakpoint($BREAKPOINT_DESKTOP) {
    .side-container {
      display: flex;
      max-width: 1200px;
      margin: 0 auto;
    }
    .Page-inner {
      width: 100%;
      padding: 30px 0px;
    }

    .side-menu-container:hover {
      width: 370px;
    }
  }

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    .Page-inner {
      padding: 0 20px;
    }
  }

  .Page-inner {
    padding-bottom: 60px;
  }

  .pending-cart-container {
    width: 100%;
    background-color: $WHITE;
    margin-top: 30px;

    th {
      color: $ALSTOM_GREY;
      font-weight: 300;
      font-size: 0.9375rem;
      line-height: 1rem;
      padding: 20px 10px;
    }

    td {
      color: $ALSTOM_DARK_GREY;
      padding: 30px 10px;

      a {
        color: $PRIMARY_COLOR;
        font-weight: 700;
      }

      .major-text {
        color: $PRIMARY_COLOR;
      }

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        font-weight: 300;
        letter-spacing: 1px;
      }
    }

    tr {
      border-bottom: 1px solid $ALSTOM_LIGHT_GREY;

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        display: flex;
        flex-direction: column;
        width: 100%;
        padding-bottom: 20px;
      }
    }

    .small {
      width: 10%;
      text-align: center;
    }

    .medium {
      width: 30%;
      padding-left: 20px;
    }

    .mobile-title {
      text-transform: uppercase;
      font-weight: 700;
      font-size: 0.8125rem;
      letter-spacing: 1px;
    }

    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      .small,
      .medium {
        width: 100%;
        padding: 20px 20px 0;
        text-align: left;
      }
    }
  }


}


/*  PROCEED TO CHECKOUT BLOCK */
.cart-checkout-infos {
  width: 80%;
  padding: 40px 0 60px;
  margin: 0 auto;

  @include breakpoint($BREAKPOINT_MOBILE_MAX) {
    width: 90%;
  }

  .info-field label.value {
    text-transform: none;
    font-weight: normal;
  }

  .cost-center-details {
    margin: 50px 0;
    padding: 30px 0;
    border-top: 1px solid #e4e5ec;
    border-bottom: 1px solid #e4e5ec;
    width: 100%;
  }

  .site-details {
    background-color: #f4f5fa;
    padding: 20px;
  }

  .site-details-container {
    display: flex;
  }

  .site-details-container .contact {
    flex-grow: 1;
  }

  .site-details-container .comments {
    flex-grow: 1;
  }

  .info-field {
    padding: 0;
    padding-top: 10px;
    color: #6b6f82;
  }

  .info-field .label {
    font-weight: bold;
    color: #032639;
  }

  .info-field .value {
    color: #6b6f82;
  }

  .custom-checkbox[disabled] + label {
      cursor: not-allowed;
      &:after {
        background-color: #a9a9a9;
      }
  }

  #site-requested-documents {
    label {
      font-size: 14px;
      margin-bottom: 0;
      padding-left: 30px;
      padding-top: 0;
      display: block;
      position: relative;
    }
    input[type=checkbox] {
      display: none;
    }
  }

  .packaging-documents {
    display: flex;
    padding: 20px;
  }

  .packaging-documents .packaging {
    flex-grow: 1;
    border-right: 1px solid #e4e5ec;
    margin: 0 20px 0 0;
  }

  .packaging-documents .documents {
    flex-grow: 1;
  }

  .custom-checkbox {
    position: relative !important;
    left: 0 !important;
    width: auto !important;
    height: auto !important;
  }

  .checkout-title {
    .title {
      color: $BLACK;
    }
  }

  .infos, .row {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-between;

    .select-blocks {
      display: flex;
      flex-direction: row;
      width: 100%;
      margin-bottom: 20px;
      justify-content: space-between;

      .block {
        flex-grow: 1;
        flex-basis: 0;
        padding: 0;
        margin: 0;

        .title {
          font-weight: 600;
          margin-bottom: 5px;
          text-transform: uppercase;
        }

        .wrapper-block {
          border: 1px solid $ALSTOM_LIGHT_GREY;
          background-color: $ALSTOM_GREY_BACKGROUND;
          margin-bottom: 10px;

          input {
            margin: 5px 0;
            padding-left: 0;
          }

          .js-select-placeholder {
            box-shadow: none;
            height: 25px;
            font-size: 0.875rem;
            font-weight: 400;
          }
        }

        .select-wrapper {
          height: 64px;
          padding: 0;
          margin-bottom: 0;
          margin-top: 0;
          background-color: transparent;
          background-position: calc(100% - 20px) 28px;

          .select-wrapper__placeholder {
            padding: 20px 40px 20px 10px;
            margin: 0;
            height: 100%;
            width: 100%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: block;
          }

          .select-wrapper__box {
            height: auto;
          }

          &:before {
            content: none;
          }
        }

        .js-select-placeholder {
          margin: 5px 0;
          border: 0;
        }

      }

      .input-disabled {
        border: 1px solid #E4E5EC;
        padding: 10px 0;
        width: 120px;
        height: 65px;
        color: #6B6F82;
        font-weight: 400;
        text-align: center;
        margin: 0;
        cursor: not-allowed;
      }

      #select-site-container, #select-address-container {
        margin-right: 20px;
      }
    }

    .input-block {
      display: flex;
      flex-direction: row;
      width: 71%;

      @include breakpoint($BREAKPOINT_MOBILE_MAX) {
        width: 100%;
      }

      .block {
        flex-grow: 1;
        flex-basis: 0;
        padding: 0;
        margin: 0;
        width: 98%;

        .title {
          font-weight: 600;
          margin-bottom: 5px;
          text-transform: uppercase;

          .tooltip {
            margin-top: 40px !important;
            margin-left: 10px !important;
          }
        }
      }
    }

    .button-block {
      flex-grow: 0;
      width: fit-content;
      display: flex;
      align-items: center;
      padding-top: 26px;
      width: 100%;
      justify-content: flex-end;

      * {
        margin: 0 0 0 10px;
      }
    }

    .seller-name {
      padding-top: 2px;
      text-transform: none
    }

    a {
      padding-left: 5px;
    }

    .tooltiptext {
      visibility: hidden;
      width: 300px;
    }
  }

  @include breakpoint(max-width 1150px) {
    .infos {
      display: flex;
      flex-direction: column;

      .select-blocks {
        width: 100%;
      }

      .button-block * {
        margin: 0;
      }
    }
  }

  @include breakpoint($BREAKPOINT_MOBILE_MAX) {
    .infos {
      display: flex;
      flex-direction: column;

      .select-blocks {
        display: flex;
        flex-direction: column;
        width: 100%;

        .block {
          margin: 1.2em 0;

          .title {
            font-weight: 600;
            margin-bottom: 5px;
          }

          .wrapper-block {
            max-width: 100%;
          }
        }

        .input-disabled {
        }
      }

      .button-block {
        width: 100%;
        display: flex;
        flex-direction: column;

        * {
          margin: 1em 0;
          width: 100%;
        }
      }
    }
  }
}

.cart-wrong-price-message {
  width: 80%;
  padding: 40px 0 60px;
  margin: 0 auto;
  font-size: 20px;
  text-align: center
}

.split-delivery {
  padding: 0;
  background: none;
  border: none;
  display: inline-block;
  transition: .2s;
  &.cancel {
    rotate: 45deg;
    transition: .2s;
    svg {
      fill: #C63C51;
    }
  }
  img {
    width: 20px;
    height: 20px;
  }
  &.not-allowed {
    opacity: .4;
    svg {
      cursor: not-allowed;
    }
  }
  &:hover {
    border: none;
  }
  svg {
    fill: #9600FF;
    width: 17px;
    height: 17px;
  }
}

.datepicker-delivery {
  &-input {
    border: none !important;
    border-bottom: 1px dashed #9600ff !important;
    box-shadow: none !important;
    height: auto !important;
    margin: 0 !important;
    padding: 0 !important;
    width: 75px !important;
    font-size: 14px !important;
    cursor: pointer;
    &:nth-child(1) {
      margin-bottom: 5px !important;
    }
  }
}

.modify-qty-delivery {
  background: #9600ff;
  padding: 3px 10px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  color: #FFF;
}

.qty-delivery {
  width: 50px !important;
  box-shadow: none !important;
  border: none !important;
  padding: 0 !important;
  height: 20px !important;
  font-size: 14px !important;
  margin: 0 5px !important;
  text-align: center;
}

.picker {
  width: 400px;
}

.picker__weekday {
  width: auto !important;
}

.picker__table td {
  padding: 0 !important;
}

.picker--opened .picker__holder {
  max-height: 30em;
}

.opacity-5 {
  opacity: .5;
}

.cart-steps {
  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    text-align: center;
  }
}

.cart-step-container {
  @include breakpoint($BREAKPOINT_DESKTOP) {
    max-width: 80%;
    margin: 0 auto;
    justify-content: space-between;
    padding: 0;
    display: flex;
  }
  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    display: inline-flex;
    flex-direction: column;
    margin-bottom: 20px;
    margin-right: 15px;
  }
  position: relative;

  &::before {
    content: '';
    display: block;
    position: absolute;
    @include breakpoint($BREAKPOINT_DESKTOP) {
      bottom: 17px;
      right: 0;
      left: 0;
      height: 6px;
    }
    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      bottom: 0;
      top: 0;
      left: 32px;
      width: 6px;
    }
    background: #E4E5EC;
    border-radius: 3px;
    z-index: 1;
  }

  .cart-step {
    text-transform: uppercase;
    text-align: center;
    font-weight: bold;
    color: #6b6f82;
    z-index: 2;
    display: flex;
    @include breakpoint($BREAKPOINT_DESKTOP) {
      flex-direction: column;
      padding: 0 30px;
      justify-content: space-between;
      flex: 1;
    }
    @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
      flex-direction: row-reverse;
      justify-content: flex-end;
    }
    align-items: center;

    &-round {
      width: 40px;
      height: 40px;
      line-height: 30px;
      text-align: center;
      border-radius: 50%;
      border: 6px solid #E4E5EC;
      background: #fff;
      @include breakpoint($BREAKPOINT_DESKTOP) {
        margin-top: 15px;
      }
      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        margin: 15px;
      }

      svg {
        display: inline-block;
        fill: #E4E5EC;
      }
    }

    &.cart-step-active {
      color: #9600FF;

      .cart-step-round {
        border-color: #9600FF;
        background: #9600FF;

        svg {
          fill: #fff;
        }
      }
    }
  }
}
