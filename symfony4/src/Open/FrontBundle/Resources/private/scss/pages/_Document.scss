@import "../_variables";

.DocumentForm {

  width: 100%;
  margin-top: 1em;

  .Messages {
    width: 100%;
    max-width: 100%;

    .Message-item {
      padding: 10px;
      width: 100%;
      box-sizing: border-box;
      margin-top: 0;
    }

    &.messages-cgu, &.messages-full-auto, &.messages-e-catalog {
      display: flex;
      align-items: center;
      color: $ALSTOM_DARK_GREY;
      font-size: 0.75rem;
      text-transform: uppercase;
      letter-spacing: 1px;
      margin-bottom: 20px;

      .Icon {
        margin: -5px 5px 0 0;
      }

      a {
        color: $PRIMARY_COLOR;
        font-weight: 700;
        margin-left: 3px;
      }

      @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
        font-size: 0.9375rem;

        @include breakpoint($BREAKPOINT_MOBILE_MAX) {
          justify-content: center;
        }
      }
    }
  }

  .form-row {
    min-height: 25px;
  }

  &-contract {
    a {
      color: $PRIMARY_COLOR
    }
  }

  &-uploadField--noborder {
    .form-row {
      border-top: none;
    }
  }

  &-cgu {
    margin: 20px 0 30px;

    .arrow {
      border-color: $BLACK;
      margin-right: 10px;
    }

    a {
      font-size: 16px;
      display: flex;
      align-items: center;
    }
  }
}

.DocumentForm-uploadField .has-error li{
  list-style-type: none;
  color :red;
}

.DocumentForm-uploadField .Message--error {
  margin-top: 30px;
}

.DocumentForm-uploadField label {
  color: $ALSTOM_DARK_GREY;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  white-space: nowrap;
  position: absolute;
  top: -28px;

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    top: 0;
  }
}

.DocumentForm-uploadField {
  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    margin-top: 30px;
  }
}

.DocumentForm-uploadField .form-row {
  height: 60px;
}

.DocumentForm-nodocs {
  margin-top: 0;

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    margin: 20px 0 !important;
  }
}

.DocumentForm-nodocs .Message-item {
  font-style: italic;
  display: inline-block;

  &.Message--error {
    border: 0;
    border-radius: 0;
    text-align: left;
    padding-left: 20px;
  }
}

.Button--upload {
  background-color: $PRIMARY_COLOR;
  color: $WHITE;
  font-weight: 600;
  padding: 3px 15px;
  display: none;
  width: 100%;
  text-transform: uppercase;
  height: 45px;

  &:hover {
    border: 0;
  }

  @include breakpoint($BREAKPOINT_DESKTOP) {
    width: auto;
    display: block;
  }

  .Icon {
    width: 19px;
    height: 13px;
  }
}

.Button--uploadMobile {
  display: block;
  margin: 30px auto;
  width: 100%;
  max-width: 400px;
  position: absolute;
  top: 0;

  @include breakpoint($BREAKPOINT_DESKTOP) {
    display: none;
  }
}


.DocumentForm-docs {
  padding-bottom: 10px;
  padding-left: 0;
  min-height: 50px;

  li {
    background: #FFF;
    list-style-type: none;
    font-style: italic;
    margin-bottom: 10px;
    padding-left: 5px;
    position: relative;
    min-height: 35px;
    display: flex;
    align-items: center;

    a{
      color: #9b9b9b;
      display: block;
      width: calc(100% - 40px);


      &:hover {
        text-decoration: none;
        color: $PRIMARY_COLOR;
      }
    }

    .IconMax {
      fill: #9b9b9b;
      pointer-events: all;
      position: absolute;
      right: 5px;
      top: 50%;
      transform: translateY(-50%);
      width: 24px;
      height: 24px;

      &:hover {
        fill: $PRIMARY_COLOR;
        cursor: pointer;
      }
    }
  }

  @include breakpoint($BREAKPOINT_NOT_DESKTOP) {
    margin-top: 30px;
  }

}

input[type="file"] {
  opacity: 0;
  position: absolute;
}

