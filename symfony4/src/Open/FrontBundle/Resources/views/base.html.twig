{% set requestUri = app.request.requestUri %}
{% if is_homepage is defined and is_homepage%}
    {% set pageClass = 'Page--homepage' %}
{% else %}
    {% set requestUri = requestUri|preg_replace('~(^\/|\/$)~')|preg_replace('~/~','_')|preg_replace('~[?=]~','_')|preg_replace('~^(fr|en|es|it|de|nl)_~', '') %}
    {% set pageClass = 'Page--' ~ requestUri %}
{% endif %}
{% if pageType is defined and pageType is not empty %}
    {% set pageClass = pageClass ~ ' Page-type--' ~ pageType %}
{% endif %}
{% if tab_active is defined %}
    {% set tab_active = tab_active %}
{% else %}
    {% set tab_active = 1 %}
{% endif %}
{% set locale = app.request.locale|split('_')[0] %}
{% set route = app.request.attributes.get('_route') %}

<!DOCTYPE html>
<html lang="{{ locale }}">
<head>
    <!-- {{  randomLength() }}-->
    <meta charset="UTF-8" />
    <title>
        {% block title %}
            {% if is_homepage is defined and is_homepage %}
                {{ 'home.page_title'|trans }}
            {% elseif requestUri == 'login' %}
                {{ 'buyer.security.login.page_title'|trans }}
            {% elseif requestUri == 'register__type_acheteur' %}
                {{ 'buyer.registration.page_title'|trans }}
            {% endif %}

        {% endblock %}
    </title>
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0">

    {{ encore_entry_link_tags('app') }}

    <link rel="apple-touch-icon" href="{{ asset('apple-icon-120x120.png') }}" sizes="120x120">
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}" />
    <link rel=”icon” sizes=”72×72″ href=”{{ asset('android-icon-72x72.png') }}”>
    <link rel=”icon” sizes=”48×48″ href=”{{ asset('android-icon-48x48.png') }}”>
    <link rel=”icon” sizes=”36×36″ href=”{{ asset('android-icon-36x36.png') }}”>
    <style>

    </style>
    {% block stylesheets %}{% endblock %}

    {{ encore_entry_script_tags('app') }}

    <script src="{{ asset('bundles/fosjsrouting/js/router.js') }}"></script>
    <script src="{{ path('fos_js_routing_js', { callback: 'fos.Router.setData' }) }}"></script>
    {% if app.request.cookies.has('cgu_cookie') %}
        {% if app.environment == 'prod' %}
            <script async src="https://www.googletagmanager.com/gtag/js?id=UA-*********-1"></script>
            <script>
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', 'UA-*********-1');
            </script>
        {% endif %}
        {% if hs_script is defined and hs_script is not empty %}
            <script type="text/javascript" id="hs-script-loader" async defer src="//js.hs-scripts.com/{{ hs_script }}.js"></script>
        {% endif %}
    {% endif %}

    <script>
        (function( factory ) {
            if ( typeof define === "function" && define.amd ) {
                define( ["jquery", "../jquery.validate"], factory );
            } else if (typeof module === "object" && module.exports) {
                module.exports = factory( require( "jquery" ) );
            } else {
                factory( jQuery );
            }
        }(function( $ ) {
        /*
         * Translated default messages for the jQuery validation plugin.
         * Locale: FR (French; français)
         */
        $.extend( $.validator.messages, {
            required: "{{ 'validator.required'|trans([], 'AppBundle') }}",
            remote: "{{ 'validator.remote'|trans([], 'AppBundle') }}",
            email: "{{ 'validator.email'|trans([], 'AppBundle') }}",
            url: "{{ 'validator.url'|trans([], 'AppBundle') }}",
            date: "{{ 'validator.date'|trans([], 'AppBundle') }}",
            number: "{{ 'validator.number'|trans([], 'AppBundle') }}",
        } );
        return $;
        }));
    </script>

    <script type="text/javascript">
        var onloadCallback = function() {
            if ($('#g-recaptcha').length > 0) {
                grecaptcha.render('g-recaptcha', {
                    'sitekey' : '{{ captcha_public }}'
                });
            }
        };
    </script>
    <script src="https://www.google.com/recaptcha/api.js?onload=onloadCallback&render=explicit"
            async defer>
    </script>

    {% block header_js %}{% endblock %}

    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push(

            {'gtm.start': new Date().getTime(),event:'gtm.js'}
        );var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','GTM-5SLDR5B');</script>
    <!-- End Google Tag Manager -->

</head>
{% if  app.debug %}
    <!-- Release : {% include 'release' ignore missing %} -->
    <!-- Environnement : {{ app.environment }} -->
{% endif %}
<body class="Page {{ pageClass }}" data-route="{{ route }}" data-menu-locked-message="{{ 'main_menu.locked'|trans([], 'AppBundle') }}" data-is-logged-in="{% if is_granted("IS_AUTHENTICATED_REMEMBERED")%}true{% else %}0{% endif %}" data-fake-button-msg="{{ 'offers.bidbutton_msg'|trans({}, 'AppBundle') }}">

<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5SLDR5B"
                  height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>

<!-- End Google Tag Manager (noscript) -->
{% include("svg-icons.svg") %}
<!--[if lt IE 10]>
<div class="Message-item Message--warning" style="padding:5px">
    {% if locale == 'fr'  %}
    Vous utilisez un navigateur obsolète! veuillez mettre à jour votre navigateur pour une expérience optimale.
    {% else %}
    Your browser is outdated! please upgrade to a more recent version for an optimal experience.
    {% endif %}
</div>
<![endif]-->

{#% set menu = menuService.findByName('hamburger') %#}

<div class="page-wrap">
    {% block header %}
        {% include ('@OpenFront/menu/header.html.twig') %}
        {{ render(controller("Open\\FrontBundle\\Controller\\CategoriesNavBarController::getAction", {}, {'from':app.request.get('_route')})) }}
    {% endblock %}

    {% block menu %}{% endblock %}
    <main role="main" class="container">
        <div class="Messages">
            {% block messages %}{% endblock %}
            {% for label, messages in app.flashes %}
                {% for message in messages %}
                    <div class="Message-item Message--{{ label }}">
                        {{ message | raw}}

                        <span class="Message-closeButton js-close-message-button">
                            <svg class="Icon">
                                <use xlink:href="#icon-close-message"></use>
                            </svg>
                        </span>
                    </div>
                {% endfor %}
            {% endfor %}
        </div>
        <div class="side-container">
            {% block sideMenu %}{% endblock %}
            {% block body %}{% endblock %}
        </div>
    </main>
</div>

<div class="cookiebarcontainer" id="js-cookies-header">
    <div class="cbmessage">
        <p>{{ 'cookie.message' |trans({}, 'AppBundle') }}</p>
        <a class="cbhide button" id="js-close-cookies-header">{{ 'cookie.accept'|trans({}, 'AppBundle') }}</a>
    </div>
</div>

{% include '@OpenFront/menu/footer.html.twig' %}

{% block javascripts %}

{% endblock %}

<script type="text/javascript">
    {# script to detect if the user is using tab to navigate -> outline on element #}

    function handleFirstTab(e) {
        if (e.keyCode === 9) {
            document.body.classList.add('user-is-tabbing');

            window.removeEventListener('keydown', handleFirstTab);
            window.addEventListener('mousedown', handleMouseDownOnce);
        }
    }

    function handleMouseDownOnce() {
        document.body.classList.remove('user-is-tabbing');

        window.removeEventListener('mousedown', handleMouseDownOnce);
        window.addEventListener('keydown', handleFirstTab);
    }

    window.addEventListener('keydown', handleFirstTab);

    function removeSpacesCustomPageMobile() {
        if ($(window).width() <= 960) {
            $(".Page-type--static").find("td").html(function (i, html) {
                return html.replace(/&nbsp;/g, '');
            });
        }
    }

    removeSpacesCustomPageMobile();
    $(window).on('resize', removeSpacesCustomPageMobile());

</script>

<script type="text/template" id="js-modal-tpl">
    <div class="Modal{{ "{{ classes }}" }}" id="{{ "{{ id }}" }}" style="display:none">
        <div class="Modal-overlay js-modal-overlay">

        </div>
        <div class="Modal-wrapper">
            <div class="Modal-content js-modal-content">
                <svg class="Icon js-close-icon">
                    <use xlink:href="#icon-close"></use>
                </svg>
                {# content will be inserted here #}
            </div>
        </div>
    </div>
</script>
<script type="text/template" id="js-confirm-modal-tpl">
    <div class="Modal-header">
        <h5 class="Modal-title">{{ "{{ title }}" }}</h5>
        {#<button type="button" class="close js-cancel-button" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">×</span>
        </button>#}
    </div>
    <div class="Modal-body">
        <p>{{ "{{ txt }}" }}</p>
    </div>
    <div class="Modal-footer">
        <button class="btn btn-secondary buttonModal js-cancel-button">{{ 'modal.no'|trans({}, 'AppBundle') }}</button>
        <button id="js-confirm-button" class="btn btn-primary buttonModal">{{ 'modal.yes'|trans({}, 'AppBundle') }}</button>
    </div>
</script>
<script type="text/template" id="js-alert-modal-tpl">
    <div class="Modal-header">
        <h5 class="Modal-title">{{ "{{ title }}" }}</h5>
        <button type="button" class="close js-cancel-button" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">×</span>
        </button>
    </div>
    <div class="Modal-body">
        <p>{{ "{{ txt }}" }}</p>
    </div>
    <div class="Modal-footer">
        <button id="js-confirm-button" class="btn btn-primary buttonModal">{{ 'modal.confirm'|trans({}, 'AppBundle') }}</button>
    </div>
</script>

<script type="text/template" id="js-loading-modal-tpl">
    <svg style="overflow:visible" width='128px' height='128px' xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid">
        <defs>
            <filter id="uil-ring-shadow" x="-100%" y="-100%" width="300%" height="300%">
                <feOffset result="offOut" in="SourceGraphic" dx="0" dy="0"></feOffset>
                <feGaussianBlur result="blurOut" in="offOut" stdDeviation="0"></feGaussianBlur>
                <feBlend in="SourceGraphic" in2="blurOut" mode="normal"></feBlend>
            </filter>
        </defs>
        <path d="M10,50c0,0,0,0.5,0.1,1.4c0,0.5,0.1,1,0.2,1.7c0,0.3,0.1,0.7,0.1,1.1c0.1,0.4,0.1,0.8,0.2,1.2c0.2,0.8,0.3,1.8,0.5,2.8 c0.3,1,0.6,2.1,0.9,3.2c0.3,1.1,0.9,2.3,1.4,3.5c0.5,1.2,1.2,2.4,1.8,3.7c0.3,0.6,0.8,1.2,1.2,1.9c0.4,0.6,0.8,1.3,1.3,1.9 c1,1.2,1.9,2.6,3.1,3.7c2.2,2.5,5,4.7,7.9,6.7c3,2,6.5,3.4,10.1,4.6c3.6,1.1,7.5,1.5,11.2,1.6c4-0.1,7.7-0.6,11.3-1.6 c3.6-1.2,7-2.6,10-4.6c3-2,5.8-4.2,7.9-6.7c1.2-1.2,2.1-2.5,3.1-3.7c0.5-0.6,0.9-1.3,1.3-1.9c0.4-0.6,0.8-1.3,1.2-1.9 c0.6-1.3,1.3-2.5,1.8-3.7c0.5-1.2,1-2.4,1.4-3.5c0.3-1.1,0.6-2.2,0.9-3.2c0.2-1,0.4-1.9,0.5-2.8c0.1-0.4,0.1-0.8,0.2-1.2 c0-0.4,0.1-0.7,0.1-1.1c0.1-0.7,0.1-1.2,0.2-1.7C90,50.5,90,50,90,50s0,0.5,0,1.4c0,0.5,0,1,0,1.7c0,0.3,0,0.7,0,1.1 c0,0.4-0.1,0.8-0.1,1.2c-0.1,0.9-0.2,1.8-0.4,2.8c-0.2,1-0.5,2.1-0.7,3.3c-0.3,1.2-0.8,2.4-1.2,3.7c-0.2,0.7-0.5,1.3-0.8,1.9 c-0.3,0.7-0.6,1.3-0.9,2c-0.3,0.7-0.7,1.3-1.1,2c-0.4,0.7-0.7,1.4-1.2,2c-1,1.3-1.9,2.7-3.1,4c-2.2,2.7-5,5-8.1,7.1 c-0.8,0.5-1.6,1-2.4,1.5c-0.8,0.5-1.7,0.9-2.6,1.3L66,87.7l-1.4,0.5c-0.9,0.3-1.8,0.7-2.8,1c-3.8,1.1-7.9,1.7-11.8,1.8L47,90.8 c-1,0-2-0.2-3-0.3l-1.5-0.2l-0.7-0.1L41.1,90c-1-0.3-1.9-0.5-2.9-0.7c-0.9-0.3-1.9-0.7-2.8-1L34,87.7l-1.3-0.6 c-0.9-0.4-1.8-0.8-2.6-1.3c-0.8-0.5-1.6-1-2.4-1.5c-3.1-2.1-5.9-4.5-8.1-7.1c-1.2-1.2-2.1-2.7-3.1-4c-0.5-0.6-0.8-1.4-1.2-2 c-0.4-0.7-0.8-1.3-1.1-2c-0.3-0.7-0.6-1.3-0.9-2c-0.3-0.7-0.6-1.3-0.8-1.9c-0.4-1.3-0.9-2.5-1.2-3.7c-0.3-1.2-0.5-2.3-0.7-3.3 c-0.2-1-0.3-2-0.4-2.8c-0.1-0.4-0.1-0.8-0.1-1.2c0-0.4,0-0.7,0-1.1c0-0.7,0-1.2,0-1.7C10,50.5,10,50,10,50z" fill="#E7B315" filter="url(#uil-ring-shadow)">
            <animateTransform attributeName="transform" type="rotate" from="0 50 50" to="360 50 50" repeatCount="indefinite" dur="1s"></animateTransform>
        </path>
    </svg>
</script>
{# Unvailable on login page to prevent the invalid CSRF token #}
{% if app.request.attributes.get('_route') != 'login_failure_route' %}
    <script type="text/template" id="js-login-modal-tpl">
        <div class="Modal-header">
        </div>
        <div class="Modal-body">
        {{ render(controller('FOS\\UserBundle\\Controller\\SecurityController::loginAction')) }}
        </div>
        <div class="Modal-footer">
        </div>
    </script>
{% endif %}
</body>
</html>
