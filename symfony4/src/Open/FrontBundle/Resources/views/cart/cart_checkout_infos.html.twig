{% trans_default_domain 'AppBundle' %}
<div class="cart-checkout-infos">
    {{ form_start(form) }}
    {{ form_widget(form.merchantShippings) }}
    {{ form_widget(form.addressId) }}
    {{ form_widget(form._token) }}
    <div class="checkout-title">
        <h2 class="title">{{ 'cart.checkout.title'|trans }}</h2>
    </div>

    <div class="row">
        <div class="select-blocks">
            <div class="col-sm-12 col-lg-4">
                {# COST CENTER SELECT INPUT #}
                <div class="block" id="select-site-container">
                    <div class="title">
                        {{ 'cart.checkout.cost_center'|trans }}
                    </div>
                    {% if userCanModifyShippingPoint(user, cart) %}
                        <div class="wrapper-block">
                            <div class="js-select-wrapper select-wrapper has-text" style="margin: 0;">
                                <select id="select-cost-center" {% if isShipping %} disabled {% endif %}>
                                    <option value="" selected disabled
                                            hidden>{{ 'cart.checkout.select.cost_center_placeholder' |trans }}</option>
                                    {% for keySite, site in sites %}
                                        <option value="{{ site.id }}">{{ site.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        {% if is_granted('ROLE_BUYER_ADMIN') %}
                            <a class="color-primary"
                               href="{{ path('front.company.sites') }}">{{ 'cart.checkout.add_new_cost_center'|trans }}</a>
                        {% endif %}
                    {% else %}
                        {% if shippingPoint %}
                            <div>
                                {{ shippingPoint.site.name }}
                                <input type="hidden" id="select-cost-center" value="{{ shippingPoint.site.id }}">
                            </div>
                        {% endif %}
                    {% endif %}
                </div>
            </div>
            <div class="col-sm-12 col-lg-4">
                {# ADDRESS SELECT INPUT #}
                <div class="block" id="select-address-container">
                    <div class="title">
                        {{ 'cart.checkout.address'|trans }}
                    </div>
                    {% if userCanModifyShippingPoint(user, cart) %}
                        <div class="wrapper-block">
                            <div class="js-select-wrapper select-wrapper has-text" style="margin: 0;">
                                <select id="select-address" {% if isShipping %} disabled {% endif %}>
                                    <option value="" selected disabled
                                            hidden>{{ 'cart.checkout.select.address_placeholder' |trans }}</option>
                                    {% if sites|length > 0 %}
                                        {% for keySP, shippingPoint in sites[0].shippingPoints %}
                                            {% set selected = '' %}
                                            {% if cart.address is not empty and shippingPoint.address.id == cart.address.id %}{% set selected = 'selected' %}{% endif %}
                                            <option {{ selected }} value='{{ shippingPoint.address.id }}'
                                                                   data-site-id="{{ sites[0].id }}">{{ shippingPoint.name }}</option>
                                        {% endfor %}
                                    {% endif %}
                                </select>
                            </div>
                        </div>
                        {% if is_granted('ROLE_BUYER_ADMIN') %}
                            <a class="color-primary"
                               href="{{ path('front.company.sites') }}">{{ 'cart.checkout.add_new_address'|trans }}</a>
                        {% endif %}
                    {% else %}
                        {% if shippingPoint %}
                            <div>
                                {{ shippingPoint.name }}
                                <input type="hidden" id="select-address" data-shipping-id="{{ shippingPoint.id }}"
                                       value="{{ shippingPoint.address.id }}">
                            </div>
                        {% endif %}
                    {% endif %}
                </div>
            </div>
            <div class="col-sm-12 col-lg-4">
                {# PAYMENT MODE SELECT INPUT #}
                {% if (is_granted('ROLE_BUYER_PAYER') or is_granted('ROLE_BUYER_ADMIN')) %}
                    <div class="block">
                        <div class="title">
                            {{ 'cart.checkout.payment_mode'|trans }}
                        </div>

                        <div class="wrapper-block">
                            <div class="js-select-wrapper select-wrapper has-text" style="margin: 0;">
                                <select id="select-payment-mode" name="{{ form.term.vars['full_name'] }}">
                                    <option value="" {% if cart.paymentMode == null %}selected{% endif %} disabled
                                            hidden>{{ 'cart.checkout.select.payment_mode_placeholder' |trans }}</option>
                                    {% for child in form.term.children %}
                                        <option {% if cart.paymentMode == child.vars.value %}selected{% endif %}
                                                value="{{ child.vars.value }}">{{ child.vars.label|trans }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="row">
        <div class="input-block w-100">
            <div class="col-sm-12 col-lg-4">
                <div class="block">
                    <div class="title">
                        {{ 'cart.checkout.accounting_email'|trans }}
                    </div>
                    <div class="wrapper-block validation-number" id="accountingEmail">
                        {{ form_widget(form.accountingEmail) }}
                    </div>
                </div>
            </div>
            <div class="col-sm-12 col-lg-4">
                {# BUYER REFERENCE INPUT #}
                {% if userCanModifyCartValidationNumber(user, cart) %}
                    <div class="block">
                        <div class="title">
                            {{ 'cart.checkout.validation_number'|trans }}
                        </div>
                        <div class="wrapper-block validation-number">
                            {{ form_widget(form.validationNumber) }}
                            <div class="tooltip position-absolute">
                                <svg class="Icon">
                                    <use xlink:href="#icon-help"></use>
                                </svg>
                                <div class="tooltiptext">
                                    {{ 'cart.checkout.validation_number'|trans }}
                                </div>
                            </div>
                        </div>
                    </div>
                {% else %}
                    {% if cart.validationNumber is not empty %}
                        <div class="block">
                            <div class="title">
                                {{ 'cart.checkout.validation_number'|trans }}
                            </div>
                            <div>
                                {{ cart.validationNumber }}
                            </div>
                        </div>
                    {% endif %}
                {% endif %}
            </div>
            <div class="col-sm-12 col-lg-4">
                    <div class="title text-uppercase font-weight-bolder">
                        {{ 'offer_detail.proforma.version'|trans }}
                    </div>
                    <div class="d-flex flex-column h-100 justify-content-center">
                        <a class="p-0" href="#" onclick="onClickProforma(event)" target="_blank" id="proforma-send">
                            <svg class="Icon m-0" style="height: 24px; fill: #9600ff">
                                <use xlink:href="#icon-comparison"></use>
                            </svg><span style="font-size: 16px">{{ 'offer_detail.proforma'|trans }}</span>
                        </a>
                    </div>
            </div>
        </div>
    </div>

    {# BILLING ADDRESS INPUT #}
    <div class="checkout-title mt-4">
        <h2 class="title">{{ 'cart.checkout.billing_address'|trans }}</h2>
    </div>
    <div class="row">
        <div class="input-block w-100">
            <div class="col-sm-12 col-lg-4">
                <div class="block">
                    <div class="title">
                        {{ 'cart.checkout.address'|trans }}
                    </div>
                    <div class="wrapper-block validation-number width-100">
                        {{ form_widget(form.billingAddress.address) }}
                    </div>
                </div>
            </div>
            <div class="col-sm-12 col-lg-4">
                <div class="block">
                    <div class="title">
                        {{ 'cart.checkout.address_complement'|trans }}
                    </div>
                    <div class="wrapper-block validation-number">
                        {{ form_widget(form.billingAddress.address2) }}
                    </div>
                </div>
            </div>
            <div class="col-sm-12 col-lg-4">
                <div class="input-block w-100">
                    <div class="block">
                        <div class="title">
                            {{ 'cart.checkout.zipcode'|trans }}
                        </div>
                        <div class="wrapper-block validation-number">
                            {{ form_widget(form.billingAddress.zipCode) }}
                        </div>
                    </div>
                    <div class="block">
                        <div class="title">
                            {{ 'cart.checkout.city'|trans }}
                        </div>
                        <div class="wrapper-block validation-number">
                            {{ form_widget(form.billingAddress.city) }}
                        </div>
                    </div>
                    <div class="block">
                        <div class="title">
                            {{ 'cart.checkout.area'|trans }}
                        </div>
                        <div class="wrapper-block validation-number">
                            {{ form_widget(form.billingAddress.regionText) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="checkout-title mt-4 mb-3">
        <h2 class="title mb-0">{{ 'seller.general_condition'|trans }}</h2>
        <small class="color-primary">{{ 'cart.checkout.buyer_need_to_accept_term_and_conditions'|trans }}</small>
    </div>
    <div class="row">
        <div class="col-sm-12 col-lg-12">
            {% for merchant in cart.merchants %}
                <div class="dispute-checkbox">
                    <input id="checkbox-sale-cgv-{{ merchant.id }}" type="checkbox" data-link-cgv="{{ merchant.cgv }}" name="sale_cgv_{{ merchant.id }}" onchange="openLinkCgv(this)"/>
                    <label class="form-control-label text-underline seller-name" for="checkbox-sale-cgv-{{ merchant.id }}">{{ merchant.name }}</label>
                </div>
            {% endfor %}
        </div>
    </div>

    <div class="infos">
        <div class="cost-center-details" style="display: none">
            <div class="site-details">
                <div class="checkout-title">
                    <h2 class="title">{{ 'cart.cost_center.contact.title' | trans }}</h2>
                </div>
                <div class="site-details-container">
                    <div class="contact">
                        <div class="info-field">
                            <span class="label">{{ 'cart.cost_center.contact.name' | trans }} : </span>
                            <span id="site-details-contact-name" class="value">{# loaded with jquery #}</span>
                        </div>
                        <div class="info-field">
                            <span class="label">{{ 'cart.cost_center.contact.email' | trans }} : </span>
                            <span id="site-details-contact-email" class="value">{# loaded with jquery #}</span>
                        </div>
                    </div>
                    <div class="comments">
                        <div class="info-field">
                            <span class="label">{{ 'cart.cost_center.comments' | trans }} : </span>
                            <p id="site-details-comment" class="value">{# loaded with jquery #}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="packaging-documents">
                <div class="packaging">
                    <div class="checkout-title">
                        <h2 class="title">{{ 'cart.cost_center.packaging_request.label' | trans }}</h2>
                    </div>
                    <ul id="site-packaging-specification">
                        {# loaded with jquery #}
                    </ul>
                </div>

                <div class="documents">
                    <div class="checkout-title">
                        <h2 class="title">{{ 'cart.cost_center.requested_documents.label' | trans }}</h2>
                    </div>
                    <div id="site-requested-documents">
                        {% if isShipping and documentsRequest is defined %}
                            {% for document in documentsRequest %}
                                <div class='info-field'>
                                    <input id="{{ document }}" class='documents-requests custom-checkbox'
                                           type='checkbox' checked disabled/>
                                    <label style="white-space: normal" for="{{ document }}"
                                           class='value'>{{ document }}</label>
                                </div>
                            {% endfor %}
                        {% endif %}
                        {# if is not shiping it will be load with jquery #}
                    </div>
                </div>
            </div>
        </div>

        <div class="button-block">
            {% if not hasShippableItems %}
                {% if userCanRejectCart(user, cart) %}
                    <button type="button" class="btn-primary" onclick="onClickReject();">
                        {{ 'cart.reject.title'|trans|upper }}
                    </button>
                {% endif %}

                {% if userCanValidateCart(user, cart) %}
                    <button id="payment_mode_select_form_submit" class="btn-primary not-allowed tooltip" disabled>
                        {{ 'cart.buttons.validate'|trans|upper }}
                        <div class="tooltiptext position-absolute">
                            <span class="info">
                                {{ 'cart.checkout.term_and_conditions_must_accept'|trans }}
                            </span>
                        </div>
                    </button>
                {% endif %}

                {% if (userCanRejectCart(user, cart) or userCanValidateCart(user, cart)) and userCanAssignCart(user, cart) %}
                    <span> or </span>
                {% endif %}

                {% if userCanAssignCart(user, cart) %}
                    <button type="button" class="assign btn-primary inverse"
                            onclick="onClickAssign({{ cart.shippingPoint.id ?? null }})">
                        {{ 'cart.buttons.assign'|trans|upper }}
                    </button>
                {% endif %}
            {% endif %}

            {% if hasShippableItems %}
                <button type="button" onclick="onClickNext()"
                        id="next-button"
                        disabled
                        class="assign btn-primary inverse not-allowed tooltip">
                    {{ 'label_next'|trans|upper }}
                    <div class="tooltiptext position-absolute">
                        <span class="info">
                            {{ 'cart.checkout.term_and_conditions_must_accept'|trans }}
                        </span>
                    </div>
                </button>
            {% endif %}
        </div>
    </div>

    {{ form_end(form, {'render_rest': false}) }}
</div>

{% block javascripts %}
    <script type="text/javascript">
        var defaultAddressId = null;
        var isShipping = '{{ isShipping }}';
        var sites = [];
        {% for keySite, site in sites %}
        sites[{{ site.id }}] = [];
        {% for keySP, shippingPoint in site.shippingPoints %}
        sites[{{ site.id }}].push({
            addressId: {{ shippingPoint.address.id }},
            siteId: {{ site.id }},
            shippingPointId: '{{ shippingPoint.id }}',
            shippingPointName: '{{ shippingPoint.name }}',
            shippingContactName: '{{ shippingPoint.contact.firstName ~ ' ' ~  shippingPoint.contact.lastName }}',
            shippingContactEmail: '{{ shippingPoint.contact.email }}',
            shippingComment: '{{ shippingPoint.comment|nl2br|e('js') }}',
            packagingRequest1: '{{ shippingPoint.packagingRequest1 }}',
            packagingRequest2: '{{ shippingPoint.packagingRequest2 }}',
            packagingRequest3: '{{ shippingPoint.packagingRequest3 }}',
            documentationRequest1: '{{ shippingPoint.documentationRequest1 }}',
            documentationRequest2: '{{ shippingPoint.documentationRequest2 }}',
            documentationRequest3: '{{ shippingPoint.documentationRequest3 }}',
            documentationRequest4: '{{ shippingPoint.documentationRequest4 }}',
            documentationRequest5: '{{ shippingPoint.documentationRequest5 }}',
            documentationRequest6: '{{ shippingPoint.documentationRequest6 }}',
            documentationRequest7: '{{ shippingPoint.documentationRequest7 }}',
            documentationRequest8: '{{ shippingPoint.documentationRequest8 }}',
            documentationRequest9: '{{ shippingPoint.documentationRequest9 }}',
            documentationRequest10: '{{ shippingPoint.documentationRequest10 }}',
            accountantEmail: '{{ shippingPoint.accountantEmail }}'
        });
        {% endfor %}
        {% endfor %}

        function getSiteAddress(siteId, addressId) {
            let siteAddresses = sites[siteId];
            for (let i = 0; i < siteAddresses.length; i++) {
                if (siteAddresses[i].addressId === addressId) {
                    fillAccountingEmailInput(siteAddresses[i].accountantEmail)

                    return siteAddresses[i];
                }
            }
            return null;
        }

        /**
         * @param {Object} accountantEmail (see the "sites" array object)
         */
        function fillAccountingEmailInput(accountantEmail) {
            const accountingEmailInput = document.querySelector('#accountingEmail input')
            if (!accountingEmailInput) {
                console.error('Invalid accounting email form')
            } else {
                const val = '{{ cart.accountingEmail }}'
                if (val !== '') {
                    accountingEmailInput.value = val
                } else if (accountingEmailInput.value === '') {
                    accountingEmailInput.value = accountantEmail
                }
            }
        }

        function affectSiteContact(site) {
            $("#site-details-contact-name").html(site.shippingContactName);
            $("#site-details-contact-email").html(site.shippingContactEmail);
        }

        function affectSiteComment(site) {
            $("#site-details-comment").html(site.shippingComment);
        }

        function affectCostCenterDetails(site) {
            if (!site) {
                $('.cost-center-details').hide();
                return;
            }

            $('.cost-center-details').show();

            var packagingSpecificationContent = "";
            var requestedDocumentContent = "";

            for (let i = 1; i <= 3; i++) {
                let property = 'packagingRequest' + i;
                if (site[property]) {
                    packagingSpecificationContent += "<li class='info-field'>" + site[property] + "</li>";
                }
            }

            for (let i = 1; i <= 10; i++) {
                let property = 'documentationRequest' + i;
                if (site[property]) {
                    requestedDocumentContent += "<div class='info-field'><input id='" + property + "' class='documents-requests custom-checkbox' type='checkbox' data-document='" + site[property] + "'/><label style='white-space: normal' for='" + property + "' class='value'>" + site[property] + "</label></div>";
                }
            }

            // update DOM
            $("#site-packaging-specification").html(
                packagingSpecificationContent !== "" ? packagingSpecificationContent : "<div class='info-field'>{{ 'cart.cost_center.packaging_request.none' | trans }}</div>"
            );
            if (!isShipping) {
                $("#site-requested-documents").html(
                    requestedDocumentContent !== "" ? requestedDocumentContent : "<div class='info-field'>{{ 'cart.cost_center.requested_documents.none' | trans }}</div>"
                );
            }
        }


        function affectValueAddress(site) {
            $('#select-address-container').find('.select-wrapper__box').remove();
            $('#select-site-container').find('.select-wrapper__box').remove();
            $('#select-address-container').find('.select-wrapper__placeholder').html('{{ 'cart.checkout.select.address_placeholder' |trans }}');
            $('#select-address').empty();

            for (let i = 0; i < site.length; i++) {
                let option = $("<option value='" + site[i].addressId + "'data-site-id='" + site[i].siteId + "' data-shipping-id='" + site[i].shippingPointId + "' >" + site[i].shippingPointName + "</option>");
                $('#select-address').append(option);
            }

            window.UI.Select.init();
            $('#select-address').val(defaultAddressId);
        }

        $('#select-address').change(function (event) {
            let options = event.target.options;


            if (typeof options === 'undefined') {
                return;
            }

            let selectedIndex = options[event.target.selectedIndex];

            let siteId = parseInt(selectedIndex.dataset.siteId);
            let addressId = parseInt(selectedIndex.value);
            let siteAddress = getSiteAddress(siteId, addressId);

            if (siteAddress) {
                affectSiteContact(siteAddress);
                affectSiteComment(siteAddress);
                affectCostCenterDetails(siteAddress);
            }
        });

        document.addEventListener('DOMContentLoaded', function () {
            window.UI.Select.init();

            defaultAddressId = {{ cart.address.id ?? null | json_encode }};

            $('#select-cost-center').change(function () {
                var id = ($('#select-cost-center :selected').val());
                var site = sites[id];
                if (typeof site !== 'undefined') {
                    affectValueAddress(site);
                }
            });

            $('#payment_mode_select_form_validationNumber').val('{{ cart.validationNumber }}');
            // document.querySelector('#accountingEmail input').value = '{{ cart.accountingEmail }}'

            {% if cart.billingAddress is not null %}
            {% set billingAddress = cart.billingAddress %}
            {% else %}
            {% if user.company.billingAddress is not null %}
            {% set billingAddress = user.company.billingAddress %}
            {% else %}
            {% set billingAddress = user.company.mainAddress %}
            {% endif %}
            {% endif %}
            $('#payment_mode_select_form_billingAddress_address').val('{{ billingAddress.address }}');
            $('#payment_mode_select_form_billingAddress_address2').val('{{ billingAddress.address2 }}');
            $('#payment_mode_select_form_billingAddress_zipCode').val('{{ billingAddress.zipCode }}');
            $('#payment_mode_select_form_billingAddress_city').val('{{ billingAddress.city }}');
            $('#payment_mode_select_form_billingAddress_regionText').val('{{ billingAddress.regionText }}');

            {% if assignSite is defined and assignSite is not null %}
            {% if cart is defined and cart is not null and cart.address is defined and cart.address is not null %}
            $('#select-cost-center').val({{ assignSite.id }}).change();
            $('#select-address').val({{ cart.address.id }}).change();
            {% endif %}
            {% endif %}

            // Submit the checkout form
            $('#payment_mode_select_form_submit').on('click', function (ev) {
                ev.preventDefault();

                var cannotProceedToCheckout = function () {
                    return (
                        ($('#select-cost-center').length > 0 && $('#select-cost-center').val() == null)
                        || ($('#select-address').length > 0 && $('#select-address').val() == null)
                        || ($('#select-payment-mode').length > 0 && $('#select-payment-mode').val() == null)
                        || ($('#payment_mode_select_form_billingAddress_address').val() == '')
                        || ($('#payment_mode_select_form_billingAddress_zipCode').val() == '')
                        || ($('#payment_mode_select_form_billingAddress_city').val() == '')
                        || ($('#payment_mode_select_form_accountingEmail').val() == '')
                        || ($('#payment_mode_select_form_validationNumber').val() == '')
                    );
                };

                {% for merchant in cart.merchants %}
                {% if merchant.subTotalWithoutVat > 0 and  merchant.subTotalWithoutVat < merchant.minimumOrderAmount %}
                window.UI.Modal.alert("{{ 'cart.checkout.minimum_order_amount'|trans|raw }}");
                return false;
                {% endif %}
                {% endfor %}

                if (cannotProceedToCheckout()) {
                    window.UI.Modal.alert('{{ 'cart.checkout.select.error_billing'|trans }}');
                } else {
                    if ("{{ companyValid }}" === "false") {
                        window.UI.Modal.alert('{{ 'offer_detail.company_not_valid'|trans }}');
                    } else {
                        if ($('#select-address').length > 0) {
                            $('#payment_mode_select_form_addressId').val($('#select-address').val());
                        }

                        window.UI.Modal.showLoading();

                        if (!isShipping) {
                            // Add documents to cart
                            saveDocs(function () {
                                $("form").submit();
                            });
                        } else {
                            $("form").submit();
                        }
                    }
                }
            });
        });

        function saveDocs(callback) {
            var address = document.getElementById('select-address');
            var shippingPointId = null;
            if (address.tagName === 'SELECT') {
                shippingPointId = address.options[address.selectedIndex].dataset.shippingId
            } else {
                shippingPointId = address.dataset.shippingId
            }

            var documentsRequestsList = [];
            var documentsRequests = $('.documents-requests');
            for (let cpt = 0; cpt < documentsRequests.length; cpt++) {
                if (documentsRequests[cpt].checked) {
                    let index = documentsRequestsList.length;
                    documentsRequestsList[index] = documentsRequests[cpt].dataset.document;
                }
            }
            loadingModal = window.UI.Modal.showLoading();
            $.post('{{ path('cart.documents.save') }}', {
                'documents': documentsRequestsList,
                'shippingPointId': shippingPointId,
                'cartId': '{{ cart.id }}'
            }).done(callback).always(function () {
                closeLoading();
            });
        }

        function openLinkCgv(checkbox) {
            if (checkbox.checked) {
                const link = $(checkbox).data('link-cgv');
                if (link) window.open(link, '_blank');
            }
            checkAllCheckboxes();
        }

        function checkAllCheckboxes() {
            const allChecked = $('input[type="checkbox"][name^="sale_cgv_"]').toArray().every(checkbox => checkbox.checked);
            if (allChecked) {
                $('#next-button').prop('disabled', false).removeClass('not-allowed tooltip');
                $('#payment_mode_select_form_submit').prop('disabled', false).removeClass('not-allowed tooltip');
            } else {
                $('#next-button').prop('disabled', true).addClass('not-allowed tooltip');
                $('#payment_mode_select_form_submit').prop('disabled', true).addClass('not-allowed tooltip');
            }
        }

        const onClickProforma = function(e) {
            e.preventDefault();
            let url = '{{ path('front.cart.offers.proforma', {cartId: cart.id}) }}';
            const billingAddressMain = $('#payment_mode_select_form_billingAddress_address').val();
            const billingAddressComplement = $('#payment_mode_select_form_billingAddress_address2').val();
            const billingAddressZipCode = $('#payment_mode_select_form_billingAddress_zipCode').val();
            const billingAddressCity = $('#payment_mode_select_form_billingAddress_city').val();
            const billingAddressArea = $('#payment_mode_select_form_billingAddress_regionText').val();
            url = url+"?billingAddressMain="+billingAddressMain+"&billingAddressComplement="+billingAddressComplement+"&billingAddressZipCode="+billingAddressZipCode+"&billingAddressCity="+billingAddressCity+"&billingAddressArea="+billingAddressArea;
            window.open(url, '_blank');
        };
    </script>
{% endblock %}
