{% extends '@OpenFront/base.html.twig' %}
{% trans_default_domain 'AppBundle' %}

{% block title %} {{ 'cart.detail' | trans }}{% endblock %}

{% block stylesheets %}
    <link href="{{ asset('css/page-default-v1.css') }}" rel="stylesheet" media="screen">
{% endblock %}

{% block body %}
    <div class="Page-cart-inner">
        {% set locale = app.session.get("_locale") %}
        {% if cart is not empty and cart.itemsCount > 0 %}

            {% include('@OpenFront/cart/cart_steps.html.twig') with {
                'hasShipping': cart.hasShippableItems(),
                'isProductAndAddressSelection' : true,
                'isShipping': false,
                'isOrderConfirmation': false
            } %}

            <div>
                <div class="cart-header">
                    <div class="cart-title">
                        <h2 id="currency" data-currency="{{ cart.currency }}" class="currency">{{ cart.currency }} cart </h2>
                        <div id="items-count">
                            <h2>&nbsp;-&nbsp;</h2>
                            <h2 class="count">{{ cart.itemsCount }}
                                {% if cart.itemsCount == 1 %}
                                    {{ 'cart.article'|trans }}
                                {% else %}
                                    {{ 'cart.articles'|trans }}
                                {% endif %}
                            </h2>
                            {% if not cart.isAssigned %}
                                <a class="cart-clear color-primary" href="{{ path("cart.clear", {cartId: cart.id, currency: cart.currency}) }}" >{{ 'cart.buttons.clear_cart'|trans }}</a>
                            {% endif %}
                        </div>
                    </div>
                    <div class="cart-header-right">
                        {% if isUserAuthorizeToEditCart and cart.valid %}
                            <a href="{{ path("front.search") }}" ><button class="return desktop-only btn-grey"><i class="arrow left"></i>{{ 'cart.buttons.continue_shopping'|trans|upper }}</button></a>
                            <a href="#" onclick="wishListForm.showWishlistForm()"><button class="return desktop-only btn-grey btn-middle">{{ 'cart.buttons.save_in_wishlist'|trans|upper }}</button></a>
                            <a href="#anchorCheckout"><button class="return desktop-only btn-primary">{{ 'cart.buttons.checkout'|trans|upper }}</button></a>
                        {% else %}
                            <a href="#anchorAssignment"><button class="return desktop-only btn-primary btn-assign-anchor">{{ 'cart.buttons.see_assignment'|trans|upper }}</button></a>
                        {% endif %}
                    </div>
                </div>
            </div>

            {% if not cart.valid %}
                    <div class="cart-wrong-price-message">
                        <p>{{ 'cart.wrong_price_message'|trans }}</p>
                    </div>
            {% endif %}

            <div class="Page-content">
                {% for merchant in cart.merchants %}
                    {% include('@OpenFront/cart/cart_seller_table.html.twig') with {'cartStatus': cart.status, 'merchant': merchant, 'index': loop.index, 'isShipping': false, 'shipping': shipping} %}
                {% endfor %}

                {# ------ TOTAL TABLE ------ #}
                <div class="total-container">
                    <table id="table-merchant-total" class="seller-table table-total">
                        <thead>
                        <tr>
                            <th class="label-seller"></th>
                            <th class="label-detail"></th>
                            <th class="upperline"></th>
                            <th class="upperline"></th>
                            <th class="upperline"></th>
                        </tr>
                        </thead>
                        <tbody>
                        {# ------ TOTAL WITHOUT VAT ------ #}
                        <tr class="resume-table subtotal">
                            <td class="hidden-desktop subtotal-title"><span>{{ 'cart.table_label.total_order'|trans }}</span> - {{ cart.itemsCount }} {{ cart.itemsCount > 1 ? 'cart.articles' | trans : 'cart.article' | trans }}</td>
                        </tr>
                        <tr class="resume-table subtotal">
                            <td class="hidden-mobile subtotal-title"><span>{{ 'cart.table_label.total_order'|trans }}</span> - {{ cart.itemsCount }} {{ cart.itemsCount > 1 ? 'cart.articles' | trans : 'cart.article' | trans }}</td>
                            <td class="hidden-mobile"></td>
                            <td class="label taxes-label">{{ 'cart.table_label.total'|trans }}</td>
                            <td></td>
                            <td class="total-without-vat">
                                {{ cart.subTotalWithoutVat | price(locale) }} {{ cart.currency }}
                            </td>
                        </tr>
                        {# ------ TAXES ------ #}
                        {% for vat,total in cart.subTotalVat %}
                            {% if vat > 0 or total > 0 %}
                            <tr id="total-taxes-{{ loop.index }}" class="total-table-taxes resume-table taxes">
                                <td class="hidden-mobile"></td>
                                <td class="hidden-mobile"></td>
                                <td class="taxes-label-cell">
                                    <p class="label-taxes label">{{ 'cart.table_label.taxes'|trans }}&nbsp;</p>
                                    <p class="vat-percent">{{ vat | number_format(2, '.')}}%</p>
                                </td>
                                <td></td>
                                <td class="vat-value">
                                    {{ total | price(locale) }} {{ cart.currency }}
                                </td>
                            </tr>
                            {% endif %}
                        {% endfor %}
                        {# ------ TOTAL WITH VAT ------ #}
                        <tr  id="subtotal-vat-total" class="resume-table subtotal-vat">
                            <td class="hidden-mobile">

                            </td>
                            <td class="hidden-mobile"></td>
                            <td class="label taxes-label" style="color: #9600FF !important; font-weight: bold;">{{ 'cart.table_label.total_vat'|trans }}</td>
                            <td></td>
                            <td class="total-cart" style="color: #9600FF !important; font-weight: bold;">
                                {{ cart.total | price(locale) }} {{ cart.currency }}
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>

                {# we want to display this block, only if user #}
                {% if isUserAuthorizeToEditCart and cart.valid %}
                    <div id="anchorCheckout">
                        {% include('@OpenFront/cart/cart_checkout_infos.html.twig') with {
                            'cartStatus': cart.status,
                            'isCartCreator': isCartCreator,
                            'isUserValidAssign': isUserValidAssign,
                            'isUserAuthorizeReject' : isUserAuthorizeReject,
                            'sites': sites,
                            'assignSite': cart.assignedSite,
                            'shippingPoint': cart.shippingPoint,
                            'hasShippableItems': cart.hasShippableItems,
                            'currencyOrCartId': currencyOrCartId,
                            'isShipping': false
                        } %}
                    </div>
                {% endif %}

                {% if not cart.valid %}
                    <div>
                        <div class="cart-wrong-price-message">
                            <p>{{ 'cart.wrong_price_message'|trans }}</p>
                        </div>
                    </div>
                {% endif %}
            </div>

            {# Tableau historique du panier (Modal?) #}
            <div class="historic-table" id="anchorAssignment">
                <div class="historic-top">
                    <h2 class="title">{{ 'cart.assignment_history'|trans }}</h2>
                </div>
                {% if historic is not null and historic|length > 0 %}
                    <div class="grid">
                        {% set index = 1 %}
                        {% for element in historic %}
                            {% set column = 'column-1' %}
                            {% if index is divisible by(2) and index != 0 %}
                                {% set column = 'column-2' %}
                            {% endif %}
                            {% set index = index+1 %}
                            <div class="{{ column }}">
                                {% include "@OpenFront/cart/cart_historic_block.html.twig" with {'element' : element, 'id' : index-1} %}
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
                {% if isUserAuthorizeToAutoAssign %}
                    <div class="grid">
                        <div class="cart-header-right">
                            <a href="{{ path('cart.auto.assign', {'cartId': cart.id}) }}">
                                <button type="button" class="btn-primary" style="float: left">
                                    {{ 'cart.assign.assign_to_myself'|trans }}
                                </button>
                            </a>
                        </div>
                    </div>
                {% endif %}
            </div>
        {% else %}
            <div class="empty-cart">
                <div>
                    <h1>{{ 'cart.empty.title' | trans }}</h1>

                    <p>{{ 'cart.empty.text' | trans | raw }}</p>
                    <a class="color-primary" href="{{ path('front.search') }}">{{ 'cart.empty.back' | trans }}</a>
                </div>

                <img alt="empty cart" class="cart-empty-img desktop-only" src="{{ asset('images/empty-cart-temporary.png') }}">
            </div>
        {% endif %}
    </div>
{% endblock %}

{% block javascripts %}
    {% set format_date_picker = (locale == 'fr' ? 'dd/mm/yyyy' : 'mm/dd/yyyy') %}
    {% if cart is not null %}
        <script type="text/javascript">
            $(document).on('click', ".edit-buyer-internal-reference", function (event) {
                event.preventDefault();
                let template = $(this).attr("data-template");
                UI.Modal.show('buyer-internal-reference','Modal--add', $($('#'+template).html()), true, true);
            });

            // cart item comment
            $(document).on('click', ".edit-cart-item-comment", function (event) {
                event.preventDefault();
                let template = $(this).attr("data-template");
                UI.Modal.show('cart-item-comment','Modal--add', $($('#'+template).html()), true, true);
            });

            $(document).on('keydown', "#cart_item_comment", function (e) {
                if ($(this).val().length >= 160 && e.keyCode !== 8)
                    e.preventDefault();
            });
            const format_date_picker = '{{ format_date_picker }}';

            $(document).ready(function() {
                $('.datepicker-delivery-input').each(function() {
                    const itemId = $(this).data('item-id');
                    const offerId = $(this).data('item-offerid');

                    const $input = $(this).pickadate({
                        format: format_date_picker,
                        formatSubmit: format_date_picker,
                        today: '',
                        clear: 'Clear',
                        close: '',
                        selectYears: true,
                        selectMonths: true,
                        editable: false,
                        onSet: function(context) {
                            if (context.select) {
                                saveSplitDelivery(itemId, offerId);
                            }
                        },
                        onClose: function() {
                            // code
                        }
                    });

                    const picker = $input.pickadate('picker');
                    const selectedDate = $input.val();

                    const parts = selectedDate.split('/');

                    let date;
                    date = new Date(parts[2], +parts[1] - 1, parts[0]);
                    if (format_date_picker === 'mm/dd/yyyy') {
                        date = new Date(parts[2], +parts[0] - 1, parts[1]);
                    }

                    picker.set('min', date);
                });
            });
            let loadingModal = null;
            let assignModal = null;
            let messageModal = null;
            let currency = '{{ cart.currency }}';
            let labelTaxes = '{{ 'cart.table_label.taxes'|trans }}';

            var closeLoading = function() {
                if(loadingModal !== null) {
                    loadingModal.close();
                }
            };

            var quantityChange = function(spinnerId, moq, stock, batchSize,offerId) {

                let quantityInput = $('#quantity_' + spinnerId);
                let qty = quantityInput.val();
                let productId = $('#product_'+spinnerId).val();
                qty = parseInt(qty);
                moq = parseInt(moq);
                stock = parseInt(stock);
                batchSize = parseInt(batchSize);
                if(qty <= 0) {
                    window.UI.Modal.alert("{{ 'offer_detail.wrong_quantity'|trans|raw }}", function() {
                        quantityInput.val(initialQuantities[spinnerId]);
                    });
                }else if(qty < moq){
                    let wrongQuantity = "{{ 'offer_detail.too_small_quantity'|trans|raw }}";
                    wrongQuantity = wrongQuantity.replace(new RegExp('%min%', 'g'), moq);
                    let contactTheVendor = "{{ 'offer_detail.contact_the_vendor'|trans|raw }}";
                    let text = wrongQuantity + "\n" + contactTheVendor;
                    window.UI.Modal.alert(text);
                } else if(qty > stock) {
                    let wrongQuantity = "{{ 'offer_detail.too_much_quantity'|trans|raw }}";
                    wrongQuantity = wrongQuantity.replace(new RegExp('%max%', 'g'), stock);
                    let contactTheVendor = "{{ 'offer_detail.contact_the_vendor'|trans|raw }}";
                    let text = wrongQuantity + "\n" + contactTheVendor;
                    window.UI.Modal.alert(text, function() {
                        quantityInput.val(initialQuantities[spinnerId]);
                    });
                } else if(qty % batchSize > 0){
                    let wrongBatchSize = "{{ 'offer_detail.not_batch_size_multiple'|trans|raw }}";
                    wrongBatchSize = wrongBatchSize.replace(new RegExp('%batchSize%', 'g'), batchSize);
                    let contactTheVendor = "{{ 'offer_detail.contact_the_vendor'|trans|raw }}";
                    let text = wrongBatchSize + "\n" + contactTheVendor;
                    window.UI.Modal.alert(text, function() {
                        quantityInput.val(initialQuantities[spinnerId]);
                    });
                } else {
                    loadingModal = window.UI.Modal.showLoading();
                    addProduct(productId, qty);
                }
            };

            var addProduct = function(productId, qty) {
                var url = '{{ path("front.cart.addFromCart.withCartId", {"productId" : "productId", "qty" : "quantity", "cartId" : cart.id }) }}';
                url = url.replace("productId", productId);
                url = url.replace("quantity", qty) ;
                $.ajax({
                    type: 'POST',
                    url: url,
                    success: function(data) {
                        location.reload();
                    },
                    error: function(XMLHttpRequest, textStatus, errorThrown) {
                        closeLoading();
                    }
                });
            };

            var removeItem = function(itemId,offerId) {
                loadingModal = window.UI.Modal.showLoading();
                $.ajax({
                    type: 'DELETE',
                    url: '{{ path('cart.details.remove_item') }}',
                    data: {
                        itemId: itemId,
                        cartId: {{ cart.id }},
                        offerId: offerId
                    },
                    success: function(data) {
                        location.reload();
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        closeLoading();
                        messageModal = window.UI.Modal.alert("{{ 'cart.remove.error'|trans|raw }}");
                    }
                });
            };

            var splitDelivery = function(event, item_id) {
                $('.container-date-split-' + item_id).toggleClass("hidden");
                $('.container-qty-split-' + item_id).toggleClass("hidden");
                $('.split-delivery-save-' + item_id).toggle();

                const $button = $(event.currentTarget);
                // $button.toggleClass('cancel');

                let $img = $button.find('img');
                let currentSrc = $img.attr('src');
                let newSrc = currentSrc.includes('plus-split.png')
                    ? '{{ asset('/images/close-split.png') }}'
                    : '{{ asset('/images/plus-split.png') }}';
                $img.attr('src', newSrc);
            };

            var saveSplitDelivery = function(item_id, offer_id) {
                const qty_on_stock_input = $(`.qty-delivery-on-stock-${item_id}`);
                const qty_demand_input = $(`.qty-delivery-demand-${item_id}`);
                const qty_on_stock_value = parseInt(qty_on_stock_input.val());
                const qty_demand_value = parseInt(qty_demand_input.val());
                const total_qty = qty_on_stock_value + qty_demand_value;

                sendUpdateSplitDelivery(item_id, buildFormDataSplitDelivery(item_id, offer_id, total_qty, {{ cart.id }}));
            };

            var buildFormDataSplitDelivery = function(item_id, offer_id = null, total_qty = null, cart_id = null) {
                const formData = new FormData();
                const csrf_token = '{{ csrf_token('form') }}';
                const deliveryData = {
                    on_stock_delivery: {
                        date: $(`.datepicker-delivery-on-stock-${item_id}`).val(),
                        quantity: $(`.qty-delivery-on-stock-${item_id}`).val()
                    },
                    demand_delivery: {
                        date: $(`.datepicker-delivery-demand-${item_id}`).val(),
                        quantity: $(`.qty-delivery-demand-${item_id}`).val()
                    }
                };
                formData.append('form[_token]', csrf_token);
                formData.append('form[field]', "cart-item-split-delivery");
                formData.append('form[value]', JSON.stringify(deliveryData));
                formData.append('form[split_delivery]', "true");
                if (offer_id) {
                    formData.append('form[offer_id]', offer_id);
                }
                if (total_qty) {
                    formData.append('form[total_qty]', total_qty);
                }
                if (cart_id) {
                    formData.append('form[cart_id]', cart_id);
                }

                return formData;
            }

            var resetSplitDelivery = function(event, item_id, offer_id) {
                const $button = $(event.currentTarget);
                const formData = new FormData();
                const csrf_token = '{{ csrf_token('form') }}';
                const deliveryData = {};
                formData.append('form[_token]', csrf_token);
                formData.append('form[field]', "cart-item-split-delivery");
                formData.append('form[value]', JSON.stringify(deliveryData));
                formData.append('form[split_delivery]', "true");
                formData.append('form[offer_id]', offer_id);

                sendUpdateSplitDelivery(item_id, formData);
            };

            let splitDeliveryTimeouts = {};
            var updateQtyDelivery = function(type, item_id, qty_step, qty_max, offer_id, quantity_limit, event) {
                const qty_on_stock_input = $(`.qty-delivery-on-stock-${item_id}`);
                const qty_demand_input = $(`.qty-delivery-demand-${item_id}`);
                const qty_on_stock_value = parseInt(qty_on_stock_input.val());
                const qty_demand_value = parseInt(qty_demand_input.val());
                const disableClass = 'not-allowed opacity-5';
                const $button_clicked = $(event.currentTarget);

                let new_qty_on_stock_value = qty_on_stock_value;
                let new_qty_demand_value = qty_demand_value;

                if (type === 'qty-delivery-on-stock') {
                    new_qty_on_stock_value = qty_on_stock_value + qty_step;
                    if (new_qty_on_stock_value <= 1 || new_qty_on_stock_value >= quantity_limit) {
                        $button_clicked.addClass(disableClass);
                    } else {
                        $(`.decrease-on-stock-${item_id}, .increase-on-stock-${item_id}`).removeClass(disableClass);
                    }
                    if (new_qty_on_stock_value > quantity_limit || new_qty_on_stock_value < 1) {
                        return;
                    }
                    qty_on_stock_input.val(new_qty_on_stock_value);
                } else if (type === 'qty-delivery-demand') {
                    new_qty_demand_value = qty_demand_value + qty_step;
                    if (new_qty_demand_value <= quantity_limit || new_qty_demand_value >= qty_max) {
                        $button_clicked.addClass(disableClass);
                    } else {
                        $(`.decrease-demand-${item_id}, .increase-demand-${item_id}`).removeClass(disableClass);
                    }
                    if (new_qty_demand_value > qty_max || new_qty_demand_value < quantity_limit) {
                        return;
                    }
                    qty_demand_input.val(new_qty_demand_value);
                }

                if (splitDeliveryTimeouts[item_id]) {
                    clearTimeout(splitDeliveryTimeouts[item_id]);
                }

                splitDeliveryTimeouts[item_id] = setTimeout(() => {
                    saveSplitDelivery(item_id, offer_id);
                }, 1000);
            }

            var sendUpdateSplitDelivery = function(itemId, formData) {
                loadingModal = window.UI.Modal.showLoading();

                var url = '{{ path("cart.details.update_item_extra_info", {'cartItemId': 'itemId'}) }}';
                url = url.replace("itemId", itemId);

                $.ajax({
                    type: 'POST',
                    url: url,
                    data: formData,
                    dataType: "json",
                    processData: false,
                    contentType: false,
                    success: function (data) {
                        if (data === "OK")
                            location.reload();
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        closeLoading();
                        messageModal = window.UI.Modal.alert("{{ 'error.internal.title'|trans|raw }}");
                    }
                });
            }

            var refreshItemPrice = function(itemId, offerId, quantity) {
                loadingModal = window.UI.Modal.showLoading();
                $.ajax({
                    type: 'PUT',
                    url: '{{ path('cart.details.refresh_item') }}',
                    data: {
                        itemId: itemId,
                        cartId: {{ cart.id }},
                        offerId: offerId,
                        quantity: quantity
                    },
                    success: function(data) {
                        location.reload();
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        closeLoading();
                        messageModal = window.UI.Modal.alert("{{ 'cart.remove.error'|trans|raw }}");
                    }
                });
            }

            var assignCart = function(elUser, elMessage) {
                assignModal.close();
                loadingModal = window.UI.Modal.showLoading();
                var userId = $(elUser).val();
                var message = ($(elMessage).val()) ? $(elMessage).val() : "";
                var addressId = $('#select-address').val();
                var siteId = $('#select-cost-center').val();
                var currency = '{{ cart.currency }}';
                var buyerReferenceId = $('#payment_mode_select_form_validationNumber').val();
                const accountingEmail = $('#accountingEmail input').val()
                var billingAddressMain = $('#payment_mode_select_form_billingAddress_address').val();
                var billingAddressComplement = $('#payment_mode_select_form_billingAddress_address2').val();
                var billingAddressZipCode = $('#payment_mode_select_form_billingAddress_zipCode').val();
                var billingAddressCity = $('#payment_mode_select_form_billingAddress_city').val();
                var billingAddressArea = $('#payment_mode_select_form_billingAddress_regionText').val();
                $.ajax({
                    type: 'POST',
                    url: '{{ path('cart.details.assign.user') }}',
                    data: {
                        userId: userId,
                        cartId: {{ cart.id }},
                        comment: message,
                        shippingPointId: {{ cart.shippingPoint.id ?? null | json_encode() }},
                        siteId: siteId,
                        addressId: addressId,
                        currency: currency,
                        buyerReferenceId: buyerReferenceId,
                        accountingEmail: accountingEmail,
                        billingAddressMain: billingAddressMain,
                        billingAddressComplement: billingAddressComplement,
                        billingAddressZipCode: billingAddressZipCode,
                        billingAddressCity: billingAddressCity,
                        billingAddressArea: billingAddressArea
                    },
                    success: function(data) {
                        closeLoading();
                        var href = '{{ path('cart.pending.details', {'cartId' : 'data'}) }}';
                        href = href.replace('data', data);
                        messageModal = window.UI.Modal.alert("{{ 'cart.assign.success'|trans|raw }}", function() {
                            window.location.href = href;
                        });

                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        closeLoading();
                        messageModal = window.UI.Modal.alert("{{ 'cart.assign.error'|trans|raw }}");
                    }
                });
            };

            var rejectCart = function(id) {
                rejectModal.close();
                loadingModal = window.UI.Modal.showLoading();
                let message = $(id).val();
                $.ajax({
                    type: 'POST',
                    url: '{{ path('cart.details.reject') }}',
                    data: {
                        cartId: {{ cart.id }},
                        comment: message
                    },
                    success: function(data) {
                        closeLoading();
                        var href = '{{ path('cart.pending.details', {'cartId' : 'data'}) }}';
                        href = href.replace('data', data);
                        window.UI.Modal.alert("{{ 'cart.reject.success'|trans|raw }}", function() {
                            window.location.href = href;
                        });
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        closeLoading();
                        window.UI.Modal.alert("{{ 'cart.reject.error'|trans|raw }}");
                    }
                });
            };

            var toggleSeller = function(idTable) {
                let tableContent = $(idTable).find('tbody');
                tableContent.toggleClass('hide');
                let arrow =  $(idTable).find('.arrow');
                if(arrow.hasClass('down')) {
                    arrow.removeClass('down');
                    arrow.addClass('up');
                } else {
                    arrow.removeClass('up');
                    arrow.addClass('down');
                }
            };

            var onClickAssign = function(shippingPointId) {
                var cannotProceedToAssign = function () {
                    return (
                        ($('#select-cost-center').length > 0 && $('#select-cost-center').val() == null)
                        || ($('#select-address').length > 0 && $('#select-address').val() == null)
                        || ($('#payment_mode_select_form_billingAddress_address').val() == '')
                        || ($('#payment_mode_select_form_billingAddress_zipCode').val() == '')
                        || ($('#payment_mode_select_form_billingAddress_city').val() == '')
                    );
                };
                if (cannotProceedToAssign()) {
                    window.UI.Modal.alert('{{ 'cart.checkout.assign.error'|trans }}');
                } else {
                    saveDocs(function() {
                        var siteId = $('#select-cost-center').val();
                        var addressId = $('#select-address').val();

                        if (parseInt(shippingPointId)) {
                            refreshUserByShippingPointId(shippingPointId);
                        } else {
                            refreshUserByCostCenter(siteId, addressId);
                        }
                    });
                }
            };

            var onClickNext = function() {
                var siteId = $('#select-cost-center').val();
                var addressId = $('#select-address').val();
                var address = document.getElementById('select-address');
                var addressOptions = address.options;
                var shippingPointId =  address.dataset.shippingId;

                if (typeof addressOptions !== 'undefined') {
                    shippingPointId =  address.options[address.selectedIndex].dataset.shippingId;
                }

                var selectPaymentMode = $('#select-payment-mode').val();
                var buyerRefId = $('#payment_mode_select_form_validationNumber').val();
                const accountingEmail = $('#accountingEmail input').val()
                var documentsRequests = $('.documents-requests');
                var documentsRequestsList = [];
                for (let cpt=0; cpt < documentsRequests.length; cpt++) {
                    if (documentsRequests[cpt].checked) {
                        let index = documentsRequestsList.length;
                        documentsRequestsList[index] = documentsRequests[cpt].dataset.document;
                    }
                }
                var billingAddressMain =  $('#payment_mode_select_form_billingAddress_address').val();
                var billingAddressComplement =  $('#payment_mode_select_form_billingAddress_address2').val();
                var billingAddressZipCode =  $('#payment_mode_select_form_billingAddress_zipCode').val();
                var billingAddressCity =  $('#payment_mode_select_form_billingAddress_city').val();
                var billingAddressArea =  $('#payment_mode_select_form_billingAddress_regionText').val();

                nextUser({
                    cartId: {{ cart.id }},
                    siteId: siteId,
                    addressId: addressId,
                    selectPaymentMode: selectPaymentMode,
                    buyerReferenceId: buyerRefId,
                    shippingPointId: shippingPointId,
                    documentsRequests: documentsRequestsList,
                    accountingEmail: accountingEmail,
                    billingAddressMain: billingAddressMain,
                    billingAddressComplement: billingAddressComplement,
                    billingAddressZipCode: billingAddressZipCode,
                    billingAddressCity: billingAddressCity,
                    billingAddressArea: billingAddressArea
                });
            };

            var onClickReject = function() {
                let modalHtml = $('#js-reject-modal-tpl').html();
                rejectModal = window.UI.Modal.show('js-reject-modal', 'Modal--reject', $(modalHtml), true);
            };

            var showAssign = function() {
                let modalHTML = $('#js-assign-modal-tpl').html();
                return window.UI.Modal.show('js-assign-modal', 'Modal--assign', $(modalHTML), true);
            };

            var noUserToAssign = function() {
                let modalHTML = $('#js-no-user-to-assign-modal-tpl').html();
                return window.UI.Modal.show('js-assign-modal', 'Modal--assign', $(modalHTML), true);
            };

            var buildAssignUsers = function(users) {
                let options = [];
                for(let i=0; i<users.length; i++) {
                    let option = $("<option></option>");
                    option.attr('value', users[i].id);
                    option.text(users[i].lastname+" "+users[i].firstname);
                    if(i === 0) {
                        option.attr('selected', 'true');
                    }
                    options.push(option);
                }
                return options;
            };

            var refreshUserByCostCenter = function(idCostCenter, idAddress) {
                refreshUser({
                    siteId: idCostCenter,
                    addressId: idAddress
                });
            };

            var refreshUserByShippingPointId = function(shippingPointId) {
                refreshUser({shippingPointId: shippingPointId});
            };

            var refreshUser = function(data) {
                $.ajax({
                    type: 'GET',
                    url: '{{ path('cart.details.buyers.list') }}',
                    data: data,
                    success: function(data) {
                        closeLoading();
                        var options = buildAssignUsers(JSON.parse(data));
                        if(options.length > 0){
                            loadingModal = window.UI.Modal.showLoading();
                            assignModal = showAssign();
                            $('#select-assign-user').append(options);
                            window.UI.Select.init();
                        }else{
                            noUserToAssign();
                        }

                    },
                    error: function(data) {
                        closeLoading();
                        messageModal = window.UI.Modal.alert(data.responseJSON);
                    }
                });
            };

            var nextUser = function(data) {
                window.UI.Modal.showLoading();
                $.ajax({
                    type: 'GET',
                    url: '{{ path('cart.details.update') }}',
                    data: data,
                    success: function(data) {
                        window.location.href = "{{ path('cart.details.shipping', {'currencyOrCartId': currencyOrCartId}) }}";
                    },
                    error: function(data) {
                        closeLoading();
                        messageModal = window.UI.Modal.alert("{{ 'cart.cost_center.noSiteId'|trans }}");
                    }
                });
            };

            var refreshTable = function(cart) {

                let itemCount = $("#items-count");
                itemCount.find('.count').html(cart.itemsCount);

                let cartIcon = $("#cart-quantity-"+currency.toLowerCase());
                cartIcon.html(cart.itemsCount);

                for(let i=0; i<cart.merchants.length; i++) {
                    for(let j=0; j<cart.merchants[i].items.length; j++) {
                        refreshItem(cart.merchants[i].items[j]);
                    }
                    refreshTableMerchant(i+1, cart.merchants[i]);
                }
                refreshTableMerchant('total', cart);
            };

            var refreshCartHistoric = function() {
                $.ajax({
                    type: 'GET',
                    url: '{{ path('cart.details.historic', {'cartId' : cart.id}) }}',
                    success: function (data) {
                        closeLoading();
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        closeLoading();
                    }
                });
            };

            var refreshItem = function(item) {

                let line = $("#item-"+item.id);
                let quantity = line.find("#quantity_"+item.id);
                let totalPrice = line.find(".item-total-price");

                quantity.attr('value', item.quantity);
                totalPrice.html(item.totalItem+" "+currency);
            };

            var refreshTableMerchant = function(id, cart) {
                let table = $("#table-merchant-"+id);

                table.find(".total-without-vat").html(cart.subTotalWithoutVat.toFixed(2)+" "+currency);

                let lineTaxes = table.find(".taxes");
                //let labelTaxes = lineTaxes.find(".label-taxes").html();

                for(let i = 0; i < lineTaxes.length; i++) {
                    $(lineTaxes[i].remove());
                }

                let i = 1;
                for(let vat in cart.subTotalVat) {
                    let line = $("<tr></tr>");
                    line.addClass('total-table-taxes resume-table taxes');
                    line.append("<td></td>");
                    line.append("<td></td>");
                    line.append("<td></td>");

                    let label = $("<p class='label'>" + labelTaxes + "&nbsp;</p>");
                    let vatPercent = $("<p class='vat-percent'></p>");
                    vatPercent.html(Number.parseFloat(vat).toFixed(2) + "%");
                    let vatColumn = $("<td class='taxes-label-cell'></td>");
                    vatColumn.append(label);
                    vatColumn.append(vatPercent);
                    line.append(vatColumn);

                    line.append("<td></td>");
                    line.append("<td class='vat-value'>" + cart.subTotalVat[vat].toFixed(2) + " " + currency + "</td>");

                    line.insertBefore("#subtotal-vat-"+id);
                    i++;
                }

                table.find(".total-cart").html(cart.total.toFixed(2)+" "+currency);
            };

            const wishListForm = {
                initForm($form) {
                    this.form = $form;
                    this.select = $('select', this.form);
                    this.submitButton = $(':submit', this.form);
                    this.selectedWishlist = 0;
                    this.newWishListForm = $('#newWishListForm', this.form);
                    UI.Select.init();
                },

                showWishlistForm() {
                    let modalContent = $($('#js-save-in-wishlist-modal-tpl').html());
                    this.modal = UI.Modal.show('js-save-in-wishlist-modal', 'Modal--save', modalContent, true, true);
                    this.initForm($('form', '#js-save-in-wishlist-modal'));
                    this.selectWishListListener();
                    this.submitListener();
                },

                showNewWishListForm () {
                    $('input[name="form[name]"]', this.newWishListForm).attr('required', true);
                    this.newWishListForm.show();
                },

                hideNewWishListForm() {
                    $('input[name="form[name]"]', this.newWishListForm).attr('required', false);
                    this.newWishListForm.hide();
                },

                selectWishListListener() {
                    let that = this;
                    this.select.on('change', function() {
                        let $this = $(this);
                        if (that.select.val()) {
                            that.selectedWishlist = $this.val();
                            that.hideNewWishListForm()
                        } else {
                            that.selectedWishlist = 0;
                            that.showNewWishListForm();
                        }
                    }).change();
                },

                productSuccessfullyAddedToWishlist() {
                    window.location.reload();
                },

                submitListener() {
                    let that = this;
                    this.form.on('submit', function(event) {
                        event.preventDefault();
                        let modalLoading = window.UI.Modal.showLoading();
                        let url = that.form.attr('action');
                        let data = new FormData(that.form.get(0));

                        $.ajax({
                            url: url,
                            data: data,
                            type: 'POST',
                            cache: false,
                            processData: false,
                            contentType: false,
                            success: function(wishList) {
                                that.modal.close();
                                that.productSuccessfullyAddedToWishlist();
                            },
                            error: function(data) {
                                modalLoading.close();
                            }
                        });
                    });
                }
            };

            refreshCartHistoric();
        </script>

        <script type="text/template" id="js-save-in-wishlist-modal-tpl">
            <div class="Modal-header">
                <h5 class="Modal-title">{{ 'offer_detail.add_to_wishlist'|trans }}</h5>
            </div>

            <form action="{{ path('wishlist.save_from_cart')}}" method="post">
                <div class="Modal-body">
                    <input type="hidden" name="form[cartId]" value="{{ cart.id }}" />

                    <div class="select-wishlist">
                        <label>{{ 'wishlist.title'|trans }} : </label>

                        <div class="js-select-wrapper select-wrapper has-text">
                            <select id="wishlistId" name="form[wishListId]">
                                {% for wishList in wishLists %}
                                    <option value="{{ wishList.id }}">{{ wishList.name }}</option>
                                {% endfor %}
                                <option value="">{{ 'wishlist.add_new'|trans }}</option>
                            </select>
                        </div>
                    </div>

                    <div id="newWishListForm" class="add-new-wishlist">
                        <input type="text" name="form[name]" placeholder="{{ 'wishlist.new_name'|trans }}" maxlength="50"/>
                        <input type="hidden" name="form[currency]" value="{{ cart.currency }}">
                    </div>
                </div>
                <div class="Modal-footer">
                    <button type="submit" id="js-confirm-button" class="btn btn-primary">{{ 'wishlist.save'|trans }}</button>
                </div>
            </form>
        </script>

        <script type="text/template" id="js-assign-modal-tpl">
            <div class="Modal-header">
                <h5 class="Modal-title">{{ 'cart.assign.modal_title'|trans }}</h5>
            </div>
            <div class="Modal-body">
                <label>{{ 'cart.assign.assign_to'|trans }}</label>
                <div class="select-container">
                    <div class="js-select-wrapper select-wrapper">
                    <select id="select-assign-user">
                    </select>
                    </div>
                </div>
                <div class="message-container">
                    <label>{{ 'cart.assign.comment'|trans }}</label>
                    <textarea id="assign-message"></textarea>
                </div>

            </div>
            <div class="Modal-footer">
                <button id="js-confirm-button" class="btn btn-primary" onclick="assignCart('#select-assign-user', '#assign-message')">{{ 'cart.assign.modal_btn_confirm'|trans|upper }}</button>
            </div>
        </script>

        <script type="text/template" id="js-reject-modal-tpl">
            <div class="Modal-header">
                <h5 class="Modal-title">{{ 'cart.reject.title'|trans }}</h5>
            </div>
            <div class="Modal-body">
                <div class="message-container">
                    <label>{{ 'cart.reject.comment'|trans }}</label>
                    <textarea id="reject-message"></textarea>
                </div>

            </div>
            <div class="Modal-footer">
                <button id="js-confirm-button" class="btn btn-primary" onclick="rejectCart('#reject-message')">{{ 'cart.reject.title'|trans|upper }}</button>
            </div>
        </script>

        <script type="text/template" id="js-checkout-not-ready-tpl">
            <div class="Modal-header">
            </div>
            <div class="Modal-body">
                <div class="message-container">
                    <h5>{{ 'cart.checkout.notReady'|trans }}</h5>
                </div>
            </div>
            <div class="Modal-footer">
            </div>
        </script>

        <script type="text/template" id="js-no-user-to-assign-modal-tpl">
            <div class="Modal-header">
            </div>
            <div class="Modal-body">
                <div class="message-container">
                    <h5 style="margin: 30px;">{{ 'cart.assign.noUser'|trans }}</h5>
                </div>
            </div>
            <div class="Modal-footer">
            </div>
        </script>
    {% endif %}
{% endblock %}
