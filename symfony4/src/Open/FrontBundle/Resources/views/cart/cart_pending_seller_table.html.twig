{% trans_default_domain 'AppBundle' %}

{# @var merchant \AppBundle\Model\Cart\CartMerchant #}
<table id="table-merchant-{{ index }}" class="seller-table">
    <thead class="underline">
    <tr>
        <th class="header-seller">
            <p class="label-seller">{{ merchant.name|upper }} ({{ merchant.country|trans }}) <span class="nb-products">{{ merchant.items|length }}
                    {% if merchant.items|length > 1 %}
                        {{ 'orders.list.merchant_products'|trans }}
                    {% else %}
                        {{ 'orders.list.merchant_product'|trans }}
                    {% endif %}</span></p>
            <i class="mobile-max-only arrow down" onclick="toggleSeller('#table-merchant-{{ index }}')"></i>
        </th>
    </tr>
    <tr class="hidden-mobile">
        <th class="label-detail" width="50" colspan="2">{{ 'cart.table_label.product_detail'|trans }}</th>
        <th class="label-date">{{ 'cart.table_label.expected_date'|trans }}</th>
        <th class="label-price">{{ 'cart.table_label.unit_price'|trans }}</th>
        <th>{{ 'cart.table_label.quantity'|trans }}</th>
        <th class="label-price" {% if isShipping %}colspan="2" {% endif %}>{{ 'cart.table_label.total_price'|trans }}</th>
    </tr>
    </thead>
    <tbody>

    {# ------ OFFERS ------ #}
    {# @var item \AppBundle\Model\Cart\CartItem #}
    {% for item in merchant.items %}

        {% if cart.merchants|length == 1 and index == merchant.items|length %}
            {% set underline = 'underline' %}
        {% else %}
            {% set underline = 'underline' %}
        {% endif %}

        {% if item.deliveryDate is defined and item.deliveryTime is not empty %}
            {% set locale = app.session.get("_locale") %}
            {% set format_date = (locale == 'fr' ? 'd/m/Y' : 'm/d/Y') %}
            {% set date = item.deliveryDate | date(format_date) %}
        {% else %}
            {% set date = '-' %}
        {% endif %}

        {% set shippingDeliveryDates = '' %}
        {% if isShipping %}
            {% set shippingDeliveryDates = shipping.getMerchantItemDeliveryDates(merchant.id, item.id) %}
        {% endif %}
        <tr class="item-cart {% if not item.valid %}invalid{% endif %}" id="item-{{ item.id }}" data-delivery="{{ date }}" {% if not item.isDangerousProduct() %}data-deliverydates='{{ shippingDeliveryDates }}'{% endif %}>
            <td class="{{ underline }}" colspan="2">
                <div class="product">
                    {% if item.name is not empty %}
                        <a href="{{ path('front.offer.detail', {'productName' : item.name|url_encode, 'ref': item.offerId})|replace({'%2F': '%252F'}) }}"></a>
                    {% else %}
                        <a href="{{ path('front.offer.detail.short', {'ref': item.offerId}) }}"></a>
                    {% endif %}

                    <div class="img-container">
                        <img src="{{ item.imageUrl }}">
                    </div>

                    <div class="detail-content">
                        <h5 class="name">{{ item.name }}</h5>
                        <div class="incoterm">
                            {% set showWarningFCA = false %}
                            {% if item.incoterm is defined %}
                                {{ item.incoterm|upper }}
                                {% if item.incoterm == 'FCA' %}
                                    {% set showWarningFCA = true %}
                                {% endif %}
                            {% endif %}
                            {% if item.countryOfDelivery is defined and item.countryOfDelivery is not empty %}
                                {{ '('~item.countryOfDelivery|upper~')' }}
                            {% endif %}
                            {% if (showWarningFCA == true and (not item.toBeShipped or item.isDangerousProduct())) %}
                                <div class="tooltip">
                                    <span style="font-size: 10px;color: #FFF;border-radius: 2px;background-color: #9600FF;padding: 1px 4px;vertical-align: middle;" >
                                        {{ 'cart.warning'|trans }}</span>
                                    <div class="tooltiptext">
                                        <span class="info">
                                            {{ 'cart.fca_info'|trans }}
                                            {% if item.fcaAddress is defined %}
                                                {{ item.fcaAddress }}
                                            {% endif %}
                                        </span>
                                    </div>
                                </div>
                            {% endif %}
                            {% if item.isDangerousProduct() %}
                                <div class="tooltip no-hide">
                                    <img src="{{ asset('/images/ico-inflammable.svg') }}" alt="Dangerous">
                                    <div class="tooltiptext">
                                        <span class="info">
                                            {{ 'Dangerous material'|trans }}
                                        </span>
                                    </div>
                                </div>
                            {% endif %}
                            {% if not merchant.upelaActive and showWarningFCA %}
                                <div class="tooltip" style="margin-left: 20px;">
                                    <img src="{{ asset('/images/no-shipping.svg') }}">
                                    <div class="tooltiptext">
                                        <span class="info">
                                            {{ 'shipping.option.no_merchant_shipping'|trans }}
                                        </span>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                        <div>
                            {% if showWarningFCA == true and merchant.upelaActive and item.toBeShipped and not item.isDangerousProduct() %}
                                <span class="with-shipping">
                                    <svg width="13px" height="11px" viewBox="0 0 13 11" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                        <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                            <g id="Desktop/Shipping-Cart-V2-B1" transform="translate(-340.000000, -640.000000)" fill="#A4A7B3">
                                                <g id="Group-8-Copy" transform="translate(338.000000, 619.000000)">
                                                    <path d="M2,27.2098214 L2,21.5 L4.2,21.5 L4.2,26.875 L10.7611607,26.875 L10.7611607,25.5915179 C10.7611607,25.405505 10.8262642,25.2473965 10.9564732,25.1171875 C11.0866822,24.9869785 11.2447907,24.921875 11.4308036,24.921875 C11.6168164,24.921875 11.7749249,24.9869785 11.9051339,25.1171875 L14.3046875,27.5167411 C14.4348965,27.6469501 14.5,27.8050586 14.5,27.9910714 C14.5,28.1770843 14.4348965,28.3351928 14.3046875,28.4654018 L11.9051339,30.8649554 C11.7749249,30.9951643 11.6168164,31.0602679 11.4308036,31.0602679 C11.2447907,31.0602679 11.0866822,30.9951643 10.9564732,30.8649554 C10.8262642,30.7347464 10.7611607,30.5766378 10.7611607,30.390625 L10.7611607,29.1071429 L2.33482143,29.1071429 C2.24181501,29.1071429 2.16276074,29.0745911 2.09765625,29.0094866 C2.03255176,28.9443821 2,28.8653278 2,28.7723214 L2,27.2098214 Z" id="Combined-Shape"></path>
                                                </g>
                                            </g>
                                        </g>
                                    </svg>&nbsp;<span style="font-size: 10px;color: #FFF;border-radius: 2px;background-color: #9600FF;padding: 1px 10px;vertical-align: middle;">DAP ({{ cart.address.country.izbFcaCountry }})</span>
                                </span>
                            {% endif %}
                        </div>
                        {% if item.sellerRef is defined and item.sellerRef is not empty %}
                            <div class="sellerRef">
                                {{ 'product.seller_reference'|trans }} : {{ item.sellerRef|upper }}
                            </div>
                        {% endif %}
                        {% if item.buyerInternalRef is defined and item.buyerInternalRef is not empty %}
                            <div class="buyerRef">
                                {{ 'product.buyer_reference'|trans }} : {{ item.buyerInternalRef }}
                            </div>
                        {% endif %}
                        {% if item.cartItemComment is defined and item.cartItemComment is not empty %}
                            <div class="buyerRef">
                                {{ 'product.cart_item_comment'|trans }} : {{ item.cartItemComment }}
                            </div>
                        {% endif %}
                        {% if item.offer.getFrameContract is not empty %}
                            <div class="sellerRef">
                                <img style="width:20px" src="{{ asset('images/sign.svg') }}" > {{ item.offer.getFrameContract }}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </td>
            {% set haveCartItemSplitDelivery = false %}
            {% set haveQtyDemandDelivery = false %}
            {% set haveQtyOnStockDelivery = false %}
            {% set haveDateDemandDelivery = false %}
            {% set haveDateOnStockDelivery = false %}
            {% if item.offer.realStock is not empty and item.qty_greater_real_stock %}
                {% set haveCartItemSplitDelivery = item.cartItemSplitDelivery is not empty %}

                {% set haveQtyDemandDelivery = haveCartItemSplitDelivery and item.cartItemSplitDelivery.demand_delivery.quantity is not empty %}
                {% set haveQtyOnStockDelivery = haveCartItemSplitDelivery and item.cartItemSplitDelivery.on_stock_delivery.quantity is not empty %}
                {% set haveDateDemandDelivery = haveCartItemSplitDelivery and item.cartItemSplitDelivery.demand_delivery.date is not empty %}
                {% set haveDateOnStockDelivery = haveCartItemSplitDelivery and item.cartItemSplitDelivery.on_stock_delivery.date is not empty %}
            {% endif %}
            <td class="{{ underline }} mobile-align" {% if haveCartItemSplitDelivery  %} style="position: relative; padding: 70px 0" {% endif %}>
                <p class="mobile-max-only mobile-label">{{ 'cart.table_label.delivery_time'|trans }} :</p>
                <div class="mobile-center">
                    <span class="updatedate">{{ haveQtyOnStockDelivery ? "-" : date }}</span>
                    {% include('@OpenFront/component/cart-item-date-split.html.twig') with {
                        'item': item,
                        'isShipping': isShipping
                    } %}
                    <div class="tooltip" style="display: none">
                        <img src="{{ asset('/images/ico-info.svg') }}" alt="Dangerous"/>
                        <div class="tooltiptext tooltip-info-service">
                            <span class="info-service-name">

                            </span>
                            (<span class="info-delivery-days"></span>)
                        </div>
                    </div>
                </div>
            </td>
            <td class="{{ underline }} mobile-align label-price">
                <p class="mobile-max-only mobile-label">{{ 'cart.table_label.unit_price'|trans }} :</p>
                <p class="mobile-center">
                    {{ item.unitPrice | price(locale) }} {{ cart.currency|upper }}
                </p>
            </td>
            <td class="{{ underline }} mobile-align" {% if haveCartItemSplitDelivery  %} style="position: relative; padding: 70px 0" {% endif %}>
                <p class="mobile-max-only mobile-label">{{ 'cart.table_label.quantity'|trans }}</p>
                {{ item.quantity }}
                {% include('@OpenFront/component/cart-item-qty.html.twig') with {
                    'item': item,
                    'isShipping': isShipping,
                    'realStock': item.offer.realStock,
                    'haveQtyOnStockDelivery': haveQtyOnStockDelivery,
                    'haveQtyDemandDelivery ': haveQtyDemandDelivery ,
                } %}
            </td>
            <td class="{{ underline }} mobile-align label-price" {% if isShipping %}colspan="2" {% endif %}>
                <p class="mobile-max-only mobile-label">{{ 'cart.table_label.total_price'|trans }} :</p>
                <p class="item-total-price mobile-center">
                    {{ item.totalItem | price(locale) }} {{ cart.currency }}
                </p>
            </td>
        </tr>
    {% endfor %}

    {% set hasShippingInfo = false %}

    {% set hasShippingInfo = merchant.upelaActive %}

    {% set shippingOption = '' %}

    {% if merchant.shippable  %}
        <tr class="shipping-cart-detail underline">
        <td colspan="4">
            <div class="shipping-selector">
                <div class="product">
                    <div class="img-container">
                        {% if not merchant.upelaActive %}
                            <img src="{{ asset('/images/no-shipping.svg') }}" style="width: 80%">
                        {% else %}
                            <img src="{{ asset('/images/Truck.svg') }}" style="width: 80%">
                        {% endif %}
                    </div>
                    <div class="detail-content">
                        <h5 class="name">StationOne Shipping offers</h5>
                        <div class="buyerRef">
                            {% if not merchant.upelaActive %}
                                {{ 'shipping.option.no_merchant_shipping'|trans }}
                            {% elseif not merchant.shippable %}
                                    No carriers were able to provide a transportation offer for these products
                            {% else %}
                                The cheapest offer has been selected by default.<br>
                                Consult other options by clicking the dropdown list
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="selector-container" style="align-self: center;">
                    {% if merchant.shippable %}
                        {{ merchant.shippingName |trans }}
                    {% elseif not merchant.upelaActive %}

                    {% else %}
                        {{ 'shipping.option.no_shipping_available'|trans }}
                    {% endif %}
                </div>
            </div>
        </td>
        <td class="mobile-align label-price" colspan="2">
        {% if merchant.upelaActive and merchant.shippable %}
            <span class="price" data-price="0">{{ merchant.shippingTotal | price(locale) }}</span> {{ cart.currency }}
        {% else %}
            -
        {% endif %}
        </td>
    </tr>
    {% endif %}


    {% if cart.merchants|length == 1 %}
        <tr class="resume-table subtotal">
            <td class="mobile-align">
                {% if merchant.subTotalWithoutVat > 0 and  merchant.subTotalWithoutVat < merchant.minimumOrderAmount %}
                    <div style="text-align: left !important; color: #FF0000;">{{ 'tab_infos_seller.minimum_order_amount'|trans({'%amount%': merchant.minimumOrderAmount|price(locale) }) }} {{ cart.currency }}</div>
                {% endif %}
            </td>
            <td class="hidden-mobile"></td>
            <td class="hidden-mobile"></td>
            <td class="hidden-mobile"></td>
            <td class="hidden-mobile"></td>
            <td class="hidden-mobile"></td>
        </tr>
    {% endif %}
    </tbody>
    <tbody {% if cart.merchants|length == 1 %}style="display: none"{% endif %}>
        <tr class="resume-table hidden-desktop" >
            <td colspan="4">
                {% if merchant.subTotalWithoutVat > 0 and  merchant.subTotalWithoutVat < merchant.minimumOrderAmount %}
                    <div style="text-align: left !important; color: #FF0000;">{{ 'tab_infos_seller.minimum_order_amount'|trans({'%amount%': merchant.minimumOrderAmount|price(locale) }) }} {{ cart.currency }}</div>
                {% endif %}
            </td>
        </tr>
        {# ------ SUBTOTAL WITHOUT VAT ------ #}
        <tr class="resume-table subtotal">

            <td class="hidden-mobile">
                {% if merchant.subTotalWithoutVat > 0 and  merchant.subTotalWithoutVat < merchant.minimumOrderAmount %}
                   <div style="text-align: left !important; color: #FF0000;">{{ 'tab_infos_seller.minimum_order_amount'|trans({'%amount%': merchant.minimumOrderAmount|price(locale) }) }} {{ cart.currency }}</div>
                {% endif %}
            </td>
            <td class="hidden-mobile"></td>
            <td class="hidden-mobile"></td>
            <td class="taxes-label">{{ 'cart.table_label.subtotal'|trans }}</td>
            <td></td>
            <td class="subtotal-without-vat">
                <span data-price="{{ merchant.subTotalWithoutVat }}">{{ merchant.subTotalWithoutVat | price(locale) }}</span> {{ cart.currency }}
            </td>
        </tr>

        {# ------ SUBTOTAL WITHOUT VAT ------ #}
        {% if merchant.shippingName is not null %}
        <tr class="resume-table subtotal">

            <td class="hidden-mobile">
                {% if merchant.subTotalWithoutVat > 0 and  merchant.subTotalWithoutVat < merchant.minimumOrderAmount %}
                    <div style="text-align: left !important; color: #FF0000;">{{ 'tab_infos_seller.minimum_order_amount'|trans({'%amount%': merchant.minimumOrderAmount|price(locale) }) }} {{ cart.currency }}</div>
                {% endif %}
            </td>
            <td class="hidden-mobile"></td>
            <td class="hidden-mobile"></td>
            <td class="taxes-label">{{ 'cart.table_label.shippingtotal'|trans }}</td>
            <td></td>
            <td class="subtotal-shipping">
                <span data-price="0">{{ merchant.shippingTotal | price(locale) }}</span> {{ cart.currency }}
            </td>
        </tr>
        {% endif %}

        {# ------ TAXES ------ #}
        {% for vat,total in merchant.subTotalVat %}
            {% if vat > 0 %}
                <tr class="resume-table taxes vat_{{ vat | number_format(2, '_') }}" {% if total == 0 %}style="display: none"{% endif %}>
                    <td class="hidden-mobile"></td>
                    <td class="hidden-mobile"></td>
                    <td class="hidden-mobile"></td>
                    <td class="taxes-label-cell">
                        <p class="label-taxes label">{{ 'cart.table_label.taxes'|trans }}&nbsp;</p>
                        <p class="vat-percent">{{ vat | number_format(2, '.') }}%</p>
                    </td>
                    <td></td>
                    <td class="subtotal-vat-price">
                        <span data-price="{{ total }}">{{ total | price(locale) }}</span> {{ cart.currency }}
                    </td>
                </tr>
            {% endif %}
        {% endfor %}

        {# ------ SUBTOTAL WITH VAT ------ #}
        <tr id="subtotal-vat-{{ index }}" class="resume-table subtotal-vat">
            <td class="hidden-mobile"></td>
            <td class="hidden-mobile"></td>
            <td class="hidden-mobile"></td>
            <td class="taxes-label">{{ 'cart.table_label.subtotal_vat'|trans }}</td>
            <td></td>
            <td class="subtotal-cart">
                <span data-price="{{ merchant.total }}">{{ merchant.total | price(locale) }}</span>
                {{ cart.currency }}
            </td>
        </tr>
    </tbody>
</table>
