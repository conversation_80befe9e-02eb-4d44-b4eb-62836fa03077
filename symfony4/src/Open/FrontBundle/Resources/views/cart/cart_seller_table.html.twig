{% trans_default_domain 'AppBundle' %}

{# @var merchant \AppBundle\Model\Cart\CartMerchant #}
<table id="table-merchant-{{ index }}" class="seller-table">
    <thead class="underline">
    <tr>
        <th class="header-seller">
            <p class="label-seller">{{ merchant.name|upper }} ({{ merchant.country|trans }}) <span class="nb-products">{{ merchant.items|length }}
                    {% if merchant.items|length > 1 %}
                        {{ 'orders.list.merchant_products'|trans }}
                    {% else %}
                        {{ 'orders.list.merchant_product'|trans }}
                    {% endif %}</span></p>
            <i class="mobile-max-only arrow down" onclick="toggleSeller('#table-merchant-{{ index }}')"></i>
        </th>
    </tr>
    <tr class="hidden-mobile">
        <th class="label-detail" width="50">{{ 'cart.table_label.product_detail'|trans }}</th>
        <th class="label-date">{{ 'cart.table_label.expected_date'|trans }}</th>
        <th class="label-price">{{ 'cart.table_label.unit_price'|trans }}</th>
        <th>{{ 'cart.table_label.quantity'|trans }}</th>
        <th class="label-price" {% if isShipping %}colspan="2" {% endif %}>{{ 'cart.table_label.total_price'|trans }}</th>
        {% if not isShipping %}
        <th style="min-width: 60px"></th>
        {% endif %}
    </tr>
    </thead>
    <tbody>

    {# ------ OFFERS ------ #}
    {# @var item \AppBundle\Model\Cart\CartItem #}
    {% for item in merchant.items %}
        {% if cart.merchants|length == 1 and index == merchant.items|length %}
            {% set underline = 'underline' %}
        {% else %}
            {% set underline = 'underline' %}
        {% endif %}

        {% if item.deliveryDate is defined and item.deliveryTime is not empty %}
            {% set locale = app.session.get("_locale") %}
            {% set format_date = (locale == 'fr' ? 'd/m/Y' : 'm/d/Y') %}
            {% set date = item.deliveryDate | date(format_date) %}
        {% else %}
            {% set date = '-' %}
        {% endif %}

        {% set shippingDeliveryDates = '' %}
        {% if isShipping and shipping %}
            {% set shippingDeliveryDates = shipping.getMerchantItemDeliveryDates(merchant.id, item.id) %}
        {% endif %}
        <tr class="item-cart {% if not item.valid %}invalid{% endif %}" id="item-{{ item.id }}" data-delivery="{{ date }}" {% if not item.isDangerousProduct() %}data-deliverydates='{{ shippingDeliveryDates }}'{% endif %}>
            <td class="{{ underline }}">
                <div class="product">
                    {% if item.name is not empty %}
                        <a href="{{ path('front.offer.detail', {'productName' : item.name|url_encode, 'ref': item.offerId})|replace({'%2F': '%252F'}) }}"></a>
                    {% else %}
                        <a href="{{ path('front.offer.detail.short', {'ref': item.offerId}) }}"></a>
                    {% endif %}

                    <div class="img-container">
                        <img src="{{ item.imageUrl }}">
                    </div>

                    <div class="detail-content">
                        <h5 class="name">{{ item.name }}</h5>
                        <div class="incoterm">
                            {% set showWarningFCA = false %}
                            {% if item.incoterm is defined %}
                                {{ item.incoterm|upper }}
                                {% if item.incoterm == 'FCA' %}
                                    {% set showWarningFCA = true %}
                                {% endif %}
                            {% endif %}
                            {% if item.countryOfDelivery is defined and item.countryOfDelivery is not empty %}
                                {{ '('~item.countryOfDelivery|upper~')' }}
                            {% endif %}
                            {% if showWarningFCA == true and isShipping and merchant.upelaActive and not item.isDangerousProduct() %}
                                <span class="with-shipping">
                                        {{ 'cart.fca_shipping'|trans }}</span>
                            {% endif %}
                            {% if item.isDangerousProduct() %}
                                <div class="tooltip no-hide">
                                    <img src="{{ asset('/images/ico-inflammable.svg') }}" alt="Dangerous">
                                    <div class="tooltiptext">
                                        <span class="info">
                                            {{ 'Dangerous material'|trans }}
                                        </span>
                                    </div>
                                </div>
                            {% endif %}
                            {% if showWarningFCA %}
                                {% if item.offer.shippable and not isShipping %}
                                    <img style="margin-left: 20px;width:30px" src="/images/Truck.svg">
                                {% elseif not merchant.upelaActive %}
                                    <div class="tooltip" style="margin-left: 20px;">
                                        <img src="{{ asset('/images/no-shipping.svg') }}">
                                        <div class="tooltiptext">
                                        <span class="info">
                                            {{ 'shipping.option.no_merchant_shipping'|trans }}
                                        </span>
                                        </div>
                                    </div>
                                {% endif %}
                            {% endif %}
                        </div>
                        <div>
                            {% if showWarningFCA == true and (not isShipping or item.isDangerousProduct()) %}
                                {% if not item.offer.shippable %}
                                    <div class="tooltip {% if item.isDangerousProduct() %}no-hide{% endif %}">
                                    <span style="font-size: 10px;color: #FFF;border-radius: 2px;background-color: #9600FF;padding: 1px 4px;vertical-align: middle;" >
                                        {{ 'cart.warning'|trans }}</span>
                                        <div class="tooltiptext">
                                        <span class="info">
                                            {{ 'cart.fca_info'|trans }}
                                            {% if item.fcaAddress is defined %}
                                                {{ item.fcaAddress }}
                                            {% endif %}
                                        </span>
                                        </div>
                                    </div>
                                {% endif %}
                            {% endif %}
                            {% if showWarningFCA == true and merchant.upelaActive and isShipping and not item.isDangerousProduct() %}
                                <span class="with-shipping">
                                    <svg width="13px" height="11px" viewBox="0 0 13 11" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                        <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                            <g id="Desktop/Shipping-Cart-V2-B1" transform="translate(-340.000000, -640.000000)" fill="#A4A7B3">
                                                <g id="Group-8-Copy" transform="translate(338.000000, 619.000000)">
                                                    <path d="M2,27.2098214 L2,21.5 L4.2,21.5 L4.2,26.875 L10.7611607,26.875 L10.7611607,25.5915179 C10.7611607,25.405505 10.8262642,25.2473965 10.9564732,25.1171875 C11.0866822,24.9869785 11.2447907,24.921875 11.4308036,24.921875 C11.6168164,24.921875 11.7749249,24.9869785 11.9051339,25.1171875 L14.3046875,27.5167411 C14.4348965,27.6469501 14.5,27.8050586 14.5,27.9910714 C14.5,28.1770843 14.4348965,28.3351928 14.3046875,28.4654018 L11.9051339,30.8649554 C11.7749249,30.9951643 11.6168164,31.0602679 11.4308036,31.0602679 C11.2447907,31.0602679 11.0866822,30.9951643 10.9564732,30.8649554 C10.8262642,30.7347464 10.7611607,30.5766378 10.7611607,30.390625 L10.7611607,29.1071429 L2.33482143,29.1071429 C2.24181501,29.1071429 2.16276074,29.0745911 2.09765625,29.0094866 C2.03255176,28.9443821 2,28.8653278 2,28.7723214 L2,27.2098214 Z" id="Combined-Shape"></path>
                                                </g>
                                            </g>
                                        </g>
                                    </svg>&nbsp;<span style="font-size: 10px;color: #FFF;border-radius: 2px;background-color: #9600FF;padding: 1px 10px;vertical-align: middle;">DAP ({{ cart.address.country.izbFcaCountry }})</span>
                                </span>
                                <div class="tooltip no-shipping">
                                    <span style="font-size: 10px;color: #FFF;border-radius: 2px;background-color: #9600FF;padding: 1px 10px;vertical-align: middle;" >
                                        {{ 'cart.warning'|trans }}</span>
                                    <div class="tooltiptext">
                                        <span class="info">
                                            {{ 'cart.fca_info'|trans }}
                                            {% if item.fcaAddress is defined %}
                                                {{ item.fcaAddress }}
                                            {% endif %}
                                        </span>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                        {% if item.sellerRef is defined and item.sellerRef is not empty %}
                            <div class="sellerRef">
                                {{ 'product.seller_reference'|trans }} : {{ item.sellerRef|upper }}
                            </div>
                        {% endif %}

                        {% if item.offer.getFrameContract is not empty %}
                            <div class="sellerRef">
                                <img style="width:20px" src="{{ asset('images/sign.svg') }}" > {{ item.offer.getFrameContract }}
                            </div>
                        {% endif %}

                        {% if isShipping and item.buyerInternalRef is not null and item.buyerInternalRef is not empty %}
                            <div class="buyer-internal-reference">
                                <span class="sellerRef">{{ 'product.buyer_reference'|trans }} : {{ item.buyerInternalRef }}</span>
                            </div>
                        {% endif %}

                        {% if isShipping and item.cartItemComment is not null and item.cartItemComment is not empty %}
                            <div class="cart-item-comment">
                                <span class="sellerRef">{{ 'product.cart_item_comment'|trans }} : {{ item.cartItemComment }}</span>
                            </div>
                        {% endif %}
                        {% if not isShipping %}
                        <div class="buyer-internal-reference">
                            <span class="sellerRef">{{ 'product.buyer_reference'|trans }} : {{ item.buyerInternalRef }}</span>
                            <div class="update-icon edit-buyer-internal-reference"
                                 style="background-image: url('{{ asset('images/pen.png') }}')"
                                 data-template="js-edit-buyer-internal-reference-tpl-{{ item.id }}"
                            ></div>
                        </div>
                        <script type="text/template" id="js-edit-buyer-internal-reference-tpl-{{ item.id }}">
                            <form style="min-width: 450px;"
                                  method="post"
                                  action="{{ path("cart.details.update_item_extra_info", {'cartItemId': item.id}) }}"
                                  id="extrainfo_update_{{ item.id }}"
                            >
                                <div class="Modal-body">
                                    <input type="hidden" name="form[_token]" value="{{ csrf_token('form') }}"/>
                                    <input type="hidden" name="form[field]" value="buyer-internal-ref"/>

                                    <div class="form-group">
                                        <label for="buyer_internal_reference">{{ 'cart.table_label.buyer_internal_reference' | trans }}</label>
                                        <input type="text"
                                               id="buyer_internal_reference"
                                               name="form[value]"
                                               placeholder="{{ 'cart.table_label.buyer_internal_reference' | trans }}"
                                               value="{{item.buyerInternalRef}}"
                                        />
                                    </div>
                                </div>
                                <div class="Modal-footer">
                                    <button type="submit" class="buttonModal">{{ 'generic.save' | trans }}</button>
                                    <button class="buttonModal">{{ 'generic.cancel' | trans }}</button>
                                </div>
                            </form>
                        </script>

                        <div class="cart-item-comment">
                            <span class="sellerRef">{{ 'product.cart_item_comment'|trans }} : {{ item.cartItemComment }}</span>
                            <div class="update-icon edit-cart-item-comment"
                                 style="background-image: url('{{ asset('images/pen.png') }}')"
                                 data-template="js-edit-cart-item-comment-tpl-{{ item.id }}"
                            ></div>
                        </div>
                        <script type="text/template" id="js-edit-cart-item-comment-tpl-{{ item.id }}">
                            <form style="min-width: 450px;"
                                  method="post"
                                  action="{{ path("cart.details.update_item_extra_info", {'cartItemId': item.id}) }}"
                                  id="extrainfo_update_{{ item.id }}"
                            >
                                <div class="Modal-body">
                                    <input type="hidden" name="form[_token]" value="{{ csrf_token('form') }}"/>
                                    <input type="hidden" name="form[field]" value="cart-item-comment"/>

                                    <div class="form-group">
                                        <label for="cart_item_comment">{{ 'cart.table_label.cart_item_comment' | trans }}</label>
                                        <textarea
                                            class="pt-3"
                                            name="form[value]"
                                            id="cart_item_comment"
                                            cols="30"
                                            rows="7"
                                            placeholder="{{ 'cart.table_label.cart_item_comment' | trans }}">{{item.cartItemComment}}</textarea>
                                    </div>
                                </div>
                                <div class="Modal-footer">
                                    <button type="submit" class="buttonModal">{{ 'generic.save' | trans }}</button>
                                    <button class="buttonModal">{{ 'generic.cancel' | trans }}</button>
                                </div>
                            </form>
                        </script>
                        {% endif %}
                    </div>
                </div>
                {% if not isShipping %}
                    {% if (cartStatus == 'CREATE' and isCartCreator) or (cartStatus == 'ASSIGN' and isCartCreator and isUserValidAssign) %}
                        {% if not item.valid %}
                            <a class="action mobile-max-only" href="#" onclick="refreshItemPrice({{ item.id }},{{ item.offerId }});">
                                <span title="Refresh">
                                    <svg class="refresh">
                                        <use xlink:href="#refresh">
                                        </use>
                                    </svg>
                                </span>
                            </a>
                        {% endif %}

                        <a class="action mobile-max-only" href="#" onclick="removeItem({{ item.id }},{{ item.offerId }});">
                            <span title="Supprimer">
                                <svg class="trash">
                                    <use xlink:href="#trash">
                                    </use>
                                </svg>
                            </span>
                        </a>
                    {% endif %}
                {% endif %}
            </td>
            {% set haveCartItemSplitDelivery = false %}
            {% set haveQtyDemandDelivery = false %}
            {% set haveQtyOnStockDelivery = false %}
            {% set haveDateDemandDelivery = false %}
            {% set haveDateOnStockDelivery = false %}
            {% if item.offer.realStock is not empty and item.qty_greater_real_stock %}
                {% set haveCartItemSplitDelivery = item.cartItemSplitDelivery is not empty %}

                {% set haveQtyDemandDelivery = haveCartItemSplitDelivery and item.cartItemSplitDelivery.demand_delivery.quantity is not empty %}
                {% set haveQtyOnStockDelivery = haveCartItemSplitDelivery and item.cartItemSplitDelivery.on_stock_delivery.quantity is not empty %}
                {% set haveDateDemandDelivery = haveCartItemSplitDelivery and item.cartItemSplitDelivery.demand_delivery.date is not empty %}
                {% set haveDateOnStockDelivery = haveCartItemSplitDelivery and item.cartItemSplitDelivery.on_stock_delivery.date is not empty %}
            {% endif %}
            <td class="{{ underline }} mobile-align" style="position: relative; padding: 40px 0">
                <p class="mobile-max-only mobile-label">{{ 'cart.table_label.delivery_time'|trans }} :</p>
                <div class="mobile-center">
                    <span class="updatedate">
                        {{ haveQtyOnStockDelivery ? "-" : date }}
                    </span>
                    {% if (cartStatus == 'CREATE' and isCartCreator) or (cartStatus == 'ASSIGN' and isCartCreator and isUserValidAssign) %}
                        {% if cart.canSplitDeliveryDate and item.offer.realStock is not empty and item.qty_greater_real_stock %}
                            <div class="w-100 container-date-split-{{ item.id }} {{ haveCartItemSplitDelivery ? '' : ' hidden' }}" style="position: absolute; bottom: {{ isShipping ? "5" : "14" }}px">
                                {% if item.quantity_on_stock is not empty and item.deliveryDateDelayBeforeShipping is not empty %}
                                    {% set dateOnStockInput = haveQtyOnStockDelivery ? item.cartItemSplitDelivery.on_stock_delivery.date : item.deliveryDateDelayBeforeShipping %}
                                    <input autocomplete="off"
                                           name="date_delivery1"
                                           class="datepicker-delivery-input datepicker-delivery-on-stock-{{ item.id }}"
                                           data-item-id="{{ item.id }}"
                                           data-item-offerId="{{ item.offerId }}"
                                           type="text"
                                           value="{{ dateOnStockInput|date(format_date) }}"
                                    />
                                {% endif %}
                                {% if item.quantity_demand is not empty and item.deliveryDate is not empty %}
                                    {% set dateDemandInput = haveQtyOnStockDelivery ? item.cartItemSplitDelivery.demand_delivery.date : item.deliveryDate %}
                                    <input autocomplete="off"
                                           name="date_delivery2"
                                           class="datepicker-delivery-input datepicker-delivery-demand-{{ item.id }}"
                                           data-item-id="{{ item.id }}"
                                           data-item-offerid="{{ item.offerId }}"
                                           type="text"
                                           value="{{ dateDemandInput|date(format_date) }}"
                                    />
                                {% endif %}
                            </div>
                        {% endif %}
                    {% else %}
                        {% include('@OpenFront/component/cart-item-date-split.html.twig') with {
                            'item': item,
                            'isShipping': isShipping
                        } %}
                    {% endif %}
                    <div class="tooltip" style="display: none">
                        <img src="{{ asset('/images/ico-info.svg') }}" alt="Dangerous"/>
                        <div class="tooltiptext tooltip-info-service">
                            <span class="info-service-name">

                            </span>
                            (<span class="info-delivery-days"></span>)
                        </div>
                    </div>
                </div>
            </td>
            <td class="{{ underline }} mobile-align label-price">
                <p class="mobile-max-only mobile-label">{{ 'cart.table_label.unit_price'|trans }} :</p>
                <p class="mobile-center">
                    {% if not item.valid %}
                        {% if item.offer.prices['EUR'] != 0.0 %}
                            {{ item.unitPrice | price(locale) }} {{ cart.currency|upper }}
                        {% else %}
                            {{ 'cart.table_label.unit_no_price'|trans }}
                        {% endif %}
                    {% else %}
                        {{ item.unitPrice | price(locale) }} {{ cart.currency|upper }}
                    {% endif %}
                </p>
            </td>
            <td class="{{ underline }} mobile-align" style="position: relative; padding: 40px 0">
                <p class="mobile-max-only mobile-label">{{ 'cart.table_label.quantity'|trans }}</p>
                {% if not isShipping %}
                    {% if (cartStatus == 'CREATE' and isCartCreator) or (cartStatus == 'ASSIGN' and isCartCreator and isUserValidAssign) %}
                        <input id="quantity_{{ item.id }}" type="number" value="{{ item.quantity }}" min="{{ item.moq }}"
                               max="{{ item.stock }}"
                               onchange="quantityChange('{{ item.id }}', '{{ item.moq }}', '{{ item.stock }}', '{{ item.batchSize }}','{{ item.offerId }}');"/>
                        <input id="product_{{ item.id }}" value="{{ item.offerId }}" type="hidden"/>
                        {% if cart.canSplitDeliveryDate and item.offer.realStock is not empty and item.qty_greater_real_stock %}
                            <div class="w-100 container-qty-split-{{ item.id }} {{ haveCartItemSplitDelivery ? '' : ' hidden'}}" style="position: absolute; bottom: 14px">
                                {% if item.quantity_on_stock is not empty %}
                                    {% set quantityOnStockInput = haveQtyOnStockDelivery ? item.cartItemSplitDelivery.on_stock_delivery.quantity : item.quantity_on_stock %}
                                    <img src="{{ asset('/images/dash.png') }}" height="20px" width="20px" class="pointer decrease decrease-on-stock-{{ item.id }}" onclick="updateQtyDelivery('qty-delivery-on-stock', {{ item.id }}, -{{ item.offer.batchSize }}, {{ item.quantity }}, {{ item.offerId }}, {{ item.quantity_on_stock }}, event);">
                                    <input type="text"
                                           class="qty-delivery qty-delivery-on-stock-{{ item.id }}"
                                           name="quantity_on_stock"
                                           value="{{ quantityOnStockInput }}"
                                           readonly />
                                    <img src="{{ asset('/images/plus.png') }}" height="20px" width="20px" class="pointer increase increase-on-stock-{{ item.id }} {{ haveQtyOnStockDelivery and item.cartItemSplitDelivery.on_stock_delivery.quantity == item.quantity_on_stock ? ' not-allowed opacity-5' : '' }}" onclick="updateQtyDelivery('qty-delivery-on-stock', {{ item.id }}, {{ item.offer.batchSize }}, {{ item.quantity }}, {{ item.offerId }}, {{ item.quantity_on_stock }}, event);">
                                {% endif %}
                                {% if item.quantity_demand is not empty %}
                                    {% set quantityDemandInput = haveQtyDemandDelivery ? item.cartItemSplitDelivery.demand_delivery.quantity : item.quantity_demand %}
                                    <img src="{{ asset('/images/dash.png') }}" height="20px" width="20px" class="pointer decrease decrease-demand-{{ item.id }} {{ haveQtyDemandDelivery and item.cartItemSplitDelivery.demand_delivery.quantity == item.quantity_demand ? ' not-allowed opacity-5' : '' }}" onclick="updateQtyDelivery('qty-delivery-demand', {{ item.id }}, -{{ item.offer.batchSize }}, {{ item.quantity }}, {{ item.offerId }}, {{ item.quantity_demand }}, event);">
                                    <input type="text"
                                           class="qty-delivery qty-delivery-demand-{{ item.id }}"
                                           name="quantity_demand"
                                           value="{{ quantityDemandInput }}"
                                           readonly />
                                    <img src="{{ asset('/images/plus.png') }}" height="20px" width="20px" class="pointer increase increase-demand-{{ item.id }}" onclick="updateQtyDelivery('qty-delivery-demand', {{ item.id }}, {{ item.offer.batchSize }}, {{ item.quantity }}, {{ item.offerId }}, {{ item.quantity_demand }}, event);">
                                {% endif %}
                            </div>
                        {% endif %}
                    {% else %}
                        <input id="quantity_{{ item.id }}" type="number" value="{{ item.quantity }}" min="{{ item.moq }}"
                               class="not-allowed" disabled/>
                        {% include('@OpenFront/component/cart-item-qty.html.twig') with {
                            'item': item,
                            'isShipping': isShipping,
                            'realStock': item.offer.realStock,
                            'haveQtyOnStockDelivery': haveQtyOnStockDelivery,
                            'haveQtyDemandDelivery ': haveQtyDemandDelivery ,
                        } %}
                    {% endif %}
                {% else %}
                    {{ item.quantity }}
                    {% include('@OpenFront/component/cart-item-qty.html.twig') with {
                        'item': item,
                        'isShipping': isShipping,
                        'realStock': item.offer.realStock,
                        'haveQtyOnStockDelivery': haveQtyOnStockDelivery,
                        'haveQtyDemandDelivery ': haveQtyDemandDelivery ,
                    } %}
                {% endif %}
            </td>
            <td class="{{ underline }} mobile-align label-price" {% if isShipping %}colspan="2" {% endif %}>
                <p class="mobile-max-only mobile-label">{{ 'cart.table_label.total_price'|trans }} :</p>
                <p class="item-total-price mobile-center">
                    {% if not item.valid %}
                        {% if item.offer.prices['EUR'] != 0.0 %}
                            {{ item.totalItem | price(locale) }} {{ cart.currency }}
                        {% else %}
                        {% endif %}
                    {% else %}
                        {{ item.totalItem | price(locale) }} {{ cart.currency }}
                    {% endif %}
                </p>
            </td>
            {% if not isShipping %}
            <td class="{{ underline }}" style="min-width: 100px">
                    {% if (cartStatus == 'CREATE' and isCartCreator) or (cartStatus == 'ASSIGN' and isCartCreator and isUserValidAssign) or (cartStatus == 'ASSIGN' and isCartCreator and not item.valid)%}
                        {% if not item.valid %}
                            <a class="action hidden-mobile" href="#" onclick="refreshItemPrice({{ item.id }},{{ item.offerId }},{{ item.quantity }});">
                                <span title="Refresh">
                                    <svg class="refresh">
                                        <use xlink:href="#refresh">
                                        </use>
                                    </svg>
                                </span>
                            </a>
                        {% endif %}
                        <a class="action hidden-mobile" href="#" onclick="removeItem({{ item.id }},{{ item.offerId }});">
                            <span title="Supprimer">
                                <svg class="trash">
                                    <use xlink:href="#trash">
                                    </use>
                                </svg>
                            </span>
                        </a>
                    {% else %}
                        <a class="action hidden-mobile not-allowed" href="#">
                            <span title="Supprimer">
                                <svg class="trash">
                                    <use xlink:href="#trash">
                                    </use>
                                </svg>
                            </span>
                        </a>
                    {% endif %}
            </td>
            {% endif %}
        </tr>
    {% endfor %}

    {% set hasShippingInfo = false %}

    {% if isShipping %}
        {# @var shippingOption \AppBundle\Model\CartMerchantShippingOptions#}
        {% set shippingOption = null %}
        {% if shipping is not null %}
            {% set shippingOption = shipping.findCartMerchantShippingOptions(merchant.id) %}
        {% endif %}

        {% set hasShippingInfo = merchant.upelaActive and shippingOption %}

        {% if merchant.shippable  %}
            <tr class="shipping-cart-detail underline">
            <td colspan="4">
                <div class="shipping-selector">
                    <div class="product">
                        <div class="img-container">
                            {% if not merchant.upelaActive %}
                                <img src="{{ asset('/images/no-shipping.svg') }}" style="width: 80%">
                            {% else %}
                                <img src="{{ asset('/images/Truck.svg') }}" style="width: 80%">
                            {% endif %}
                        </div>
                        <div class="detail-content">
                            <h5 class="name">StationOne Shipping offers</h5>
                            <div class="buyerRef">
                                {% if not merchant.upelaActive %}
                                    {{ 'shipping.option.no_merchant_shipping'|trans }}
                                {% elseif not shippingOption %}
                                        No carriers were able to provide a transportation offer for these products
                                {% else %}
                                    The cheapest offer has been selected by default.<br>
                                    Consult other options by clicking the dropdown list
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="selector-container" style="align-self: center;">
                        {% if shippingOption %}
                        <div class="js-select-wrapper select-wrapper has-text">
                            <select name="shipping" id="shipping_{{ index }}" class="shipping-select" data-merchantid="{{ merchant.id }}" data-vatrate="{{ shippingOption.vatRate }}">
                                <option value="" data-price="0"  data-vatprice="0" {% if not shippingOption.cheapestOption.price %}selected{% endif %}>{{ 'shipping.option.noshipping'|trans }}</option>
                                {% set index = 0 %}
                                {% if shippingOption.fastestOption and shippingOption.fastestOption.price %}
                                    <option
                                        value="fastest"
                                        data-price="{{ shippingOption.fastestOption.price }}"
                                        data-vatprice="{{ shippingOption.fastestOption.vatPrice }}"
                                    >
                                        {{ 'shipping.option.fastest'|trans }} ({{ shippingOption.fastestOption.price }} {{ cart.currency }})
                                    </option>
                                {% endif %}
                                {% if shippingOption.cheapestOption and shippingOption.cheapestOption.price %}
                                    <option
                                        value="cheapest"
                                        selected
                                        data-price="{{ shippingOption.cheapestOption.price }}"
                                        data-vatprice="{{ shippingOption.cheapestOption.vatPrice }}"
                                    >
                                        {{ 'shipping.option.cheapest'|trans }} ({{ shippingOption.cheapestOption.price }} {{ cart.currency }})
                                    </option>
                                {% endif %}
                                {% if shippingOption.monoCarrierOption %}
                                    {% for shippingItem in shippingOption.monoCarrierOption %}
                                        {% set ship  = shippingItem.carrierOffers|first %}
                                        <option
                                            value="mono_{{ index }}"
                                            data-price="{{ ship.price }}"
                                            data-vatprice="{{ ship.vatPrice }}"
                                        >
                                            {{ ship.serviceName }} ({{ ship.price }} {{ cart.currency }})
                                        </option>
                                        {% set index = index + 1 %}
                                    {% endfor %}
                                {% endif %}
                            </select>
                        </div>
                        {% elseif not merchant.upelaActive %}

                        {% else %}
                            {{ 'shipping.option.no_shipping_available'|trans }}
                        {% endif %}
                    </div>
                </div>
            </td>
            <td class="mobile-align label-price" colspan="2">
            {% if merchant.upelaActive and shippingOption %}
                <span class="price" data-price="0">{{ 0 | price(locale) }}</span> {{ cart.currency }}
            {% else %}
                -
            {% endif %}
            </td>
        </tr>
        {% endif %}
    {% endif %}


    {% if cart.merchants|length == 1 %}
        <tr class="resume-table subtotal">
            <td class="mobile-align">
                {% if merchant.subTotalWithoutVat > 0 and  merchant.subTotalWithoutVat < merchant.minimumOrderAmount %}
                    <div style="text-align: left !important; color: #FF0000;">{{ 'tab_infos_seller.minimum_order_amount'|trans({'%amount%': merchant.minimumOrderAmount|price(locale) }) }} {{ cart.currency }}</div>
                {% endif %}
            </td>
            <td class="hidden-mobile"></td>
            <td class="hidden-mobile"></td>
            <td class="hidden-mobile"></td>
            <td class="hidden-mobile"></td>
            <td class="hidden-mobile"></td>
        </tr>
    {% endif %}
    </tbody>
    <tbody {% if cart.merchants|length == 1 %}style="display: none"{% endif %}>
        <tr class="resume-table hidden-desktop" >
            <td colspan="4">
                {% if merchant.subTotalWithoutVat > 0 and  merchant.subTotalWithoutVat < merchant.minimumOrderAmount %}
                    <div style="text-align: left !important; color: #FF0000;">{{ 'tab_infos_seller.minimum_order_amount'|trans({'%amount%': merchant.minimumOrderAmount|price(locale) }) }} {{ cart.currency }}</div>
                {% endif %}
            </td>
        </tr>
        {# ------ SUBTOTAL WITHOUT VAT ------ #}
        <tr class="resume-table subtotal">

            <td class="hidden-mobile">
                {% if merchant.subTotalWithoutVat > 0 and  merchant.subTotalWithoutVat < merchant.minimumOrderAmount %}
                   <div style="text-align: left !important; color: #FF0000;">
                       {{ 'tab_infos_seller.minimum_order_amount'|trans({'%amount%': merchant.minimumOrderAmount|price(locale) }) }} {{ cart.currency }}
                   </div>
                {% endif %}
            </td>
            <td class="hidden-mobile"></td>
            <td class="hidden-mobile"></td>
            <td class="taxes-label">{{ 'cart.table_label.subtotal'|trans }}</td>
            <td></td>
            <td class="subtotal-without-vat">
                <span data-price="{{ merchant.subTotalWithoutVat }}">{{ merchant.subTotalWithoutVat | price(locale) }}</span> {{ cart.currency }}
            </td>
        </tr>

        {# ------ SUBTOTAL WITHOUT VAT ------ #}
        {% if isShipping and hasShippingInfo %}
        <tr class="resume-table subtotal">

            <td class="hidden-mobile">
                {% if merchant.subTotalWithoutVat > 0 and  merchant.subTotalWithoutVat < merchant.minimumOrderAmount %}
                    <div style="text-align: left !important; color: #FF0000;">{{ 'tab_infos_seller.minimum_order_amount'|trans({'%amount%': merchant.minimumOrderAmount|price(locale) }) }} {{ cart.currency }}</div>
                {% endif %}
            </td>
            <td class="hidden-mobile"></td>
            <td class="hidden-mobile"></td>
            <td class="taxes-label">{{ 'cart.table_label.shippingtotal'|trans }}</td>
            <td></td>
            <td class="subtotal-shipping">
                <span data-price="0">{{ 0 | price(locale) }}</span> {{ cart.currency }}
            </td>
        </tr>
        {% endif %}

        {# ------ TAXES ------ #}
        {% for vat,total in merchant.subTotalVat %}
            {% if vat > 0 %}
                <tr class="resume-table taxes vat_{{ vat | number_format(2, '_') }}" {% if total == 0 %}style="display: none"{% endif %}>
                    <td class="hidden-mobile"></td>
                    <td class="hidden-mobile"></td>
                    <td class="hidden-mobile"></td>
                    <td class="taxes-label-cell">
                        <p class="label-taxes label">{{ 'cart.table_label.taxes'|trans }}&nbsp;</p>
                        <p class="vat-percent">{{ vat | number_format(2, '.') }}%</p>
                    </td>
                    <td></td>
                    <td class="subtotal-vat-price">
                        <span data-price="{{ total }}">{{ total | price(locale) }}</span> {{ cart.currency }}
                    </td>
                </tr>
            {% endif %}
        {% endfor %}

        {# ------ SUBTOTAL WITH VAT ------ #}
        <tr id="subtotal-vat-{{ index }}" class="resume-table subtotal-vat">
            <td class="hidden-mobile"></td>
            <td class="hidden-mobile"></td>
            <td class="hidden-mobile"></td>
            <td class="taxes-label">{{ 'cart.table_label.subtotal_vat'|trans }}</td>
            <td></td>
            <td class="subtotal-cart">
                <span data-price="{{ merchant.total }}">{{ merchant.total | price(locale) }}</span>
                {{ cart.currency }}
            </td>
        </tr>
    </tbody>
</table>
