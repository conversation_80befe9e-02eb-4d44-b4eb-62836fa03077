{% extends '@OpenFront/base.html.twig' %}
{% trans_default_domain 'AppBundle' %}
{% set locale = app.request.locale %}

{% block title %}{{ 'comparaisonSheet.sticky' | trans }}{% endblock %}
{% block body %}
    <div class="Page-inner">
        {% if cart is not empty and cart|length > 0 %}
            <div class="Page-content">
                <div class="title">
                    <h1>{{ 'comparaisonSheet.page.title'|trans([], 'AppBundle') }}</h1>
                </div>
                <table class="seller-table">
                    <tbody>
                    {% for item in cart %}
                        <tr id="item-{{ item.id }}">
                            <td class="underline img-td">
                                <div class="img-container">
                                    <img src="{{ item.image_url }}">
                                    <div class="mobile-only remove-item">
                                        <a class="action" href="#" onclick="removeItem('{{ item.id }}');">
                                        <span title="Supprimer">
                                            <svg class="trash Icon">
                                                <use xlink:href="#trash">
                                                </use>
                                            </svg>
                                        </span>
                                        </a>
                                    </div>
                                </div>

                            </td>
                            <td class="underline">
                                <div class="detail-content">
                                    <p class="details-value mobile-only">{{ 'comparaisonSheet.page.product'|trans([], 'AppBundle') }} :&nbsp;</p>
                                    <div class="details-align">
                                        <h3 class="name"><a href="{{  path('front.offer.detail.short',{'ref':item.offer_id}) }}">{{ item.name }}</a></h3>
                                    </div>
                                </div>
                                {% if item.merchant %}
                                    <div>
                                        <p class="details-value">{{ 'product.seller'|trans }} :&nbsp;</p>
                                        <div class="details">{{ item.merchant|upper }}</div>
                                    </div>
                                {% endif %}
                                {% if item.merchant_reference %}
                                    <div>
                                        <span class="details-value">{{ 'product.seller_reference'|trans }} :&nbsp;</span>
                                        <div class="details">{{ item.merchant_reference }}</div>
                                    </div>
                                {% endif %}
                                {% if item.manufacturer %}
                                    <div>
                                        <span class="details-value">{{ 'product.manufacturer'|trans }} :&nbsp;</span>
                                        <div class="details">{{ item.manufacturer }}</div>
                                    </div>
                                {% endif %}
                                {% if item.manufacturer_reference %}
                                    <div>
                                        <span class="details-value">{{ 'product.manufacturer_reference'|trans }} :&nbsp;</span>
                                        <div class="details">{{ item.manufacturer_reference }}</div>
                                    </div>
                                {% endif %}

                                {% if item.frame_contract is not empty %}
                                    <div>
                                        <span class="details-value"><img style="width:20px" src="{{ asset('images/sign.svg') }}"> {{ 'invoice.detail.frame_contract'|trans }} : </span>
                                        <div class="details">{{ item.frame_contract }}</div>
                                    </div>
                                {% endif %}

                                {% if item.priceValidityDate %}
                                    <div>
                                        <span class="details-value">{{ "offer_detail.frame_contract_valid_date"|trans }} : </span>
                                        <div class="details">{{ item.priceValidityDate | format_datetime('short', 'none', locale=locale)}}</div>
                                    </div>
                                {% endif %}
                            </td>
                            <td class="underline" width="250px">
                                    {% if item.unitPrice is defined %}
                                    <div class="align-end-price">
                                        <span class="details-value">{{ 'comparaisonSheet.page.price_excl_tax'|trans }} :&nbsp;</span>
                                        <div class="details">
                                            {% if locale == 'en' %}
                                                {{ item.unitPrice | number_format('2', '.', ',') }}
                                            {% else %}
                                                {{ item.unitPrice | number_format('2', ',', ' ')}}
                                            {% endif %}
                                            {% if item.currency == 'EUR' %} € {%  elseif item.currency == 'USD' %} $ {% endif %}
                                        </div>
                                    </div>
                                    {% else %}
                                        <div class="align-end-price">
                                            <span class="details-value">{{ 'comparaisonSheet.page.no_price'|trans }}</span>
                                        </div>
                                    {% endif %}
                                    <div class="tresholds">
                                        {% if item.thresholds is defined %}
                                            {% set firstThreshold = true %}
                                            {% for key,value in item.thresholds %}
                                                {% if firstThreshold %}
                                                    <div>
                                                        <span class="details-value">{{ 'offer_detail.quantity'|trans }} < {{ key }} :&nbsp;</span>
                                                        <div>
                                                            <div class="details">
                                                                {% if locale == "en" %}
                                                                    {{ item.prices[item.currency] | number_format('2', '.', ',') }}
                                                                {% else %}
                                                                    {{ item.prices[item.currency] | number_format('2', ',', ' ') }}
                                                                {% endif %}
                                                                {% if item.currency == 'EUR' %} € {%  elseif item.currency == 'USD' %} $ {% endif %}
                                                            </div>
                                                        </div>
                                                    </div>
                                                {% endif %}
                                                <div>
                                                    <span class="details-value">{{ 'offer_detail.quantity'|trans }} > {{ key }} :&nbsp;</span>
                                                    <div class="details">
                                                        {% if locale == 'en' %}
                                                            {{ value | number_format('2', '.', ',') }}
                                                        {% else %}
                                                            {{ value | number_format('2', ',', ' ') }}
                                                        {% endif %}
                                                        {% if item.currency == 'EUR' %} € {%  elseif item.currency == 'USD' %} $ {% endif %}</p>
                                                    </div>
                                                </div>
                                                {% set firstThreshold = false %}
                                            {%  endfor %}
                                        {%  endif%}
                                    </div>
                                   {% if item.incoterm is defined and item.incoterm|length > 0 %}
                                    <div class="align-end-price">
                                        <span class="details-value">{{ 'comparaisonSheet.page.delivery'|trans }} :&nbsp;</span>
                                        <div class="details">
                                            {{ item.incoterm|upper }}
                                            {% if item.incoterm_country %}
                                                ({{ item.incoterm_country|upper }})
                                            {% endif %}
                                        </div>
                                    </div>
                                    {% endif %}
                                    {% if item.delivery_time and item.unitPrice is defined %}
                                        <div class="align-end-price">
                                            <span class="details-value">{{ 'product.delivery_time'|trans }} :&nbsp;</span>
                                            <div class="details">{{ item.delivery_time }} {{ 'site.form.days'|trans|lower }}</div>
                                        </div>
                                    {% endif %}
                            </td>
                            <td class="underline">
                                <a class="action desktop-only" href="#" onclick="removeItem('{{ item.id }}');">
                                    <span title="Supprimer">
                                        <svg class="trash Icon">
                                            <use xlink:href="#trash">
                                            </use>
                                        </svg>
                                    </span>
                                </a>
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>

                    <div class="comment-row">
                        {{ form_start(form) }}
                            <div class="comment-title">{{ 'comparaisonSheet.comment' | trans }}</div>
                            {{ form_row(form.comment) }}
                            {{ form_row(form.submit) }}
                        {{ form_end(form) }}
                    </div>
            </div>
        {% else %}
            <div class="empty-comparison">
                <h1>{{ 'comparaisonSheet.no_article' | trans }}</h1>
                <a class="color-primary" href="{{ path('homepage') }}">{{ 'comparaisonSheet.back_to_home' | trans }}</a>
                <img class="cart-empty-img desktop-only" src="{{ asset('images/empty-cart-temporary.png') }}">
            </div>
        {% endif %}
    </div>
{% endblock %}


{% block javascripts %}
    <script type="text/javascript">

        var removeItem = function(itemId) {
            loadingModal = window.UI.Modal.showLoading();
            $.ajax({
                type: 'DELETE',
                url: '{{ path('comparisonSheet.remove_item') }}',
                data: {
                    itemId: itemId
                },
                success: function(data) {
                    location.reload();
                },
                error: function (XMLHttpRequest, textStatus, errorThrown) {
                    console.log('Erreur: ' + textStatus);
                }
            });
        };
    </script>
{% endblock %}

