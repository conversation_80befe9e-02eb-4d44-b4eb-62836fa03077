{% trans_default_domain 'AppBundle' %}
<style>
    .header {
        width: 100%;
        background-color: #FFFFFF;
        padding: 0px 50px 10px;
        height: 168px;
        border-bottom: grey 3px solid;
    }

    .header .date-user td {
        padding: 0;
        text-align: left;
    }

    .header .date-user-company .info {
        color: #6B6F82;
        font-size: 13px;
        font-family: sans-serif;
    }

    .header .table-company td {
        padding: 0;
        width: 100%;
    }

    .body {
        background-color: #F4F5FA;
        padding: 20px 0 0;
        height: 73.2%;
        width: 100%;
    }

    .body h1 {
        font-family: sans-serif;
        font-size: 21px;
        margin-bottom: 25px;
    }

    .seller-table {
        width: 90%;
        margin: 10px auto 0;
        border: 1px solid #A4A7B3;
        background-color: white;
    }

    .seller-table .line {
        width: 100%;
    }

    .seller-table .line .product-info {
        margin-top: -20px;
        margin-left: 10px;
    }

    .seller-table .line .product-info td {
        margin-top: 3px;
    }

    .seller-table .line .inline {
        padding: 10px;
        height: 140px;
    }

    .title, .product-data {
        color: #6B6F82;
        font-size: 13px;
        font-family: sans-serif;
        line-height: 25px;
    }

    .details {
        color: #6B6F82;
        font-weight: lighter;
        letter-spacing: 0.5px;
    }

    .darker {
        color: #393b44;
    }

    .title {
        color: #6B6F82;
        font-weight: bold;
    }

    .tresholds {
        text-align: left;
        margin-top: 0px;
    }

    .footer {
        width: 100%;
        height: 2%;
        background-color: #031239;
        padding: 30px 30px 50px;
        text-align: right;
        color: #6B6F82;
        font-weight: bold;
        position: fixed;
        bottom: 0;
    }

</style>
{% set locale = app.request.locale %}
<div class="Page-inner">
        <div class="Page-content">
            <div class="header">
                <table>
                    <tbody>
                        <tr>
                            <td style="padding-top: 40px;">
                                <div class="logo">
                                    <img src="data:image/png;base64,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">
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <table style="margin-top: 10px; height: 100px; vertical-align: top">
                    <tbody>
                        <tr>
                            <td style="width: 50%">
                                <table class="date-user date-user-company">
                                    <tbody>
                                        <tr>
                                            <td class="info">
                                                <span class="darker">{{ 'comparaisonSheet.page.date'|trans }} :</span> {{ 'now'| format_datetime('short', 'none', locale=locale) }}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>

                                <table class="table-company date-user-company">
                                    <tbody>
                                    <tr>
                                        <td class="info">
                                            <span class="darker">{{ 'comparaisonSheet.page.company_name'|trans }} :</span> {{ user.company.name|upper }}
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>

                                <table class="table-company date-user-company">
                                    <tbody>
                                    <tr>
                                        <td class="info">
                                            <span class="darker">{{ 'comparaisonSheet.page.author'|trans }} :</span> {{ user.firstname|capitalize }}&nbsp;{{ user.lastname|capitalize }}
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </td>
                            <td style="width: 50%; max-height: 150px; padding-left: 50px">
                                {% if comment is defined and comment|length > 0 %}
                                    <table class="date-user date-user-company" style="vertical-align: top">
                                        <tbody>
                                        <tr>
                                            <td class="info">
                                                <span class="darker">{{ 'comparaisonSheet.page.comment'|trans }} :</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div style="color:#6B6F82; max-height: 150px; font-size: 13px">
                                                    {{ comment }}
                                                </div>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                {% endif %}
                            </td>
                        </tr>
                    </tbody>
                </table>
                    </div>
                        <div class="body">
                            <table style="margin-left: 50px">
                                <tbody>
                                    <tr>
                                        <td>
                                            <h1>{{ 'comparaisonSheet.page.title'|trans }}</h1>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>{{ 'comparaisonSheet.information'|trans }}</td>
                                    </tr>
                                </tbody>
                            </table>

                            {% for item in cart %}
                                <table class="seller-table">
                                    <tbody>
                                        <tr id="item-{{ item.id }}" class="line">
                                            <td class="inline" style="width: 122px; padding-left: 15px">
                                                <div class="img-container">
                                                    <img src="{{ item.image_url }}" style="width: 100px; height: auto; max-height: 100px; border: 1px solid #A4A7B3;">
                                                </div>
                                            </td>
                                            <td style="text-align: left; padding-top: 0; width: 45%">
                                                <table class="product-info">
                                                    <tbody>
                                                        <tr>
                                                            <td style="padding-top: 20px">
                                                                <div class="title" style="color:#9600FF">
                                                                    {{ item.name }}
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <div class="product-data">
                                                                    <span class="darker">{{ 'product.seller'|trans }} :&nbsp;</span>
                                                                    <span class="details">{{ item.merchant|upper }}</span>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <div class="product-data">
                                                                    <span class="darker">{{ 'product.seller_reference'|trans }} :&nbsp;</span>
                                                                    <span class="details">{{ item.merchant_reference }}</span>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <div class="product-data">
                                                                    <span class="darker">{{ 'product.manufacturer'|trans }} :&nbsp;</span>
                                                                    <span class="details">{{ item.manufacturer }}</span>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <div class="product-data">
                                                                    <span class="darker">{{ 'product.manufacturer_reference'|trans }} :&nbsp;</span>
                                                                    <span class="details">{{ item.manufacturer_reference }}</span>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        {% if item.frame_contract is not empty %}
                                                            <tr>
                                                                <td>
                                                                    <div class="product-data">
                                                                        <span class="darker">{{ 'invoice.detail.frame_contract'|trans }} :&nbsp;</span>
                                                                        <span class="details">{{ item.frame_contract }}</span>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        {% endif %}

                                                        {% if item.priceValidityDate %}
                                                            <tr>
                                                                <td>
                                                                    <div class="product-data">
                                                                        <span class="darker">{{ 'offer_detail.frame_contract_valid_date'|trans }} :&nbsp;</span>
                                                                        <span class="details">{{ item.priceValidityDate | format_datetime('short', 'none', locale=locale) }}</span>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        {% endif %}
                                                    </tbody>
                                                </table>
                                            </td>
                                            <td class="inline" style="padding-top: 0; width: 15%">
                                            </td>
                                            <td class="inline" style="width: 30%; vertical-align: middle">
                                                <table class="tresholds">
                                                    <tbody>
                                                        {% if item.unitPrice is defined %}
                                                        <tr>
                                                            <td style="text-align: right">
                                                                <div class="product-data">
                                                                    <span class="darker">
                                                                        {{ 'comparaisonSheet.page.price_excl_tax'|trans }} :
                                                                    </span>

                                                                    <span class="details">
                                                                        {% if locale == 'en' %}
                                                                            {{ item.unitPrice | number_format('2', '.', ',') }}
                                                                        {% else %}
                                                                            {{ item.unitPrice | number_format('2', ',', ' ')}}
                                                                        {% endif %}
                                                                        {% if item.currency == 'EUR' %} € {%  elseif item.currency == 'USD' %} $ {% endif %}
                                                                    </span>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        {% else %}
                                                        <tr>
                                                            <td style="text-align: right">
                                                                <div class="product-data">
                                                                    <span class="darker">
                                                                       {{ 'comparaisonSheet.page.no_price'|trans }}
                                                                    </span>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        {% endif %}
                                                        {% if item.thresholds is defined and item.thresholds|length > 0 %}
                                                            {% for key,value in item.thresholds %}
                                                                <tr>
                                                                    <td style="text-align: right">
                                                                        <div class="product-data">
                                                                            <span class="darker">
                                                                                {{ 'offer_detail.quantity'|trans }} > {{ key }} :
                                                                            </span>
                                                                            <span class="details">
                                                                                {% if locale == 'en' %}
                                                                                    {{ value | number_format('2', '.', ',') }}
                                                                                {% else %}
                                                                                    {{ value | number_format('2', ',', ' ') }}
                                                                                {% endif %}
                                                                                {% if item.currency == 'EUR' %} € {%  elseif item.currency == 'USD' %} $ {% endif %}
                                                                            </span>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                            {%  endfor %}
                                                        {% endif %}
                                                        {% if item.incoterm %}
                                                        <tr>
                                                            <td style="text-align: right">
                                                                <div class="product-data">
                                                                    <span class="darker"> {{ 'comparaisonSheet.page.delivery'|trans }} :&nbsp;</span>
                                                                    <span class="details">
                                                                        {{ item.incoterm|upper }}
                                                                        {% if item.incoterm_country %}
                                                                            ({{ item.incoterm_country|upper }})
                                                                        {% endif %}
                                                                    </span>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        {% endif %}
                                                        <tr>
                                                            <td style="text-align: right">
                                                                <div class="product-data">
                                                                    {% if item.delivery_time and item.unitPrice is defined %}
                                                                    <span class="darker">{{ 'product.delivery_time'|trans }} :&nbsp;</span>
                                                                    <span class="details">{{ item.delivery_time }} {{ 'site.form.days'|trans|lower }}</span>
                                                                    {% endif %}
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            {% endfor %}
                    </div>

            <div class="footer" style="position: absolute; bottom: 0px;">
                {{ 'footer.additional.alstom'|trans }}
            </div>
        </div>
</div>
