{% trans_default_domain 'AppBundle' %}

{% if realStock is not empty and item.qty_greater_real_stock %}
    <div class="w-100" style="position: absolute; bottom: {{ isShipping ? "5" : "14" }}px">
        {% if haveQtyOnStockDelivery %}
            <span>{{ item.cartItemSplitDelivery.on_stock_delivery.quantity }}</span>
            <br>
        {% endif %}
        {% if haveQtyDemandDelivery %}
            <span>{{ item.cartItemSplitDelivery.demand_delivery.quantity }}</span>
        {% endif %}
    </div>
{% endif %}
