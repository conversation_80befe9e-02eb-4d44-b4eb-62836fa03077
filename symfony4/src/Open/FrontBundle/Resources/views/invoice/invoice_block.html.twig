{% trans_default_domain 'AppBundle' %}
{% set locale = app.request.locale %}

<div class="invoice-block">
    <div class="left-block">
         <div class="invoice-row">
             <span class="title">{{ 'invoice.list.date'|trans([], 'AppBundle') }}</span>
             {% if invoice.id_number is not null and  invoice.pdf_file is not null%}
                 <span class="value"><a target="_blank" href="{{ invoice.pdf_file }}">{{ invoice.id_number }}</a></span>
             {% else %}
                 <span class="value">-</span>
             {% endif %}
         </div>
        <div class="invoice-row">
            <span class="title">{{ 'invoice.list.order'|trans([], 'AppBundle') }}</span>
            {% if invoice.orderId is defined %}
                <span class="value"><a href="{{ path('front.order.detail', {'orderId' : invoice.orderId }) }}">{{ invoice.orderId }}</a></span>
            {% else %}
                <span class="value">-</span>
            {% endif %}
        </div>
        <div class="invoice-row">
            <span class="title">{{ 'invoice.list.seller'|trans([], 'AppBundle') }}</span>
            <span class="value">{{ invoice.issuer_name }}</span>
        </div>
    </div>

    <div class="right-block">
        <div class="invoice-row">
            <span class="title">{{ 'invoice.list.date'|trans([], 'AppBundle') }}</span>
            <span class="value">{{ invoice.created_on|format_datetime('short', 'none', locale=locale) }}</span>
        </div>

        <div class="invoice-row">
            <span class="title">{{ 'invoice.list.amount'|trans([], 'AppBundle') }}</span>
            <span class="value">
                {% if locale == 'en' %}
                    {{ invoice.total_amount_with_taxes| number_format('2', '.', ',') }}
                {% else %}
                    {{ invoice.total_amount_with_taxes| number_format('2', ',', ' ')}}
                {% endif %}
                {{ invoice.currency }}
            </span>
        </div>

        {% if invoicesList.subType == 'NOT_PAID_INVOICE' %}
            <div class="invoice-row">
                <span class="title">{{ 'invoice.list.due_date'|trans([], 'AppBundle') }}</span>
                <span class="value">
                    {% if invoice.due_on is not null %}
                        {{ invoice.due_on|format_datetime('short', 'none', locale=locale) }}
                    {% else %}
                        -
                    {% endif %}
                </span>
            </div>
            <div class="invoice-row">
                <span class="title">{{ 'invoice.list.remain'|trans([], 'AppBundle') }}</span>
                <span class="value">
                    {% if locale == 'en' %}
                        {{ invoice.remaining_amount| number_format('2', '.', ',') }}
                    {% else %}
                        {{ invoice.remaining_amount| number_format('2', ',', ' ')}}
                    {% endif %}
                    {{ invoice.currency }}
                </span>
            </div>
        {% endif %}
    </div>
</div>
