{% extends '@OpenFront/base.html.twig' %}

{% trans_default_domain 'AppBundle' %}
{% set locale = app.request.locale %}
{% block title %}{{ 'invoice.detail.title' |trans([], 'AppBundle') }}{% endblock %}
{% block stylesheets %}
    <link href="{{ asset('css/page-default-v1.css') }}" rel="stylesheet" media="screen">
{% endblock %}
{% block body %}
    <div class="Page-invoice-inner">

        <div class="back_to_order">
            <a class="previous-page" href="{{ path('front.order.detail', { 'orderId': paymentSummary.order.id}) }}"><i class="arrow left"></i>{{ 'invoice.detail.go_back' | trans }} {{ paymentSummary.order.idNumber }}</a>
        </div>

        <div class="invoice-block invoice-detail">
            <div class="left-block">
                <div class="invoice-row">
                    <span class="title">{{ 'invoice.detail.order_id' |trans }}</span>
                    <span class="value">{{ paymentSummary.order.idNumber }} - {{ paymentSummary.merchantOrder.merchant.id }}</span>
                </div>
                <div class="invoice-row">
                    <span class="title">{{ 'invoice.detail.order_seller'|trans }}</span>
                    <span class="value">{{ paymentSummary.merchantOrder.merchant.name }}</span>
                </div>
                <div class="invoice-row desktop-only">
                    <span class="title invoice_bold">{{ 'invoice.detail.order_amount' | trans | upper}}</span>
                    <span class="value invoice_bold">
                        {{ paymentSummary.merchantOrder.amountVatIncluded| price(locale) }}
                        {{ paymentSummary.currency }}
                    </span>
                </div>
            </div>
            <div class="middle-block">
                <div class="invoice-row">
                    <span class="title">{{ 'invoice.detail.order_date' |trans }}</span>
                    <span class="value">{{ paymentSummary.order.createdOn|format_datetime('short', 'none', locale=locale) }}</span>
                </div>
            </div>
            <div class="right-block">
                <div class="invoice-row">
                    <span class="title">{{ 'invoice.detail.order_status' |trans }}</span>
                    <span class="value">{{ ('orders.status.status_' ~ paymentSummary.merchantOrder.status) | trans }}</span>
                </div>

                {% if not paymentSummary.cancelled %}
                    <div class="invoice-row">
                        <span class="title invoice_bold">{{ 'invoice.detail.order_payment_status' | trans | upper }}</span>
                        <span class="value">
                            {% if paymentSummary.paid %}
                                {{ 'invoice.detail.paid' | trans }}
                            {% else %}
                                {{ 'invoice.detail.not_paid' | trans }}
                            {% endif %}
                        </span>
                    </div>
                {% endif %}

                <div class="invoice-row mobile-only">
                    <span class="title invoice_bold">{{ 'invoice.detail.order_amount' | trans | upper}}</span>
                    <span class="value invoice_bold">
                        {{ paymentSummary.merchantOrder.amount| price(locale) }}
                        {{ paymentSummary.currency }}
                    </span>
                </div>
            </div>
        </div>

        <div class="not-yet-invoiced">
            <div class="not-yet-inline">
                <div>
                    <div class="not-yet-text">
                        <div class="not-yet-title">{{ 'invoice.detail.not_yet_invoiced' | trans | upper }}</div>
                        {{ paymentSummary.notYetInvoiced | price(locale) }} {{ paymentSummary.currency }} {{ 'invoice.detail.including_taxes' | trans }}
                    </div>
                    <div class="not-yet-text">
                        <div class="not-yet-title">{{ 'invoice.detail.already_invoiced' | trans | upper }}</div>
                        {{ paymentSummary.alreadyInvoiced | price(locale) }} {{ paymentSummary.currency }} {{ 'invoice.detail.including_taxes' | trans }}
                    </div>
                </div>
                {% if paymentSummary.termPaymentType %}
                    <div>
                        <div class="not-yet-text">
                            <div class="not-yet-title">{{ 'invoice.detail.total_remaining_to_pay' | trans | upper }}</div>
                            {{ paymentSummary.totalRemainingAmountToPay | price(locale) }} {{ paymentSummary.currency }} {{ 'invoice.detail.including_taxes' | trans }}
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>

        <table class="invoice_table">
            {% if paymentSummary.hasPayment() or paymentSummary.hasRefund() %}
                <thead class="desktop-only">
                    <th style="width: 35%"></th>
                    <th style="width: 15%">{{ 'invoice.list.date' |trans }}</th>
                    <th style="width: 20%"></th>
                    <th style="width: 15%">{{ 'invoice.detail.invoice_amount' |trans }} ({{ paymentSummary.currency }})</th>
                    <th style="width: 15%">{{ 'invoice.detail.invoice_payment' |trans }} ({{ paymentSummary.currency }})</th>
                </thead>
            {% endif %}
            <tbody>
                {% for  paymentProcess in paymentSummary.paymentProcesses %}
                    {% if paymentProcess.creditNoteType %}
                        <tr class="invoice-item">
                            <td class="invoice-name">{{ 'invoice.detail.credit_note' |trans | upper }}</td>
                            <td>
                                <div class="mobile-only">{{ 'invoice.detail.invoice_date' |trans }} :</div>
                                {{ paymentProcess.createdOn |format_datetime('short', 'none', locale=locale) }}
                            </td>
                            <td></td>
                            <td class="invoice-amount">
                                <div class="mobile-only">{{ 'invoice.detail.invoice_amount' |trans }} {{ paymentSummary.currency }} :</div>
                                -{{ paymentProcess.amount | price(locale) }}
                            </td>
                            <td></td>
                        </tr>
                    {% endif %}

                    {% if paymentProcess.invoiceType %}

                        <tr class="desktop-only border-invoice">
                            <th></th>
                            <th>{{ 'invoice.list.date' |trans }}</th>
                            <th>{{ 'invoice.detail.invoice_due_date' |trans }}</th>
                            <th>{{ 'invoice.detail.invoice_amount' |trans }} ({{ paymentSummary.currency }})</th>
                            <th>{{ 'invoice.detail.invoice_payment' |trans }} ({{ paymentSummary.currency }})</th>
                        </tr>

                        <tr class="mobile-only border-invoice invoice-item" style="padding: 0; border-bottom: 0; border-color: #F4F5FA">
                            <td style="padding: 0"></td>
                            <td style="padding: 0"></td>
                            <td style="padding: 0"></td>
                            <td style="padding: 0"></td>
                            <td style="padding: 0"></td>
                        </tr>

                        <tr class="invoice-item">
                            <td class="invoice-name">
                                {% if paymentProcess.pdfUrl %}
                                    {{ 'invoice.detail.invoice' | trans | upper}} <a target="_blank" href="{{ path('front.invoice.pdf', {'invoiceId' : paymentProcess.izbId}) }}">{{ paymentProcess.id }}</a>
                                {% else %}
                                    {{ 'invoice.detail.invoice' | trans | upper}} {{ paymentProcess.id }}
                                {% endif %}
                            </td>
                            <td>
                                <div class="mobile-only">{{ 'invoice.list.date' | trans }} :</div>
                                {{ paymentProcess.createdOn |format_datetime('short', 'none', locale=locale) }}
                            </td>
                            <td>
                                {% if paymentProcess.dueOn is defined %}
                                    {{ paymentProcess.dueOn |format_datetime('short', 'none', locale=locale) }}
                                {% endif %}
                            </td>
                            <td class="invoice-amount">
                                <div class="mobile-only">{{ 'invoice.detail.invoice_amount' |trans }} {{ paymentSummary.currency }} :</div>
                                {{ paymentProcess.amount | price(locale) }} <span class="desktop-only"></span>
                            </td>
                            <td></td>
                        </tr>

                        {% for invoicePaymentProcess in paymentProcess.paymentProcesses %}

                            {% if invoicePaymentProcess.creditNoteType %}
                                <tr class="invoice-item invoice-sub-item">
                                    <td class="invoice-name">
                                        {% if invoicePaymentProcess.pdfUrl %}
                                            {{ 'invoice.detail.credit_note' | trans | upper}} <a target="_blank" href="{{ path('front.credit_note.pdf', {'creditNoteId' : invoicePaymentProcess.izbId}) }}">{{ invoicePaymentProcess.id }}</a>
                                        {% else %}
                                            {{ 'invoice.detail.credit_note' | trans | upper}} {{ invoicePaymentProcess.id }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="mobile-only">{{ 'invoice.detail.invoice_date' |trans }} :</div>
                                        {{ invoicePaymentProcess.createdOn |format_datetime('short', 'none', locale=locale) }}
                                    </td>
                                    <td></td>
                                    <td class="invoice-amount">
                                        <div class="mobile-only">{{ 'invoice.detail.invoice_amount' |trans }} {{ paymentSummary.currency }} :</div>
                                        -{{ invoicePaymentProcess.amount | price(locale) }}
                                    </td>
                                    <td></td>
                                </tr>
                            {% endif %}

                            {% if invoicePaymentProcess.paymentType %}
                                <tr class="invoice-item invoice-sub-item">
                                    <td class="invoice-name">{{ 'invoice.detail.payment' | trans | upper}} {{ invoicePaymentProcess.id }}</td>
                                    <td>
                                        <div class="mobile-only">{{ 'invoice.detail.invoice_date' |trans }} :</div>
                                        {{ invoicePaymentProcess.createdOn | format_datetime('short', 'none', locale=locale) }}
                                    </td>
                                    <td></td>
                                    <td></td>
                                    <td class="invoice-amount">
                                        <div class="mobile-only">{{ 'invoice.detail.invoice_payment' |trans }} {{ paymentSummary.currency }} :</div>
                                        {{ invoicePaymentProcess.payment | price(locale) }}
                                    </td>
                                </tr>
                            {% endif %}

                            {% if invoicePaymentProcess.refundType %}
                                <tr class="invoice-item invoice-sub-item">
                                    <td class="invoice-name">{{ 'invoice.detail.refund' | trans | upper}} {{ invoicePaymentProcess.id }}</td>
                                    <td>
                                        <div class="mobile-only">{{ 'invoice.detail.invoice_date' |trans }} :</div>
                                        {{ invoicePaymentProcess.createdOn |format_datetime('short', 'none', locale=locale) }}
                                    </td>
                                    <td></td>
                                    <td></td>
                                    <td class="invoice-amount">
                                        <div class="mobile-only">{{ 'invoice.detail.invoice_amount' |trans }} {{ paymentSummary.currency }} :</div>
                                        -{{ invoicePaymentProcess.payment | price(locale) }}
                                    </td>
                                </tr>
                            {% endif %}

                        {% endfor %}

                        {% if paymentProcess.paymentProcesses | length > 0 %}
                            <tr class="invoice-item invoice-sub-item invoice-total">
                                <td class="invoice-name">
                                    <div class="separator-top desktop-only"></div>
                                    <div class="total-invoice-val no-padding-left">{{ 'invoice.detail.total' | trans | upper}} </div>
                                </td>
                                <td><div class="separator-top desktop-only"></div></td>
                                <td><div class="separator-top desktop-only"></div></td>
                                <td>
                                    <div class="separator-top desktop-only"></div>
                                    <div class="mobile-only">{{ 'invoice.detail.invoice_amount' |trans }} {{ paymentSummary.currency }} :</div>
                                    <div class="total-invoice-val">{{ paymentProcess.totalAmount | price(locale) }}</div>
                                </td>
                                <td>
                                    <div class="separator-top desktop-only"></div>
                                    <div class="mobile-only">{{ 'invoice.detail.invoice_payment' |trans }} {{ paymentSummary.currency }} :</div>
                                    <div class="total-invoice-val">{{ paymentProcess.totalPayment | price(locale) }}</div>
                                </td>
                            </tr>
                        {% endif %}

                        {% if paymentSummary.termPaymentType %}
                            <tr class="invoice-item invoice-sub-item invoice-remaining">
                                <td class="invoice-name">
                                    <div class="total-invoice-val no-padding-left">{{ 'invoice.detail.remaining_to_pay' | trans | upper}} </div>
                                </td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td>
                                    <div class="mobile-only">{{ 'invoice.detail.invoice_payment' |trans }} {{ paymentSummary.currency }} :</div>
                                    <div class="total-invoice-val {% if  paymentProcess.dueOn <  now %} red {% endif %}">{{ paymentProcess.totalRemainingAmountToPay  | price(locale) }}</div>
                                </td>
                            </tr>
                        {% endif %}

                    {% endif %}

                    {% if paymentProcess.paymentType %}

                        <tr class="invoice-item">
                            <td class="invoice-name">{{ 'invoice.detail.payment' | trans }} {{ paymentProcess.id }}</td>
                            <td>
                                <div class="mobile-only">{{ 'invoice.detail.invoice_date' |trans }} :</div>
                                {{ paymentProcess.createdOn | format_datetime('short', 'none', locale=locale) }}
                            </td>
                            <td></td>
                            <td></td>
                            <td class="invoice-amount">
                                <div class="mobile-only">{{ 'invoice.detail.invoice_payment' |trans }} {{ paymentSummary.currency }} :</div>
                                {{ paymentProcess.payment | price(locale) }}
                            </td>
                        </tr>

                    {% endif %}

                    {% if paymentProcess.refundType %}
                        <tr class="invoice-item">
                            <td class="invoice-name">{{ 'invoice.detail.refund' | trans }} {{ paymentProcess.id }}</td>
                            <td>
                                <div class="mobile-only">{{ 'invoice.detail.invoice_date' |trans }} :</div>
                                {{ paymentProcess.createdOn |format_datetime('short', 'none', locale=locale) }}
                            </td>
                            <td></td>
                            <td></td>
                            <td class="invoice-amount">
                                <div class="mobile-only">{{ 'invoice.detail.invoice_amount' |trans }} {{ paymentSummary.currency }} :</div>
                                - {{ paymentProcess.payment | price(locale) }}
                            </td>
                        </tr>
                    {% endif %}
                {% endfor %}
            </tbody>
        </table>

    </div>
{% endblock %}
