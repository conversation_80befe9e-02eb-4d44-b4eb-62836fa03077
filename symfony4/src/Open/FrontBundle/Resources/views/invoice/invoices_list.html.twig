{% extends '@OpenFront/menu/buyer_menu.html.twig' %}
{% trans_default_domain 'AppBundle' %}

{% block title %}{{ 'invoice.list.title' |trans([], 'AppBundle') }}{% endblock %}
{% block stylesheets %}
    <link href="{{ asset('css/page-default-v1.css') }}" rel="stylesheet" media="screen">
{% endblock %}
{% block body %}
    <div class="Page-inner">
        <div class="content-orders">
            <div class="export" style="margin: 0 -10px 20px">
                <form action="{{ path('front.invoice.export') }}" style="display: flex;align-items: flex-end;padding-top: 0;">
                    <div style="flex: 1; padding: 0 10px">
                        <label>{{ 'invoice.list.from' | trans }}</label>
                        <div style="position: relative">
                            <input autocomplete="off" name="dateFrom" class="datepicker" type="text"style="margin: 0"/>
                        </div>
                    </div>
                    <div style="flex: 1; padding: 0 10px">
                        <label>{{ 'invoice.list.to' | trans }}</label>
                        <div style="position: relative">
                            <input autocomplete="off" name="dateTo" class="datepicker" type="text" style="margin: 0"/>
                        </div>
                    </div>
                    <div style="padding: 0 10px">
                        <button class="Btn" type="submit">{{ 'invoice.list.export' | trans }}</button>
                    </div>
                </form>
            </div>
            {% embed ('@OpenFront/component/tab-infos.html.twig')
                with {'attributes': invoicesTab, 'activeTab': activeTab, 'class': 'plain-tab', 'type': 'CUSTOM', 'searchForm': form, 'searchFormId': 'invoice_form_submit', 'searchFormRow': form.invoice } %}

                {% block content %}
                    <div class="tab-content show">
                        {% if invoices is not empty %}
                            <div class="content-list">
                                {% if activeInvoiceTab.subType == 'NOT_PAID_INVOICE' %}
                                    <div class="additional-row">
                                            <div class="invoice-block">
                                                {% if totalToPayEUR != 0 %}
                                                    <div class="left-block">
                                                        <div class="invoice-row">
                                                            <span class="title">{{ 'invoice.list.total_to_pay_eur' |trans([], 'AppBundle') }}</span>
                                                            <span class="value">
                                                                {{ totalToPayEUR | price(locale) }}
                                                            </span>
                                                        </div>
                                                        <div class="invoice-row">
                                                            <span class="title"> {{ 'invoice.list.due_date_total_eur' |trans([], 'AppBundle') }}</span>
                                                            <span class="value">
                                                                {{ toPayThisMonthEUR | price(locale) }}
                                                            </span>
                                                        </div>
                                                        <div class="invoice-row">
                                                            <span class="title"> {{ 'invoice.list.late_payment_eur' |trans([], 'AppBundle') }}</span>
                                                            <span class="value{% if latePaymentEUR > 0 %} due-date{% endif %}">
                                                                {{ latePaymentEUR | price(locale) }}
                                                            </span>
                                                        </div>
                                                    </div>
                                                {% endif %}
                                                {% if totalToPayUSD != 0 %}
                                                    <div class="right-block">
                                                        <div class="invoice-row">
                                                            <span class="title">{{ 'invoice.list.total_to_pay_usd' |trans([], 'AppBundle') }}</span>
                                                            <span class="value">
                                                                {{ totalToPayUSD | price(locale) }}
                                                            </span>
                                                        </div>
                                                        <div class="invoice-row">
                                                            <span class="title">{{ 'invoice.list.due_date_total_usd' |trans([], 'AppBundle') }}</span>
                                                            <span class="value">
                                                                {{ toPayThisMonthUSD | price(locale) }}
                                                            </span>
                                                        </div>
                                                        <div class="invoice-row">
                                                            <span class="title"> {{ 'invoice.list.late_payment_usd' |trans([], 'AppBundle') }}</span>
                                                            <span class="value{% if latePaymentUSD > 0 %} due-date{% endif %}">
                                                                {{ latePaymentUSD | price(locale) }}
                                                            </span>
                                                        </div>
                                                    </div>
                                                {% endif %}
                                            </div>
                                    </div>

                                    <div class="h-line"></div>
                                {% endif %}
                                <table>
                                    <thead class="desktop-only">
                                        <th>{{ 'invoice.list.invoice'|trans([], 'AppBundle') }}</th>
                                        <th>{{ 'invoice.list.seller'|trans([], 'AppBundle') }}</th>
                                        <th>{{ 'invoice.list.date'|trans([], 'AppBundle') }}</th>
                                        <th>{{ 'invoice.list.order'|trans([], 'AppBundle') }}</th>
                                        <th>{{ 'invoice.list.amount'|trans([], 'AppBundle') }}</th>
                                        {% if activeInvoiceTab.subType == 'NOT_PAID_INVOICE' %}
                                            <th>{{ 'invoice.list.due_date'|trans([], 'AppBundle') }}</th>
                                            <th>{{ 'invoice.list.remain'|trans([], 'AppBundle') }}</th>
                                        {% endif %}
                                    </thead>
                                    {% for invoice in invoices %}
                                        <tr {% if invoice.isCreditNoteType %}  class="credit_note" {% endif %}>
                                            <td>
                                                <div class="label-mobile mobile-only">{{ 'invoice.list.invoice'|trans([], 'AppBundle') }}: </div>
                                                {#{% if invoice.numberId is not null and  invoice.hasPdfFile %}#}
                                                {% if invoice.numberId is not null %}
                                                    {% if invoice.isInvoiceType %}
                                                        <a target="_blank" href="{{ path('front.invoice.pdf', {'invoiceId': invoice.izbergId, 'hasPdfFile': invoice.hasPdfFile}) }}">{{ invoice.numberId }}</a>
                                                    {% else %}
                                                        <a target="_blank" href="{{ path('front.creditNote.pdf', {'creditNoteId' : invoice.izbergId, 'hasPdfFile': invoice.hasPdfFile}) }}">{{ invoice.numberId }}</a>
                                                    {% endif %}

                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="label-mobile mobile-only">{{ 'invoice.list.seller'|trans([], 'AppBundle') }}: </div>
                                                {{ invoice.issuerName }}
                                            </td>
                                            <td>
                                                <div class="label-mobile mobile-only">{{ 'invoice.list.date'|trans([], 'AppBundle') }}: </div>
                                                {{ invoice.createdOn|date("d/m/Y") }}
                                            </td>
                                            <td>
                                                <div class="label-mobile mobile-only">{{ 'invoice.list.order'|trans([], 'AppBundle') }}: </div>
                                                {% if invoice.orderId is defined and invoice.orderId is not empty %}
                                                    <a href="{{ path('front.order.detail', {'orderId' : invoice.orderId }) }}">{{ invoice.numOrder }}</a>
                                                {% else %}
                                                    -
                                                {% endif %}

                                            </td>
                                            <td>
                                                <div class="label-mobile mobile-only">{{ 'invoice.list.amount'|trans([], 'AppBundle') }}: </div>
                                                {% if invoice.isCreditNoteType %}
                                                -
                                                {% endif %}
                                                {{ invoice.totalAmountWithTaxes | price(locale) }} {{ invoice.currency }}
                                            </td>
                                            {% if activeInvoiceTab.subType == 'NOT_PAID_INVOICE' %}

                                                {% if invoice.dueOn is defined and invoice.dueOn is not null %}
                                                    <td>
                                                        <div class="label-mobile mobile-only">{{ 'invoice.list.due_date'|trans([], 'AppBundle') }}: </div>
                                                        {{ invoice.dueOn|date("d/m/Y") }}
                                                    </td>
                                                {% else %}
                                                    <td>
                                                        <div class="label-mobile mobile-only">{{ 'invoice.list.due_date'|trans([], 'AppBundle') }}: </div>
                                                        -
                                                    </td>
                                                {% endif %}
                                                {% if invoice.dueOn is defined %}
                                                    <td{% if date(invoice.dueOn) < date() %} class="due-date"{% endif %}>
                                                        <div class="label-mobile mobile-only">{{ 'invoice.list.remain'|trans([], 'AppBundle') }}: </div>
                                                        {{ invoice.remainingAmount | price(locale) }} {{ invoice.currency }}
                                                    </td>
                                                {% else %}
                                                    <td>
                                                        <div class="label-mobile mobile-only">{{ 'invoice.list.remain'|trans([], 'AppBundle') }}: </div>
                                                    </td>
                                                {% endif %}

                                            {% endif %}
                                        </tr>
                                    {% endfor %}
                                </table>
                            </div>
                        {% else %}
                            <div class="empty-tab order-block">{{ 'invoice.list.tab.empty'|trans([], 'AppBundle') }}</div>
                        {% endif %}
                        <div class="between"></div>
                        <div class="tab_pagination">
                            {{ knp_pagination_render(invoices, '@OpenTicket/pagination/pagination.html.twig') }}
                        </div>
                    </div>
                {% endblock %}
            {% endembed %}
        </div>
    </div>
{% endblock %}
