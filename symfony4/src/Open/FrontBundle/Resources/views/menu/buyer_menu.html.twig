{% extends '@OpenFront/base.html.twig' %}
{% trans_default_domain 'AppBundle' %}

{% set showBuyerMenu = customShowMenu is defined ? customShowMenu : app.session.get(constant('AppBundle\\Controller\\MkoController::SESSION_ACCOUNT_CREATION')) %}
{% set e_catalog_url = getECatalogUrl() %}
{% set current_url = app.request.uri %}

{% block header %}
    {% if showBuyerMenu is defined and showBuyerMenu == true %}
        <div class="breadcrumb-container desktop-only">
            <div class="desktop_breadcrumb">
                <div class="logo--home">
                    <a href="{{ path('homepage') }}">
                        <img src="{{ asset('images/logo.png') }}">
                    </a>
                </div>
                <div class="breadcrumb-part">
                </div>
                <div class="breadcrumb-logout">

                    <a href="{{ path('fos_user_security_logout') }}" class="logout-link">
                        <div class="onboarding-step">
                            {{ 'home.logout'| trans([],'AppBundle') }}
                        </div>
                        <div class="onboarding-step">
                            <svg class="Icon icon-grey">
                                <use xlink:href="#icon-logout"></use>
                            </svg>
                        </div>
                    </a>
                </div>
            </div>
        </div>
        <div class="mobile-only">
            {% include ('@OpenFront/menu/header.html.twig') %}
        </div>
    {% else %}
        {{ parent() }}
    {% endif %}
{% endblock %}

{% block sideMenu %}
    {% if showBuyerMenu is defined and showBuyerMenu != true or showBuyerMenu is not defined %}
        <div class="side-menu-container desktop-only">
            <a href="{{ path('front.orders.list') }}" class="{{ route == 'front.orders.list' or route == 'front.order.detail' or route == 'front.merchantOrder.dispute.create' or route == 'front.merchantOrder.dispute.list' ? 'active' }}">
                <div class="side-menu-item">
                    <div class="item-title">
                        <svg class="Icon stroke">
                            <use xlink:href="#icon-side-orders"></use>
                        </svg>
                        <div class="item-label">
                            {{ 'menu.desktop.orders'|trans }}
                        </div>
                    </div>
                </div>
            </a>
            <a href="{{ path('front.invoice.list') }}" class="{{ route == 'front.invoice.list' or route == 'front.merchantOrder.invoices' ? 'active' }}">
                <div class="side-menu-item">
                    <div class="item-title">
                        <svg class="Icon stroke">
                            <use xlink:href="#icon-side-profile"></use>
                        </svg>
                        <div class="item-label">
                            {{ 'menu.desktop.invoices'|trans }}
                        </div>
                    </div>
                    <i class="arrow down"></i>
                </div>
            </a>
            <a href="{{ path('purchase_request.details') }}" class="{{ route == 'purchase_request.details' ? 'active' }}">
                <div class="side-menu-item">
                    <div class="item-title">
                        <svg class="Icon stroke">
                            <use xlink:href="#icon-side-profile"></use>
                        </svg>
                        <div class="item-label">
                            {{ 'menu.desktop.purchase_request'|trans }}
                        </div>
                    </div>
                    <i class="arrow down"></i>
                </div>
            </a>
            <a href="{{ path('cart.pending.list') }}" class="{{ route == 'cart.pending.list' ? 'active' }}">
                <div class="side-menu-item">
                    <div class="item-title">
                        <svg class="Icon stroke">
                            <use xlink:href="#icon-side-orders"></use>
                        </svg>
                        {% if app.session.get('pendingCart', 0) > 0 %}
                             <div class="counter-bubble-container">
                                <div class="counter-bubble">
                                    {{ app.session.get('pendingCart', 0) }}
                                </div>
                             </div>
                        {% endif %}
                        <div class="item-label">
                            {{ 'menu.desktop.pending_carts'|trans }}
                        </div>
                    </div>
                </div>
            </a>
            <a href="{{ path('front.wishlist') }}" class="{{ route == 'front.wishlist' or route == 'front.wishlist.details' ? 'active' }}">
                <div class="side-menu-item">
                    <div class="item-title">
                        <svg class="Icon stroke">
                            <use xlink:href="#icon-list"></use>
                        </svg>
                        <div class="item-label">
                            {{ 'menu.desktop.wishlist'|trans }}
                        </div>
                    </div>
                </div>
            </a>
            <a href="{{ path('buyer.ticket.list') }}" class="{{ route == 'buyer.ticket.list' or route == 'buyer.ticket.edit' or route == 'buyer.ticket.create' or route == 'izberg.ticket.edit' ? 'active' }}">
                <div class="side-menu-item">
                    <div class="item-title">
                        <svg class="Icon">
                            <use xlink:href="#icon-side-messages"></use>
                        </svg>
                        {% if app.session.get('unreadMessage', 0) > 0 %}
                            <div class="counter-bubble-container">
                                <div class="counter-bubble">
                                    {{ app.session.get('unreadMessage', 0) }}
                                </div>
                            </div>
                        {% endif %}
                        <div class="item-label">
                            {{ 'main_menu.messages'|trans }}
                        </div>
                    </div>
                </div>
            </a>
            <a href="{{ path('front.company.info') }}" class="{{ route == 'front.company.info' ? 'active' }}">
                <div class="side-menu-item">
                    <div class="item-title">
                        <svg class="Icon">
                            <use xlink:href="#icon-side-company"></use>
                        </svg>
                        <div class="item-label">
                            {{ 'menu.desktop.company'|trans }}
                        </div>
                    </div>
                </div>
            </a>
            <a href="{{ path('front.company.sites') }}" class="{{ route == 'front.company.sites' or route == 'front.site.edit' or route == 'front.shipping_point.edit' or route == 'front.shipping_point.create' ? 'active' }}">
                <div class="side-menu-item">
                    <div class="item-title">
                        <svg class="Icon">
                            <use xlink:href="#icon-side-sites"></use>
                        </svg>
                        <div class="item-label">
                            {{ 'menu.desktop.sites'|trans }}
                        </div>
                    </div>
                </div>
            </a>
            <a href="{{ path('dashboard.stats') }}" class="{{ route == 'dashboard.stats' ? 'active' }}">
                <div class="side-menu-item">
                    <div class="item-title">
                        <svg class="Icon stroke">
                            <use xlink:href="#icon-stats"></use>
                        </svg>
                        <div class="item-label">
                            {{ 'menu.desktop.stats'|trans }}
                        </div>
                    </div>
                </div>
            </a>
            <a href="{{ path('front.company.users') }}" class="{{ route == 'front.company.users' or route == 'front.user.new' ? 'active' }}">
                <div class="side-menu-item">
                    <div class="item-title">
                        <svg class="Icon">
                            <use xlink:href="#icon-side-users"></use>
                        </svg>
                        <div class="item-label">
                            {{ 'menu.desktop.users'|trans }}
                        </div>
                    </div>
                </div>
            </a>
            <a href="{{ path('front.company.paymentmodes') }}" class="{{ route == 'front.company.paymentmodes' ? 'active' }}">
                <div class="side-menu-item">
                    <div class="item-title">
                        <svg class="Icon">
                            <use xlink:href="#icon-side-payment"></use>
                        </svg>
                        <div class="item-label">
                            {{ 'menu.desktop.payment_modes'|trans }}
                        </div>
                    </div>
                </div>
            </a>
            <a href="{{ path('front.company.profile') }}" class="{{ route == 'front.company.profile' ? 'active' }}">
                <div class="side-menu-item">
                    <div class="item-title">
                        <svg class="Icon">
                            <use xlink:href="#icon-side-profile"></use>
                        </svg>
                        <div class="item-label">
                            {{ 'menu.desktop.profile'|trans }}
                        </div>
                    </div>
                </div>
            </a>
            {% if e_catalog_url is not null %}
                <a href="{{ e_catalog_url }}" class="{{ current_url == e_catalog_url ? 'active' : '' }}" target="_blank">
                    <div class="side-menu-item">
                        <div class="item-title">
                            <svg class="Icon">
                                <use xlink:href="#icon-catalog"></use>
                            </svg>
                            <div class="item-label">
                                {{ 'main_menu.e_catalog_label'|trans }}
                            </div>
                        </div>
                    </div>
                </a>
            {% endif %}
        </div>

        <div class="side-menu-container mobile-only">
            <a href="{{ route == 'front.orders.list' or route == 'front.order.detail' or route == 'front.merchantOrder.dispute.create' or route == 'front.merchantOrder.dispute.list' or route == 'izberg.ticket.edit' ? '#' : path('front.orders.list') }}" class="{{ route == 'front.orders.list' or route == 'front.order.detail' or route == 'front.merchantOrder.dispute.create' or route == 'front.merchantOrder.dispute.list' or route == 'izberg.ticket.edit' ? 'active' }}">
                <div class="side-menu-item">
                    <div class="item-title">
                        <svg class="Icon stroke">
                            <use xlink:href="#icon-side-orders"></use>
                        </svg>
                        <div class="item-label">
                            {{ 'menu.desktop.orders'|trans }}
                        </div>
                    </div>
                    <i class="arrow down"></i>
                </div>
            </a>
            <a href="{{ route == 'front.invoice.list' or route == 'front.invoice.detail' ? '#' : path('front.invoice.list') }}" class="{{ route == 'front.invoice.list' or route == 'front.invoice.detail' ? 'active' }}">
                <div class="side-menu-item">
                    <div class="item-title">
                        <svg class="Icon stroke">
                            <use xlink:href="#icon-side-profile"></use>
                        </svg>
                        <div class="item-label">
                            {{ 'menu.desktop.invoices'|trans }}
                        </div>
                    </div>
                    <i class="arrow down"></i>
                </div>
            </a>
            <a href="{{ route == 'purchase_request.details' ? '#' : path('purchase_request.details') }}" class="{{ route == 'purchase_request.details' ? 'active' }}">
                <div class="side-menu-item">
                    <div class="item-title">
                        <svg class="Icon stroke">
                            <use xlink:href="#icon-side-profile"></use>
                        </svg>
                        <div class="item-label">
                            {{ 'menu.desktop.purchase_request'|trans }}
                        </div>
                    </div>
                    <i class="arrow down"></i>
                </div>
            </a>
            <a href="{{ route == 'cart.pending.list' ? '#' : path('cart.pending.list') }}" class="{{ route == 'cart.pending.list' ? 'active' }}">
                <div class="side-menu-item">
                    <div class="item-title">
                        <svg class="Icon stroke">
                            <use xlink:href="#icon-side-orders"></use>
                        </svg>
                        <div class="item-label">
                            {{ 'menu.desktop.pending_carts'|trans }}
                        </div>
                    </div>
                    <i class="arrow down"></i>
                </div>
            </a>
            <a href="{{ route == 'front.wishlist' or route == 'front.wishlist.details' ? '#' : path('front.wishlist') }}" class="{{ route == 'front.wishlist' or route == 'front.wishlist.details' ? 'active' }}">
                <div class="side-menu-item">
                    <div class="item-title">
                        <svg class="Icon stroke">
                            <use xlink:href="#icon-list"></use>
                        </svg>
                        <div class="item-label">
                            {{ 'menu.desktop.wishlist'|trans }}
                        </div>
                    </div>
                    <i class="arrow down"></i>
                </div>
            </a>
            <a href="{{ route == 'buyer.ticket.list' ? '#' : path('buyer.ticket.list') }}" class="{{ route == 'buyer.ticket.list' or route == 'buyer.ticket.edit' or route == 'buyer.ticket.create' ? 'active' }}">
                <div class="side-menu-item">
                    <div class="item-title">
                        <svg class="Icon">
                            <use xlink:href="#icon-side-messages"></use>
                        </svg>
                        <div class="item-label">
                            {{ 'main_menu.messages'|trans }}
                        </div>
                    </div>
                    <i class="arrow down"></i>
                </div>
            </a>
            <a href="{{ route == 'front.company.info' ? '#' : path('front.company.info') }}" class="{{ route == 'front.company.info' ? 'active' }}">
                <div class="side-menu-item">
                    <div class="item-title">
                        <svg class="Icon">
                            <use xlink:href="#icon-side-company"></use>
                        </svg>
                        <div class="item-label">
                            {{ 'menu.desktop.company'|trans }}
                        </div>
                    </div>
                    <i class="arrow down"></i>
                </div>
            </a>
            <a href="{{ route == 'front.company.sites' or route == 'front.site.edit' or route == 'front.shipping_point.edit' or route == 'front.shipping_point.create' ? '#' : path('front.company.sites') }}" class="{{ route == 'front.company.sites' or route == 'front.site.edit' or route == 'front.shipping_point.edit' or route == 'front.shipping_point.create' ? 'active' }}">
                <div class="side-menu-item">
                    <div class="item-title">
                        <svg class="Icon">
                            <use xlink:href="#icon-side-sites"></use>
                        </svg>
                        <div class="item-label">
                            {{ 'menu.desktop.sites'|trans }}
                        </div>
                    </div>
                    <i class="arrow down"></i>
                </div>
            </a>
            <a href="{{ route == 'dashboard.stats' ? '#' : path('dashboard.stats') }}" class="{{ route == 'dashboard.stats' ? 'active' }}">
                <div class="side-menu-item">
                    <div class="item-title">
                        <svg class="Icon stroke">
                            <use xlink:href="#icon-stats"></use>
                        </svg>
                        <div class="item-label">
                            {{ 'menu.desktop.stats'|trans }}
                        </div>
                    </div>
                    <i class="arrow down"></i>
                </div>
            </a>
            <a href="{{ route == 'front.company.users' or route == 'front.user.new' ? '#' : path('front.company.users') }}" class="{{ route == 'front.company.users' or route == 'front.user.new' ? 'active' }}">
                <div class="side-menu-item">
                    <div class="item-title">
                        <svg class="Icon">
                            <use xlink:href="#icon-side-users"></use>
                        </svg>
                        <div class="item-label">
                            {{ 'menu.desktop.users'|trans }}
                        </div>
                    </div>
                    <i class="arrow down"></i>
                </div>
            </a>
            <a href="{{ route == 'front.company.paymentmodes' ? '#' : path('front.company.paymentmodes') }}" class="{{ route == 'front.company.paymentmodes' ? 'active' }}">
                <div class="side-menu-item">
                    <div class="item-title">
                        <svg class="Icon">
                            <use xlink:href="#icon-side-payment"></use>
                        </svg>
                        <div class="item-label">
                            {{ 'menu.desktop.payment_modes'|trans }}
                        </div>
                    </div>
                    <i class="arrow down"></i>
                </div>
            </a>
            <a href="{{ route == 'front.company.profile' ? '#' : path('front.company.profile') }}" class="{{ route == 'front.company.profile' ? 'active' }}">
                <div class="side-menu-item">
                    <div class="item-title">
                        <svg class="Icon">
                            <use xlink:href="#icon-side-profile"></use>
                        </svg>
                        <div class="item-label">
                            {{ 'menu.desktop.profile'|trans }}
                        </div>
                    </div>
                    <i class="arrow down"></i>
                </div>
            </a>
        </div>

        <script type="text/javascript">
            'use strict';

            $('.active').click(function() {
                $('.side-menu-container').toggleClass('show-all-menu');
                $(this).children('.side-menu-item').children('.arrow').toggleClass('up');
            });
        </script>
    {% endif %}
{% endblock %}

{% block menu %}
    {% if showBuyerMenu is defined and showBuyerMenu == true %}
        <div class="breadcrumb-container mobile-only">
            <div class="mobile_breadcrumb">
                <div class="breadcrumb-part">
                </div>
            </div>
        </div>
    {% endif %}
{% endblock %}
