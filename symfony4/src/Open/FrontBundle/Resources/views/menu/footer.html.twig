{% trans_default_domain 'AppBundle' %}
<footer class="Footer">
    <div class="background-image desktop-only">
        <div class="background-container">
            <svg>
                <use xlink:href="#footer-background"></use>
            </svg>
        </div>
    </div>
    <div class="content inverse-mobile">
        <div>
            <img class="footer-logo desktop-only" src="{{ asset('images/logo-footer.png') }}">
            <ul class="list-footer">
                    {% include '@OpenFront/component/footer-section.html.twig' with {
                        'id': 'about-us',
                        'title': 'footer.about_us.title'|trans([], 'AppBundle'),
                        'links': [  {'name': 'footer.about_us.company'|trans([], 'AppBundle'), 'URL': '/aboutus'|localize(locale)},
                                    {'name': 'footer.about_us.our_mission'|trans([], 'AppBundle'), 'URL': '/our-mission'|localize(locale)},
                                    {'name': 'footer.about_us.code_of_conduct'|trans([], 'AppBundle'), 'URL': '/code-of-conduct'|localize(locale)},
                                    {'name': 'footer.about_us.legal_notice'|trans([], 'AppBundle'), 'URL': '/cgu'|localize(locale)},
                                    {'name': 'footer.about_us.data_privacy_chart'|trans([], 'AppBundle'), 'URL': '/data-privacy-charter'|localize(locale)},
                                    {'name': 'footer.about_us.cookies'|trans([], 'AppBundle'), 'URL': '/cookies'|localize(locale)},
                        ]
                    } %}
                    {% include '@OpenFront/component/footer-section.html.twig' with {
                        'id': 'buy-on-sation-one',
                        'title': 'footer.buy.title'|trans([], 'AppBundle'),
                        'links': [  {'name': 'footer.buy.create_an_account'|trans([], 'AppBundle'), 'URL': path('fos_user_registration_register')},
                                    {'name': 'footer.buy.benefits_for_buyers'|trans([], 'AppBundle'), 'URL': '/benefits-for-buyers'|localize(locale)},
                                    {'name': 'footer.buy.general_terms_and_conditions'|trans([], 'AppBundle'), 'URL': '/gtu-buyers'|localize(locale)} ] } %}
                    {% include '@OpenFront/component/footer-section.html.twig' with {
                        'id': 'press',
                        'title': 'footer.press.title'|trans([], 'AppBundle'),
                        'links': [
                            {'name': 'footer.press.press_releases'|trans([], 'AppBundle'), 'URL': '/press-releases'|localize(locale)},
                            {'name': 'footer.press.blog'|trans([], 'AppBundle'), 'URL':  'https://blog.station-one.com/'}
                        ] }
                    %}
            </ul>
        </div>
        <div class="h-line desktop-only"></div>
        <div>
            <ul class="list-footer inverse-mobile">

                {% include '@OpenFront/component/footer-section.html.twig' with {
                    'id': 'help',
                    'title': 'footer.help.title'|trans([], 'AppBundle'),
                    'links': [  {'name': 'footer.help.call_us_at'|trans ~' '~'footer.help.phone_number_1'|trans },
                                {'name': 'footer.help.contact_us'|trans([], 'AppBundle'), 'URL': path('anonymous.ticket.create')},
                                {'name': 'footer.help.illegal_content'|trans([], 'AppBundle'), 'URL': path('illegal_content.create')}
                ]
                } %}

                {# FAQ desactivated : {'name': 'footer.help.questions'|trans([], 'AppBundle'), 'URL': '/questions'|localize(locale)}, #}

                <li class="visit-section">
                    <div class="footer-section">
                        <div class="title-container">
                            <h4 class="section-title">{{ 'footer.visit.title'|trans }}</h4>
                        </div>
                        <div class="section-content">
                            <div class="section-column">
                                <div class="footer-link">{{ 'footer.visit.address_1'|trans }}</div>
                                <div class="footer-link">{{ 'footer.visit.address_2'|trans }}</div>
                                <div class="footer-link">{{ 'footer.visit.address_3'|trans }}</div>
                            </div>
                        </div>
                    </div>
                </li>

                <li>
                    <div class="footer-section no-border-mobile">
                        <h4 class="section-title desktop-only">{{  'footer.follow_us.title'|trans([], 'AppBundle') }}</h4>
                        <div class="icons-section">
                            <a href="https://www.linkedin.com/company/station-one-marketplace/" target="_blank"> <svg class="Icon"><use xlink:href="#icon-linkedin"></use></svg></a>
                            <a href="https://twitter.com/station__one" target="_blank"><svg class="Icon"><use xlink:href="#icon-twitter"></use></svg></a>
                        </div>
                    </div>
                </li>
                <li>
                    {#<div class="footer-section newsletter">
                        <h4 class="section-title">{{ 'footer.newsletter.title'|trans }}</h4>
                        <div class="input-email">
                            <input type="text" placeholder="{{ 'footer.newsletter.placeholder'|trans }}"/>
                            <i class="Icon arrow right"></i>
                        </div>
                    </div>#}
                </li>
                <li class="container-logo mobile-only">
                    <img class="footer-logo" src="{{ asset('images/logo-footer.png') }}">
                </li>
            </ul>
        </div>
    </div>
    <div class="additional-content">
        <p>{{ 'footer.additional.copyright'|trans }}</p>
        <p class="company">{{ 'footer.additional.alstom'|trans }}</p>
    </div>
</footer>
