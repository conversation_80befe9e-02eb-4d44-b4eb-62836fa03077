{% set e_catalog_url = getECatalogUrl() %}
<ul class="Header-menuItems mobile-only">
    {% if is_granted("IS_AUTHENTICATED_REMEMBERED") %}
        {% if app.user.company is not null %}
            {% set newMessageRoute = 'buyer.ticket.create' %}
            {# Account dropdown #}
            {% embed('@OpenFront/component/li-dropdown.html.twig') with {
                'extendedClass' : '',
                'idContent' : 'Dropdown-Account-Mobile',
                'hamburger': 'true'
            } %}
                {% block display %}
                    <div class="Menu-item has-icon counter-bubble-container">
                        <a>
                            <span>
                                <svg class="Icon Icon-flag">
                                    <use xlink:href="#icon-user"></use>
                                </svg>
                            </span>
                            <div class="Icon-button-text">
                                <div class="second-level">
                                    {{ 'main_menu.account'| trans([],'AppBundle')|upper }}
                                </div>
                            </div>
                            {% set totalCount = app.session.get('pendingCart', 0) + app.session.get('unreadMessage', 0) %}
                            {% if totalCount > 0 %}
                                <div class="totalCount counter-bubble">
                                    {{ totalCount }}
                                </div>
                            {% endif %}
                        </a>
                        <div class="cross"></div>
                    </div>
                {% endblock %}
                {% block dropdownList %}
                    {% if app.user.company is defined and app.user.company.step == 3 %}
                        <a href="{{ path('front.orders.list') }}">{{ 'menu.desktop.orders'|trans([], 'AppBundle') }}</a>
                        <a href="{{ path('front.invoice.list') }}">{{ 'menu.desktop.invoices'|trans([], 'AppBundle') }}</a>
                        <a href="{{ path('purchase_request.details') }}">{{ 'menu.desktop.purchase_request'|trans([], 'AppBundle') }}</a>
                        <a href="{{ path('cart.pending.list') }}" class="counter-bubble-container">
                            {{ 'menu.desktop.pending_carts'|trans([], 'AppBundle') }}
                            {% if app.session.get('pendingCart', 0) > 0 %}
                                <div class="counter-bubble">
                                    {{ app.session.get('pendingCart', 0) }}
                                </div>
                            {% endif %}
                        </a>
                        <a href="{{ path('front.wishlist') }}">{{ 'menu.desktop.wishlist'|trans([], 'AppBundle') }}</a>
                        <a href="{{ path('front.company_catalog') }}">{{ 'menu.desktop.my_catalog'|trans([], 'AppBundle') }}</a>
                        <a href="{{ path('buyer.ticket.list') }}" class="counter-bubble-container">
                            {{ 'main_menu.messages'| trans([],'AppBundle') }}
                            {% if app.session.get('unreadMessage', 0) > 0 %}
                                <div class="counter-bubble">
                                    {{ app.session.get('unreadMessage', 0) }}
                                </div>
                            {% endif %}
                        </a>
                    {% endif %}

                    {% if app.user.company is defined %}
                        <a href="{{ path('front.company.info') }}">{{ 'menu.desktop.company'|trans([], 'AppBundle') }}</a>
                        <a href="{{ path('front.company.sites') }}"> {{ 'menu.desktop.sites'|trans([], 'AppBundle') }}</a>
                        {% if app.user.company.step == 3 %}
                            <a href="{{ path('dashboard.stats') }}">{{ 'menu.desktop.stats'|trans([], 'AppBundle') }}</a>
                        {% endif %}
                        <a href="{{ path('front.company.users') }}">{{ 'menu.desktop.users'|trans([], 'AppBundle') }}</a>
                        <a href="{{ path('front.company.paymentmodes') }}">{{ 'menu.desktop.payment_modes'|trans([], 'AppBundle') }}</a>
                    {% endif %}

                    <a href="{{ path('front.company.profile') }}">{{ 'menu.desktop.profile'| trans([],'AppBundle') }}</a>

                    {% if not is_granted('ROLE_PREVIOUS_ADMIN') %}
                        <a href="{{ path('fos_user_security_logout') }}">{{ 'home.logout'| trans([],'AppBundle') }}</a>
                    {% endif %}
                    {% if is_granted('ROLE_PREVIOUS_ADMIN') %}
                        <a href="{{ path('admin.admin.list', {'_piggy': '_exit', 'qualifier' : 'all'}) }}">{{ 'back.commons.piggy_exit'| trans([],'AppBundle') }}</a>
                    {% endif %}
                {% endblock %}
            {% endembed %}


        {% else %}
            {% embed('@OpenFront/component/li-dropdown.html.twig') with {
                'extendedClass' : '',
                'idContent' : 'Dropdown-Account-mobile',
                'hamburger' : true
            } %}
                {% block display %}
                    <div class="Menu-item">
                        <a>
                            {{ 'main_menu.account'| trans([],'AppBundle')|upper }}
                        </a>
                    </div>
                {% endblock %}
                {% block dropdownList %}
                    <a href="{{ path('fos_user_security_logout') }}">
                        {{ 'home.logout'| trans([],'AppBundle') }}
                    </a>
                {% endblock %}
            {% endembed %}
        {% endif %}

        {# Not connected #}
    {% else %}
        <li class="dropdown-display">
            <div class="Menu-item has-icon">
                <a href="#" onclick="window.UI.Modal.showLogin()">
                    <span>
                        <svg class="Icon Icon-flag">
                            <use xlink:href="#icon-user"></use>
                        </svg>
                    </span>
                    <div class="Icon-button-text">
                        <div class="second-level">
                            {{ 'main_menu.account'| trans([],'AppBundle')|upper }}
                        </div>
                    </div>
                </a>
            </div>
        </li>
    {% endif %}

    {# RENDER #}
    {{ render(controller("Open\\FrontBundle\\Controller\\CategoriesNavBarController::getMobileAction", {}, {'from':app.request.get('_route')})) }}

    <li id="cart-1" class="dropdown-display Item-cart">
        <div class="Menu-item has-icon">
            <a {% if is_granted("IS_AUTHENTICATED_REMEMBERED") %}
                    href="{{ path('cart.details.before_buy', {'currencyOrCartId' : 'eur'}) }}"
                {% else %}
                    href="#" onclick="window.UI.Modal.showLogin()"
                {% endif %} >
                <span>
                    <svg class="Icon Icon-flag">
                        <use xlink:href="#icon-panier-1"></use>
                    </svg>
                    {% if app.user and app.user.itemInCartEUR > 0 %}
                        <div class="quantity">
                        <p id="cart-quantity-eur">{{ app.user.itemInCartEUR }}</p>
                    </div>
                    {% endif %}
                </span>
                <div class="Icon-button-text">
                    <div class="second-level">
                        {{ 'main_menu.eur_cart'| trans([],'AppBundle') }}
                    </div>
                </div>
            </a>
        </div>
    </li>

    <li id="cart-2" class="dropdown-display Item-cart">
        <div class="Menu-item has-icon">
            <a {% if is_granted("IS_AUTHENTICATED_REMEMBERED") %}
                href="{{ path('cart.details.before_buy', {'currencyOrCartId' : 'usd'}) }}"
            {% else %}
                href="#" onclick="window.UI.Modal.showLogin()"
            {% endif %} >
                <span>
                    <svg class="Icon Icon-flag">
                        <use xlink:href="#icon-panier-2"></use>
                    </svg>
                    {% if app.user and app.user.itemInCartUSD > 0 %}
                        <div class="quantity">
                        <p id="cart-quantity-usd">{{ app.user.itemInCartUSD }}</p>
                    </div>
                    {% endif %}
                </span>

                <div class="Icon-button-text">
                    <div class="second-level">
                        {{ 'main_menu.usd_cart'| trans([],'AppBundle') }}
                    </div>
                </div>
            </a>
        </div>
    </li>
    <div class="simple-links">
        {% if e_catalog_url is not null %}
            <li class="hamburger-link">
                <a class="link" href="{{ e_catalog_url }}" target="_blank">{{ 'main_menu.e_catalog_label'| trans([],'AppBundle') }}</a>
            </li>
        {% endif %}

        <li class="hamburger-link">
            <a class="link" href="{{ path('anonymous.ticket.create') }}">{{ 'main_menu.contact_us'| trans([],'AppBundle') }}</a>
        </li>

        <li class="hamburger-link">
            <a class="link" href="{{ '/howitworks'|localize(locale) }}">{{ 'main_menu.help'| trans([],'AppBundle') }}</a>
        </li>

        <li class="hamburger-link">
            <a class="link" href="{{ '/register'|localize(locale) }}">{{ 'main_menu.join_us'| trans([],'AppBundle') }}</a>
        </li >

        <li class="hamburger-link">
            <a class="link" href="{{ '/gtu-buyers'|localize(locale) }}">{{ 'main_menu.terms'| trans([],'AppBundle') }}</a>
        </li>
    </div>
    {% embed('@OpenFront/component/li-dropdown.html.twig') with {
        'extendedClass' : '',
        'idContent' : 'Dropdown-Flag-Mobile',
        'hamburger': 'true'
    } %}
        {% block display %}
            <div class="Menu-item has-icon">
                <a>
                    <span>
                        <svg class="Icon Icon-flag">
                            <use xlink:href="#icon-flag-{{ current_flag(app.session.get("_locale")) }}"></use>
                        </svg>
                    </span>
                </a>
            </div>
        {% endblock %}
        {% block dropdownList %}
            {% set flags = flags() %}
            {% if flags|length > 1 %}
                {% for flag in flags %}
                    <div class="Menu-item has-icon js-locale-button" data-locale="{{ flag }}">
                        <a>
                            <span>
                                <svg class="Icon Icon-flag">
                                    <use xlink:href="#icon-flag-{{ flag }}"></use>
                                </svg>
                            </span>
                        </a>
                    </div>
                {% endfor %}
            {% endif %}
        {% endblock %}
    {% endembed %}
    {# Categorie dropdown #}
    {# {{ render(controller("Open\\FrontBundle\\Controller\\CategoriesNavBarController::getMobileAction", {}, {'from':app.request.get('_route')})) }}
    <li class="h-line"></li>#}
    {# Terms & Conditions dropdown #}
    {#{% embed('@OpenFront/component/li-dropdown.html.twig') with {
        'extendedClass' : '',
        'idContent' : 'Dropdown-Terms-mobile',
        'hamburger' : true
    } %}
        {% block display %}
            <div class="Menu-item">
                <a>
                    <i class="arrow down"></i>
                    {{ 'main_menu.terms'| trans([],'AppBundle') }}
                </a>
            </div>
        {% endblock %}
        {% block dropdownList %}
        {% endblock %}
    {% endembed %}
    <li class="h-line"></li>#}
    {# Terms & Conditions dropdown #}
    {#{% embed('@OpenFront/component/li-dropdown.html.twig') with {
        'extendedClass' : '',
        'idContent' : 'Dropdown-Language-mobile',
        'hamburger' : true
    } %}
        {% block display %}
            <div class="Menu-item">
                <a>
                    <i class="arrow down"></i>
                    {{ 'main_menu.languages'| trans([],'AppBundle') }}
                </a>
            </div>
        {% endblock %}
        {% block dropdownList %}
            <div class="js-locale-button" data-locale="fr"><a>{{ 'node.form.lang.fr'|trans([],'AppBundle') }}</a></div>
            <div class="js-locale-button" data-locale="en"><a>{{ 'node.form.lang.en'|trans([],'AppBundle') }}</a></div>
            <div class="js-locale-button" data-locale="es"><a>{{ 'node.form.lang.es'|trans([],'AppBundle') }}</a></div>

        {% endblock %}
    {% endembed %}#}
</ul>
