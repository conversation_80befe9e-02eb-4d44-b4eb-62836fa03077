<ul class="Header-menuItems desktop-only">

    {% if is_granted("IS_AUTHENTICATED_REMEMBERED") %}
        {% if app.user.company is not null %}
            {% set newMessageRoute = 'buyer.ticket.create' %}
            {#<li class="Menu-item {% if route == 'buyer.ticket.list' %}is-active{% endif %}">
                <a href="{{ path('buyer.ticket.list') }}">{{ 'main_menu.messages'| trans([],'AppBundle') }}</a>
            </li>#}
            {#--------- TICKETS ----------#}
            {#<li class="Menu-item has-icon">
                {% include('@OpenFront/component/link-icon.html.twig')
                    with {'link': path('buyer.ticket.list'),
                    'refIcon': '#icon-envelop',
                    'label': 'main_menu.messages'| trans([],'AppBundle')} only%}
            </li>#}

            {#--------- ACCOUNT ----------#}
            {% embed('@OpenFront/component/li-dropdown.html.twig') with {
                'extendedClass' : '',
                'idContent' : 'Dropdown-Account',
                'notMouseLeave' : true
            } %}
                {% block display %}
                    <div class="Menu-item has-icon counter-bubble-container">
                        <a>
                            <span>
                                <svg class="Icon Icon-flag">
                                    <use xlink:href="#icon-user"></use>
                                </svg>
                            </span>
                            <div class="Icon-button-text ">
                                <div class="second-level">
                                    {{ app.user.company.name }}
                                    <br>
                                    {{ app.user.firstname }} {{ app.user.lastname }}
                                </div>
                            </div>
                            {% set totalCount = app.session.get('pendingCart', 0) + app.session.get('unreadMessage', 0) %}
                            {% if totalCount > 0 %}
                                <div class="totalCount counter-bubble">
                                    {{ totalCount }}
                                </div>
                            {% endif %}
                        </a>
                    </div>
                {% endblock %}
                {% block dropdownList %}
                    {% if app.user.company is defined and app.user.company.step == 3 %}
                        <a href="{{ path('front.orders.list') }}">{{ 'menu.desktop.orders'|trans([], 'AppBundle') }}</a>
                        <a href="{{ path('front.invoice.list') }}">{{ 'menu.desktop.invoices'|trans([], 'AppBundle') }}</a>
                        <a href="{{ path('purchase_request.details') }}">{{ 'menu.desktop.purchase_request'|trans([], 'AppBundle') }}</a>
                        <a href="{{ path('cart.pending.list') }}" class="counter-bubble-container">{{ 'menu.desktop.pending_carts'|trans([], 'AppBundle') }}
                            {% if app.session.get('pendingCart', 0) > 0 %}
                                <div class="counter-bubble">
                                    {{ app.session.get('pendingCart', 0) }}
                                </div>
                            {% endif %}
                        </a>
                        <a href="{{ path('front.wishlist') }}">{{ 'menu.desktop.wishlist'|trans([], 'AppBundle') }}</a>
                        <a href="{{ path('front.company_catalog') }}">{{ 'menu.desktop.my_catalog'|trans([], 'AppBundle') }}</a>
                        <a href="{{ path('buyer.ticket.list') }}" class="counter-bubble-container">{{ 'main_menu.messages'| trans([],'AppBundle') }}
                            {% if app.session.get('unreadMessage', 0) > 0 %}
                                <div class="counter-bubble">
                                    {{ app.session.get('unreadMessage', 0) }}
                                </div>
                            {% endif %}
                        </a>
                    {% endif %}

                    {% if app.user.company is defined %}
                        <a href="{{ path('front.company.info') }}">{{ 'menu.desktop.company'|trans([], 'AppBundle') }}</a>
                        <a href="{{ path('front.company.sites') }}"> {{ 'menu.desktop.sites'|trans([], 'AppBundle') }}</a>
                        {% if app.user.company.step == 3 %}
                            <a href="{{ path('dashboard.stats') }}">{{ 'menu.desktop.stats'|trans([], 'AppBundle') }}</a>
                        {% endif %}
                        <a href="{{ path('front.company.users') }}">{{ 'menu.desktop.users'|trans([], 'AppBundle') }}</a>
                        <a href="{{ path('front.company.paymentmodes') }}">{{ 'menu.desktop.payment_modes'|trans([], 'AppBundle') }}</a>
                    {% endif %}

                    <a href="{{ path('front.company.profile') }}">{{ 'menu.desktop.profile'| trans([],'AppBundle') }}</a>
                    <a href="{{ path('api_doc') }}" target="_blank">{{ 'menu.desktop.api_doc'| trans([],'AppBundle') }}</a>

                    {% if not is_granted('ROLE_PREVIOUS_ADMIN') %}
                        <a class="Menu-item has-icon">
                            {% include('@OpenFront/component/link-icon.html.twig')
                                with {'link': path('fos_user_security_logout'),
                                'refIcon': '#icon-logout',
                                'label': 'home.logout'| trans([],'AppBundle')} only%}
                        </a>
                    {% endif %}
                    {% if is_granted('ROLE_PREVIOUS_ADMIN') %}
                        <a class="Menu-item has-icon">
                            {% include('@OpenFront/component/link-icon.html.twig')
                                with {'link': path('admin.admin.list', {'_piggy': '_exit', 'qualifier' : 'all'}),
                                'refIcon': '#icon-user',
                                'label': 'back.commons.piggy_exit'| trans([],'AppBundle')} only%}
                        </a>
                    {% endif %}
                {% endblock %}
            {% endembed %}
        {% else %}

            {% embed('@OpenFront/component/li-dropdown.html.twig') with {
                'extendedClass' : '',
                'idContent' : 'Dropdown-Account'
            } %}
                {% block display %}
                    <div class="Menu-item has-icon">
                        <a>
                                <span>
                                    <svg class="Icon Icon-flag">
                                        <use xlink:href="#icon-user"></use>
                                    </svg>
                                </span>
                            {{ 'main_menu.account'| trans([],'AppBundle') }}
                        </a>
                    </div>
                {% endblock %}
                {% block dropdownList %}
                    <div class="Menu-item has-icon">
                        {% include('@OpenFront/component/link-icon.html.twig')
                            with {'link': path('fos_user_security_logout'),
                            'refIcon': '#icon-logout',
                            'label': 'home.logout'| trans([],'AppBundle')} only%}
                    </div>
                {% endblock %}
            {% endembed %}
        {% endif %}

        {# Not connected #}
    {% else %}
        {#--------- LOGIN ----------#}
        <li class="Menu-item has-icon">
            <div class="Menu-item has-icon">
                <a href="#" onclick="window.UI.Modal.showLogin()">
                                <span>
                                    <svg class="Icon Icon-flag">
                                        <use xlink:href="#icon-user"></use>
                                    </svg>
                                </span>
                    <div class="Icon-button-text">
                        <div class="first-level">
                            {{ 'main_menu.signin'| trans([],'AppBundle') }}
                        </div>
                        <div class="second-level">
                            {{ 'main_menu.account'| trans([],'AppBundle') }}
                        </div>
                    </div>
                </a>
            </div>
        </li>
    {% endif %}

    <li id="cart-1" class="Menu-item has-icon Item-cart">
        <div class="Menu-item has-icon">
            <a  {% if is_granted("IS_AUTHENTICATED_REMEMBERED") %}
                    href="{{ path('cart.details.before_buy', {'currencyOrCartId' : 'eur'}) }}"
                {% else %}
                    href="#" onclick="window.UI.Modal.showLogin()"
                {% endif %}
            >
                <span>
                    <svg class="Icon Icon-flag">
                        <use xlink:href="#icon-panier-1"></use>
                    </svg>
                </span>
            </a>
        </div>
        {% if app.user and app.user.itemInCartEUR > 0 %}
            <div class="quantity">
                <p id="cart-quantity-eur">{{ app.user.itemInCartEUR }}</p>
            </div>
        {% else %}
            <div class="quantity hide">
                <p id="cart-quantity-eur"></p>
            </div>
        {% endif %}
    </li>

    <li id="cart-2" class="Menu-item has-icon Item-cart">
        <div class="Menu-item has-icon">
            <a {% if is_granted("IS_AUTHENTICATED_REMEMBERED") %}
                href="{{ path('cart.details.before_buy', {'currencyOrCartId' : 'usd'}) }}"
            {% else %}
                href="#" onclick="window.UI.Modal.showLogin()"
            {% endif %} >
                <span>
                    <svg class="Icon Icon-flag">
                        <use xlink:href="#icon-panier-2"></use>
                    </svg>
                </span>
            </a>
        </div>
        {% if app.user and app.user.itemInCartUSD > 0 %}
            <div class="quantity">
                <p id="cart-quantity-usd">{{ app.user.itemInCartUSD }}</p>
            </div>
        {% else %}
            <div class="quantity hide">
                <p id="cart-quantity-usd"></p>
            </div>
        {% endif %}
    </li>
</ul>
