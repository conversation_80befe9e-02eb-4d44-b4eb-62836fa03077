{% set e_catalog_url = getECatalogUrl() %}
<div id="Header-navigation">
    <ul class="Header-navItems" onmouseleave="window.UI.Dropdown.hideDropdownContainer()">
        {% set activeDepartment = app.session.get(constant('AppBundle\\Controller\\MkoController::SESSION_DEPARTMENT_SEARCH')) %}

        {% for category in categories %}
            {% if  category.id not in ignored_categories %}

                {% if firstLevelCategoryOnly() %}
                    {# FIRST LEVEL#}
                    <div class="categories">
                        <a href="{{ path('front.search', {'category': category.id}) }}" class="Nav-item{{ activeDepartment == category.name and from == 'front.search' ? ' active' }}">
                            {{ category.name }}
                        </a>
                    </div>
                {% else %}
                    {# OTHER LEVELS TEMPORARY HIDDEN #}
                    {% embed ('@OpenFront/component/li-dropdown.html.twig') with {
                        'extendedClass': 'categories',
                        'idContent': 'Navbar-Category-'~loop.index,
                        'dropdownCache': true
                    } %}
                        {% block display %}
                            <a href="{{ path('front.search', {'category': category.id}) }}" class="Nav-item{{ activeDepartment == category.name and from == 'front.search' ? ' active' }}">
                                {{ category.name }}
                            </a>
                        {% endblock %}
                        {% block dropdownList %}
                            <div class="Menu-item">
                                {% include('@OpenFront/component/navbar-category.html.twig') with {'category': category} %}
                            </div>
                        {% endblock %}
                    {% endembed %}
                {% endif %}
            {% endif %}
        {% endfor %}
    </ul>
    <div class="right-content">
        {% if e_catalog_url is not null %}
        <div class="Menu-Item">
            <a href="{{ e_catalog_url }}" target="_blank">{{ 'main_menu.e_catalog_label'|trans([],'AppBundle') }}</a>
        </div>
        {% endif %}
        <div class="Menu-Item">
            <a href="{{ path('anonymous.ticket.create') }}">{{ 'main_menu.contact_us'|trans([],'AppBundle') }}</a>
        </div>

<!--        <div class="Menu-Item">
            <a href="{{ '/howitworks'|localize(locale) }}">{{ 'main_menu.help'|trans([],'AppBundle') }}</a>
        </div>-->

        {% if not is_granted('IS_AUTHENTICATED_REMEMBERED') %}
            <div class="Menu-Item">
                <a  class="color-primary"
                    href="{{ path('fos_user_registration_register') }}">{{ 'main_menu.join_us'|trans([],'AppBundle') }}</a>
            </div>
        {% endif %}

        {% embed('@OpenFront/component/li-dropdown.html.twig') with {
            'extendedClass' : '',
            'idContent' : 'Dropdown-Lang',
            'notMouseLeave' : true}
        %}
            {% block display %}
                <div class="Menu-item has-icon">
                    <a>
                        <span>
                            <svg class="Icon Icon-flag">
                                <use xlink:href="#icon-flag-{{ current_flag(app.session.get("_locale")) }}"></use>
                            </svg>
                        </span>
                        <i class="arrow down"></i>
                    </a>
                </div>
            {% endblock %}
            {% block dropdownList %}
                {% for flag in flags() %}
                    <div class="Menu-item has-icon js-locale-button" data-locale="{{ flag }}">
                        {% include('@OpenFront/component/link-icon.html.twig')
                            with {'link': '#', 'refIcon': '#icon-flag-' ~ flag, 'label': '' } only%}
                    </div>
                {% endfor %}
            {% endblock %}
        {% endembed %}
    </div>
</div>
