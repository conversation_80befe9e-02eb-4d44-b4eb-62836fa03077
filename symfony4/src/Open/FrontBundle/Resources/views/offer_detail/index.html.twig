{% extends '@OpenFront/base.html.twig' %}
{% form_theme contactMerchantForm _self %}
{% trans_default_domain 'AppBundle' %}

{% block title %}{{ 'offer_detail.title'|trans }}{% endblock %}

{% block meta_description %}{{ 'offer_detail.description'|trans }}{% endblock %}

{% block header_js %}
    <script src="{{ asset('js/jquery.sliderPro.min.js') }}"></script>
{% endblock %}

{% block body %}
    <div class="offer-container">
        <div class="breadcrumb" style="padding-top: 10px">
            {% for categoryId,cateogryName in breadcrumb %}
                <a style="color: #9600FF;" href="{{ path('front.search', {'category': categoryId}) }}">{{ cateogryName }}</a>
                {% if not loop.last %}
                    <i class="arrow right"></i>
                {% endif %}
            {% endfor %}
        </div>
        <div class="back-to-search">
            {% if previousUrl is defined and previousUrl is not empty %}
                <a href="{{ path('front.search', previousUrl) }}">
                    <i class="arrow left"></i>
                    <div>{{ 'offer_detail.back_to_search'|trans }}</div>
                </a>
            {% endif %}
        </div>
        <div class="Offer-detail-product">
            <div class="Offer-image" style="display: flex;flex-direction: column;justify-content: space-between">
                <div>
                    <div class="Offer-image-big" style="background-image:url(' {{ asset ( offer.offer.offerPictures[0] ) }} ');"></div>

                    <div class="small-pic-container">
                        {% for picture in offer.offer.offerPictures|slice(0, 3) %}
                            <div class="Offer-image-small{{ loop.index0 == 0 ? ' selected'}}" image-src="url(' {{ asset ( picture ) }} ')" style="background-image:url(' {{ asset ( picture ) }} ');"></div>
                        {% endfor %}
                    </div>
                </div>

                {% if is_granted('IS_AUTHENTICATED_REMEMBERED') and not is_granted('ROLE_OPERATOR') and not is_granted('ROLE_SUPER_ADMIN') %}
                    <div class="my-2">
                            <span class="text-dark font-weight-bold">
                                {{ 'offer_detail.ask_title'|trans }}
                            </span>
                        <div class="mt-1">
                            <button style="
                                            font-size: 14px;
                                            letter-spacing: normal;
                                            padding-left: 28px;
                                        " class="btn btn-outline-primary font-weight-bold" type="button" id="js-contact-merchant">
                                <img style="
                                            position: absolute;
                                            top: 50%;
                                            margin-top: -6px;
                                            left: 10px;
                                        " src="{{ asset('images/contact_icon.png') }}" alt="contact">&nbsp;
                                {{ 'offer_detail.ask_question'|trans }}
                            </button>
                        </div>
                    </div>
                {% endif %}
            </div>
            <div class="Offer-image-print">
                <img class="sp-image" src="{{ asset ( offer.offer.offerPictures[0] ) }}"/>
            </div>
            <div class="Offer-image-mobile">
                <div class="Offer-detail-slider slider-pro" id="js-offer-slider-mobile">
                    <div class="sp-slides">
                        {% for picture in offer.offer.offerPictures|slice(0, 3) %}
                            <div class="sp-slide">
                                <img class="sp-image" src="{{ asset ( picture ) }}"/>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <div class="Offer-data">
                <div class="Offer-data-body">
                    <div class="Offer-data-mycatalog-ref">
                        {% if myCatalogRef is not empty %}
                            {{ myCatalogRef }}
                            <a id="edit-icon-catalog-reference" class="js-onClickAddReferenceToMyCatalog">
                                <svg class="Icon stroke">
                                    <use xlink:href="#icon-site-edit"></use>
                                </svg>
                            </a>
                        {% else %}
                            {% if myCatalogReferenceForm is not empty %}
                                <a href="#" class="js-onClickAddReferenceToMyCatalog">{{ 'company_catalog.add_in_catalog'|trans }}</a>
                            {% endif %}
                        {% endif %}
                    </div>
                    <h1>
                        {{ offer.offer.offerTitle }}
                    </h1>

                    <div class="Offer-data-details">
                        <div>
                            {{ 'product.seller'|trans }} :
                            <span class="details-value">
                                <a href="{{ path('front.search', {'commonFacetFilters[merchant:name][]': offer.offer.merchant.name, sortBy: 'relevance', 'searchType':'search.searchbar.in_marketplace'}) }}">
                                    {{ offer.offer.merchant.name }}
                                </a>
                            </span>
                            {% if offer.offer.sellerRef is not empty %}
                                <span class="ref">(Ref: {{ offer.offer.sellerRef }})</span>
                            {% endif %}
                        </div>
                        <div>
                            {{ 'product.manufacturer'|trans }} : <span class="details-value">{{ offer.offer.manufacturerName }}</span>
                            {% if offer.offer.manufacturerRef is not empty %}
                                <span class="ref">(Ref: {{ offer.offer.manufacturerRef }})</span>
                            {% endif %}
                        </div>

                    </div>

                    <div class="Offer-data-stock{{ offer.offer.stockAvailability is defined and offer.offer.stockAvailability and  offer.offer.stockAvailability == 'on_stock' ? ' in-stock'}}">
                        <div style="display: flex;justify-content: space-between;">
                            {% if offer.offer.stockAvailability is defined and offer.offer.stockAvailability is not empty%}
                                <div class="stock-block">
                                    <div class="info-stock"></div>{{ ("offer_detail." ~ offer.offer.stockAvailability)|trans }}
                                </div>
                            {% endif %}
                            {% if is_granted("IS_AUTHENTICATED_REMEMBERED") and offer.offer.deliveryTime is not empty  %}
                                {% if not offer.offer.limited %}
                                    <div class="info-delivery">
                                        {{ 'product.delivery_time'|trans }} : <span class="details-value">{{ offer.offer.deliveryTime }} {{ 'site.form.days'|trans|lower }}</span>
                                    </div>
                                {% endif %}
                            {% endif %}
                        </div>
                        <div class="Offer-data-details">
                            {% if is_granted("IS_AUTHENTICATED_REMEMBERED") and offer.offer.stockClearance and offer.offer.stockAvailability is defined and offer.offer.stockAvailability is not empty%}
                                <div class="Offer-data-quantity">
                                    {{ 'product.max_quantity'|trans }} : <span class="details-value">{{ offer.offer.quantity }}</span>
                                </div>
                            {% endif %}
                        </div>

                    </div>

                    <div class="Offer-data-description">
                        <p id="short-desc">{{ offer.offer.shortDescription|shortDescription(120) |raw }}</p>
                        {% if offer.offer.shortDescription|length > 120 %}
                            <p id="long-desc" style="display:none">{{ offer.offer.shortDescription|shortDescription|raw }}</p>
                            <a href="#" id="see-more">{{ 'offer_detail.see_more'|trans }}</a>
                            <a href="#" id="see-less" style="display:none">{{ 'offer_detail.see_less'|trans }}</a>
                        {% endif %}
                    </div>
                </div>

                <br>
                <div class="Offer-data-seller">
                    <div class="Offer-data-seller-ca">
                        {% if offer.offer.warrantyPeriod is not empty and is_granted("IS_AUTHENTICATED_REMEMBERED") %}
                            <span style="color: #9600ff" class="font-weight-bold">{{ 'offer_detail.warranty_period'|trans({'%month%': offer.offer.warrantyPeriod}) }}</span>
                        {% endif %}
                    </div>

                    {% if offer.offer.cgv is defined and is_granted("IS_AUTHENTICATED_REMEMBERED") %}
                        <div class=Offer-data-seller-cgv">
                            {% if not offer.offer.limited and not offer.offer.getFrameContract() %}
                                <a href="{{ offer.offer.cgv }}" target="_blank" class="link-product-seller text-underline" style="color:#6B6F82;">{{ 'tab_infos_seller.cgv'|trans }}</a>
                            {% endif %}
                            {% if offer.offer.getFrameContract() %}
                                <br>{{ 'tab_infos_seller.frame_contract'|trans({'%frame_contract%': offer.offer.getFrameContract() }) }}
                            {% endif %}
                        </div>
                    {% endif %}

                </div>

                <div class="Offer-data-footer" style="border-top:1px solid #E4E5EC">
                    <div class="d-flex" style="
                        width: 100%;
                        padding: 15px 0;
                        justify-content: space-between;
                    ">
                        {% if is_granted("IS_AUTHENTICATED_REMEMBERED") and not offer.offer.limited %}
                            <div class="compare mr-2">
                                <a href="{{ path('front.offer.proforma', {'offerRef': offer.offer.izbergReference}) }}" target="_blank" id="proforma-send">
                                    <svg class="Icon">
                                        <use xlink:href="#icon-comparison"></use>
                                    </svg>
                                    {{ 'offer_detail.proforma'|trans }}
                                </a>
                            </div>
                        {% endif %}
                        <div class="compare mr-2">
                            {% if is_granted("IS_AUTHENTICATED_REMEMBERED") and not offer.offer.limited%}
                                <a href="{{ path('comparisonSheet.add', {'offerId' : offer.offer.izbergReference, 'currency' : offer.offer.currency})  }}">
                                    <svg class="Icon">
                                        <use xlink:href="#icon-comparison"></use>
                                    </svg>
                                    {{ 'product.comparison'|trans }}
                                </a>
                            {% endif %}
                        </div>
                        {% if is_granted("IS_AUTHENTICATED_REMEMBERED") and not offer.offer.limited %}
                            <div class="wishlist-link">
                                <svg class="Icon">
                                    <use xlink:href="#icon-star"></use>
                                </svg>
                                <a href="#" id="js-showWishlistForm">{{ 'offer_detail.add_to_wishlist'|trans }} </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% set locale = app.session.get("_locale") %}
            <div id="locale" style="display:none">{{ locale }}</div>
            {% if is_granted("IS_AUTHENTICATED_REMEMBERED") %}
                {# if offer is not active #}
                {% if not offer.offer.active %}
                    <div class="offer-notice">
                        <p>{{ 'offer_detail.expired_offer'|trans }}</p>
                    </div>
                {# if offer has no stock #}
                {% elseif not offer.offer.hasStock %}
                    <div class="offer-notice">
                        <p>{{ 'offer_detail.out_of_stock_description'|trans }}</p>
                    </div>
                {# if incoterm is dap and country of delivery different than buyer country #}
                {% elseif not offer.offer.incotermValid %}
                    <div class="offer-notice">
                        <p>{{ 'offer_detail.not_available_country'|trans }}</p>
                    </div>
                {% elseif not offer.offer.businessEverywhere %}
                    <div class="offer-notice">
                        <p>{{ 'offer_detail.not_business_everywhere'|trans }}</p>
                    </div>
                {% endif %}
            {% endif %}
            <div class="{{  not offer.offer.limited ? 'Offer-price-cart' : 'anonymous-display' }}">
                 {% if not offer.offer.limited %}
                    {% if offer.offer.quantityPerSku and offer.offer.skuUnit %}
                        <div class="Offer-price-cart-unit">{{ 'offer_detail.price_quantity'|trans({'%quantity%': offer.offer.quantityPerSku, '%unit%': offer.offer.skuUnit }) }}</div>
                    {% endif %}

                    <div class="Offer-price-cart-unit-price">
                        <div id="unit-price">
                            <div id="price-val" data-offer-price-for-current-currency="{{ offer.offer.prices[offer.offer.currency] }}">
                                {% if locale == "en" %}
                                    {{ offer.offer.prices[offer.offer.currency]|number_format('2', '.', ',') }}
                                {% else %}
                                    {{ offer.offer.prices[offer.offer.currency]|number_format('2', ',', ' ')  }}
                                {% endif %}
                            </div>
                            {% if offer.offer.currency == 'EUR' %} € {%  elseif offer.offer.currency == 'USD' %} $ {% endif %}
                        </div>
                    </div>


                     <div class="Offer-price-cart-incoterm">
                         {{ offer.offer.incoterm }} {% if offer.offer.incotermCountry %}({{ offer.offer.incotermCountry }}) {% endif %}
                         {% if offer.offer.shippable %}
                             <img style="width:30px" src="{{ asset('/images/Truck.svg') }}" />
                         {% endif %}
                     </div>

                     {% if offer.offer.getFrameContract() %}
                         <div style="padding: 15px 0">
                             <img style="width:20px" src="{{ asset('images/sign.svg') }}" > {{ 'invoice.detail.frame_contract'|trans }} : <br>{{ offer.offer.getFrameContract() }}
                         </div>
                     {% endif %}

                     {% if offer.offer.priceValidityDate %}
                         <div style="padding-bottom: 15px">
                             {{ "offer_detail.frame_contract_valid_date"|trans }} {{ offer.offer.priceValidityDate | format_datetime('short', 'none', locale=locale)}}
                         </div>
                     {% endif %}


                    {% if offer.offer.thresholds is defined and offer.offer.thresholds|length > 0 %}
                        <div class="Offer-price-cart-discount-info">
                            <div class="discount-title">
                                {{ 'offer_detail.buy_more_for_discount'|trans }}
                                <div id="discount-plus"></div>
                                <div id="discount-minus" style="display: none"></div>
                            </div>
                            <div class="discount-lines" style="display: none">
                                {% set firstThreshold = true %}
                                {% for key,value in offer.offer.thresholds %}

                                    {% if firstThreshold %}
                                        <div class="line">
                                            <div>{{ 'offer_detail.quantity'|trans }} < <div class="line-key"> {{ key }}</div></div>
                                            <div>
                                                <div class="line-value" data-threshold-value="{{ offer.offer.prices[offer.offer.currency] }}">
                                                    {% if locale == "en" %}
                                                        {{ offer.offer.prices[offer.offer.currency]|number_format('2', '.', ',') }}
                                                    {% else %}
                                                        {{ offer.offer.prices[offer.offer.currency]|number_format('2', ',', ' ')  }}
                                                    {% endif %}
                                                </div>
                                                {% if offer.offer.currency == 'EUR' %} € {%  elseif offer.offer.currency == 'USD' %} $ {% endif %}
                                            </div>
                                        </div>
                                    {% endif %}

                                    <div class="line">
                                        <div>{{ 'offer_detail.quantity'|trans }} >= <div class="line-key"> {{ key }}</div></div>
                                        <div>
                                            <div class="line-value" data-threshold-value="{{ value }}">
                                                {% if locale == "en" %}
                                                    {{ value|number_format('2', '.', ',') }}
                                                {% else %}
                                                    {{ value|number_format('2', ',', ' ') }}
                                                {% endif %}
                                            </div>
                                            {% if offer.offer.currency == 'EUR' %} € {%  elseif offer.offer.currency == 'USD' %} $ {% endif %}
                                        </div>
                                    </div>
                                    {% set firstThreshold = false %}
                                {%  endfor %}
                            </div>
                        </div>
                    {% endif %}
                {% endif %}

                <div class="Offer-price-cart-quantity-form">
                    <div>
                        {% if not offer.offer.limited %}
                            <div id="moq" style="display: none">{{ offer.offer.moq }}</div>
                            <label>{{ 'offer_detail.quantity'|trans }}
                                {% if not offer.offer.limited %}
                                    <input type="number" id="quantity" name="Quantity" min="{{ offer.offer.moq > 0 ? offer.offer.moq : 1 }}" max="{{ offer.offer.quantity }}" placeholder="Min {{ offer.offer.moq > 0 ? offer.offer.moq : '1'}}"/>
                                {% else %}
                                    <input type="number" id="quantity" name="Quantity" disabled/>
                                {% endif %}
                                <div class="spin">
                                    <div class="arrow" id="js-onClickQuantityChange-add"><i class="arrow up pointer" ></i> </div>
                                    <div class="arrow" id="js-onClickQuantityChange-sub"><i class="arrow down pointer" ></i> </div>
                                </div>
                            </label>

                            <div class="total-price">
                                {{ 'offer_detail.total_price'|trans }}  :
                                <div class="price">
                                    <div id="price-calc">0.00</div>
                                    {% if offer.offer.currency == 'EUR' %} € {%  elseif offer.offer.currency == 'USD' %} $ {% endif %}
                                </div>
                            </div>
                        {% endif %}
                    </div>

                    <div class="align-button-tablet">
                        {% if is_granted("IS_AUTHENTICATED_REMEMBERED") %}
                            {% if offer.offer.active and offer.offer.incotermValid %}
                                {% if not offer.offer.limited %}
                                    <button id="js-onClickAdd">{{ 'offer_detail.add_to_cart'|trans }} </button>
                                {% elseif offer.offer.noPrice and offer.offer.businessEverywhere %}
                                    {% include '@OpenFront/shared/btn_ask_vendor_price.html.twig' %}
                                {% else %}
                                    {% if offer.offer.bafv and offer.offer.businessEverywhere %}
                                        {% include '@OpenFront/shared/btn_ask_vendor_price.html.twig' %}
                                    {% endif %}
                                {% endif %}
                            {% endif %}
                        {% else %}
                            <button class="btn-primary" onclick="showLogin('js-cart-login-modal')">{{ 'offer_detail.sign_in_to_buy'|trans }}</button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="Offer-detail-info">
        <div class="offer-container">
            {% include ('@OpenFront/component/tab-infos.html.twig')
                with {'attributes': informationTab,'dataSheet': offer.offer.dataSheet} %}
        </div>
    </div>
    <div class="Offer-detail-products">
        <!-- products you may like -->
        {% if offer.associatedProducts is not empty  %}
            <div class="Offer-detail-products-list offer-container">
                {% include '@OpenFront/shared/_product_list.html.twig' with { title : 'offer_detail.similar_products' , products : offer.associatedProducts, id : 'similar' } %}
            </div>
        {% endif %}

        <!-- Related Products -->
        {% if offer.associatedServices  is not empty %}
            <div class="Offer-detail-products-list offer-container related-products">
                {% include '@OpenFront/shared/_product_list.html.twig' with { title : 'offer_detail.related_products' , products : offer.associatedServices, id : 'related' } %}
            </div>
        {% endif %}
    </div>

{% endblock %}

{% block javascripts %}
    <script>

        /**
         * Files upload for messages
         **/
        const fileChooser = {
            addFileBtnMin: null,
            addFileBtnMinTooltip: null,
            inputFile: null,
            addFileBtn: null,
            addFileBtnMobile: null,
            chosenFiles: null,
            chosenFilesMessage: null,
            fileSizeValidationMessage: null,

            newInput: "",
            files: [],
            counter: 0,

            inputClick: function () {
                fileChooser.inputFile.click();
            },

            displayFileSizeValidationMessage: function (visible) {
                if (fileChooser.fileSizeValidationMessage !== null) {
                    if (visible === true) {
                        fileChooser.fileSizeValidationMessage.style.visibility = 'visible'
                    } else {
                        fileChooser.fileSizeValidationMessage.style.visibility = 'hidden'
                    }
                }
            },

            changeEvent: function (event) {
                fileChooser.displayFileSizeValidationMessage(false);

                var filesToUpload = event.currentTarget.files;
                var fileToUpload = filesToUpload[0];
                var filesize = ((fileToUpload.size / 1024) / 1024).toFixed(4);

                if (filesize > 5) {
                    fileChooser.displayFileSizeValidationMessage(true);
                    return;
                }


                if (fileChooser.inputFile.value) {
                    const file = fileChooser.inputFile.value.match(/[\/\\]([\w\d\s\.\-\(\)]+)$/)[1];

                    fileChooser.files.push(file);

                    let newEl = document.createElement('section');
                    newEl.innerHTML = ('<span>' + file + '</span><img class="remove-file" onclick="fileChooser.removeFile(event)" src="/images/ico-delete2.svg" data-content="file-input-' + fileChooser.counter + '">');

                    fileChooser.chosenFiles.insertBefore(newEl, fileChooser.addFileBtnMin);

                    let oldInput = event.target;
                    oldInput.removeEventListener("change", fileChooser.changeEvent);

                    fileChooser.counter += 1;
                    fileChooser.inputFile = fileChooser.inputFile.cloneNode();
                    fileChooser.inputFile.value = null;
                    fileChooser.inputFile.id = "file-input-" + fileChooser.counter;

                    let previous = document.getElementById('add-file-min');

                    previous.parentNode.insertBefore(fileChooser.inputFile, previous.nextSibling);

                    fileChooser.inputFile.addEventListener("change", fileChooser.changeEvent);
                    fileChooser.addFileBtnMin.removeEventListener("click", fileChooser.inputClick);
                    if (fileChooser.addFileBtn) {
                        fileChooser.addFileBtn.addEventListener("click", fileChooser.inputClick);
                    }

                    if (fileChooser.addFileBtnMobile) {
                        fileChooser.addFileBtnMobile.addEventListener("click", fileChooser.inputClick);
                    }

                    if (fileChooser.addFileBtnMin) {
                        fileChooser.addFileBtnMin.addEventListener("click", fileChooser.inputClick);
                    }
                    fileChooser.checkFilesList();

                    if (fileChooser.files.length >= 3) {
                        fileChooser.addFileBtnMin.style.visibility = 'hidden';
                    }
                } else {
                    fileChooser.chosenFiles.innerHTML = "No file chosen, yet.";
                }
            },

            arrayRemove: function (arr, value) {
                let index = arr.indexOf(value);
                if (index !== -1) arr.splice(index, 1);
            },

            removeFile: function (e) {
                const f = e.target.parentElement.textContent;
                e.target.parentElement.remove();
                let id = e.target.dataset.content;

                fileChooser.arrayRemove(fileChooser.files, f);

                let domEl = document.getElementById(id);
                if (domEl) {
                    domEl.parentNode.removeChild(domEl);
                }

                fileChooser.checkFilesList();

                fileChooser.counter += 1;
            },

            checkFilesList: function () {
                fileChooser.addFileBtnMin.style.display = "none";
                if (fileChooser.files.length > 2) {
                    if (fileChooser.addFileBtn) {
                        fileChooser.addFileBtn.style.display = "none";
                    }
                    if (fileChooser.addFileBtnMobile) {
                        fileChooser.addFileBtnMobile.style.display = "none";
                    }
                    fileChooser.addFileBtnMinTooltip.style.display = "block";
                } else {
                    if (fileChooser.addFileBtn) {
                        fileChooser.addFileBtn.style.display = "block";
                    }
                    if (fileChooser.addFileBtnMobile) {
                        fileChooser.addFileBtnMobile.style.display = "block";
                    }

                    document.querySelector('[id^="file-input"]').id = "file-input-0";
                }
            },

            init: function() {
                fileChooser.addFileBtnMin = document.getElementById("add-file-btn-min");
                fileChooser.addFileBtnMinTooltip = document.getElementById("add-file-min");
                fileChooser.inputFile = document.querySelector('[id^="file-input"]');
                fileChooser.addFileBtn = document.getElementById("add-file-btn");
                fileChooser.addFileBtnMobile = document.getElementById("add-file-btn-mobile");
                fileChooser.chosenFiles = document.getElementById("chosen-files");
                fileChooser.chosenFilesMessage = document.getElementById("chosen-files-message");
                fileChooser.fileSizeValidationMessage = document.getElementById('file-size-validation-message');

                if (fileChooser.addFileBtn && fileChooser.addFileBtnMin) {
                    fileChooser.addFileBtn.addEventListener("click", fileChooser.inputClick);
                    fileChooser.addFileBtnMin.addEventListener("click", fileChooser.inputClick);
                }

                if (fileChooser.addFileBtnMobile) {
                    fileChooser.addFileBtnMobile.addEventListener("click", fileChooser.inputClick);
                }

                if (fileChooser.inputFile) {
                    fileChooser.inputFile.addEventListener("change", fileChooser.changeEvent);
                }
            }
        };

        $("#js-contact-merchant").click( function (e){
            e.preventDefault();
            {% if is_granted('IS_AUTHENTICATED_REMEMBERED') and not is_granted('ROLE_OPERATOR') and not is_granted('ROLE_SUPER_ADMIN') %}
            UI.Modal.show('js-contact-merchant-modal','Modal--add', $($('#js-contact-merchant-modal-tpl').html()), true, true);
            fileChooser.init();
            {% else %}
            UI.Modal.show('js-not-authorized-login-modal','Modal--add', $($('#js-not-authorized-modal').html()), true, true);
            {% endif %}
        });

        $('#ask-vendor-for-price').on('click',function (e) {
            e.preventDefault();
            UI.Modal.show('js-contact-merchant-for-price-modal','Modal--add', $($('#js-contact-merchant-for-price-modal-tpl').html()), true, true);
        });

        $('#ask-vendor-for-no-price-offer').on('click',function (e) {
            e.preventDefault();
            UI.Modal.show('js-contact-merchant-for-no-price-offer-modal','Modal--add', $($('#js-contact-merchant-for-no-price-offer-modal-tpl').html()), true, true);
        });

        $('.Offer-image-small').click(function() {
            $('.Offer-image-small').removeClass('selected');
            $('.Offer-image-big').css('background-image', $(this).attr('image-src'));
            $(this).addClass('selected');
        });

        $('#see-more').click(function() {
            $('#short-desc').css('display', 'none');
            $('#long-desc').css('display', 'block');
            $('#see-more').css('display', 'none');
            $('#see-less').css('display', 'block');
        });

        $('#see-less').click(function() {
            $('#short-desc').css('display', 'block');
            $('#long-desc').css('display', 'none');
            $('#see-more').css('display', 'block');
            $('#see-less').css('display', 'none');
        });

        $('#discount-plus').click(function() {
            $('.discount-lines').css('display', 'block');
            $('#discount-plus').css('display', 'none');
            $('#discount-minus').css('display', 'block');
        });

        $('#discount-minus').click(function() {
            $('.discount-lines').css('display', 'none');
            $('#discount-minus').css('display', 'none');
            $('#discount-plus').css('display', 'block');
        });

        var initialPrice = 0;

        var computePrice = function(){

            var lastPriceVal = +(parseFloat($('#price-val').attr('data-offer-price-for-current-currency')));

            if (initialPrice == 0) {
                initialPrice = $('#price-val').text();
            }

            $('.line').each(function() {
                if (+($(this).find('.line-key').text()) <= $('#quantity').val()) {
                    lastPriceVal = +(parseFloat($(this).find('.line-value').attr('data-threshold-value')));
                }
            });

            let price = 0;
            if ($('#quantity').val() >= parseInt($('#moq').text())) {
                price = lastPriceVal * $('#quantity').val();
                if ($('#locale').text() == 'en') {
                    $('#price-val').text(new Intl.NumberFormat('en', {minimumFractionDigits: 2}).format(lastPriceVal.toFixed(2)));
                    $('#price-calc').html(new Intl.NumberFormat('en', {minimumFractionDigits: 2}).format((price).toFixed(2)));
                } else {
                    $('#price-val').text(new Intl.NumberFormat('fr-FR', {minimumFractionDigits: 2}).format(lastPriceVal.toFixed(2)));
                    $('#price-calc').html(new Intl.NumberFormat('fr-FR', {minimumFractionDigits: 2}).format((price).toFixed(2)));
                }
            } else {
                $('#price-val').text(initialPrice);
                $('#price-calc').html(parseFloat(0).toFixed(2));
            }
            $('#price-calc').data('price', price);
        }

        $('#quantity').on('keyup input', computePrice );

        var onClickAddReferenceToMyCatalog = function() {
            UI.Modal.show('js-add-reference-to-mycatalog-modal','Modal--add', $($('#js-add-reference-to-mycatalog-modal-tpl').html()), true, true);
        };

        $(".js-onClickAddReferenceToMyCatalog").click( function(e){
            e.preventDefault();
            onClickAddReferenceToMyCatalog();
        });

        var wishListForm = null;
        var select = null;
        var submitButton = null;
        var selectedWishlist = null;
        var newWishListForm = null;
        var modalWishList = null;


        wishListForm_initForm = function($form) {
            wishListForm = $form;
            select = $('select', wishListForm);
            submitButton = $(':submit', wishListForm);
            selectedWishlist = 0;
            newWishListForm = $('#newWishListForm', wishListForm);
            UI.Select.init();

            $('input[name="form[quantity]"]', wishListForm).val($('#quantity').val());
        };

        wishListForm_showWishlistForm = function() {
            if(cartService_isUserSelectionValid()) {
                var modalContent = $($('#js-save-in-wishlist-modal-tpl').html());
                modalWishList = UI.Modal.show('js-save-in-wishlist-modal', 'Modal--save', modalContent, true, true);
                wishListForm_initForm($('form', '#js-save-in-wishlist-modal'));
                wishListForm_selectWishListListener();
                wishListForm_submitListener();
            }
        };

        wishListForm_showNewWishListForm = function() {
            $('input[name="form[name]"]', newWishListForm).attr('required', true);
            newWishListForm.show();
        };

        wishListForm_hideNewWishListForm = function() {
            $('input[name="form[name]"]', newWishListForm).attr('required', false);
            newWishListForm.hide();
        };

        wishListForm_selectWishListListener = function() {
            this.select.on('change', function() {
                var $this = $(this);
                if (select.val()) {
                    selectedWishlist = $this.val();
                    wishListForm_hideNewWishListForm()
                } else {
                    selectedWishlist = 0;
                    wishListForm_showNewWishListForm();
                }
            }).change();
        };

        wishListForm_productSuccessfullyAddedToWishlist = function(wishlistId) {
            var modalContent = $($('#js-add-wishlist-modal-tpl').html());
            var modal = UI.Modal.show('js-add-wishlist-modal', 'Modal--save', modalContent, true, true);

            $('#goToWishList', '#js-add-wishlist-modal').on('click', function() {
                window.location.href = '{{ path('front.wishlist.details', {'wishlistId':'WISHLISTID'}) }}'.replace('WISHLISTID', wishlistId);
            });

            $('#continueShopping', '#js-add-wishlist-modal').on('click', function() {
                modal.close();
            });
        };

        wishListForm_submitListener = function() {
            wishListForm.on('submit', function(event) {
                event.preventDefault();
                var modalLoading = window.UI.Modal.showLoading();

                if(cartService_isUserSelectionValid()) {
                    var url = wishListForm.attr('action');
                    var data = new FormData(wishListForm.get(0));

                    $.ajax({
                        url: url,
                        data: data,
                        type: 'POST',
                        cache: false,
                        processData: false,
                        contentType: false,
                        success: function(wishList) {
                            modalLoading.close();
                            modalWishList.close();
                            wishListForm_productSuccessfullyAddedToWishlist(wishList.id);
                        },
                        error: function(data) {
                            modalLoading.close();
                        }
                    });
                }
            });
        };

        $("#js-showWishlistForm").click( function(e){
            e.preventDefault();
            wishListForm_showWishlistForm();
        });

        var batchSize = parseInt({{ offer.offer.batchSize }});

        var cartService_isUserSelectionValid = function() {
            var qty = $('#quantity').val();
            batchSize = parseInt(1);
            {% if offer.offer.batchSize is not null %}
            batchSize = {{ offer.offer.batchSize }};
            {% endif %}

            if(!qty) {
                window.UI.Modal.alert("{{ 'offer_detail.wrong_quantity'|trans|raw }}");
                return false;
            }

            if(qty < {{ offer.offer.moq }}) {
                let wrongQuantity = "{{ 'offer_detail.too_small_quantity'|trans({'%min%': offer.offer.moq})|raw }}";
                let contactTheVendor = "{{ 'offer_detail.contact_the_vendor'|trans|raw }}";
                let text = wrongQuantity + "\n" + contactTheVendor;
                window.UI.Modal.alert(text);
                return false;
            }

            if( qty > {{ offer.offer.quantity }}) {
                let wrongQuantity = "{{ 'offer_detail.too_much_quantity'|trans({'%max%': offer.offer.quantity})|raw }}";
                let contactTheVendor = "{{ 'offer_detail.contact_the_vendor'|trans|raw }}";
                let text = wrongQuantity + "\n" + contactTheVendor;
                window.UI.Modal.alert(text);
                return false;
            }

            if(qty % batchSize > 0) {
                let wrongBatchSize = "{{ 'offer_detail.not_batch_size_multiple'|trans({'%batchSize%': offer.offer.batchSize})|raw }}";
                let contactTheVendor = "{{ 'offer_detail.contact_the_vendor'|trans|raw }}";
                let text = wrongBatchSize + "\n" + contactTheVendor;
                window.UI.Modal.alert(text);
                return false;
            }

            return true;
        };

        var loadingModal = null;
        var successModal = null;
        var minQty = parseInt({{ offer.offer.moq }});
        var maxQty = parseInt({{ offer.offer.quantity }});

        var onClickQuantityChange = function(modifier) {
            var qty = parseInt($("#quantity").val());

            if( !qty || (qty+modifier) < minQty) {
                if (minQty == 0){
                    $("#quantity").val(1);
                }else {
                    $("#quantity").val(minQty);
                }
            } else if((qty+modifier) > maxQty) {
                $("#quantity").val(maxQty);
            } else {
                $("#quantity").val(qty + parseInt(modifier));
            }
            $('#quantity').change();
            computePrice();
        };

        var onClickAdd = function() {
            var ref = '{{ offer.offer.izbergReference }}';
            var qty = $('#quantity').val();

            if (cartService_isUserSelectionValid()) {
                loadingModal = window.UI.Modal.showLoading();
                $.ajax({
                    type: 'POST',
                    url: '{{ path('front.cart.add') }}',
                    data: {
                        productId: ref,
                        qty: qty
                    },
                    success: function(data) {
                        var cptEUR = data['EUR'];
                        var cptUSD = data['USD'];
                        if(cptUSD && cptUSD > 0){
                            $("#cart-quantity-usd").html(cptUSD);
                            $("#cart-quantity-usd").parent().removeClass("hide");
                        }
                        if(cptEUR && cptEUR > 0){
                            $("#cart-quantity-eur").html(cptEUR);
                            $("#cart-quantity-eur").parent().removeClass("hide");
                        }
                        loadingModal.close();
                        var modalHTML = $('#js-add-modal-tpl').html();
                        successModal = window.UI.Modal.show('js-add-modal', 'Modal--add', $(modalHTML), false);
                    },
                    error: function(XMLHttpRequest, textStatus, errorThrown) {
                        loadingModal.close();
                        window.UI.Modal.alert("{{ 'offer_detail.add_error'|trans|raw }}");
                    }
                });
            }
        };

        var showLogin = function(id) {
            $('#' + id).remove();
            UI.Modal.showLogin();
        };

        var onClickAnonymous = function(){
            UI.Modal.show('js-cart-login-modal','Modal--add', $($('#js-login-add-to-cart').html()), true, true);
        };

        var updateCartHeader = function(qty) {
            var currency = '{{ offer.offer.currency }}';
            var cart = $('#cart-quantity-'+currency.toLowerCase());
            var newQty = 0;
            if(cart.text()) {
                newQty = parseInt(cart.text());
            }
            newQty += parseInt(qty);
            cart.text(newQty);
            if(cart.parent().hasClass('hide')) {
                cart.parent().removeClass('hide');
            }
        };

        var closeSuccessModal = function() {
            if(successModal !== null) {
                successModal.close();
            }
        };

        var goToCart = function() {
            window.location.href = '{{ path('cart.details.before_buy', {'currencyOrCartId' : offer.offer.currency}) }}';
        };

        $("#js-onClickQuantityChange-add").click( function(e){
            e.preventDefault();

            {% if offer.offer.batchSize is not null %}
            onClickQuantityChange({{ offer.offer.batchSize }});
            {% else %}
            onClickQuantityChange(1);
            {% endif %}
        });

        $("#js-onClickQuantityChange-sub").click( function(e){
            e.preventDefault();

            {% if offer.offer.batchSize is not null %}
            onClickQuantityChange(-{{ offer.offer.batchSize }});
            {% else %}
            onClickQuantityChange(-1);
            {% endif %}
        });

        $('#js-onClickAdd').click( function(e){
            e.preventDefault();
            onClickAdd();
        });
    </script>
    <script type="text/javascript">
        var page = 1;
        document.addEventListener('DOMContentLoaded', function () {
            var $w = $(window);
            var screenWidth = $('body').width();
            var screenHeight = $w.height();
            var navHeight = 0;
            var $slider;
            var orientation = window.UI.Utils.getScreenOrientation();

            var sliderOptions = {
                width : '100%',
                height : '215px',
                slideDistance : 0,
                touchSwipe : Modernizr.touchevents,
                fade : Modernizr.touchevents,
                fadeOutPreviousSlide : Modernizr.touchevents,
                fadeArrows : true,
                buttons: true,
                autoplayDelay : 4000
            };

            $slider = initSlider(screenWidth, screenHeight, navHeight, orientation, sliderOptions);

            // Listen for resize changes
            $w.on("resize", function() {
                screenWidth = $('body').width();
                screenHeight = $w.height();
                navHeight = $('.Menu .navbar-header').height();

                var newOrientation = window.UI.Utils.getScreenOrientation();

                // If orientation changed then reinit the slider
                if (newOrientation !== orientation) {
                    orientation = newOrientation;
                    $slider = initSlider(screenWidth, screenHeight, navHeight, orientation, sliderOptions);
                }
            });

            $('#proforma-send').on('click', function(e){
                const href = $(this).attr('href');
                const qty = parseInt($('#quantity').val());
                const total = parseFloat($('#price-calc').data('price'));
                if (!isNaN(qty) && !isNaN(total)) {
                    e.preventDefault();
                    const anchor = document.createElement('a');
                    anchor.href = href + '/' + qty + '/' + total;
                    anchor.className = 'd-none';
                    anchor.target = '_blank';
                    anchor.click();
                }
            });
        });

        /**
         * Initialize homepage slider with the correct ratio
         * @param screenWidth
         * @param screenHeight
         * @param navHeight
         * @param orientation
         * @param sliderOptions
         */
        var initSlider = function(screenWidth, screenHeight, navHeight, orientation, sliderOptions) {
            if($('#js-offer-slider-mobile').find('.sp-slide').length <= 1) {
                sliderOptions.touchSwipe = false;
            }
            return jQuery('#js-offer-slider-mobile').sliderPro(
                jQuery.extend({}, sliderOptions, {})
            ).data( 'sliderPro' );
        };

        var showNext = function(id, nbPages) {
            page = page + 1;
            if(page == nbPages) {
                $('#next-' + id).removeClass('active');
                $('#prev-' + id).addClass('active');
            } else {
                $('#prev-' + id).addClass('active');
            }

            showPage(id, page);
        };

        var showPrev = function(id, nbPages) {
            page = page - 1;
            $('#next-' + id).addClass('active');
            if(page === 1) {
                $('#prev-' + id).removeClass('active');
            }

            showPage(id, page);
        };

        var showPage = function(id, page) {
            switch (page) {
                case 1 :
                    $('#first-part-' + id).removeClass('hide');
                    $('#third-part-' + id).addClass('hide');
                    $('#fourth-part-' + id).addClass('hide');
                    $('#second-part-' + id).addClass('hide');
                    break;
                case 2 :
                    $('#first-part-' + id).addClass('hide');
                    $('#third-part-' + id).addClass('hide');
                    $('#fourth-part-' + id).addClass('hide');
                    $('#second-part-' + id).removeClass('hide');
                    break;
                case 3 :
                    $('#first-part-' + id).addClass('hide');
                    $('#second-part-' + id).addClass('hide');
                    $('#third-part-' + id).removeClass('hide');
                    $('#fourth-part-' + id).addClass('hide');
                    break;
                case 4 :
                    $('#first-part-' + id).addClass('hide');
                    $('#second-part-' + id).addClass('hide');
                    $('#third-part-' + id).addClass('hide');
                    $('#fourth-part-' + id).removeClass('hide');
                    break;
            }
        };

    </script>

    <script type="text/template" id="js-add-reference-to-mycatalog-modal-tpl">
        {% if myCatalogReferenceForm is not empty %}

            {{ form_start(myCatalogReferenceForm, {'attr':{'novalidate':true}}) }}

            <div class="Modal-body">
                <div class="form-group">
                    {{ form_row(myCatalogReferenceForm.companyId) }}
                    {{ form_row(myCatalogReferenceForm.manufacturerReference) }}
                    <div>
                        {{ form_label(myCatalogReferenceForm.buyerReference) }}
                        {{ form_widget(myCatalogReferenceForm.buyerReference) }}
                    </div>
                </div>
            </div>
            <div class="Modal-footer">
                {{ form_widget(myCatalogReferenceForm.save, {'attr': {'class': 'buttonModal'}}) }}
                {{ form_widget(myCatalogReferenceForm.cancel, {'attr': {'class': 'buttonModal'}}) }}
                {{ form_widget(myCatalogReferenceForm.delete, {'attr': {'class': 'buttonModal'}}) }}
            </div>
            {{ form_end(myCatalogReferenceForm) }}
        {% endif %}
    </script>

    <script type="text/template" id="js-save-in-wishlist-modal-tpl">
        <div class="Modal-header">
            <h5 class="Modal-title">{{ 'offer_detail.add_to_wishlist'|trans }} </h5>
        </div>

        <form action="{{ path('wishlist.save_offer_quantity') }}" method="post">
            <div class="Modal-body">
                <input type="hidden" name="form[offerId]" value="{{ offer.offer.izbergReference }}" />
                <input type="hidden" name="form[quantity]" value="" />

                <div class="select-wishlist">
                    <label>{{ 'wishlist.title'|trans }} : </label>

                    <div class="js-select-wrapper select-wrapper has-text">
                        <select id="wishlistId" name="form[wishListId]">
                            {% for wishList in wishLists %}
                                <option value="{{ wishList.id }}">{{ wishList.name }}</option>
                            {% endfor %}
                            <option value="">{{ 'wishlist.add_new'|trans }}</option>
                        </select>
                    </div>
                </div>

                <div id="newWishListForm" class="add-new-wishlist">
                    <input type="text" name="form[name]" placeholder="{{ 'wishlist.new_name'|trans }}"/>
                    <input type="hidden" name="form[currency]" value="{{ offer.offer.currency }}" />
                </div>
            </div>
            <div class="Modal-footer">
                <button type="submit" id="js-confirm-button" class="btn btn-primary">{{ 'wishlist.save'|trans }}</button>
            </div>
        </form>
    </script>

    <script type="text/template" id="js-add-modal-tpl">
        <div class="Modal-header">
            <h5 class="Modal-title">{{ 'node.form.lang.button'|trans }}</h5>
        </div>
        <div class="Modal-body">
            {{ 'offer_detail.add_success'|trans }}
        </div>
        <div class="Modal-footer">
            <button id="js-confirm-button" class="btn-primary" onclick="closeSuccessModal()">{{ 'offer_detail.continue_shopping'|trans|upper }}</button>
            <button id="js-confirm-button" class="btn-primary" onclick="goToCart()">{{ 'offer_detail.see_cart'|trans|upper }}</button>
        </div>
    </script>

    <script type="text/template" id="js-add-wishlist-modal-tpl">
        <div class="Modal-header">
            <h5 class="Modal-title">{{ 'offer_detail.add_to_wishlist'|trans }} </h5>
        </div>
        <div class="Modal-body">
            {{ 'offer_detail.add_wishlist_success'|trans }}
        </div>
        <div class="Modal-footer">
            <button id="continueShopping" class="btn-primary">{{ 'offer_detail.continue_shopping'|trans|upper }}</button>
            <button id="goToWishList" class="btn-primary">{{ 'offer_detail.see_wishlist'|trans|upper }}</button>
        </div>
    </script>

    <script type="text/template" id="js-contact-merchant-modal-tpl">
        <div class="Modal-inner">
            <div class="Modal-header">
                <h5 class="Modal-title">{{ 'offer_detail.contact_seller.modal.title'| trans({'%vendor%': offer.offer.merchant.name }) }}</h5>
            </div>
            {{ form_start(contactMerchantForm, {'enctype': 'multipart/form-data'}) }}
            <div class="Modal-body">
                <div class="form-group">
                    {{ form_row(contactMerchantForm.object) }}
                    {{ form_row(contactMerchantForm.message) }}
                </div>
                <section id="chosen-files-message">
                    {{ 'contactMerchant.attachment_limit'|trans({'%limit%' : 3}) }}
                    - {{ 'contactMerchant.authorized_types'|trans }}
                </section>
                <section id="file-size-validation-message" style="visibility: hidden">{{ 'contactMerchant.file_too_big'|trans({'%size-limit%' : '5 MB'}) }}</section>
                <div id="add-file-min">
                    <section id="chosen-files">
                        <div id="add-file-btn-min" class="pointer tooltip-total">
                        </div>
                    </section>

                </div>
                {{ form_widget(contactMerchantForm.attachments) }}

                <button id="add-file-btn" type="button" class="btn-white" >{{ 'contactMerchant.add_file' | trans | upper }} <span>+</span></button>

            </div>

            <div class="Modal-footer">
                {{ form_widget(contactMerchantForm.save, {'attr': {'class': 'buttonModal'}}) }}
            </div>
            {{ form_end(contactMerchantForm) }}
        </div>
    </script>

   {% include '@OpenFront/shared/contact_merchant_for_price_modal.html.twig' %}

    <script type="text/template" id="js-not-authorized-modal">
        <div class="Modal-header">
            <h5 class="Modal-title">{{ 'offer_detail.anonymous.not_authorized'|trans }}</h5>
        </div>
        <div class="Modal-body">
            {{ 'offer_detail.anonym_contact_merchant'|trans }}
        </div>
        <div class="Modal-footer">
            <a href="#" onclick="showLogin('js-not-authorized-login-modal')" class="modal-link">
                <div class="buttonModal">{{ 'offer_detail.login'|trans|upper }}</div>
            </a>
        </div>
    </script>

    <script type="text/template" id="js-login-add-to-cart">
        <div class="Modal-inner">
            <div class="Modal-header">
                <h5 class="Modal-title">{{ 'offer_detail.anonymous.not_authorized'|trans }}</h5>
            </div>
            <div class="Modal-body">
                {{ 'offer_detail.anonymous.add_to_cart'|trans }}
            </div>
            <div class="Modal-footer">
                <a href="#" onclick="showLogin('js-cart-login-modal')" class="modal-link">
                    <div class="buttonModal" >{{ 'offer_detail.login'|trans|upper }}</div>
                </a>
            </div>
        </div>
    </script>
{% endblock %}

{% block _form_attachments_widget %}
    <input id='file-input-0' type='file' hidden="hidden" name="form[attachments][]" accept="image/png, image/jpeg, application/pdf, image/gif, image/png, image/tiff"/>
{% endblock %}
