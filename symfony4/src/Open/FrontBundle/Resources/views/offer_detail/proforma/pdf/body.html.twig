{% trans_default_domain 'AppBundle' %}
{% set locale = app.request.locale %}
<div style="padding: 0 30px">
    <table>
        <tr>
            <td style="text-align: center; width: 50%; vertical-align: top;padding-right: 10px">
                {{ 'proforma.address_title' | trans }}<br>
                <b>{{ merchant.name }}</b>
                {% if merchantAttribute.AA003_corporate_name is defined and not merchantAttribute.AA003_corporate_name == merchant.name %}
                    <br>{{merchantAttribute.AA003_corporate_name}}
                {% endif %}
                {% if merchantAttribute.AA006_legal_form is defined %}
                    <br>{{merchantAttribute.AA006_legal_form}}  {{ 'proforma.capital'|trans }}
                    <br>
                    {% if locale == 'en' %}
                        {{ merchantAttribute.AA008_share_capital | number_format('0', '.', ',') }}
                    {% else %}
                        {{ merchantAttribute.AA008_share_capital | number_format('0', ',', ' ')}}
                    {% endif %}
                    {% if merchantAttribute.AA009_share_capital_currency == 'EUR' %} € {%  elseif merchantAttribute.AA009_share_capital_currency == 'USD' %} $ {% endif %}
                {% endif %}
                {% if merchantAttribute.AA007_rcs_number is defined %}
                    <br>{{ 'proforma.rcs'|trans }} : {{merchantAttribute.AA007_rcs_number}}
                {% endif %}
                {% if merchantAttribute.AA0031_Company_identification_number is defined %}
                    <br>{{ 'proforma.siret'|trans }} : {{merchantAttribute.AA0031_Company_identification_number}}
                {% endif %}
                {% if merchantCompany.vat_number is defined and merchantCompany.vat_number is not empty%}
                    <br>{{ 'proforma.vat_number'|trans }} : {{merchantCompany.vat_number}}
                {% endif %}
                <br><br>
                <b>{{ 'proforma.address'|trans }}</b>
                {% set addressFilter = merchant.addresses | filter(a => a.billing_address == true) %}
                {% set address = null %}
                {% if addressFilter %}
                    {% set address = addressFilter[0] %}
                {% endif %}
                {% if address is not empty%}
                    <br>{{ address.address }}
                    {% if address.address2 is not empty%}
                        <br>{{ address.address2 }}
                    {% endif %}
                    <br>{{ address.zipcode }} {{ address.city }}
                    <br>{{ address.country.name }}
                {% endif %}
            </td>
            <td style="text-align: center; width: 50%; vertical-align: top;padding-left: 10px">
                <div>
                    <img src="images/logo.png">
                </div>
                {% if userCompany %}
                    <b>{{ userCompany.name }}</b>

                    {% if userCompany.billingAddress %}
                        <br><br>{{ 'proforma.billing_address'|trans }}
                        <br>{{ userCompany.billingAddress.address }}
                        {% if userCompany.billingAddress.address2 %}
                            <br>{{ userCompany.billingAddress.address2 }}
                        {% endif %}
                        <br>{{ userCompany.billingAddress.zipCode }} {{ userCompany.billingAddress.city }}
                        <br>{{ userCompany.mainAddress.country.code }} {# quand billingAddress != mainAddress -> pas de country dans la billingAddress #}
                    {% else %}
                        <br><br>{{ 'proforma.billing_address'|trans }}
                        <br>{{ userCompany.mainAddress.address }}
                        {% if userCompany.mainAddress.address2 %}
                            <br>{{ userCompany.mainAddress.address2 }}
                        {% endif %}
                        <br>{{ userCompany.mainAddress.zipCode }} {{ userCompany.mainAddress.city }}
                        <br>{{ userCompany.mainAddress.country.code }}
                    {% endif %}

                    {% if userCompany.identification %}
                        <br><br>{{ 'proforma.vat_number'|trans }} : {{userCompany.identification}}
                    {% endif %}
                {% endif %}
            </td>
        </tr>
    </table>
    <p style="padding-top: 30px;">
        {{ 'proforma.date'|trans }} {{ "now"|format_datetime('short', 'none', locale=locale) }}<br>
        {{ 'proforma.text'|trans }}
    </p>
    <div style="border: 2px solid #000; padding: 10px">
        <table>
            <tr>
                <td style="vertical-align: top">
                    <img src="{{ offer.offer.offerPictures[0] }}" style="width: 15%">
                </td>
                <td style="vertical-align: top; width: 55%">
                    <div style="border-left: 10px solid white">
                        <div style="color: #9600FF; font-weight: bold">
                            <b>{{ offer.offer.offerTitle }}</b>
                        </div>
                        <div>
                            <b>{{ 'product.seller'|trans }} : </b>{{ offer.offer.merchant.name }}
                        </div>
                        <div>
                            <b>{{ 'product.seller_reference'|trans }} : </b>{{ offer.offer.sellerRef }}
                        </div>
                        <div>
                            <b>{{ 'product.manufacturer'|trans }} : </b>{{ offer.offer.manufacturerName }}
                        </div>
                        <div>
                            <b>{{ 'product.manufacturer_reference'|trans }} : </b>{{ offer.offer.manufacturerRef }}
                        </div>
                        {% if offer.offer.frameContract %}
                        <div>
                            <b>{{ 'invoice.detail.frame_contract'|trans }} : </b>{{ offer.offer.frameContract }}
                        </div>
                        {% endif %}
                    </div>
                </td>
                <td style="vertical-align: top; text-align: right; white-space: nowrap">
                    {% if offer.offer.quantityPerSku and offer.offer.skuUnit %}
                        {{ 'offer_detail.price_quantity'|trans({'%quantity%': offer.offer.quantityPerSku, '%unit%': offer.offer.skuUnit }) }}
                    {% endif %}
                    <div>
                        <b>{{ 'comparaisonSheet.page.price_excl_tax'|trans }} :&nbsp;</b>
                        {% if locale == 'en' %}
                            {{ offer.offer.price(offer.offer.currency) | number_format('2', '.', ',') }}
                        {% else %}
                            {{ offer.offer.price(offer.offer.currency) | number_format('2', ',', ' ')}}
                        {% endif %}
                        {% if offer.offer.currency == 'EUR' %} € {%  elseif offer.offer.currency == 'USD' %} $ {% endif %}
                    </div>
                    <div>
                        {% if offer.offer.thresholds is defined %}
                            {% set firstThreshold = true %}
                            {% for key,value in offer.offer.thresholds %}
                                {% if firstThreshold %}
                                    <div>
                                        <b>{{ 'offer_detail.quantity'|trans }} < {{ key }} : </b>
                                        {% if locale == "en" %}
                                            {{ offer.offer.prices[offer.offer.currency] | number_format('2', '.', ',') }}
                                        {% else %}
                                            {{ offer.offer.prices[offer.offer.currency] | number_format('2', ',', ' ') }}
                                        {% endif %}
                                        {% if offer.offer.currency == 'EUR' %} € {%  elseif offer.offer.currency == 'USD' %} $ {% endif %}
                                    </div>
                                {% endif %}
                                <div>
                                    <b>{{ 'offer_detail.quantity'|trans }} >= {{ key }} :&nbsp;</b>
                                    {% if locale == 'en' %}
                                        {{ value | number_format('2', '.', ',') }}
                                    {% else %}
                                        {{ value | number_format('2', ',', ' ') }}
                                    {% endif %}
                                    {% if offer.offer.currency == 'EUR' %} € {%  elseif offer.offer.currency == 'USD' %} $ {% endif %}</p>
                                </div>
                                {% set firstThreshold = false %}
                            {%  endfor %}
                        {%  endif%}
                    </div>
                    {% if offer.offer.incoterm is defined and offer.offer.incoterm|length > 0 %}
                    <div>
                        <b>{{ 'comparaisonSheet.page.delivery'|trans }} :&nbsp;</b>
                        {{ offer.offer.incoterm|upper }}
                        {% if offer.offer.incotermCountry %}
                            ({{ offer.offer.incotermCountry|upper }})
                        {% endif %}
                    </div>
                    {% endif %}
                    {% if offer.offer.deliveryTime %}
                        <b>{{ 'product.delivery_time'|trans }} :&nbsp;</b>
                        {{ offer.offer.deliveryTime }} {{ 'site.form.days'|trans|lower }}
                    {% endif %}
                    {% if offer.offer.priceValidityDate %}
                        <div>
                            <b>{{ "offer_detail.frame_contract_valid_date"|trans }} : </b> {{ offer.offer.priceValidityDate | format_datetime('short', 'none', locale=locale)}}
                        </div>
                    {% endif %}
                    {% if quantity and total %}
                        <br>
                        <br>
                        <b>{{ 'offer_detail.proforma_pdf.quantity'|trans }} : </b>{{ quantity }}<br>
                        <b>{{ 'offer_detail.proforma_pdf.total_price'|trans }} : </b> :
                        {% if locale == 'en' %}
                            {{ total | number_format('2', '.', ',') }}
                        {% else %}
                            {{ total | number_format('2', ',', ' ') }}
                        {% endif %}
                        {% if offer.offer.currency == 'EUR' %} € {%  elseif offer.offer.currency == 'USD' %} $ {% endif %}
                    {% endif %}
                </td>
            </tr>
            <tr>
                <td colspan="3" style="padding-top: 10px">
                    {{ offer.offer.shortDescription }}
                </td>
            </tr>
        </table>
    </div>
    {% if merchantAttribute.AA020_minimum_order_amount is defined %}
    <p>
        {{ 'tab_infos_seller.minimum_order_amount'|trans({'%amount%': merchantAttribute.AA020_minimum_order_amount | round  }) }} {{ offer.offer.currency|upper }}
    </p>
    {% endif %}
    <p style="font-weight: bold">
        {{ 'proforma.condition.title'|trans }} :
    </p>
    <ul>
        <li style="margin-bottom: 10px;">
            {{ 'proforma.condition.purchase'|trans({'%vendor_name%': merchant.name}) }}
        </li>
        <li style="margin-bottom: 10px;">
            {{ 'proforma.condition.payment'|trans }}
            <div style="text-align: center">
                WEBHELP STATION ONE<br>
                BIC: SOGEFRPP<br>
                IBAN:***************************
            </div>
        </li>
        <li style="margin-bottom: 10px;">
            {{ 'proforma.condition.adress'|trans }}
        </li>
    </ul>
</div>
