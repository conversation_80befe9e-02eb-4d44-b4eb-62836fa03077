{% trans_default_domain 'AppBundle' %}
{% set locale = app.request.locale %}
<div style="padding: 0 30px">
    <div style="display: flex; width: 50%; margin: 0 auto; text-align: center">
        <img src="images/logo.png"><br>
        {% if userCompany %}
            <b>{{ userCompany.name }}</b>

            {% if billingAddress is not null %}
                <br><br>{{ 'proforma.billing_address'|trans }}
                <br>{{ billingAddress.address }}
                {% if billingAddress.address2 %}
                    <br>{{ billingAddress.address2 }}
                {% endif %}
                <br>{{ billingAddress.zipCode }} {{ billingAddress.city }}
                <br>{{ userCompany.mainAddress.country.code }}
            {% else %}
                {% if userCompany.billingAddress %}
                    <br><br>{{ 'proforma.billing_address'|trans }}
                    <br>{{ userCompany.billingAddress.address }}
                    {% if userCompany.billingAddress.address2 %}
                        <br>{{ userCompany.billingAddress.address2 }}
                    {% endif %}
                    <br>{{ userCompany.billingAddress.zipCode }} {{ userCompany.billingAddress.city }}
                    <br>{{ userCompany.mainAddress.country.code }} {# quand billingAddress != mainAddress -> pas de country dans la billingAddress #}
                {% else %}
                    <br><br>{{ 'proforma.billing_address'|trans }}
                    <br>{{ userCompany.mainAddress.address }}
                    {% if userCompany.mainAddress.address2 %}
                        <br>{{ userCompany.mainAddress.address2 }}
                    {% endif %}
                    <br>{{ userCompany.mainAddress.zipCode }} {{ userCompany.mainAddress.city }}
                    <br>{{ userCompany.mainAddress.country.code }}
                {% endif %}
            {% endif %}

            {% if userCompany.identification %}
                <br><br>{{ 'proforma.vat_number'|trans }} : {{ userCompany.identification }}
            {% endif %}
        {% endif %}
    </div>
    <p style="padding-top: 30px;">
        {{ 'proforma.date'|trans }} {{ "now"|format_datetime('short', 'none', locale=locale) }}<br>
        {{ 'proforma.text'|trans }}
    </p>
    {% for merchant in data %}
        <div style="page-break-inside: avoid">
            <table style="width: 100%; margin-top: 1.25rem;">
                <tr>
                    <td style="vertical-align: top; font-size: 18px">
                        {{ merchant.merchant.name }}
                    </td>
                    <td style="vertical-align: top; text-align: right; font-size: 16px">
                        <b>{{ 'cart.table_label.total'|trans }}:</b>
                        {{ merchant.totalVatExcl | price(locale) }}
                        {% if merchant.currency == 'EUR' %} € {% elseif merchant.currency == 'USD' %} $ {% endif %}<br>
                        {% for key,value in merchant.taxes %}
                            {% if key != 0 %}
                                {{ 'cart.table_label.taxes'|trans }} {{ key | price(locale) }}% : {{ value | price(locale) }}
                                {% if merchant.currency == 'EUR' %} € {% elseif merchant.currency == 'USD' %} $ {% endif %}<br>
                            {% endif %}
                        {% endfor %}
                        <b>{{ 'cart.table_label.total_vat'|trans }}:</b>
                        {{ merchant.subtotal | price(locale) }}
                        {% if merchant.currency == 'EUR' %} € {% elseif merchant.currency == 'USD' %} $ {% endif %}
                    </td>
                </tr>
            </table>
            <div style="border: 2px solid #000; padding: 10px">
                {% for offer in merchant.offers %}
                    <table style="padding-bottom: 25px; page-break-inside: avoid" autosize="1">
                        <tr>
                            <td style="vertical-align: top">
                                <img src="{{ offer.detailedOffer.offer.offerPictures[0] }}" style="width: 15%">
                            </td>
                            <td style="vertical-align: top; width: 55%">
                                <div style="border-left: 10px solid white">
                                    <div style="color: #9600FF; font-weight: bold">
                                        <b>{{ offer.detailedOffer.offer.offerTitle }}</b>
                                    </div>
                                    <div>
                                        <b>{{ 'product.seller'|trans }} : </b>{{ offer.detailedOffer.offer.merchant.name }}
                                    </div>
                                    <div>
                                        <b>{{ 'product.seller_reference'|trans }} : </b>{{ offer.detailedOffer.offer.sellerRef }}
                                    </div>
                                    <div>
                                        <b>{{ 'product.manufacturer'|trans }} : </b>{{ offer.detailedOffer.offer.manufacturerName }}
                                    </div>
                                    <div>
                                        <b>{{ 'product.manufacturer_reference'|trans }}
                                            : </b>{{ offer.detailedOffer.offer.manufacturerRef }}
                                    </div>
                                    {% if offer.detailedOffer.offer.frameContract %}
                                        <div>
                                            <b>{{ 'invoice.detail.frame_contract'|trans }}
                                                : </b>{{ offer.detailedOffer.offer.frameContract }}
                                        </div>
                                    {% endif %}
                                </div>
                            </td>
                            <td style="vertical-align: top; text-align: right; white-space: nowrap">
                                {% if offer.detailedOffer.offer.quantityPerSku and offer.detailedOffer.offer.skuUnit %}
                                    {{ 'offer_detail.price_quantity'|trans({'%quantity%': offer.detailedOffer.offer.quantityPerSku, '%unit%': offer.detailedOffer.offer.skuUnit }) }}
                                {% endif %}
                                <div>
                                    <b>{{ 'comparaisonSheet.page.price_excl_tax'|trans }} :&nbsp;</b>
                                    {{ offer.detailedOffer.offer.price(offer.detailedOffer.offer.currency) | price(locale) }}
                                    {% if offer.detailedOffer.offer.currency == 'EUR' %} € {% elseif offer.detailedOffer.offer.currency == 'USD' %} $ {% endif %}
                                </div>
                                <div>
                                    <b>{{ 'cart.table_label.quantity'|trans }} :&nbsp;</b>
                                    {{ offer.cartItem.quantity | price(locale) }}
                                </div>
                                <div>
                                    <b>{{ 'cart.table_label.total'|trans }} :&nbsp;</b>
                                    {{ offer.cartItem.totalItem | price(locale) }}
                                    {% if offer.detailedOffer.offer.currency == 'EUR' %} € {% elseif offer.detailedOffer.offer.currency == 'USD' %} $ {% endif %}
                                </div>
                                <div>
                                    {% if offer.detailedOffer.offer.thresholds is defined %}
                                        {% set firstThreshold = true %}
                                        {% for key,value in offer.detailedOffer.offer.thresholds %}
                                            {% if firstThreshold %}
                                                <div>
                                                    <b>{{ 'offer_detail.quantity'|trans }} < {{ key }} : </b>
                                                    {% if locale == "en" %}
                                                        {{ offer.detailedOffer.offer.prices[offer.detailedOffer.offer.currency] | number_format('2', '.', ',') }}
                                                    {% else %}
                                                        {{ offer.detailedOffer.offer.prices[offer.detailedOffer.offer.currency] | number_format('2', ',', ' ') }}
                                                    {% endif %}
                                                    {% if offer.detailedOffer.offer.currency == 'EUR' %} € {% elseif offer.detailedOffer.offer.currency == 'USD' %} $ {% endif %}
                                                </div>
                                            {% endif %}
                                            <div>
                                                <b>{{ 'offer_detail.quantity'|trans }} >= {{ key }} :&nbsp;</b>
                                                {{ value | price(locale) }}
                                                {% if offer.detailedOffer.offer.currency == 'EUR' %}
                                                € {% elseif offer.detailedOffer.offer.currency == 'USD' %} $ {% endif %}</p>
                                            </div>
                                            {% set firstThreshold = false %}
                                        {% endfor %}
                                    {% endif %}
                                </div>
                                {% if offer.detailedOffer.offer.incoterm is defined and offer.detailedOffer.offer.incoterm|length > 0 %}
                                    <div>
                                        <b>{{ 'comparaisonSheet.page.delivery'|trans }} :&nbsp;</b>
                                        {{ offer.detailedOffer.offer.incoterm|upper }}
                                        {% if offer.detailedOffer.offer.incotermCountry %}
                                            ({{ offer.detailedOffer.offer.incotermCountry|upper }})
                                        {% endif %}
                                    </div>
                                {% endif %}
                                {% if offer.detailedOffer.offer.deliveryTime %}
                                    <b>{{ 'product.delivery_time'|trans }} :&nbsp;</b>
                                    {{ offer.detailedOffer.offer.deliveryTime }} {{ 'site.form.days'|trans|lower }}
                                {% endif %}
                                {% if offer.detailedOffer.offer.priceValidityDate %}
                                    <div>
                                        <b>{{ "offer_detail.frame_contract_valid_date"|trans }}
                                            : </b> {{ offer.detailedOffer.offer.priceValidityDate | format_datetime('short', 'none', locale=locale) }}
                                    </div>
                                {% endif %}
                                {% if quantity and total %}
                                    <br>
                                    <br>
                                    <b>{{ 'offer_detail.proforma_pdf.quantity'|trans }} : </b>{{ quantity }}<br>
                                    <b>{{ 'offer_detail.proforma_pdf.total'|trans }} : </b> :
                                    {{ total | price(locale) }}
                                    {% if offer.detailedOffer.offer.currency == 'EUR' %} € {% elseif offer.detailedOffer.offer.currency == 'USD' %} $ {% endif %}
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                {% endfor %}
            </div>
        </div>
    {% endfor %}
    {% if merchantAttribute.AA020_minimum_order_amount is defined %}
        <p>
            {{ 'tab_infos_seller.minimum_order_amount'|trans({'%amount%': merchantAttribute.AA020_minimum_order_amount | round  }) }} {{ offer.detailedOffer.offer.currency|upper }}
        </p>
    {% endif %}

    <table style="width: 100%; margin-top: 1.25rem;">
        <tr>
            <td style="vertical-align: top; text-align: right; font-size: 16px">
                <b>{{ 'cart.table_label.total_cart'|trans }}:</b>
                {{ totalVatExcluded|price(locale) }}
                {% if currency == 'EUR' %} € {% elseif currency == 'USD' %} $ {% endif %}<br>
                {% for key,value in cartTaxes %}
                    {% if key != 0 %}
                        {{ 'cart.table_label.taxes'|trans }} {{ key | price(locale) }}% : {{ value | price(locale) }}
                        {% if currency == 'EUR' %} € {% elseif currency == 'USD' %} $ {% endif %}<br>
                    {% endif %}
                {% endfor %}
                <b>{{ 'cart.table_label.total_vat'|trans }}:</b>
                {{ cartTotal | price(locale) }}
                {% if currency == 'EUR' %} € {% elseif currency == 'USD' %} $ {% endif %}
            </td>
        </tr>
    </table>

    <p style="font-weight: bold">
        {{ 'proforma.condition.title'|trans }} :
    </p>
    <ul>
        <li style="margin-bottom: 10px;">
            {{ 'proforma.condition.payment'|trans }}
            <div style="text-align: center">
                WEBHELP STATION ONE<br>
                BIC: SOGEFRPP<br>
                IBAN:***************************
            </div>
        </li>
        <li style="margin-bottom: 10px;">
            {{ 'proforma.condition.adress'|trans }}
        </li>
    </ul>
</div>
