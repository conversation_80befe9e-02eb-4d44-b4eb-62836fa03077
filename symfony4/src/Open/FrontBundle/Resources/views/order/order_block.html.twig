{% trans_default_domain 'AppBundle' %}
{# @var order \AppBundle\Model\View\OrderDetail\Order#}
{% set locale = app.request.locale %}
<div class="order-block">
    <div class="block order-info" style="flex: 2">
        <div class="order-date order-row mobile-max-only">
            <span class="title text-bold">{{ 'orders.list.block.date_title'|trans([], 'AppBundle') }}&nbsp;</span>
            <span class="value">{{ order.createdOn|format_datetime('short', 'none', locale=locale) }}</span>
        </div>
        <div class="order-id order-row">
            <span class="title text-bold">{{ 'orders.list.block.order_title'|trans([], 'AppBundle') }}&nbsp;</span>
            <span class="value">{{ order.idNumber }}</span>
        </div>
        <div class="order-address order-row">
            <span class="title text-bold">{{ 'orders.list.block.cost_center_title'|trans([], 'AppBundle') }}&nbsp;</span>
            <span class="value">{{ order.costCenter }}</span>
        </div>
        <div class="order-address order-row">
            <span>
                <svg id="cost-center-additional-info-icon" data-open="true" class="Icon" style="height: 13px; fill: #6B6F82;">
                    <use xlink:href="#icon-plus"></use>
                </svg>
            </span>
            <span class="title text-underlined text-bold">
                {{ 'orders.list.block.addition_information'|trans([], 'AppBundle') }}
            </span>
            <div class="cost-center-additional-info">
                <div>
                    <span class="title text-bold">{{ 'orders.list.block.packaging_specifications'|trans([], 'AppBundle') }}</span>
                    {% if order.packagingRequest1 is not empty %}<p>{{ order.packagingRequest1 }}</p>{% endif %}
                    {% if order.packagingRequest2 is not empty %}<p>{{ order.packagingRequest2 }}</p>{% endif %}
                    {% if order.packagingRequest3 is not empty %}<p>{{ order.packagingRequest3 }}</p>{% endif %}
                </div>
                <div>
                    <span class="title text-bold">{{ 'orders.list.block.requested_documents'|trans([], 'AppBundle') }}</span>
                    {% if order.documentationRequest1 is not empty %}<p>{{ order.documentationRequest1 }}</p>{% endif %}
                    {% if order.documentationRequest2 is not empty %}<p>{{ order.documentationRequest2 }}</p>{% endif %}
                    {% if order.documentationRequest3 is not empty %}<p>{{ order.documentationRequest3 }}</p>{% endif %}
                    {% if order.documentationRequest4 is not empty %}<p>{{ order.documentationRequest4}}</p>{% endif %}
                    {% if order.documentationRequest5 is not empty %}<p>{{ order.documentationRequest5 }}</p>{% endif %}
                    {% if order.documentationRequest6 is not empty %}<p>{{ order.documentationRequest6 }}</p>{% endif %}
                    {% if order.documentationRequest7 is not empty %}<p>{{ order.documentationRequest7 }}</p>{% endif %}
                    {% if order.documentationRequest8 is not empty %}<p>{{ order.documentationRequest8 }}</p>{% endif %}
                    {% if order.documentationRequest9 is not empty %}<p>{{ order.documentationRequest9 }}</p>{% endif %}
                    {% if order.documentationRequest10 is not empty %}<p>{{ order.documentationRequest10 }}</p>{% endif %}
                </div>
            </div>
        </div>
        <div class="order-address order-row">
            <span class="title text-bold">{{ 'orders.list.block.validation_number_title'|trans([], 'AppBundle') }}&nbsp;</span>
            <span class="value">{{ order.validationNumber }}</span>
            <span><a id="edit-validation-number" class="js-onClickEditValidationNumber validation_number_action" href="#">
                    <svg class="Icon" style="height: 13px">
                            <use xlink:href="#icon-pencil"></use>
                        </svg>
                </a></span>
        </div>
        <div class="order-address order-row">
            <span class="title text-bold">{{ 'orders.list.block.address_title'|trans([], 'AppBundle') }}&nbsp;</span>
            <span class="value">{{ order.shippingAddress.address }}, {{ order.shippingAddress.address2 }} {{ order.shippingAddress.zipCode }}
                {{ order.shippingAddress.city }}</span>
        </div>
        <div class="order_payment order-row">
            <span class="title text-bold">{{ 'orders.list.block.payment_terms'|trans([], 'AppBundle') }}</span>
            <span class="value">
                {% if order.paymentCode is defined %}
                    {% if order.paymentCode == 'cb' %}
                        {{ 'payment_mode.Prepayment_creditcard'|trans([], 'AppBundle')  }}
                    {% elseif order.paymentCode == 'bankwire' %}
                        {{ 'payment_mode.Prepayment_moneytransfert'|trans([], 'AppBundle')  }}
                    {% else %}
                        {{ 'payment_mode.Termpayment_moneytransfert'|trans([], 'AppBundle')  }}
                    {% endif %}
                {% else %}
                    {{ 'payment_mode.Termpayment_moneytransfert'|trans([], 'AppBundle')  }}
                {% endif %}
            </span>
        </div>
        {% if order.reconciliationKey is defined and order.reconciliationKey is not empty %}
            <div class="order-row">
                <span class="title text-bold">{{ 'orders.list.block.iban_account_name' |trans([], 'AppBundle') }}</span>
                <span class="value">{{ order.ibanAccountName }}</span>
            </div>
            <div class="order-row">
                <span class="title text-bold">{{ 'orders.list.block.iban_number' |trans([], 'AppBundle') }}</span>
                <span class="value">{{ order.iban }}</span>
            </div>
            <div class="order-row">
                <span class="title text-bold">{{ 'orders.list.block.key' |trans([], 'AppBundle') }}</span>
                <span class="value">{{ order.reconciliationKey }}</span>
            </div>
        {% endif %}
        <div class="order_status order-row mobile-max-only">
            <span class="title text-bold">{{ 'orders.list.block.status'|trans([], 'AppBundle') }}</span>
            <span class="value">{{ ('orders.status.status_' ~ order.status) |trans }}</span>
        </div>
        <div class="order-total order-row">
            <span class="title text-bold">{{ 'orders.list.block.total_title'|trans([], 'AppBundle') }}&nbsp;</span>
            <span class="value">
                {{ order.amount| price(locale) }} {{ order.currency }}
            </span>
        </div>
    </div>

    <div class="block order-action" style="flex: 1">
        <div class="order-date order-row desktop-tablet-only">
            <span class="title text-bold">{{ 'orders.list.block.date_title'|trans([], 'AppBundle') }}&nbsp;</span>
            <span class="value tinh2">{{ order.createdOn|format_datetime('short', 'none', locale=locale) }}</span>
        </div>

        <div class="order_date order-row">
            {% if order.firstExpectedDate %}
                <span class="title text-bold">{{ 'orders.list.block.expectedDate'|trans([], 'AppBundle') }}</span>
                <span class="value">{{ order.firstExpectedDate|format_datetime('short', 'none', locale=locale) }}</span>
            {% endif %}
        </div>

        <div class="order_status order-row desktop-tablet-only">
            <span class="title">{{ 'orders.list.block.status'|trans([], 'AppBundle') }}</span>
            <span class="value">{{ ('orders.status.status_' ~ order.status) |trans }}</span>
        </div>
        <div class="order-links">
            <div class="order-row">
                <span class="details-link">
                    <a href="{{ path('front.order.detail', {'orderId': order.id }) }}">{{ 'orders.list.link.details'|trans([], 'AppBundle') }}</a>
                &nbsp;-&nbsp;</span>
                {% if companyId is defined %}
                    <span><a href="{{ path('front.order.export', {'companyId':companyId,'orderId': order.id}) }}">{{ 'orders.list.link.export'|trans([], 'AppBundle') }}</a></span>
                    <span>&nbsp;-&nbsp;</span>
                {% endif %}
                <span><a href="{{ path('front.order.newCart', {'orderId': order.id}) }}">{{ 'orders.list.link.buy_again'|trans([], 'AppBundle') }}</a></span>

                {% if order.documentFileUploadedNames is not empty %}
                    <span>&nbsp;-&nbsp;</span>

                    <span class="title" style="color: #6B6F82; font-weight: 600;">
                        {% if order.documentFileUploadedNames|length > 1 %}
                            {{ 'orders.list.link.documents'|trans([], 'AppBundle') }}:
                        {% else %}
                            {{ 'orders.list.link.document'|trans([], 'AppBundle') }}:
                        {% endif %}
                    </span>

                    {% for attrKey, attrName in order.documentFileUploadedNames %}
                        <div>
                            <a href="{{ path('front.order.singledocument.uploaded', {'orderId': order.id, 'attrKey': attrKey}) }}" target="_blank">{{ attrName }}</a>
                        </div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
    $("#cost-center-additional-info-icon").on('click', function (event) {
        if ($(this).data('open')) {
            $(this).data('open', false);
            $(this).html('<use xlink:href="#icon-minus"></use>');
            $(".cost-center-additional-info").css('display', 'block');

        } else {
            $(this).data('open', true);
            $(this).html('<use xlink:href="#icon-plus"></use>');
            $(".cost-center-additional-info").css('display', 'none');
        }

    });

    $(".js-onClickEditValidationNumber").click( function(e){
        e.preventDefault();
        onClickEditValidationNumber();
    });


    function onClickEditValidationNumber(){
        UI.Modal.show('js-add-reference-to-mycatalog-modal','Modal--add', $($('#js-add-reference-to-mycatalog-modal-tpl').html()), true, true);
    }
</script>


<script type="text/template" id="js-add-reference-to-mycatalog-modal-tpl">
    {{ form_start(formValidationNumber, {'attr':{'novalidate':true}}) }}
        <div class="Modal-body">

            <div class="form-group">
                {{ form_row(formValidationNumber.validationNumber) }}
            </div>
        </div>
        <div class="Modal-footer">
            {{ form_widget(formValidationNumber.save, {'attr': {'class': 'buttonModal'}}) }}
            {{ form_widget(formValidationNumber.cancel, {'attr': {'class': 'buttonModal'}}) }}
            {{ form_widget(formValidationNumber.delete, {'attr': {'class': 'buttonModal'}}) }}
        </div>
    {{ form_end(formValidationNumber) }}

</script>
