{% trans_default_domain 'AppBundle' %}
{# @var order \AppBundle\Model\View\OrderDetail\Order#}
{% set locale = app.request.locale %}
<div class="Page-content">
    <div class="Page-body">

        {# @var merchantOrder \AppBundle\Model\View\OrderDetail\MerchantOrder #}
        {% for merchantOrder in order.merchantOrders %}
            <div class="merchant-bloc-actions">
                <div>
                    <h2 class="merchant-title"><i class="arrow pointer expandable-arrow down"
                                                  data-merchant-id="{{ merchantOrder.merchantId }}"></i>
                        {{ merchantOrder.merchantName }} <span class="nb-products">({{ merchantOrder.items|length }}
                            {% if merchantOrder.items|length > 1 %}
                                {{ 'orders.list.merchant_products'|trans }})
                            {% else %}
                                {{ 'orders.list.merchant_product'|trans }})
                            {% endif %}</span>
                    </h2>
                    <div class="order-sub-id">
                        {{ 'orders.list.sub_order_id'|trans }} : {{ order.idNumber }} - {{ merchantOrder.merchantId }}
                    </div>
                    {# Update buyer internal Order ID #}
                    <div class="order-id order-row">
                        <span class="order-sub-id">{{ 'orders.list.block.buyer_internal_order_id' | trans }} : {{ merchantOrder.buyerInternalOrderId }}</span>
                        <div class="edit-buyer-internal-order-id update-icon"
                             style="background-image: url('{{ asset('images/pen.png') }}')"
                             data-template="js-edit-buyer-internal-order-id-tpl-{{ merchantOrder.id }}"
                        ></div>
                    </div>
                    <script type="text/template" id="js-edit-buyer-internal-order-id-tpl-{{ merchantOrder.id }}">
                        <form style="min-width: 450px;"
                              method="post"
                              action="{{ path("order_details_update_merchant_order_internal_id", {'orderMerchantId': merchantOrder.id}) }}"
                        >
                            <div class="Modal-body">
                                <input type="hidden" name="_token" value="{{ csrf_token('form') }}"/>
                                <div class="form-group">
                                    <label for="buyer_internal_reference">{{ 'orders.list.block.buyer_internal_order_id' | trans }}</label>
                                    <input type="text"
                                           id="buyer_internal_reference"
                                           name="value"
                                           placeholder="{{ 'orders.list.block.buyer_internal_order_id' | trans }}"
                                           value="{{ merchantOrder.buyerInternalOrderId }}"
                                    />
                                </div>
                            </div>
                            <div class="Modal-footer">
                                <button type="submit" class="buttonModal">{{ 'generic.save' | trans }}</button>
                                <button class="buttonModal">{{ 'generic.cancel' | trans }}</button>
                            </div>
                        </form>
                    </script>
                    {# end #}
                </div>

                <div class="right-block">
                    <div class="order-status">
                        <span class="title">{{ 'orders.list.block.status'|trans([], 'AppBundle') }}&nbsp;</span>
                        <span class="value">{{ ('orders.status.status_'~merchantOrder.status ) |trans([], 'AppBundle') }}</span>
                    </div>
                    <div class="order-links-mobile">
                        {% if merchantOrder.status != 2000 and merchantOrder.status != 3000 %}
                            {% if hasInvoice is defined and hasInvoice == true %}
                                <a href="{{ path('front.merchantOrder.invoices', {'merchantOrderId':merchantOrder.id}) }}">{{ 'invoice.seeInvoices' | trans }}</a>
                            {% endif %}
                            <div class="disputes-actions">
                                <a href="{{ path('front.merchant_order.export', {'merchantOrderId': merchantOrder.id}) }}">{{ 'orders.list.link.export'|trans([], 'AppBundle') }}</a>
                                -
                                <a href="{{ path('front.merchantOrder.dispute.create', {'merchantOrderId':merchantOrder.id}) }}">{{ 'dispute.form.new' |trans }}</a>
                                {% if merchantOrder.disputes is defined %}
                                    - <a
                                        href="{{ path('front.merchantOrder.dispute.list', {'merchantOrderId':merchantOrder.id}) }} ">{{ 'dispute.form.see' |trans }}</a>
                                {% endif %}
                            </div>
                        {% else %}
                            <a href="{{ path('front.merchant_order.export', {'merchantOrderId': merchantOrder.id}) }}">{{ 'orders.list.link.export'|trans([], 'AppBundle') }}</a>
                        {% endif %}
                    </div>
                </div>
            </div>
            <table id="{{ merchantOrder.merchantId }}" class="seller-table">
                <thead class="underline desktop-only">
                <tr>
                    <th class="label-detail" style="width: 40%">{{ 'cart.table_label.product_detail'|trans }}</th>
                    <th style="width: 15%">{{ 'cart.table_label.unit_price'|trans }}</th>
                    <th style="width: 15%">{{ 'cart.table_label.quantity'|trans }}</th>
                    <th style="width: 15%">{{ 'cart.table_label.expected_date'|trans }}</th>
                    <th style="width: 15%">{{ 'cart.table_label.total_price'|trans }}</th>
                </tr>
                </thead>
                <tbody>
                {% for item in merchantOrder.items %}
                    {# @var item \AppBundle\Model\View\OrderDetail\MerchantOrderItem #}
                    <tr>
                        <td class="underline">
                            <div class="product">
                                {% if item.name is not empty %}
                                    <a href="{{ path('front.offer.detail', {'productName' : item.name, 'ref': item.offerId})|replace({'%2F': '%252F'}) }}"></a>
                                {% else %}
                                    <a href="{{ path('front.offer.detail.short', {'ref': item.offerId}) }}"></a>
                                {% endif %}

                                <div class="img-container">
                                    {% if item.imageUrl is not empty %}
                                        <img src="{{ item.imageUrl }}"/>
                                    {% else %}
                                        <img src="/images/no_image_available.svg?v1'"/>
                                    {% endif %}
                                </div>


                                <div class="detail-content">
                                    <h5 class="name">{{ item.name }}</h5>
                                    <div class="incoterm">
                                        {% if item.incoterm is defined %}
                                            {{ item.incoterm }}

                                            {% if  item.toBeShipped %}
                                                <span class="with-shipping">
                                                {{ 'cart.fca_shipping'|trans }}
                                            </span>
                                            {% endif %}
                                        {% endif %}

                                        {% if item.dangerousProduct %}
                                            <div class="tooltip">
                                                &nbsp;
                                                <img src="{{ asset('/images/ico-inflammable.svg') }}" alt="Dangerous">
                                                <div class="tooltiptext">
                                                    <span class="info">
                                                        {{ 'Dangerous material'|trans }}
                                                    </span>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </div>

                                    {% if item.toBeShipped %}
                                        <div>
                                        <span class="with-shipping">
                                            <svg width="13px" height="11px" viewBox="0 0 13 11" version="1.1"
                                                 xmlns="http://www.w3.org/2000/svg">
                                                <g id="Page-1" stroke="none" stroke-width="1" fill="none"
                                                   fill-rule="evenodd">
                                                    <g id="Desktop/Shipping-Cart-V2-B1"
                                                       transform="translate(-340.000000, -640.000000)" fill="#A4A7B3">
                                                        <g id="Group-8-Copy"
                                                           transform="translate(338.000000, 619.000000)">
                                                            <path d="M2,27.2098214 L2,21.5 L4.2,21.5 L4.2,26.875 L10.7611607,26.875 L10.7611607,25.5915179 C10.7611607,25.405505 10.8262642,25.2473965 10.9564732,25.1171875 C11.0866822,24.9869785 11.2447907,24.921875 11.4308036,24.921875 C11.6168164,24.921875 11.7749249,24.9869785 11.9051339,25.1171875 L14.3046875,27.5167411 C14.4348965,27.6469501 14.5,27.8050586 14.5,27.9910714 C14.5,28.1770843 14.4348965,28.3351928 14.3046875,28.4654018 L11.9051339,30.8649554 C11.7749249,30.9951643 11.6168164,31.0602679 11.4308036,31.0602679 C11.2447907,31.0602679 11.0866822,30.9951643 10.9564732,30.8649554 C10.8262642,30.7347464 10.7611607,30.5766378 10.7611607,30.390625 L10.7611607,29.1071429 L2.33482143,29.1071429 C2.24181501,29.1071429 2.16276074,29.0745911 2.09765625,29.0094866 C2.03255176,28.9443821 2,28.8653278 2,28.7723214 L2,27.2098214 Z"
                                                                  id="Combined-Shape"></path>
                                                        </g>
                                                    </g>
                                                </g>
                                            </svg>&nbsp;<span
                                                    style="font-size: 10px;color: #FFF;border-radius: 2px;background-color: #9600FF;padding: 1px 10px;vertical-align: middle;">DAP {% if item.country %}({{ item.country|trans }}){% endif %}</span>
                                        </span>
                                        </div>
                                    {% endif %}
                                    {% if item.sellerRef is defined and item.sellerRef is not empty %}
                                        <div class="sellerRef">
                                            {{ 'product.seller_reference'|trans }} : {{ item.sellerRef|upper }}
                                        </div>
                                    {% endif %}
                                    {% if item.status  and item.status == '2000' %}
                                        <div class="order-item-status">
                                            {{ ('orders.status.status_'~item.status ) |trans([], 'AppBundle') }}
                                        </div>
                                    {% endif %}
                                    {# Buyer internal reference #}
                                    {% if item.buyerInternalReference is not null %}
                                    <div class="buyerRef">
                                        {{ 'product.buyer_reference' | trans }} : {{ item.buyerInternalReference | upper }}
                                    </div>
                                    {% endif %}
                                    {# Cart item comment #}
                                    {% if item.cartItemComment is not null %}
                                    <div class="buyerRef">
                                        {{ 'product.cart_item_comment' | trans }} : {{ item.cartItemComment }}
                                    </div>
                                    {% endif %}
                                    {# Order line #}
                                    <div class="order-id order-row">
                                        <span class="buyerRef">{{ 'orders.list.block.order_line' | trans }} : {{ item.orderLine}}</span>
                                        <div class="edit-order-line update-icon"
                                             style="background-image: url('{{ asset('images/pen.png') }}')"
                                             data-template="js-edit-order-line-tpl-{{ item.id }}"
                                        ></div>
                                    </div>
                                    <script type="text/template" id="js-edit-order-line-tpl-{{ item.id }}">
                                        <form style="min-width: 450px;"
                                              method="post"
                                              action="{{ path("order.details.update_item_extra_info", {'orderItemId': item.id}) }}"
                                              id="extrainfo_update_{{ item.id }}"
                                        >
                                            <div class="Modal-body">
                                                <input type="hidden" name="form[_token]" value="{{ csrf_token('form') }}"/>
                                                <input type="hidden" name="form[field]" value="order-line"/>

                                                <div class="form-group">
                                                    <label for="buyer_internal_reference">{{ 'orders.list.block.order_line' | trans }}</label>
                                                    <input type="text"
                                                           id="buyer_internal_reference"
                                                           name="form[value]"
                                                           placeholder="{{ 'orders.list.block.order_line' | trans }}"
                                                           value="{{ item.orderLine }}"
                                                    />
                                                </div>
                                            </div>
                                            <div class="Modal-footer">
                                                <button type="submit"
                                                        class="buttonModal">{{ 'generic.save' | trans }}</button>
                                                <button class="buttonModal">{{ 'generic.cancel' | trans }}</button>
                                            </div>
                                        </form>
                                    </script>
                                    {# end#}
                                    {# Frame contract #}
                                    {% if item.frameContract %}
                                        <div class="buyerRef">
                                            <img style="width:20px" src="{{ asset('images/sign.svg') }}" > {{ item.frameContract }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="underline">
                            <p class="mobile-max-only mobile-label">{{ 'cart.table_label.unit_price'|trans }} :</p>
                            <p class="mobile-center">
                                {{ item.price | price(locale) }} {{ item.currency.code|upper }}
                            </p>
                        </td>

                        {% set haveCartItemSplitDelivery = false %}
                        {% set haveQtyDemandDelivery = false %}
                        {% set haveQtyOnStockDelivery = false %}
                        {% set haveDateDemandDelivery = false %}
                        {% set haveDateOnStockDelivery = false %}
                        {% if item.realStock is not empty and item.qty_greater_real_stock %}
                            {% set haveCartItemSplitDelivery = item.cartItemSplitDelivery is not empty %}

                            {% set haveQtyDemandDelivery = haveCartItemSplitDelivery and item.cartItemSplitDelivery.demand_delivery.quantity is not empty %}
                            {% set haveQtyOnStockDelivery = haveCartItemSplitDelivery and item.cartItemSplitDelivery.on_stock_delivery.quantity is not empty %}
                            {% set haveDateDemandDelivery = haveCartItemSplitDelivery and item.cartItemSplitDelivery.demand_delivery.date is not empty %}
                            {% set haveDateOnStockDelivery = haveCartItemSplitDelivery and item.cartItemSplitDelivery.on_stock_delivery.date is not empty %}
                        {% endif %}

                        <td class="underline" style="position: relative; padding: 40px 0">
                            <p class="mobile-max-only mobile-label">{{ 'cart.table_label.quantity'|trans }} :</p>
                            <p class="mw-100">{{ item.quantity }}</p>
                            {% include('@OpenFront/component/cart-item-qty.html.twig') with {
                                'item': item,
                                'realStock': item.realStock,
                                'isShipping': false,
                                'haveQtyOnStockDelivery': haveQtyOnStockDelivery,
                                'haveQtyDemandDelivery ': haveQtyDemandDelivery ,
                            } %}
                        </td>
                        {% if item.deliveryDate is not null and item.status %}
                            <td class="underline" style="position: relative; padding: 40px 0">
                                <p class="mobile-max-only mobile-label">{{ 'cart.table_label.expected_date'|trans }}
                                    :</p>
                                <p class="mobile-center">
                                    {% if item.isPartiallyDelivered or item.isFullyDelivered %}
                                <div class="tooltip" style="">
                                    {% if item.isPartiallyDelivered %}
                                        <div class="shipping-indicator shipping-50"></div>
                                        <div class="tooltiptext tooltip-info-service">
                                            {{ 'orders.detail.shipping.last_delivery'|trans({'%separator%': ':'}) }} {{ item.lastDeliveredDate | format_datetime('short', 'none', locale=locale) }}
                                        </div>
                                    {% endif %}

                                    {% if item.isFullyDelivered %}
                                        <div class="shipping-indicator shipping-100"></div>
                                        <div class="tooltiptext tooltip-info-service">
                                            {{ 'orders.detail.shipping.delivered'|trans({'%separator%': ':'}) }} {{ item.lastDeliveredDate | format_datetime('short', 'none', locale=locale) }}
                                        </div>
                                    {% endif %}


                                </div>
                                {% endif %}
                                {% set format_date = (locale == 'fr' ? 'j/n/y' : 'n/j/y') %}
                                {{ haveQtyOnStockDelivery ? "-" : item.deliveryDate | format_datetime('short', 'none', locale=locale) }}
                                {% if item.realStock is not empty and item.qty_greater_real_stock %}
                                    {% include('@OpenFront/component/cart-item-date-split.html.twig') with {
                                        'item': item,
                                        'isShipping': false
                                    } %}
                                {% endif %}
                                </p>
                            </td>
                        {% else %}
                            <td class="underline desktop-only">
                                <p> - </p>
                            </td>
                        {% endif %}
                        {% if item.status and item.status != '2000' %}
                            <td class="underline">
                                <p class="mobile-max-only mobile-label">{{ 'cart.table_label.total_price'|trans }} :</p>
                                <p class="item-total-price mobile-center">
                                    {{ (item.price * item.quantity) | price(locale) }} {{ item.currency.code|upper }}
                                </p>
                            </td>
                        {% else %}
                            <td class="underline desktop-only">
                                <p> - </p>
                            </td>
                        {% endif %}
                    </tr>
                {% endfor %}

                {% if merchantOrder.shippingTotal %}
                    <tr class="shipping-cart-detail">
                        <td colspan="4" class="underline">
                            <div class="shipping-selector">
                                <div class="product">
                                    <div class="img-container">
                                        <img src="/images/Truck.svg">
                                    </div>
                                    <div class="detail-content">
                                        <h5 class="name">{{ 'orders.detail.shipping.title'|trans() }}</h5>
                                        <div class="buyerRef">
                                            {{ merchantOrder.shippingName|trans }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="mobile-align label-price underline">
                            <p class="item-total-price mobile-center">
                                {{ merchantOrder.shippingTotal  | price(locale) }} {{ order.currency|upper }}
                            </p>
                        </td>
                    </tr>
                {% endif %}

                {% if order.merchantOrders|length > 1 %}
                    <tr class="resume-table subtotal">
                        <td></td>
                        <td></td>
                        <td>{{ 'cart.table_label.subtotal'|trans }}</td>
                        <td></td>
                        <td class="total-without-vat">
                            {{ merchantOrder.amount | price(locale) }} {{ order.currency|upper }}
                        </td>
                    </tr>

                    {% for vat,total in merchantOrder.subTotalVat %}
                        {% if vat > 0 %}
                            <tr class="resume-table taxes">
                                <td></td>
                                <td></td>
                                <td>
                                    <p class="label-taxes label">{{ 'cart.table_label.taxes'|trans }}&nbsp;</p>
                                    <p class="vat-percent">{{ vat | number_format(2, '.') }}%</p>
                                </td>
                                <td></td>
                                <td>
                                    {{ total | price(locale) }} {{ order.currency|upper }}
                                </td>
                            </tr>
                        {% endif %}
                    {% endfor %}

                    <tr class="resume-table subtotal-vat">
                        <td></td>
                        <td></td>
                        <td>{{ 'cart.table_label.subtotal_vat'|trans }}</td>
                        <td></td>
                        <td class="total-cart">
                            {{ merchantOrder.amountVatIncluded | price(locale) }} {{ order.currency|upper }}
                        </td>
                    </tr>
                {% endif %}
                </tbody>
            </table>

            {% if merchantOrder.trackingError %}
            <div style="
                text-align: center;
                padding-top: 20px;
                padding-bottom: 20px;
                color: red;
            ">
                {{ 'orders.detail.tracking.error'|trans }}
            </div>
            {% endif %}

            {% if merchantOrder.shipments|length %}
                <div class="shipping-order-lists">
                    <input class="show-detail" type="checkbox" id="shipping-order-{{ merchantOrder.merchantId }}"/>
                    <label for="shipping-order-{{ merchantOrder.merchantId }}">
                        {{ 'orders.detail.tracking.title'|trans }}
                    </label>

                    {# @var shipment \AppBundle\Model\View\OrderDetail\Shipment #}
                    {% for shipment in merchantOrder.shipments %}
                        <div class="shipping-order-details">
                            <div class="shipping-order-list-title">
                                <div>
                                    <span class="shipping-name">{{ shipment.name }}</span>
                                    <span> {{ 'orders.detail.tracking.delivery_date'|trans({'%separator%': ':'}) }} {{ shipment.deliveryDate | format_datetime('short', 'none', locale=locale) }}</span>
                                </div>
                                <div class="shipping-status">
                                    {{ ('orders.detail.tracking.status_' ~ shipment.status)|trans }}
                                </div>
                            </div>
                            {# @var shipmentProduct \AppBundle\Model\View\OrderDetail\ShipmentProduct #}
                            {% for shipmentProduct in shipment.products %}
                                <div class="shipping-order-list-item">
                                    <div class="product">
                                        <div class="img-container">
                                            <img src="{{ shipmentProduct.imageUrl }}">
                                        </div>

                                        <div class="detail-content">
                                            <div class="name">{{ shipmentProduct.name }}</div>
                                            <div class="sellerRef">
                                                {{ 'orders.detail.tracking.vendor_ref'|trans({'%separator%': ':'}) }} {{ shipmentProduct.vendorRef }}
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        {{ 'orders.detail.tracking.quantity'|trans({'%separator%': ':'}) }} {{ shipmentProduct.quantity }}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endfor %}
    </div>
    <div class="Page-footer">
        <div class="total-container">
            <table id="table-merchant-total" class="seller-table table-total">
                <thead>
                <tr>
                    <th class="label-detail desktop-only"></th>
                    <th class="desktop-only"></th>
                    <th class="upperline"></th>
                    <th class="upperline"></th>
                </tr>
                </thead>
                <tbody>
                <tr class="resume-table subtotal">
                    <td class="desktop-only"></td>
                    <td class="desktop-only"></td>
                    <td class="label">{{ 'cart.table_label.total'|trans }}</td>
                    <td class="total-without-vat">
                        {{ order.amount | price(locale) }} {{ order.currency|upper }}
                    </td>
                </tr>
                {% for vat, total in order.subTotalVat %}
                    {% if vat > 0 %}
                        <tr class="total-table-taxes resume-table taxes">
                            <td class="desktop-only"></td>
                            <td class="desktop-only"></td>
                            <td class="label">
                                <p class="label-taxes label">{{ 'cart.table_label.taxes'|trans }} <span
                                            class="vat-percent">{{ vat | number_format(2, '.') }}%</span></p>
                            </td>
                            <td class="vat-value">
                                {{ total | price(locale) }} {{ order.currency|upper }}
                            </td>
                        </tr>
                    {% endif %}
                {% endfor %}
                <tr id="subtotal-vat-total" class="resume-table subtotal-vat">
                    <td class="desktop-only"></td>
                    <td class="desktop-only"></td>
                    <td class="label">{{ 'cart.table_label.total_vat'|trans }}</td>
                    <td class="total-cart">
                        {{ order.amountVatIncluded | price(locale) }} {{ order.currency|upper }}
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
