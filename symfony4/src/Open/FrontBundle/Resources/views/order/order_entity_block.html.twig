{% trans_default_domain 'AppBundle' %}
{% set locale = app.request.locale %}

<div class="order-block">
    <div class="block order-info">
        <div class="order-date order-row mobile-max-only">
            <span class="title">{{ 'orders.list.block.date_title'|trans([], 'AppBundle') }}&nbsp;</span>
            <span class="value">{{ order.createdOn|format_datetime('short', 'none', locale=locale) }}</span>
        </div>
        <div class="order-id order-row">
            <span class="title">{{ 'orders.list.block.order_title'|trans([], 'AppBundle') }}&nbsp;</span>
            <span class="value">
                {% if order.isPendingCreation %}
                    {{ 'orders.status.pending_creation'|trans([], 'AppBundle') }} ({{ order.id }})
                {% else %}
                    {{ order.numberId }}
                {% endif %}
            </span>
        </div>
        {% if  order.validationNumber is not null%}
            <div class="order-validation-number order-row">
                <span class="title">{{ 'orders.list.block.validation_number_title'|trans([], 'AppBundle') }}&nbsp;</span>
                <span class="value">{{ order.validationNumber }}</span>
            </div>
        {%  endif %}

        {% if order.site is not empty %}
            <div class="order-address order-row">
                <span class="title">{{ 'orders.list.block.cost_center_title'|trans([], 'AppBundle') }}&nbsp;</span>
                <span class="value">{{ order.site.name }}</span>
            </div>
        {% endif %}

        {% if order.address is not empty %}
            <div class="order-address order-row">
                <span class="title">{{ 'orders.list.block.address_title'|trans([], 'AppBundle') }}&nbsp;</span>
                <span class="value">{{ order.address.address }}, {{ order.address.address2 }} {{ order.address.zipCode }}
                    {{ order.address.city }}</span>
            </div>
        {% endif %}

        <div class="order_payment order-row">
            <span class="title">{{ 'orders.list.block.payment_terms'|trans([], 'AppBundle') }}</span>
            <span class="value">
            {% if order.paymentCode is defined %}
                {% if order.paymentCode == 'cb' %}
                    {{ 'payment_mode.Prepayment_creditcard'|trans([], 'AppBundle')  }}
                {% elseif order.paymentCode == 'bankwire' %}
                    {{ 'payment_mode.Prepayment_moneytransfert'|trans([], 'AppBundle')  }}
                {% else %}
                    {{ 'payment_mode.Termpayment_moneytransfert'|trans([], 'AppBundle')  }}
                {% endif %}
            {% else %}
                {{ 'payment_mode.Termpayment_moneytransfert'|trans([], 'AppBundle')  }}
            {% endif %}
        </span>
        </div>
        {% if order.reconciliation_key is defined and order.reconciliation_key is not empty %}
            <div class="order-row">
                <span class="title">{{ 'orders.list.block.iban_number' |trans([], 'AppBundle') }}</span>
                <span class="value">{{ order.iban }}</span>
            </div>
            <div class="order-row">
                <span class="title">{{ 'orders.list.block.key' |trans([], 'AppBundle') }}</span>
                <span class="value">{{ order.reconciliation_key }}</span>
            </div>
        {% endif %}
        <div class="order_status order-row mobile-max-only">
            <span class="title">{{ 'orders.list.block.status'|trans([], 'AppBundle') }}</span>
            <span class="value">
                  {% if order.isPendingCreation %}
                      pending creation
                  {% else %}
                      {{ ('orders.status.status_'~order.izbergStatus) |trans }}
                  {% endif %}
            </span>
        </div>
        <div class="order-total order-row">
            <span class="title">{{ 'orders.list.block.total_title'|trans([], 'AppBundle') }}&nbsp;</span>
            <span class="value">
            {% if locale == 'en' %}
                {{ order.amount| number_format('2', '.', ',') }}
            {% else %}
                {{ order.amount| number_format('2', ',', ' ')}}
            {% endif %}
                {{ order.currency }}
        </span>
        </div>
    </div>

        <div class="block order-action">
            <div class="order-date order-row desktop-tablet-only">
                <span class="title">{{ 'orders.list.block.date_title'|trans([], 'AppBundle') }}&nbsp;</span>
                <span class="value">{{ order.createdOn|format_datetime('short', 'none', locale=locale) }}</span>
            </div>

            <div class="order_date order-row">
                {% if firstExpectedDate %}
                    <span class="title">{{ 'orders.list.block.expectedDate'|trans([], 'AppBundle') }}</span>
                    <span class="value">{{ firstExpectedDate|format_datetime('short', 'none', locale=locale) }}</span>
                {% endif %}
            </div>

            <div class="order_status order-row desktop-tablet-only">
                <span class="title">{{ 'orders.list.block.status'|trans([], 'AppBundle') }}</span>
                <span class="value">
                    {% if order.isPendingCreation %}
                        pending creation
                    {% else %}
                        {{ ('orders.status.status_'~order.izbergStatus) |trans }}
                    {% endif %}
                </span>
            </div>
            {% if not order.isPendingCreation %}
            <div class="order-links">
                <div class="order-row">
            <span class="details-link">
                <a href="{{ path('front.order.detail', {'orderId': order.izbergId}) }}">{{ 'orders.list.link.details'|trans([], 'AppBundle') }}</a>
            &nbsp;-&nbsp;</span>
                    {% if companyId is defined %}
                        <span><a href="{{ path('front.order.export', {'companyId':companyId,'orderId': order.izbergId}) }}">{{ 'orders.list.link.export'|trans([], 'AppBundle') }}</a></span>
                        <span>&nbsp;-&nbsp;</span>
                    {% endif %}
                    <span><a href="{{ path('front.order.newCart', {'orderId': order.izbergId}) }}">{{ 'orders.list.link.buy_again'|trans([], 'AppBundle') }}</a></span>
                </div>
            </div>
            {% endif %}
        </div>

</div>

