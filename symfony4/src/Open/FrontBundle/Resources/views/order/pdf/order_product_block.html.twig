{% trans_default_domain 'AppBundle' %}
{# @var merchantOrderItem \AppBundle\Model\View\OrderDetail\MerchantOrderItem #}
{% set format_date = (locale == 'fr' ? 'd/m/y' : 'm/d/y') %}
<div style="width: 100%; padding: 0 20px;{{ first ? ' padding-top: 255px' }}">
    <table style="background-color: #FFFFFF; width: 100%; padding: 0 15px 0 35px; margin-top: 2px">
        <tbody>
            <tr>
                <td style="width: 55%">
                    <table>
                        <tbody>
                            <tr>
                                <td style="padding: 10px 0; height: 120px">
                                    {% if merchantOrderItem.imageUrl != "" %}
                                        <img src="{{ merchantOrderItem.imageUrl }}" style="width: 100px; height: auto; max-height: 100px"/>
                                    {% else %}
                                        <img src="images/no_image_available.png" style="width: 100px; height: auto; max-height: 100px"/>
                                    {% endif %}
                                </td>
                                <td style="padding-left: 30px">
                                    <h5 class="name" style="font-size: 16px">{{ merchantOrderItem.name }}</h5>
                                    <div style="font-size: 13px; color:#6B6F82">
                                        {{ 'header.vendor_ref'|trans }}:
                                        {{ merchantOrderItem.sellerRef }}
                                    </div>
                                    {% if merchantOrderItem.buyerInternalReference is not null %}
                                    <div style="font-size: 13px; color:#6B6F82">
                                        {{ 'product.buyer_reference' | trans }} : {{ merchantOrderItem.buyerInternalReference | upper }}
                                    </div>
                                    {% endif %}
                                    {% if merchantOrderItem.cartItemComment  is not null %}
                                    <div style="font-size: 13px; color:#6B6F82">
                                        {{ 'product.cart_item_comment' | trans }} : {{ merchantOrderItem.cartItemComment  | upper }}
                                    </div>
                                    {% endif %}
{#                                    <div style="font-size: 13px; color:#6B6F82">#}
{#                                        {{ 'header.delivery_delay'|trans }}:#}
{#                                        {% if locale == 'fr' %}#}
{#                                            {{ merchantOrderItem.deliveryDate|date('d/m/y') }}#}
{#                                        {% else %}#}
{#                                            {{ merchantOrderItem.deliveryDate|date('m/d/y') }}#}
{#                                        {% endif %}#}
{#                                    </div>#}
                                    {% if merchantOrderItem.orderLine is not null or merchantOrderItem.orderLine is not empty %}
                                    <div style="font-size: 13px; color:#6B6F82">{{ 'orders.list.block.order_line' | trans }} : {{ merchantOrderItem.orderLine}}</div>
                                    {% endif %}
                                    <div style="font-size: 13px; color:#6B6F82">
                                        {{ 'header.incoterm'|trans }}:
                                            {{ merchantOrderItem.incoterm }}
                                            {% if merchantOrderItem.toBeShipped %}
                                                <span class="with-shipping">
                                                    {{ 'cart.fca_shipping'|trans }}
                                                </span>
                                            {% endif %}

                                        {% if merchantOrderItem.toBeShipped %}
                                            <div>
                                        <span class="with-shipping">
                                            <svg width="13px" height="11px" viewBox="0 0 13 11" version="1.1"
                                                 xmlns="http://www.w3.org/2000/svg">
                                                <g id="Page-1" stroke="none" stroke-width="1" fill="none"
                                                   fill-rule="evenodd">
                                                    <g id="Desktop/Shipping-Cart-V2-B1"
                                                       transform="translate(-340.000000, -640.000000)" fill="#A4A7B3">
                                                        <g id="Group-8-Copy"
                                                           transform="translate(338.000000, 619.000000)">
                                                            <path d="M2,27.2098214 L2,21.5 L4.2,21.5 L4.2,26.875 L10.7611607,26.875 L10.7611607,25.5915179 C10.7611607,25.405505 10.8262642,25.2473965 10.9564732,25.1171875 C11.0866822,24.9869785 11.2447907,24.921875 11.4308036,24.921875 C11.6168164,24.921875 11.7749249,24.9869785 11.9051339,25.1171875 L14.3046875,27.5167411 C14.4348965,27.6469501 14.5,27.8050586 14.5,27.9910714 C14.5,28.1770843 14.4348965,28.3351928 14.3046875,28.4654018 L11.9051339,30.8649554 C11.7749249,30.9951643 11.6168164,31.0602679 11.4308036,31.0602679 C11.2447907,31.0602679 11.0866822,30.9951643 10.9564732,30.8649554 C10.8262642,30.7347464 10.7611607,30.5766378 10.7611607,30.390625 L10.7611607,29.1071429 L2.33482143,29.1071429 C2.24181501,29.1071429 2.16276074,29.0745911 2.09765625,29.0094866 C2.03255176,28.9443821 2,28.8653278 2,28.7723214 L2,27.2098214 Z"
                                                                  id="Combined-Shape"></path>
                                                        </g>
                                                    </g>
                                                </g>
                                            </svg>&nbsp;<span
                                                    style="font-size: 10px;color: #FFF;border-radius: 2px;background-color: #9600FF;padding: 1px 10px;vertical-align: middle;">DAP {% if merchantOrderItem.country %}({{ merchantOrderItem.country|trans }}){% endif %}</span>
                                        </span>
                                            </div>
                                        {% endif %}
                                        {% if merchantOrderItem.frameContract %}
                                            <div class="buyerRef">
                                                {{ 'invoice.detail.frame_contract'|trans }}: {{ merchantOrderItem.frameContract }}
                                            </div>
                                        {% endif %}
                                    </div>
                                    {% if merchantOrderItem.status  and merchantOrderItem.status == '2000' %}
                                        <div class="order-item-status">
                                            {{ ('orders.status.status_'~merchantOrderItem.status ) |trans([], 'AppBundle') }}
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </td>
                <td style="color:#6B6F82; width: 15%; text-align: center">
                    {{ merchantOrderItem.price| price(locale)}}
                    {{ merchantOrderItem.currency.code|upper }}
                </td>
                {% set haveCartItemSplitDelivery = false %}
                {% set haveQtyDemandDelivery = false %}
                {% set haveQtyOnStockDelivery = false %}
                {% set haveDateDemandDelivery = false %}
                {% set haveDateOnStockDelivery = false %}
                {% if merchantOrderItem.realStock is not empty and merchantOrderItem.qty_greater_real_stock %}
                    {% set haveCartItemSplitDelivery = merchantOrderItem.cartItemSplitDelivery is not empty %}

                    {% set haveQtyDemandDelivery = haveCartItemSplitDelivery and merchantOrderItem.cartItemSplitDelivery.demand_delivery.quantity is not empty %}
                    {% set haveQtyOnStockDelivery = haveCartItemSplitDelivery and merchantOrderItem.cartItemSplitDelivery.on_stock_delivery.quantity is not empty %}
                    {% set haveDateDemandDelivery = haveCartItemSplitDelivery and merchantOrderItem.cartItemSplitDelivery.demand_delivery.date is not empty %}
                    {% set haveDateOnStockDelivery = haveCartItemSplitDelivery and merchantOrderItem.cartItemSplitDelivery.on_stock_delivery.date is not empty %}
                {% endif %}
                <td style="color:#6B6F82; width: 15%; text-align: center; position: relative; ">
                    <div style="margin-bottom: 20px">{{ merchantOrderItem.quantity }}</div>
                    {% include('@OpenFront/component/cart-item-qty.html.twig') with {
                        'item': merchantOrderItem,
                        'realStock': merchantOrderItem.realStock,
                        'isShipping': false,
                        'haveQtyOnStockDelivery': haveQtyOnStockDelivery,
                        'haveQtyDemandDelivery ': haveQtyDemandDelivery ,
                    } %}
                </td>
                <td style="color:#6B6F82; width: 15%; text-align: center; position: relative; ">
                    {{ haveQtyOnStockDelivery ? "-" : merchantOrderItem.deliveryDate | date(format_date) }}
                    {% if merchantOrderItem.realStock is not empty and merchantOrderItem.qty_greater_real_stock %}
                        {% include('@OpenFront/component/cart-item-date-split.html.twig') with {
                            'item': merchantOrderItem,
                            'isShipping': false
                        } %}
                    {% endif %}
                </td>
                <td style="color:#6B6F82; width: 15%; text-align: center">
                    {{ (merchantOrderItem.price * merchantOrderItem.quantity) | price(locale)}}
                    {{ merchantOrderItem.currency.code|upper }}
                </td>
            </tr>
        </tbody>
    </table>
</div>
