{% trans_default_domain 'AppBundle' %}
{# @var merchantOrder \AppBundle\Model\View\OrderDetail\MerchantOrder #}
<div style="width: 100%; padding: 0 20px;{{ first ? ' padding-top: 255px' }};">
    <table style="background-color: #FFFFFF; width: 100%; padding: 15px 15px 10px 35px; margin-top: 2px">
        <tbody>
            <tr>
                <td colspan="2" style="padding-bottom: 20px">
                    <h2 class="merchant-title">{{ merchantOrder.merchantName }}</h2>
                    <div style="font-size: 13px">
                        <strong>{{ 'orders.list.sub_order_id'|trans }}:</strong> {{ orderId }} - {{ merchantOrder.merchantId }}
                    </div>
                    <div style="font-size: 13px">
                        <strong>{{ 'orders.list.block.buyer_internal_order_id' | trans }}:</strong> {{ merchantOrder.buyerInternalOrderId }}
                    </div>
                </td>
                <td colspan="2" style="text-align: right">
                    <strong class="title">{{ 'orders.list.block.status'|trans([], 'AppBundle') }}&nbsp;</strong>
                    <span class="value">{{ ('orders.status.status_'~merchantOrder.status ) |trans([], 'AppBundle') }}</span>
                </td>
            </tr>
            <tr>
                <td style="color:#A4A7B3; font-size:13px; width: 55%">{{ 'cart.table_label.product_detail'|trans }}</td>
                <td style="color:#A4A7B3; font-size:13px; width: 15%; text-align: center">{{ 'cart.table_label.unit_price'|trans }}</td>
                <td style="color:#A4A7B3; font-size:13px; width: 15%; text-align: center">{{ 'cart.table_label.quantity'|trans }}</td>
                <td style="color:#A4A7B3; font-size:13px; width: 15%; text-align: center">{{ 'header.delivery_delay'|trans }}</td>
                <td style="color:#A4A7B3; font-size:13px; width: 15%; text-align: center">{{ 'cart.table_label.total_price'|trans }}</td>
            </tr>
        </tbody>
    </table>
</div>
