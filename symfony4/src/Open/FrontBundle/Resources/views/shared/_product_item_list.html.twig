{% trans_default_domain 'AppBundle' %}
{# @var product AppBundle\Model\Offer #}

<div class="Product">

   <div class="Product-compare">
       {% if is_granted("IS_AUTHENTICATED_REMEMBERED") and not product.limited %}
       <a href="{{ path('comparisonSheet.add', {'offerId' : product.izbergReference, 'currency' : product.currency})  }}">
           <svg class="Icon">
               <use xlink:href="#icon-comparison"></use>
           </svg>
           {{ 'product.comparison'|trans }}
       </a>
       {% endif %}
   </div>

    {% set offerTitle =  '- Undefined -' %}
    {% if previousUrl is defined %}
        {% set frontOfferDetailUrl = path('front.offer.detail.short', {'ref': product.izbergReference, 'previousURL' : previousUrl}) %}
        {% if pagination is defined %}
            {% set frontOfferDetailUrl = path('front.offer.detail.short', {'ref': product.izbergReference , 'page' : pagination.current_page, 'previousURL' : previousUrl}) %}
        {% endif %}

        {% if product.offerTitle is not empty %}
            {% set offerTitle = product.offerTitle %}
            {% set frontOfferDetailUrl = path('front.offer.detail', {'productName' : product.offerTitle|e('url'), 'ref': product.izbergReference, 'previousURL' : previousUrl})|replace({'%2F': '%252F'}) %}
            {% if pagination is defined %}
                {% set frontOfferDetailUrl = path('front.offer.detail', {'productName' : product.offerTitle|e('url'), 'ref': product.izbergReference , 'previousURL' : previousUrl})|replace({'%2F': '%252F'}) %}
            {% endif %}
        {% endif %}
    {% else %}
        {% set frontOfferDetailUrl = path('front.offer.detail.short', {'ref': product.izbergReference}) %}
        {% if pagination is defined %}
            {% set frontOfferDetailUrl = path('front.offer.detail.short', {'ref': product.izbergReference , 'page' : pagination.current_page}) %}
        {% endif %}

        {% if product.offerTitle is not empty %}
            {% set offerTitle = product.offerTitle %}
            {% set frontOfferDetailUrl = path('front.offer.detail', {'productName' : product.offerTitle|e('url'), 'ref': product.izbergReference})|replace({'%2F': '%252F'}) %}
            {% if pagination is defined %}
                {% set frontOfferDetailUrl = path('front.offer.detail', {'productName' : product.offerTitle|e('url'), 'ref': product.izbergReference})|replace({'%2F': '%252F'}) %}
            {% endif %}
        {% endif %}
    {% endif %}



   <div class="Product-body">
       {% if product.offerPictures is not empty  %}
           {% if frontOfferDetailUrl is not empty %}
               <a href="{{ frontOfferDetailUrl  }}">
                   <div class="Product-image" style="background-image:url(' {{ asset ( product.offerPictures[0] ) }} ');"></div>
               </a>
           {% else %}
               <div class="Product-image" style="background-image:url(' {{ asset ( product.offerPictures[0] ) }} ');"></div>
           {% endif %}
       {% endif %}
        <div style="flex:2 1 0;max-width: calc(50% - 70px);display: flex;flex-direction: column;justify-content: center;padding-right: 10px;">
            <div class="Product-reference">{{ product.sellerRef }}</div>

            <div class="tooltip tooltip-product-name">
               <div class="Product-name">
                  <a href="{{ frontOfferDetailUrl }}">{{ offerTitle }}</a>
               </div>
               <div class="tooltiptext">
                   {{ offerTitle }}
               </div>
            </div>
        </div>
        <div class="Product-info">
            <div class="details" style="margin: 0">{{ 'product.seller'|trans }} : <span class="details-value">{{ product.merchant.name }}</span></div>
            <div class="details">{{ 'product.manufacturer'|trans }} : <span class="details-value">{{ product.manufacturerName }}</span></div>
            <div class="details">
                <span class="Product-incoterm">
                    {{ product.incoterm }}
                    {% if product.incotermCountry and is_granted("IS_AUTHENTICATED_REMEMBERED")%}
                        ({{ product.incotermCountry }})
                    {% endif %}
                    {% if product.shippable %}
                        <img style="width:20px" src="{{ asset('/images/Truck.svg') }}" />
                    {% endif %}
                    {% if product.getFrameContract() %}
                        <img style="width:20px" src="{{ asset('images/sign.svg') }}" />
                    {% endif %}
                 </span>
            </div>
        </div>

      <div class="display-mobile" style="flex: 1 1 0">
          <div class="Product-price">
              {% if not product.limited %}
                      <div class="value">
                          {% if locale == 'en' %}
                              {{ product.prices[product.currency]| number_format('2', '.', ',') }}
                          {% else %}
                              {{ product.prices[product.currency]| number_format('2', ',', ' ')}}
                          {% endif %}
                          <span class="currency-indicator">
                     {% if product.currency == "EUR" %}
                         €
                     {% elseif product.currency == "USD" %}
                         $
                     {% endif %}
                 </span>
                      </div>
                      <div class="tooltip">
                          {% if product.prices.EUR is defined and product.currency == "USD" %}
                              <div class="eur-info">
                                  ({% if locale == 'en' %}
                                      {{ product.prices["EUR"]| number_format('2', '.', ',') }}
                                  {% else %}
                                      {{ product.prices["EUR"]| number_format('2', ',', ' ')}}
                                  {% endif %} €)
                              </div>
                          {% elseif  product.prices.USD is defined and product.currency == "EUR" %}
                              <div class="eur-info">
                                  ({% if locale == 'en' %}
                                      {{ product.prices["USD"]| number_format('2', '.', ',') }}
                                  {% else %}
                                      {{ product.prices["USD"]| number_format('2', ',', ' ')}}
                                  {% endif %} $)
                              </div>
                          {% endif %}
                          <div class="tooltiptext">
                              <span class="info">{{ 'product.info_converted_price'|trans }}</span>
                              {{ 'product.converted_price'|trans }}
                          </div>
                          {% if product.quantityPerSku and product.skuUnit %}
                              <div class="sku-info">{{ 'offer_detail.price_quantity'|trans({'%quantity%': product.quantityPerSku, '%unit%': product.skuUnit }) }}</div>
                          {% endif %}
                      </div>
              {% endif %}
          </div>

            <div>
                {% if product.noPrice and product.businessEverywhere %}
                    {% include '@OpenFront/shared/btn_ask_vendor_price_item_list.html.twig' with { product: product } %}
                {% else %}
                    {% if product.limited and product.bafv and product.businessEverywhere %}
                        {% include '@OpenFront/shared/btn_ask_vendor_price_item_list.html.twig' %}
                    {% endif %}
                {% endif %}
            </div>
      </div>
   </div>
</div>
