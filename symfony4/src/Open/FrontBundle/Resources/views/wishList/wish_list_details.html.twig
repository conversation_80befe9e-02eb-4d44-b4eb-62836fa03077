{% extends '@OpenFront/menu/buyer_menu.html.twig' %}
{% trans_default_domain 'AppBundle' %}

{% block title %}
    {{ 'menu.desktop.wishlist' | trans }}
{% endblock %}

{% block body %}
    <div class="Page-inner">
        <div class="previous-block">
            <a class="previous-page" href="{{ path('front.wishlist') }}"><i
                    class="arrow left"></i>{{ 'wishlist.go_back'|trans|upper }}</a>
        </div>

        {% set locale = app.session.get("_locale") %}
        <div id="locale" style="display:none">{{ locale }}</div>

        {% if offers|length > 0 %}
            <div class="top-wishlist">
                <h1 class="wishlist-title">{{ wishlist.name }}</h1>

                {% if wishlistValid %}
                    <button
                        onclick="charge({{ wishlist.id }}, '{{ wishlist.currency }}', {{ offers|length }})">{{ 'wishlist.add_to_cart'|trans|upper }}</button>
                {% else %}
                    {{ 'wishlist.notification_message.no_price_offer' | trans }}
                {% endif %}
            </div>

            <table class="wishlist-container wishlist-offer-list">
                <thead class="desktop-only">
                <tr>
                    <th style="width: 30%">
                        {{ 'cart.table_label.product_detail'|trans }}
                    </th>
                    <th style="width: 15%; text-align: left">
                        {{ 'wishlist.table.delivery_time_item'|trans }}
                    </th>
                    <th class="label-price" style="width: 15%">{{ 'cart.table_label.unit_price'|trans }}</th>
                    <th class="quantity" style="width: 15%">
                        {{ 'cart.table_label.quantity'|trans }}
                    </th>
                    <th class="label-price" style="width: 15%">{{ 'cart.table_label.total_price'|trans }}</th>
                    <th style="width: 10%"></th>
                </tr>
                </thead>
                <tbody>
                {% set maxtime = 0 %}
                {% for offer in offers %}
                    <tr class="wishlist wishlist-row {% if offer.offer.noprice %}invalid{% endif %}">
                        <td class="details">
                            <div class="product">
                                {% set frontOfferDetailUrl = '#' %}
                                {% if offer.offer.offerTitle is defined and offer.offer.offerTitle is not empty and offer.offer.izbergReference is defined and offer.offer.izbergReference is not empty %}
                                    {% set frontOfferDetailUrl = path('front.offer.detail', {'productName' : offer.offer.offerTitle|e('url'), 'ref': offer.offer.izbergReference})|replace({'%2F': '%252F'}) %}
                                {% endif %}

                                {% if offer.offer.offerPictures is defined and offer.offer.offerPictures is not empty %}
                                    <div class="img-container">
                                        <a href="{{ frontOfferDetailUrl }}">
                                            <img src="{{ offer.offer.offerPictures[0] }}">
                                        </a>
                                    </div>
                                {% endif %}
                                <div class="product-details">
                                    {% if offer.offer.sellerRef is defined %}
                                        <div class="Product-reference">{{ offer.offer.sellerRef }}</div>
                                    {% endif %}

                                    {% if offer.offer.offerTitle is defined and offer.offer.offerTitle is not empty %}
                                        <a href="{{ frontOfferDetailUrl }}">
                                            <h3 class="title">{{ offer.offer.offerTitle }}</h3>
                                        </a>
                                    {% endif %}
                                    <div class="Product-info">
                                        <div>{{ 'product.seller'|trans }} : <span
                                                class="details-value">{{ offer.offer.merchant.name }}</span></div>
                                        <div>{{ 'product.manufacturer'|trans }} : <span
                                                class="details-value">{{ offer.offer.manufacturerName }}</span></div>
                                    </div>

                                    {% if offer.offer.incoterm is defined and offer.offer.incoterm is not empty %}
                                        <div class="Product-incoterm">
                                            {{ offer.offer.incoterm }}
                                            {% if offer.offer.incotermCountry is defined and offer.offer.incotermCountry is not empty %}
                                                ({{ offer.offer.incotermCountry }})
                                            {% endif %}
                                            {% if offer.offer.shippable %}
                                                <img style="margin-left: 5px;width:30px" src="/images/Truck.svg">
                                            {% endif %}
                                            {% if offer.offer.getFrameContract is not empty %}
                                                <span class="sellerRef">
                                                    <img style="width:20px" src="{{ asset('images/sign.svg') }}">
                                                </span>
                                            {% endif %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="actions mobile-only">
                                <button onclick="onDelete({{ offer.id }})">
                                    <svg class="Icon">
                                        <use xlink:href="#icon-site-delete"></use>
                                    </svg>
                                </button>
                            </div>
                        </td>
                        <td class="delivery-time">
                            <div class="mobile-only">{{ 'wishlist.table.delivery_time_item'|trans }} :</div>
                            <div>
                                {% if offer.offer.deliveryTime is defined and offer.offer.deliveryTime is not empty and not offer.offer.noPrice %}
                                    {% if offer.offer.deliveryTime > maxtime %}
                                        {% set maxtime = offer.offer.deliveryTime %}
                                    {% endif %}
                                    {{ offer.offer.deliveryTime }} {{ 'wishlist.table.days'|trans }}
                                {% else %}
                                    -
                                {% endif %}
                            </div>
                        </td>
                        <td class="unit-price">
                            {% if offer.price is defined %}
                                <div class="mobile-only">{{ 'cart.table_label.unit_price'|trans }} :</div>
                                <div>
                                    <span id="unit-price-offer-{{ offer.offerId }}">
                                        {% if locale == "en" %}
                                            {{ offer.price | number_format('2', '.', ',') }}
                                        {% else %}
                                            {{ offer.price | number_format('2', ',', ' ') }}
                                        {% endif %}
                                    </span>
                                    {{ offer.offer.currency|upper }}
                                </div>
                            {% endif %}
                        </td>
                        <td>
                            <div class="quantity">
                                <div class="mobile-only">
                                    {{ 'cart.table_label.quantity'|trans }} :
                                </div>
                                <div id="quantitySave_{{ offer.id }}" class="save-quantity">{{ offer.quantity }}</div>
                                <input id="quantity_{{ offer.id }}" type="number" value="{{ offer.quantity }}"
                                       min="{{ offer.offer.moq }}" max="{{ offer.offer.quantity }}"
                                       onchange="quantityChange('{{ offer.id }}', '{{ offer.offer.moq }}', '{{ offer.offer.quantity }}', '{{ offer.offer.batchSize }}', '{{ offer.offerId }}');"/>
                            </div>
                        </td>
                        <td class="total-price">
                            {% if offer.price is defined %}
                                <div class="mobile-only">{{ 'cart.table_label.total_price'|trans }} :</div>
                                <div>
                                    <span id="total-price-offer-{{ offer.offerId }}" class="total-price-container">
                                        {% if locale == "en" %}
                                            {{ (offer.price * offer.quantity) | number_format('2', '.', ',') }}
                                        {% else %}
                                            {{ (offer.price * offer.quantity) | number_format('2', ',', ' ') }}
                                        {% endif %}
                                    </span>
                                    {{ offer.offer.currency|upper }}
                                </div>
                            {% endif %}
                        </td>
                        <td class="actions desktop-only">
                            <div class="button-align">
                                <button onclick="onDelete({{ offer.id }})">
                                    <svg class="Icon">
                                        <use xlink:href="#icon-site-delete"></use>
                                    </svg>
                                </button>
                            </div>
                        </td>
                    </tr>
                {% endfor %}
                <tr class="wishlist-total-container">
                    <td class="desktop-only"></td>
                    <td class="delivery-total">
                        <div class="title-delivery">
                            {{ 'wishlist.table.delivery_time_wishlist'|trans }} :
                        </div>
                        <div>
                            {{ maxtime }} {{ 'wishlist.table.days'|trans }}
                        </div>
                    </td>
                    <td class="desktop-only">{{ 'cart.table_label.total_vat'|trans|upper }} :</td>
                    <td class="desktop-only"></td>
                    <td class="offer-total-price">
                        <div class="mobile-only">{{ 'cart.table_label.total_vat'|trans|upper }} :</div>
                        <div>
                                <span id="total-wishlist">
                                    {% if locale == "en" %}
                                        {{ totalPrice | number_format('2', '.', ',') }}
                                    {% else %}
                                        {{ totalPrice | number_format('2', ',', ' ') }}
                                    {% endif %}
                                </span>
                            {{ offers[0].offer.currency|upper }}
                        </div>
                    </td>
                    <td class="desktop-only"></td>
                </tr>
                </tbody>
            </table>
        {% else %}
            <div class="top-wishlist">
                <h1 class="wishlist-title">{{ wishlist.name }}</h1>
            </div>
            <div class="wishlist-no-items">
                {{ 'wishlist.item.noItem'|trans }}
            </div>
        {% endif %}

    </div>
{% endblock %}

{% block javascripts %}
    <script type="text/javascript">

        function charge(wishlistId, currency, size) {
            var message = "{{ 'wishlist.charge.confirm'|trans({'%currency%': 'curName'}, 'AppBundle')|raw }}";
            message = message.replace("curName", currency);
            var chargeModal = window.UI.Modal.confirm("", message,
                function () {
                    loadingModal = window.UI.Modal.showLoading();
                    var url = '{{ path("front.wishlist.charge", {"wishlistId" : "id" }) }}';
                    url = url.replace("id", wishlistId);
                    $.ajax({
                        type: 'POST',
                        url: url,
                        success: function (data) {
                            if (data !== 0) {
                                if (currency === 'USD') {
                                    $("#cart-quantity-usd").html(data);
                                    $("#cart-quantity-usd").parent().removeClass("hide");
                                }
                                if (currency === 'EUR') {
                                    $("#cart-quantity-eur").html(data);
                                    $("#cart-quantity-eur").parent().removeClass("hide");
                                }
                            }
                            var redirect = "{{ path('cart.details.before_buy', {'currencyOrCartId' : 'currencyToReplace'}) }}";
                            redirect = redirect.replace("currencyToReplace", currency);
                            window.location.href = redirect;
                        },
                        error: function (error) {
                            location.reload();
                        }
                    });
                }, function () {
                    chargeModal.close();
                });
        }

        function onDelete(itemId) {
            deleteModal = window.UI.Modal.confirm("", "{{ 'wishlist.item.delete_confirm'|trans|raw }}",
                function () {
                    loadingModal = window.UI.Modal.showLoading();
                    $.ajax({
                        type: 'DELETE',
                        url: '{{ path('wishlist.item.delete') }}',
                        data: {
                            'itemId': itemId
                        },
                        success: function () {
                            location.reload();
                            messageModal = window.UI.Modal.alert("{{ 'wishlist.item.delete.success'|trans({}, 'AppBundle')|raw }}");
                        },
                        error: function () {
                            messageModal = window.UI.Modal.alert("{{ 'wishlist.item.delete.error'|trans({}, 'AppBundle')|raw }}");
                        }
                    });
                }, function () {
                    deleteModal.close();
                });
        }

        function quantityChange(itemId, moq, stock, batchSize, offerId) {
            let quantityInput = $('#quantity_' + itemId);
            let quantitySaved = $('#quantitySave_' + itemId);
            let qty = quantityInput.val();
            let qtySaved = parseInt(quantitySaved.html());
            qty = parseInt(qty);
            moq = parseInt(moq);
            stock = parseInt(stock);
            batchSize = parseInt(batchSize);
            if (qty <= 0) {
                quantityInput.val(qtySaved);
                window.UI.Modal.alert("{{ 'offer_detail.wrong_quantity'|trans|raw }}");
            } else if (qty < moq) {
                let wrongQuantity = "{{ 'offer_detail.too_small_quantity'|trans|raw }}";
                wrongQuantity = wrongQuantity.replace(new RegExp('%min%', 'g'), moq);
                quantityInput.val(qtySaved);
                window.UI.Modal.alert(wrongQuantity);
            } else if (qty > stock) {
                let wrongQuantity = "{{ 'offer_detail.too_much_quantity'|trans|raw }}";
                wrongQuantity = wrongQuantity.replace(new RegExp('%max%', 'g'), stock);
                quantityInput.val(qtySaved);
                window.UI.Modal.alert(wrongQuantity);
            } else if (qty % batchSize > 0) {
                let wrongBatchSize = "{{ 'offer_detail.not_batch_size_multiple'|trans|raw }}";
                wrongBatchSize = wrongBatchSize.replace(new RegExp('%batchSize%', 'g'), batchSize);
                quantityInput.val(qtySaved);
                window.UI.Modal.alert(wrongBatchSize);
            } else {
                loadingModal = window.UI.Modal.showLoading();
                $.ajax({
                    type: 'POST',
                    url: '{{ path('wishlist.item.update') }}',
                    data: {
                        'itemId': itemId,
                        'quantity': qty,
                        'offerId': offerId
                    },
                    success: function (data) {
                        let unitPrice = parseFloat(data);
                        if ($('#locale').text() === 'en') {
                            $('#total-price-offer-' + offerId).html(new Intl.NumberFormat('en', {minimumFractionDigits: 2}).format((qty * unitPrice).toFixed(2)));
                            $('#unit-price-offer-' + offerId).html(new Intl.NumberFormat('en', {minimumFractionDigits: 2}).format((unitPrice).toFixed(2)))
                        } else {
                            $('#total-price-offer-' + offerId).html(new Intl.NumberFormat('fr-FR', {minimumFractionDigits: 2}).format((qty * unitPrice).toFixed(2)));
                            $('#unit-price-offer-' + offerId).html(new Intl.NumberFormat('fr-FR', {minimumFractionDigits: 2}).format((unitPrice).toFixed(2)))
                        }

                        let totalPrice = 0.0;
                        $('.total-price-container').each(function () {
                            if ($('#locale').text() === 'en') {
                                totalPrice += parseFloat($(this).text().toString().replace(/\s+/g, '').replace(',', ''));
                            } else {
                                totalPrice += parseFloat($(this).text().toString().replace(/\s+/g, '').replace(',', '.'));
                            }
                        });
                        if ($('#locale').text() === 'en') {
                            $('#total-wishlist').html(new Intl.NumberFormat('en', {minimumFractionDigits: 2}).format(totalPrice.toFixed(2)));
                        } else {
                            $('#total-wishlist').html(new Intl.NumberFormat('fr-FR', {minimumFractionDigits: 2}).format(totalPrice.toFixed(2)));
                        }
                        quantitySaved.html(qty);
                        messageModal = window.UI.Modal.alert("{{ 'wishlist.item.update.success'|trans({}, 'AppBundle') }}");
                    },
                    error: function () {
                        quantityInput.val(qtySaved);
                        messageModal = window.UI.Modal.alert("{{ 'wishlist.item.update.error'|trans({}, 'AppBundle') }}");
                    }
                });
            }
        }
    </script>
{% endblock %}
