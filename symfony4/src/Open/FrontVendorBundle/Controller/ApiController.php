<?php

namespace Open\FrontVendorBundle\Controller;

use AppBundle\Entity\BafvRequest;
use AppBundle\Entity\UserBafvMerchantList;
use AppBundle\Entity\Vendor;
use AppBundle\Exception\SpecificPriceException;
use AppBundle\Repository\BafvRequestRepository;
use AppBundle\Repository\UserBafvMerchantListRepository;
use AppBundle\Services\MailService;
use AppBundle\Services\OfferCatalogService;
use AppBundle\Services\SpecificPriceService;
use Exception;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Translation\Exception\NotFoundResourceException;
use Symfony\Contracts\Translation\TranslatorInterface;

class ApiController extends AbstractController
{
    private const TRANSLATION_DOMAIN = 'AppBundle';

    /**
     * @param Request               $request
     * @param BafvRequestRepository $bafvRequestRepository
     * @return JsonResponse
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/front/vendor/price-request', name: 'front.vendor.price.request', options: ['i18n' => false])]
    public function getBafvRequest(Request $request,BafvRequestRepository $bafvRequestRepository): JsonResponse
    {

        $merchantId = $this->getUser()->getId();
        $companyName = $request->get('company') ?? null;

        $requests = $bafvRequestRepository->findByFilter($merchantId, $companyName);

        return $this->json($requests);
    }

    /**
     * @param Request                        $request
     * @param TranslatorInterface            $translator
     * @param MailService                    $mailService
     * @param BafvRequestRepository          $bafvRequestRepository
     * @param UserBafvMerchantListRepository $bafvMerchantListRepository
     * @return JsonResponse
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/front/vendor/price-request-update-status', name: 'front.vendor.price.update.status', options: ['i18n' => false])]
    public function updateStatusOfTheRequest(Request $request, TranslatorInterface $translator, MailService $mailService, BafvRequestRepository $bafvRequestRepository, UserBafvMerchantListRepository $bafvMerchantListRepository)
    {
        $id = $request->get('id');
        $status = (int)$request->get('status');
        $rejectionReason = $request->get('rejectionReason');
        /** by using ssso the UserId has the Merchant id */
        /** AppBundle\Security\IzbergVendorAuthenticator line 58 */
        $user = $this->getUser();
        $merchantId = $this->getUser()->getId();


        try {
            /** @var BafvRequest $bafvRequest */
            $bafvRequest = $bafvRequestRepository->find($id);

            if (empty($bafvRequest)) {
                throw new NotFoundResourceException('BAFV Request not found');
            }

            if ($bafvRequest->getMerchantId() === $merchantId) {
                $company = $bafvRequest->getCompany();
                $bafvRequest->setStatus($status);
                $bafvRequest->setRejectionReason($rejectionReason);
                $bafvRequestRepository->save($bafvRequest);

                $bafvMerchantList = $bafvMerchantListRepository->findOneBy([
                    'company' => $company,
                    'merchantId' => $merchantId
                ]);

                $companyName = '';

                if (!empty($this->getUser())) {
                    if (!empty($this->getUser()->getCompanyName())) {
                        $companyName = $this->getUser()->getCompanyName();
                    }
                }

                if ($status == BafvRequest::STATUS_ACCEPTED) {
                    if (empty($bafvMerchantList)) {
                        $bafvMerchantList = (new UserBafvMerchantList())
                            ->setMerchantId($merchantId)
                            ->setCompany($company);

                        $bafvMerchantListRepository->save($bafvMerchantList);

                        $mailService->sendEmailMessage(MailService::BAFV_REQUEST_APPROVED, $bafvRequest->getUser()->getLocale(), $bafvRequest->getUser()->getEmail(), [
                            'vendorName' => $companyName
                        ]);
                    }
                }

                if ($status == BafvRequest::STATUS_REJECT) {
                    if (!empty($bafvMerchantList)) {
                        $bafvMerchantListRepository->delete($bafvMerchantList);
                    }

                    $mailService->sendEmailMessage(MailService::BAFV_REQUEST_REJECTED, $bafvRequest->getUser()->getLocale(), $bafvRequest->getUser()->getEmail(), [
                        'rejectionReason' => $rejectionReason,
                        'vendorName' => $companyName
                    ]);
                }
            }

        } catch (Exception $e) {
            return $this->json(['message' => $e->getMessage()], Response::HTTP_NOT_FOUND);
        }

        return $this->json(['message' =>
            $translator->trans('merchant.update', [], self::TRANSLATION_DOMAIN)
        ]);
    }

    /**
     * @param SpecificPriceService $specificPriceService
     * @return JsonResponse
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/front/vendor/specific-prices-request', name: 'front.vendor.specific.prices.request', options: ['i18n' => false], methods: ['GET'])]
    public function getSpecificPricesRequest(SpecificPriceService $specificPriceService): JsonResponse
    {
        /** @var Vendor $merchant */
        $merchant = $this->getUser();

        return $this->json($this->getSpecificPrices($specificPriceService, $merchant));
    }

    /**
     * @param Request $request
     * @param SpecificPriceService $specificPriceService
     * @return JsonResponse
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/front/vendor/specific-prices-update-request', name: 'front.vendor.specific.prices.update.request', options: ['i18n' => false], methods: ['POST'])]
    public function setSpecificPricesRequest(Request $request, SpecificPriceService $specificPriceService): JsonResponse
    {
        /** @var UploadedFile $file */
        $file = $request->files->get('specific_prices');

        /** @var Vendor $merchant */
        $merchant = $this->getUser();

        try {
            $specificPriceService->updateSpecificPrices($file, $merchant->getId());
            return $this->json($this->getSpecificPrices($specificPriceService, $merchant));

        } catch (SpecificPriceException $exception) {
            return new JsonResponse([
                'message' => $exception->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * @param Request $request
     * @param SpecificPriceService $specificPriceService
     * @return StreamedResponse
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/front/vendor/specific-prices-export-request', name: 'front.vendor.specific.prices.export.request', options: ['i18n' => false], methods: ['POST'])]
    public function exportSpecificPricesRequest(Request $request, SpecificPriceService $specificPriceService): StreamedResponse
    {
        /** @var Vendor $merchant */
        $merchant = $this->getUser();
        $requestContent = json_decode($request->getContent());
        $companyCode = $requestContent->companyCode;

        return new StreamedResponse(
            function () use ($specificPriceService, $merchant, $companyCode) {
                if ($companyCode) {
                    $specificPriceService->exportCompanySpecificPricesToCsv($merchant, $companyCode);
                } else {
                    $specificPriceService->exportAllSpecificPricesToCsv($merchant);
                }
            },
            Response::HTTP_OK,
            [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="specific-prices.csv"',
            ]
        );
    }

    /**
     * @param Request             $request
     * @param OfferCatalogService $offerCatalogService
     * @param null                $locale
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/front/vendor/offer-catalog/export/{type}', name: 'vendor.offer_catalog_export_for_merchant_all')]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/front/vendor/offer-catalog/export/{type}/{locale}', name: 'vendor.offer_catalog_export_for_merchant')]
    public function exportOfferCatalogForMerchant(Request $request, $type, OfferCatalogService $offerCatalogService, $locale = null)
    {
        $merchant = $this->getUser();
        if(!$merchant instanceof Vendor){
            return new StreamedResponse(null,
                Response::HTTP_FORBIDDEN
            );
        }
        return new StreamedResponse(
            function () use ($offerCatalogService, $merchant, $type, $locale) {
                $offerCatalogService->export(false, $merchant->getId(), $locale, $type);
            },
            Response::HTTP_OK,
            [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="offer-catalog.csv"',
            ]
        );
    }

    /**
     * @param SpecificPriceService $specificPriceService
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/specificPricesAutomation', name: 'specific_prices_automation')]
    public function specificPricesAutomation(SpecificPriceService $specificPriceService): Response
    {
        $specificPricesPath = $this->getParameter('kernel.project_dir').'/var/tmp_specific_prices/';
        $specificPriceService->processImportAutomation($specificPricesPath);

        return new JsonResponse([], Response::HTTP_OK);
    }


    private function getSpecificPrices(SpecificPriceService $specificPriceService, Vendor $merchant): array
    {
        return [
            'buyersCount' => $specificPriceService->getSpecificPricesBuyersCount($merchant),
            'productsCount' => $specificPriceService->getSpecificPricesProductsCount($merchant),
            'items' => $specificPriceService->getSpecificPricesAsBuyerSpecificPrices($merchant),
        ];
    }
}
