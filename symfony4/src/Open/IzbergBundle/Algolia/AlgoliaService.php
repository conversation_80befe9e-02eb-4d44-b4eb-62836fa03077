<?php

namespace Open\IzbergBundle\Algolia;

use Algolia\AlgoliaSearch\Exceptions\AlgoliaException;
use Algolia\AlgoliaSearch\SearchClient;
use Algolia\AlgoliaSearch\SearchIndex;
use Exception;

abstract class AlgoliaService
{

    /** @deprecated */
    public const RANKING_BY_NEWEST = 'newest';

    public const OR_OPERATOR = 'OR';
    public const AND_OPERATOR = 'AND';

    public const ALGOLIA_HITS_PER_PAGE_PARAM = 'hitsPerPage';
    public const ALGOLIA_NB_PAGES_PARAM = 'nbPages';
    public const ALGOLIA_HITS_PARAM = 'hits';
    public const ALGOLIA_NB_HITS_PARAM = 'nbHits';
    public const ALGOLIA_PAGE_PARAM = 'page';
    public const ALGOLIA_ATTRIBUTES_TO_RETRIEVE_PARAM = 'attributesToRetrieve';
    public const ALGOLIA_FACETS = 'facets';

    public const RANKING_BY_RELEVANCE = 'relevance';
    public const RANKING_BY_PRICE_DESC = 'price_max';
    public const RANKING_BY_PRICE_ASC = 'price_min';
    public const RANKING_BY_DELIVERY_TIME_ASC = 'delivery_time_min';


    private const EQUAL_STRING = ':';
    private const EQUAL_NUM = '=';

    private const DEFAULT_INT_PER_PAGE = 20;

    private SearchIndex $index;

    /**
     * @var string $baseIndexName
     */
    private $baseIndexName;

    /**
     * @var string $applicationID
     */
    private $applicationID;

    /**
     * @var string $apiKey
     */
    private $apiKey;

    /**
     * @var string $writeApiKey
     */
    private $writeApiKey;

    /**
     * @var int|null
     */
    private $transportCategory;

    public function __construct(string $applicationID, string $apiKey, string $baseIndexName, string $writeApiKey)
    {
        $this->applicationID = $applicationID;
        $this->apiKey = $apiKey;
        $this->writeApiKey = $writeApiKey;
        $this->baseIndexName = $baseIndexName;
        $this->setRanking();
    }

    public function getTransportCategory(): ?int
    {
        return $this->transportCategory;
    }

    public function setTransportCategory(?int $transportCategory): self
    {
        $this->transportCategory = $transportCategory;
        return $this;
    }

    public function getBaseIndexName(): string
    {
        return $this->baseIndexName;
    }

    public function setBaseIndexName(string $baseIndexName): void
    {
        $this->baseIndexName = $baseIndexName;
    }

    public function buildFullName($replicaName = ''): string
    {
        return $this->baseIndexName . $replicaName;
    }

    public function getReplicaNames(): array
    {
        return [
            $this->buildFullName(self::RANKING_BY_RELEVANCE),
            // VOLUNTARY HIDE THE FOLLOWING REPLICAS TO AVOID BUILDING TOO MANY REPLICAS IN MULTI LANGUAGES MODE
            //$this->buildFullName(self::RANKING_BY_PRICE_ASC),
            //$this->buildFullName(self::RANKING_BY_PRICE_DESC),
            //$this->buildFullName(self::RANKING_BY_DELIVERY_TIME_ASC),
        ];
    }

    public function getShortName(string $replicaName): string
    {
        return substr($replicaName, strlen($this->getBaseIndexName()));
    }

    public function getDefaultReplicaName(): string
    {
        return $this->buildFullName(self::RANKING_BY_RELEVANCE);
    }

    /**
     * @param mixed $ranking
     * @throws Exception
     */
    public function setRanking($ranking = null): void
    {
        if ((null === $ranking) || ('' === $ranking)) {
            $ranking = $this->getDefaultReplicaName();
        } else {
            $ranking = $this->buildFullName($ranking);
        }

        // protect against unwanted values
        if (in_array($ranking, $this->getReplicaNames(), true)) {
            $selectedRanking = $ranking;
        } else {
            $selectedRanking = $this->getDefaultReplicaName();
        }

        $indexName = $selectedRanking;
        $client = SearchClient::create($this->applicationID, $this->apiKey);
        $this->index = $client->initIndex($indexName);
    }

    /**
     * @param string $indexName
     * @return SearchIndex
     * @throws AlgoliaException
     * @throws Exception
     */
    public function getWriteIndex(string $indexName): SearchIndex
    {
        $client = SearchClient::create($this->applicationID, $this->writeApiKey);

        return $client->initIndex($indexName);
    }

    ////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // REPLICAS MANAGEMENT
    ////////////////////////////////////////////////////////////////////////////////////////////////////////////

    /**
     * Adds a replica index on an index
     * A replica index is mandatory to manage sorting of results.
     *
     * @param $replicaName
     *
     * @throws AlgoliaException
     */
    public function addReplica($replicaName)
    {
        $writeIndex = $this->getWriteIndex($this->baseIndexName);
        $this->deleteReplica($replicaName);

        // Get replicas
        $replicas = $writeIndex->getSettings()['replicas'] ?? [];

        $replicas[] = $replicaName;
        self::updateIndexSettings($writeIndex, ['replicas' => array_unique($replicas)]);
    }

    /**
     * @param string $replicaName
     * @throws AlgoliaException
     */
    public function deleteReplica(string $replicaName): void
    {
        $writeIndex = $this->getWriteIndex($this->baseIndexName);

        $replicas = $writeIndex->getSettings()['replicas'] ?? null;

        if (!$replicas) {
            return;
        }

        $replicas = array_values(array_diff($replicas, [$replicaName]));
        self::updateIndexSettings($writeIndex, ['replicas' => $replicas]);
    }

    /**
     * @param array $settings
     */
    public static function updateIndexSettings(SearchIndex $index, array $settings)
    {
        $taskID = $index->setSettings($settings)['taskID'] ?? null;

        // Wait for task to complete
        if ($taskID) {
            $index->waitTask($taskID);
        }
    }

    /**
     * Very simple function to build an Algolia Where clause:
     *   - Can't add several times the same attribute in the clause.
     *
     * @param array $conditions the list of column=>value
     * @param string $operator the logical operator
     *
     * @return string the algolia where clause to use as filter
     * @deprecated
     *
     */
    public static function buildAlgoliaClause(array $conditions, string $operator)
    {
        $result = '';
        $cnt = count($conditions);
        $index = 0;
        foreach ($conditions as $column => $value) {
            if (is_string($value)) {
                $result .= $column . self::EQUAL_STRING . $value;
            } elseif (is_numeric($value)) {
                $result .= $column . self::EQUAL_NUM . (string)$value;
            }

            if (++$index !== $cnt) {
                $result .= ' ' . $operator . ' ';
            }
        }

        return $result;
    }

    /**
     * Very simple function to build an Algolia Where clause
     *     - unique attribute with possibles values.
     *
     * @param string $attributeName the name of the attribute
     * @param array $values possible values for this attribute
     * @param string $operator the logical operator
     *
     * @return string the algolia where clause to use as filter
     * @deprecated
     *
     */
    public static function buildAlgoliaClauseForSameAttribute($attributeName, $values, $operator)
    {
        $result = '';

        $cnt = count($values);
        foreach ($values as $index => $value) {
            if (is_string($value)) {
                $result .= $attributeName . self::EQUAL_STRING . $value;
            } elseif (is_numeric($value)) {
                $result .= $attributeName . self::EQUAL_NUM . (string)$value;
            }

            $index = intval($index);

            if (++$index !== $cnt) {
                $result .= ' ' . $operator . ' ';
            }
        }

        return $result;
    }

    /**
     * find all the results for the specified search and parameters.
     *
     * @param $search
     * @param $args
     * @param string $attributesToRetrieve
     *
     * @return array
     * @throws AlgoliaException
     *
     */
    public function search($search, $args, $attributesToRetrieve = '*')
    {
        $args[self::ALGOLIA_HITS_PER_PAGE_PARAM] = self::DEFAULT_INT_PER_PAGE;
        $args[self::ALGOLIA_PAGE_PARAM] = 0;

        $args[self::ALGOLIA_ATTRIBUTES_TO_RETRIEVE_PARAM] = $attributesToRetrieve;
        $results = [];
        $results[self::ALGOLIA_HITS_PARAM] = [];

        do {
            $response = $this->index->search($search, $args);
            $nbPages = $response[self::ALGOLIA_NB_PAGES_PARAM] ?? 0;
            $page = $response[self::ALGOLIA_PAGE_PARAM] ?? 0;
            ++$args[self::ALGOLIA_PAGE_PARAM];
            $results[self::ALGOLIA_HITS_PARAM] = array_merge($results[self::ALGOLIA_HITS_PARAM], $response[self::ALGOLIA_HITS_PARAM]);
            $results[self::ALGOLIA_HITS_PARAM] += $response[self::ALGOLIA_HITS_PARAM];

            if (isset($response[self::ALGOLIA_FACETS])) {
                // Retrieve FACETS
                $results[self::ALGOLIA_FACETS] = $response[self::ALGOLIA_FACETS];
            } else {
                $results[self::ALGOLIA_FACETS] = [];
            }
        } while ($page < $nbPages - 1);

        $results[self::ALGOLIA_HITS_PER_PAGE_PARAM] = self::DEFAULT_INT_PER_PAGE;
        $results[self::ALGOLIA_NB_HITS_PARAM] = count($results[self::ALGOLIA_HITS_PARAM]);
        $results[self::ALGOLIA_NB_PAGES_PARAM] = $nbPages;

        return $results;
    }

    public function searchForFacetValues($facetName, $facetQuery)
    {
        return $this->index->searchForFacetValues($facetName, $facetQuery);
    }

    /**
     * perform a paginated request to algolia.
     *
     * @param $search
     * @param bool $disjunctiveSearch
     * @param AlgoliaQueryParams $queryParams
     * @param int $hitsPerPage the number of hits per page
     * @param int $page the page number to get
     *
     * @return mixed
     * @throws AlgoliaException
     */
    public function paginatedSearch($search, bool $disjunctiveSearch, AlgoliaQueryParams $queryParams, $hitsPerPage, $page)
    {
        if ($this->transportCategory) {
            $queryParams->addFacetFilters(sprintf(
                '%s:%s',
                AlgoliaField::PRODUCT_CATEGORY,
                '-' . $this->transportCategory
            ));
        }

        // You can not use disjunctive faceting and the filters parameter
        if ($disjunctiveSearch) {
            $queryParams->resetFilters();
        }

        //$args = ($disjunctiveSearch) ? $queryParams->disjunctiveArray() : $queryParams->toArray();
        $args = $queryParams->toArray();

        $args[self::ALGOLIA_HITS_PER_PAGE_PARAM] = $hitsPerPage;
        $args[self::ALGOLIA_PAGE_PARAM] = $page;

        return $this->index->search($search, $args);
    }

    /**
     * @param string $query
     * @param array $queryParams
     * @return mixed
     * @throws AlgoliaException
     */
    public function browse(string $query = '', array $queryParams = [])
    {
        return $this->getWriteIndex($this->getBaseIndexName())->browseObjects($queryParams);
    }
}
