<?php

namespace Open\IzbergBundle;

use AppBundle\Entity\User;
use AppBundle\Services\AlstomCustomAttributes;
use AppBundle\Services\SecurityService;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use JMS\Serializer\SerializerInterface;
use Open\IzbergBundle\Api\ApiClientManager;
use Open\IzbergBundle\Api\ApiConfigurator;
use Open\IzbergBundle\Api\ApiException;
use Open\IzbergBundle\Api\IzbergUtils;
use Open\IzbergBundle\Model\M2MResponse;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Exception\SessionNotFoundException;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Unirest;
use Symfony\Component\HttpFoundation\RequestStack;

abstract class Api implements LoggerAwareInterface
{
    ////////////////////////////////////////
    /// ALL API CONSTANTS
    ////////////////////////////////////////

    public const HTTP_GET_OPERATION = "GET";
    public const HTTP_POST_OPERATION = "POST";
    public const HTTP_PUT_OPERATION = "PUT";
    public const HTTP_PATCH_OPERATION = "PATCH";
    public const HTTP_DELETE_OPERATION = "DELETE";

    public const LOG_IZBERG_API_ERROR = "IZBERG_API_ERROR";
    public const SECURITY_TOKEN_STORAGE = "security.token_storage";

    //session attributes
    public const IZBERG_ACCESS_TOKEN = "izberg_access_token";
    public const IZBERG_USERNAME = "open_izberg.username";
    public const IZBERG_USER_ID = "izberg_user_id";

    public const HEADER_CONTENT_TYPE = 'Content-Type';
    public const HEADER_ACCEPT_LANGUAGE = 'Accept-Language';
    public const HEADER_AUTHORIZATION = 'Authorization';

    public const CONTENT_JSON = 'application/json';

    public const IZBERG_CART_EUR_ID = "IZBERG_CART_EUR_ID";
    public const IZBERG_CART_USD_ID = "IZBERG_CART_USD_ID";
    public const IZBERG_SESSION_ID = "IZBERG_SESSION_ID";

    public const IZBERG_ANONYMOUS_USERNAME = 'Anonymous';

    public const OPTION_429_RETRY = '429Retry';
    private const OPTION_429_RETRY_NUMBER = '429RetryNumber';
    private const OPTION_429_RETRY_PAUSE = '429RetryPause';
    private const OPTION_429_RETRY_LIMIT = '429RetryLimit';

    public const OPTION_DATA_ARRAY = 'dataArray';
    public const OPTION_IS_OPERATOR = 'isOperator';
    public const OPTION_NO_DATA_RETURN = 'noDataReturn';
    public const OPTION_USE_M2M_AUTH = 'm2m';

    private const PARAM_TYPE_INTEGER = '(?P<integer>\d+)';
    private const PARAM_TYPE_STRING = '(?P<string>\w+)';

    private ApiClientManager $apiClientManager;
    protected LoggerInterface $logger;
    private TokenStorageInterface $tokenStorage;
    private SessionInterface $session;
    protected SerializerInterface $serializer;
    private SecurityService $securityService;
    protected AlstomCustomAttributes $customAttributes;
    protected Client $client;

    private $requestStack;

    public function __construct(
        ApiClientManager $apiClientManager,
        TokenStorageInterface $tokenStorage,
        RequestStack $requestStack,
        SerializerInterface $serializer,
        SecurityService $securityService,
        AlstomCustomAttributes $customAttributes
    )
    {
        $this->apiClientManager = $apiClientManager;
        $this->tokenStorage = $tokenStorage;
        $this->requestStack = $requestStack;
        $this->serializer = $serializer;
        $this->securityService = $securityService;
        $this->customAttributes = $customAttributes;
        $this->client = new Client(['http_errors' => false]);
    }

    abstract public function getUri();

    abstract public function getItemClass();

    public function findByFilters(array $filters = [], bool $isOperator = false): \Generator
    {
        $response = null;

        //build query uri
        $filterString = IzbergUtils::buildQueryUriFromFilters($filters);

        $requestUrl = $this->getUri() . $filterString;
        //iterate over izberg pagination
        do {
            try {
                $options = [];
                $options [self::OPTION_IS_OPERATOR] = $isOperator;

                $response = $this->sendApiRequest(self::HTTP_GET_OPERATION, $requestUrl, null, [], $options);
                foreach ($response->body->objects as $object) {
                    yield $this->serializer->deserialize(json_encode($object), $this->getItemClass(), 'json');
                }
                $requestUrl = $response->body->meta->next;

            } catch (Exception $e) {
                $this->logger->error(
                    "an unexpected error occurred while invoking izberg API",
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => Api::LOG_IZBERG_API_ERROR,
                        "message" => $e->getMessage(),
                        "url" => $this->getUri() . $filterString
                    ])
                );
                $this->throwGenericError(
                    ($response) ? $response->code : 0,
                    $this->getUri() . $filterString
                );
            }
        } while ($requestUrl !== null);
    }

    public function findOneByFilters(array $filters = [], bool $isOperator = false)
    {
        $requestUrl = $this->getUri() . IzbergUtils::buildQueryUriFromFilters($filters);

        $options = [];
        $options [self::OPTION_IS_OPERATOR] = $isOperator;

        $response = $this->sendApiRequest(self::HTTP_GET_OPERATION, $requestUrl, null, [], $options);

        foreach ($response->body->objects as $object) {
            return $this->serializer->deserialize(json_encode($object), $this->getItemClass(), 'json');
        }

        return null;
    }

    public function find(int $id, bool $isOperator = false)
    {
        $requestUrl = $this->getUri() . '/' . $id . '/';

        $options = [];
        $options [self::OPTION_IS_OPERATOR] = $isOperator;

        $response = $this->sendApiRequest(self::HTTP_GET_OPERATION, $requestUrl, null, [], $options);
        return $this->serializer->deserialize(json_encode($response->body), $this->getItemClass(), 'json');
    }

    /**
     * Generate bearer string
     * @param $username
     * @param $token
     * @param string|null $applicationNamespace
     * @return string
     */
    public function generateBearer($username, $token, $applicationNamespace = null): string
    {
        if ($applicationNamespace === null) {
            return 'Bearer ' . $username . ':' . $token;
        }

        return 'Bearer ' . $username . ':' . $applicationNamespace . ':' . $token;
    }

    private function extractBearerUsernameFromAuthorizationHeaders(array $authorizationHeaders): ?string
    {
        $bearer = $authorizationHeaders[self::HEADER_AUTHORIZATION] ?? null;
        $start = 0;
        if (strpos(strtoupper($bearer), "BEARER ") !== FALSE) {
            $start = 7;
        }
        return !$bearer ? null : substr(
            $bearer,
            $start,
            strpos($bearer, ':') - $start
        );
    }

    /**
     * get authorization headers to call izberg API
     * @param bool $operator if true create headers to call api with operator user. if false, try to get authorization info from session. If it doesn't exist, create anonymous token
     * @return array the HTTP authorization headers
     */
    public function getAuthorizationHeaders(bool $operator = false, bool $useM2mAuth = false): array
    {
        $authorizationHeaders = [];

        if ($this->isOperator($operator)) {
            $authorizationHeaders[self::HEADER_CONTENT_TYPE] = self::CONTENT_JSON;

            if ($useM2mAuth) {
                $authorizationHeaders[self::HEADER_AUTHORIZATION] = 'Bearer ' . $this->authenticateOperatorWithM2M()->accessToken;
            } else {
                $authorizationHeaders[self::HEADER_AUTHORIZATION] = $this->getAuthorization();
            }

            return $authorizationHeaders;
        }

        if ($this->isAsyncUserAction()) {

            $authorizationHeaders = [
                self::HEADER_CONTENT_TYPE => self::CONTENT_JSON,
                self::HEADER_AUTHORIZATION => $this->getUserAuthorization()
            ];

            return $authorizationHeaders;
        }

        try {
            $this->session = $this->requestStack->getSession();
            if ($this->session->get(Api::IZBERG_ACCESS_TOKEN)) {

                if ($this->session->get(Api::IZBERG_USERNAME) !== Api::IZBERG_ANONYMOUS_USERNAME) {
                    $authorization = $this->generateBearer($this->session->get(Api::IZBERG_USERNAME), $this->session->get(Api::IZBERG_ACCESS_TOKEN));
                } else {
                    $authorization = $this->generateBearer(
                        $this->session->get(self::IZBERG_USERNAME),
                        $this->session->get(Api::IZBERG_ACCESS_TOKEN),
                        $this->getApplicationNamespace()
                    );
                }

                $authorizationHeaders = [
                    self::HEADER_CONTENT_TYPE => self::CONTENT_JSON,
                    self::HEADER_AUTHORIZATION => $authorization
                ];

                return $authorizationHeaders;
            }
        } catch (SessionNotFoundException $sessionNotFoundException) { }

        $this->logger->debug(
            "unable to build Izberg authorization headers: no IZBERG_ACCESS_TOKEN found in session",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME => EventNameEnum::TECHNICAL_ERROR,
            ])
        );

        return [];
    }

    public function isOperator(bool $forceOperator): bool
    {
        return (
            in_array($this->apiClientManager->inUseConnection(), ['console', 'webhook', 'operator'])
            || $forceOperator
        );
    }

    public function isAsyncUserAction(): bool
    {
        return ($this->apiClientManager->inUseConnection() === ApiConfigurator::CONNECTION_ASYNC_USER_ACTION);
    }

    /**
     * format a object to json to be posted as body to the izberg api
     * @param array $data the array to format in json
     * @return string the json as a string
     * @throws ApiException
     */
    public function formatObjectToJson($data)
    {
        try {
            return Unirest\Request\Body::json($data);
        } catch (Unirest\Exception $e) {
            $this->writeGenericErrorLog(sprintf("Error while formating data to be posted to the izberg API : %s", print_r($data, true)), []);
            throw new ApiException(sprintf("Error while formating data to be posted to the izberg API : %s", print_r($data, true)), $e);
        }
    }

    /**
     * write a error log for the API
     * @param string $message
     * @param array $context
     */
    public function writeGenericErrorLog(string $message, array $context)
    {
        $authenticatedUsername = "anonymous";
        if ($this->tokenStorage->getToken() !== null &&
            $this->tokenStorage->getToken()->getUser() !== null &&
            $this->tokenStorage->getToken()->getUser() instanceof User) {
            $authenticatedUsername = $this->tokenStorage->getToken()->getUser()->getUsername();
        }

        $context[LogUtil::EVENT_NAME] = Api::LOG_IZBERG_API_ERROR;
        $context[LogUtil::USER_NAME] = $authenticatedUsername;

        $this->logger->error($message, LogUtil::buildContext($context));
    }

    /**
     * Throw a basic error
     * @param int $code the http status code from the api
     * @param string $apiCall the API URL called
     * @param string $message the izberg message
     * @param array $externalContext additional key/value information for this error
     * @throws ApiException
     */
    public function throwGenericError($code, $apiCall, $message = null, array $externalContext = []): never
    {
        if (empty($message)) {
            $exception = new ApiException('An error occurred while invoking Izberg API.', $code);
        } else {
            $exception = new ApiException('An error occurred while invoking Izberg API: ' . $message . $code, $code);
            $exception->setIzbergMessage($message);

            //try to get an izberg code
            $json = $this->decodeJson($message);
            if (!is_null($json) && property_exists($json, "errors")) {
                $error = $json->errors[0];
                if (property_exists($error, "code") && is_int($error->code)) {
                    $exception->setIzbergCode($json->errors[0]->code);

                }
            }
        }
        $exception->setApplicationId($this->getApplicationId());
        $exception->setApiCall($apiCall);
        $exception->setExternalContext($externalContext);
        $this->writeGenericErrorLog('An error occurred while invoking Izberg API. Izberg code:', $exception->getContext());
        throw $exception;

    }

    /**
     * decode a json string
     * @param string $string the string to decode
     * @return mixed
     */
    private function decodeJson($string)
    {
        $result = json_decode($string);
        if (json_last_error() == JSON_ERROR_NONE) {
            return $result;
        } else {
            return null;
        }
    }

    /**
     * Get API Url without version
     * @return string
     */
    public function getBaseUrl()
    {
        return $this->apiClientManager->getConfiguration()->getProtocol() . '://' . $this->apiClientManager->getConfiguration()->getDomain() . '/';
    }

    /**
     * Get API Url with version number
     * @return string
     */
    public function getApiUrl()
    {
        return $this->apiClientManager->getConfiguration()->getApiUrl();
    }

    /**
     * Get API Version number (without 'V')
     * @return integer
     */
    public function getVersion()
    {
        return $this->apiClientManager->getConfiguration()->getVersion();
    }

    /**
     * Get Authorization operator token
     */
    public function getAuthorization(): string
    {
        return $this->generateBearer(
            $this->apiClientManager->getConfiguration()->getUsername(),
            $this->apiClientManager->getConfiguration()->getAccessToken()
        );
    }

    public function getUserAuthorization(): string
    {
        return $this->generateBearer(
            $this->apiClientManager->getConfiguration()->getUserUsername(),
            $this->apiClientManager->getConfiguration()->getUserAccessToken()
        );
    }

    /**
     * Get Application Id
     * @return string
     */
    public function getApplicationId()
    {
        return $this->apiClientManager->getConfiguration()->getApplicationId();
    }

    public function getCreateMerchantUserUrl(): string
    {
        return (string) $this->apiClientManager->getConfiguration()->getCreateMerchantUrl();
    }

    public function getIdentityApiUrl(): string
    {
        return (string) $this->apiClientManager->getConfiguration()->getIdentityApiUrl();
    }

    /**
     * Get Application Namespace
     * @return string
     */
    public function getApplicationNamespace()
    {
        return $this->apiClientManager->getConfiguration()->getApplicationNamespace();
    }

    public function getDomainId(): string
    {
        return (string) $this->apiClientManager->getConfiguration()->getDomainId();
    }

    /**
     * Get Secret Key
     * @return string
     */
    public function getSecretKey()
    {
        return $this->apiClientManager->getConfiguration()->getSecretKey();
    }

    /**
     * Get E-mail address
     * @return string
     */
    public function getEmail()
    {
        return $this->apiClientManager->getConfiguration()->getEmail();
    }

    /**
     * Get First Name
     * @return string
     */
    public function getFirstName()
    {
        return $this->apiClientManager->getConfiguration()->getFirstName();
    }

    /**
     * Get Last Name
     * @return string
     */
    public function getLastName()
    {
        return $this->apiClientManager->getConfiguration()->getLastName();
    }

    /**
     * Get Username
     * @return string
     */
    public function getUsername()
    {
        return $this->apiClientManager->getConfiguration()->getUsername();
    }

    /**
     * Return the JWT secret phrase
     * @return mixed
     */
    public function getJWTSecret()
    {
        return $this->apiClientManager->getConfiguration()->getJwtSecret();
    }

    /**
     * @return mixed
     */
    public function getSellerApiUrl()
    {
        return $this->apiClientManager->getConfiguration()->getSellerApiUrl();
    }

    /**
     * @return mixed
     */
    public function getSellerEmailDomain()
    {
        return $this->apiClientManager->getConfiguration()->getSellerEmailDomain();
    }

    /**
     * @return M2MResponse Bearer to authentify operator
     * @throws ApiException
     */
    private function authenticateOperatorWithM2M(): M2MResponse
    {
        $apiConfiguration = $this->apiClientManager->getConfiguration();

        $data = [
            "grant_type" => "client_credentials",
            "client_id" => $apiConfiguration->getClientId(),
            "client_secret" => $apiConfiguration->getClientSecret(),
            "domain_id" => $apiConfiguration->getDomainId(),
            "response_type" => "token",
            "scope" => "identity:admin application:admin merchant:admin pim:admin oms:admin finance:admin customer:admin customer_service:admin technical:admin profile:admin email:admin",
            "audience" => $apiConfiguration->getAudience()
        ];

        try {
            $response = $this->client->post(
                uri: 'https://api.izberg.me/auth/token',
                options: [
                    'headers' => [
                        'Accept' => 'application/json',
                    ],
                    'json' => $data
                ]
            );

            return $this->serializer->deserialize((string) $response->getBody(), M2MResponse::class, 'json');
        } catch (GuzzleException $exception) {
            $this->throwGenericError($exception->getCode(), 'https://api.izberg.me/auth/token', $exception->getMessage());
        }
    }

    public function sendConcurrentApiRequest(
        array $apiRequestParameters,
        bool $isOperator = false,
        int $concurrentRequest = 5,
        array $options = []
    )
    {
        $defaultOptions = [
            self::OPTION_DATA_ARRAY => false,
            self::OPTION_IS_OPERATOR => false,
            self::OPTION_429_RETRY => true,
            self::OPTION_429_RETRY_NUMBER => 0,
            self::OPTION_429_RETRY_PAUSE => 3,
            self::OPTION_429_RETRY_LIMIT => 3,
            self::OPTION_NO_DATA_RETURN => false,
        ];

        $options[self::OPTION_IS_OPERATOR] = $isOperator;
        $options = $options + $defaultOptions;
        $retryOn429 = $options[self::OPTION_429_RETRY];
        $retryOn429Number = $options[self::OPTION_429_RETRY_NUMBER];
        $retryOn429Limit = $options[self::OPTION_429_RETRY_LIMIT];
        $retryOn429Pause = $options[self::OPTION_429_RETRY_PAUSE];
        $noDataReturn = $options[self::OPTION_NO_DATA_RETURN];

        $requests = function (array $apiRequestParameters) use ($isOperator, $noDataReturn): \Generator {
            /** @var ApiRequestParameter $apiRequestParameter */
            foreach ($apiRequestParameters as $apiRequestParameter) {
                $authorizationHeaders = $this->getAuthorizationHeaders($isOperator);

                $requestUrl = $this->getApiUrl() . $apiRequestParameter->getRoute();

                if ($noDataReturn) {
                    $requestUrl = sprintf(
                        '%s%sreturn_data=false',
                        $requestUrl,
                        (parse_url($requestUrl, PHP_URL_QUERY)) ? '&' : '?'
                    );
                }

                $headers = $authorizationHeaders + $apiRequestParameter->getHeaders();
                $apiRequestParameter->setAttribute(
                    'bearer',
                    $this->extractBearerUsernameFromAuthorizationHeaders($authorizationHeaders)
                );
                $apiRequestParameter->startRequest();

                $this->logApiRequestOrResponse(
                    $apiRequestParameter->getMethod(),
                    sprintf(
                        'Request Izberg API %s - %s',
                        strtoupper($apiRequestParameter->getMethod()),
                        $requestUrl
                    ),
                    [
                        'method' => strtoupper($apiRequestParameter->getMethod()),
                        'bearer' => $apiRequestParameter->getAttribute('bearer'),
                        'operator' => $isOperator,
                        'data' => $apiRequestParameter->getData(),
                    ]
                );

                yield new Request(
                    $apiRequestParameter->getMethod(),
                    $requestUrl,
                    $headers,
                    $this->formatObjectToJson($apiRequestParameter->getData())
                );
            }
        };

        $fullfilled = function (Response $response, $index) use (
            $apiRequestParameters,
            $retryOn429,
            $retryOn429Number,
            $retryOn429Limit,
            $retryOn429Pause,
            $options
        ) {
            /** @var ApiRequestParameter $apiRequestParameter */
            $apiRequestParameter = $apiRequestParameters[$index];
            $apiRequestParameter->endRequest();

            // check for 429
            if ($retryOn429 && $response->getStatusCode() === 429 && $retryOn429Number < $retryOn429Limit) {
                $options[self::OPTION_429_RETRY_NUMBER] += 1;

                $this->logger->info(
                    sprintf(
                        'Response Izberg API %s - Retry %s/%s - %s [%s]',
                        strtoupper($apiRequestParameter->getMethod()),
                        $retryOn429Number,
                        $retryOn429Limit,
                        $this->getApiUrl() . $apiRequestParameter->getRoute(),
                        $response->getStatusCode()
                    ),
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::IZBERG_API,
                        'method' => strtoupper($apiRequestParameter->getMethod()),
                        'bearer' => $apiRequestParameter->getAttribute('bearer'),
                        'response time' => $apiRequestParameter->executionTime(),
                        'data' => $apiRequestParameter->getData(),
                        'message' => (string)$response->getBody()
                    ])
                );

                sleep((int)($retryOn429Pause * exp($retryOn429Number)));
                return $this->sendApiRequest(
                    $apiRequestParameter->getMethod(),
                    $apiRequestParameter->getRoute(),
                    $apiRequestParameter->getData(),
                    $apiRequestParameter->getHeaders(),
                    $options
                );
            }

            if (in_array($response->getStatusCode(), [200, 201, 202, 204, 206, 207])) {
                $this->logApiRequestOrResponse(
                    $apiRequestParameter->getMethod(),
                    sprintf(
                        'Response Izberg API %s - %s [%s]',
                        strtoupper($apiRequestParameter->getMethod()),
                        $this->getApiUrl() . $apiRequestParameter->getRoute(),
                        $response->getStatusCode()
                    ),
                    [
                        'method' => strtoupper($apiRequestParameter->getMethod()),
                        'bearer' => $apiRequestParameter->getAttribute('bearer'),
                        'response time' => $apiRequestParameter->executionTime(),
                        'data' => $apiRequestParameter->getData(),
                    ]

                );
                return true;
            }

            // other Error
            $this->logger->error(
                sprintf(
                    'Response Izberg API ERROR %s - %s - [%s]',
                    strtoupper($apiRequestParameter->getMethod()),
                    $this->getApiUrl() . $apiRequestParameter->getRoute(),
                    $response->getStatusCode()
                ),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_API,
                    'method' => strtoupper($apiRequestParameter->getMethod()),
                    'bearer' => $apiRequestParameter->getAttribute('bearer'),
                    'response time' => $apiRequestParameter->executionTime(),
                    'data' => $apiRequestParameter->getData(),
                    'message' => (string)$response->getBody(),
                ])
            );

            return false;
        };

        $rejected = function (Exception $reason, $index) use ($apiRequestParameters) {
            /** @var ApiRequestParameter $apiRequestParameter */
            $apiRequestParameter = $apiRequestParameters[$index];
            $apiRequestParameter->endRequest();

            $this->logger->error(
                sprintf(
                    'Response Izberg API ERROR %s - %s - [%s]',
                    strtoupper($apiRequestParameter->getMethod()),
                    $this->getApiUrl() . $apiRequestParameter->getRoute(),
                    $reason->getMessage()
                ),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_API,
                    'method' => strtoupper($apiRequestParameter->getMethod()),
                    'bearer' => $apiRequestParameter->getAttribute('bearer'),
                    'response time' => $apiRequestParameter->executionTime(),
                    'data' => $apiRequestParameter->getData(),
                ])
            );

            return false;
        };

        $pool = new Pool($this->client, $requests($apiRequestParameters), [
            'concurrency' => $concurrentRequest,
            'fulfilled' => $fullfilled,
            'rejected' => $rejected,
        ]);

        $pool->promise()->wait();
    }

    public function sendApiRequest(
        string $method,
        string $route,
        $data = null,
        array $headers = [],
        array $options = []
    ): ?Unirest\Response
    {
        $defaultOptions = [
            self::OPTION_DATA_ARRAY => false,
            self::OPTION_IS_OPERATOR => false,
            self::OPTION_429_RETRY => true,
            self::OPTION_429_RETRY_NUMBER => 0,
            self::OPTION_429_RETRY_PAUSE => 3,
            self::OPTION_429_RETRY_LIMIT => 3,
            self::OPTION_NO_DATA_RETURN => false,
            self::OPTION_USE_M2M_AUTH => false,
        ];

        $options = $options + $defaultOptions;
        $isOperator = $this->isOperator($options[self::OPTION_IS_OPERATOR]);
        $retryOn429 = $options[self::OPTION_429_RETRY];
        $retryOn429Number = $options[self::OPTION_429_RETRY_NUMBER];
        $retryOn429Limit = $options[self::OPTION_429_RETRY_LIMIT];
        $retryOn429Pause = $options[self::OPTION_429_RETRY_PAUSE];
        $noDataReturn = $options[self::OPTION_NO_DATA_RETURN];
        $useM2mAuth = $options[self::OPTION_USE_M2M_AUTH];

        $method = strtolower($method);
        $authorizedMethods = [
            self::HTTP_GET_OPERATION,
            self::HTTP_POST_OPERATION,
            self::HTTP_DELETE_OPERATION,
            self::HTTP_PUT_OPERATION,
            self::HTTP_PATCH_OPERATION
        ];

        $response = null;

        if (!in_array(strtoupper($method), $authorizedMethods)) {
            throw new \InvalidArgumentException(
                sprintf(
                    'HTTP method %s is not authorized. Only the following methods are authorized %s',
                    $method,
                    implode(', ', $authorizedMethods)
                )
            );
        }

        $requestUrl = $this->getApiUrl() . $route;
        if (strpos($route, 'http') === 0) {
            $requestUrl = $route;
        }

        if ($noDataReturn) {
            $requestUrl = sprintf(
                '%s%sreturn_data=false',
                $requestUrl,
                (parse_url($requestUrl, PHP_URL_QUERY)) ? '&' : '?'
            );
        }

        $body = $data;
        if (!is_null($data) && !is_string($data) && !$options[self::OPTION_DATA_ARRAY]) {
            $body = $this->formatObjectToJson($data);
        }

        $startTime = microtime(true);
        $retryWithAdmin = false;
        $authorizationHeaders = [];

        if (!$isOperator) {
            $authorizationHeaders = $this->getAuthorizationHeaders();

            $this->logApiRequestOrResponse(
                $method,
                sprintf(
                    'Request Izberg API %s - %s %s',
                    strtoupper($method),
                    $requestUrl,
                    ($retryOn429Number > 0) ? sprintf('- %d RETRY AFTER 429 -', $retryOn429Number) : ''
                ),
                [
                    'method' => strtoupper($method),
                    'bearer' => $this->extractBearerUsernameFromAuthorizationHeaders($authorizationHeaders),
                    'operator' => $isOperator,
                    'data' => $data
                ]

            );

            /** @var Unirest\Response $response */
            $response = Unirest\Request::$method(
                $requestUrl,
                $authorizationHeaders + $headers,
                $body
            );

            if ($response->code === 401) {
                if ($this->routeCanBeRetriedWithAdmin($requestUrl)) {
                    $retryWithAdmin = true;
                } else {
                    $this->logger->error(
                        sprintf(
                            'SECURITY ALERT - try to request - Izberg API %s - %s %s',
                            strtoupper($method),
                            $requestUrl,
                            ($retryOn429Number > 0) ? sprintf('- %d RETRY AFTER 429 -', $retryOn429Number) : ''
                        ),
                        LogUtil::buildContext([
                            LogUtil::EVENT_NAME => EventNameEnum::IZBERG_API,
                            'method' => strtoupper($method),
                            'code response' => $response->code,
                            'bearer' => $this->extractBearerUsernameFromAuthorizationHeaders($authorizationHeaders),
                            'operator' => $isOperator,
                            'data' => $data
                        ])
                    );
                }
            }

            if ($retryWithAdmin) {
                // code d'erreur
                $this->logger->error(
                    sprintf(
                        'Failed Request => RETRY with ADMIN - Izberg API %s - %s %s',
                        strtoupper($method),
                        $requestUrl,
                        ($retryOn429Number > 0) ? sprintf('- %d RETRY AFTER IZBERG TOO MANY REQUESTS 429 -', $retryOn429Number) : ''
                    ),
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::IZBERG_API,
                        'method' => strtoupper($method),
                        'code response' => $response->code,
                        'bearer' => $this->extractBearerUsernameFromAuthorizationHeaders($authorizationHeaders),
                        'operator' => $isOperator,
                        'data' => $data
                    ])
                );
            }
        }

        if ($retryWithAdmin || $isOperator) {
            $authorizationHeaders = $this->getAuthorizationHeaders(operator: true, useM2mAuth: $useM2mAuth);
            $this->logApiRequestOrResponse(
                $method,
                sprintf(
                    'Request ADMIN Izberg API %s - %s %s',
                    strtoupper($method),
                    $requestUrl,
                    ($retryOn429Number > 0) ? sprintf('- %d RETRY AFTER 429 -', $retryOn429Number) : ''
                ),
                [
                    'method' => strtoupper($method),
                    'bearer' => $this->extractBearerUsernameFromAuthorizationHeaders($authorizationHeaders),
                    'operator' => true,
                    'data' => $data,
                ]
            );

            $startTime = microtime(true);

            $response = Unirest\Request::$method(
                $requestUrl,
                $authorizationHeaders + $headers,
                $body
            );
        }

        $endTime = microtime(true);

        if ($response) {
            if ($retryOn429 && $response->code === 429 && $retryOn429Number < $retryOn429Limit) {
                $options[self::OPTION_429_RETRY_NUMBER] += 1;

                $this->logger->info(
                    sprintf(
                        'Response %s Izberg API %s - Retry %s/%s - %s [%s]',
                        ($retryWithAdmin || $isOperator) ? 'ADMIN' : '',
                        strtoupper($method),
                        $retryOn429Number,
                        $retryOn429Limit,
                        $requestUrl,
                        $response->code
                    ),
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::IZBERG_API,
                        'method' => strtoupper($method),
                        'bearer' => $this->extractBearerUsernameFromAuthorizationHeaders($authorizationHeaders),
                        'response time' => $endTime - $startTime,
                        'data' => $response->body,
                    ])
                );

                sleep((int)($retryOn429Pause * exp($retryOn429Number)));
                return $this->sendApiRequest($method, $route, $data, $headers, $options);
            }

            if (!in_array($response->code, [200, 201, 202, 204, 206, 207])) {
                $this->logger->error(
                    sprintf(
                        'Response %s Izberg API %s - %s [%s]',
                        ($retryWithAdmin || $isOperator) ? 'ADMIN' : '',
                        strtoupper($method),
                        $requestUrl,
                        $response->code
                    ),
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::IZBERG_API,
                        'method' => strtoupper($method),
                        'bearer' => $this->extractBearerUsernameFromAuthorizationHeaders($authorizationHeaders),
                        'response time' => $endTime - $startTime,
                        'message' => $response->raw_body,
                    ])

                );

                $this->throwGenericError($response->code, $requestUrl, $response->raw_body, ['response time' => $endTime - $startTime]);
            }

            $this->logApiRequestOrResponse(
                $method,
                sprintf(
                    'Response %s Izberg API %s - %s [%s]',
                    ($retryWithAdmin || $isOperator) ? 'ADMIN' : '',
                    strtoupper($method),
                    $requestUrl,
                    $response->code
                ),
                [
                    'method' => strtoupper($method),
                    'bearer' => $this->extractBearerUsernameFromAuthorizationHeaders($authorizationHeaders),
                    'response time' => $endTime - $startTime,
                    'message' => $response->raw_body,
                ]
            );
        }

        return $response;
    }

    /**
     * Custom base64 encoding for URL
     * @param $source
     * @return mixed|string
     */
    private function base64url_encode($source)
    {
        // Encode in classical base64
        $encodedSource = base64_encode($source);

        // Remove padding equal characters
        $encodedSource = preg_replace('/=+$/', '', $encodedSource);

        // Replace characters according to base64url specifications
        $encodedSource = str_replace('+', '-', $encodedSource);

        return str_replace('/', '_', $encodedSource);
    }

    /**
     * log a request
     * @param string $method
     * @param string $message
     * @param array $context
     */
    private function logApiRequestOrResponse(string $method, string $message, array $context)
    {
        $context[LogUtil::EVENT_NAME] = EventNameEnum::IZBERG_API;

        if (strtoupper($method) === self::HTTP_GET_OPERATION) {
            $this->logger->debug($message, LogUtil::buildContext($context));
        } else {
            $this->logger->info($message, LogUtil::buildContext($context));
        }
    }

    private function routeCanBeRetriedWithAdmin(string $requestUrl)
    {
        $blackListedRoutes = [
            ['/v1/cart/{id}/', ['id' => self::PARAM_TYPE_INTEGER]],
            ['/v1/order/{id}/', ['id' => self::PARAM_TYPE_INTEGER]],
            ['/v1/merchant_order/{id}/', ['id' => self::PARAM_TYPE_INTEGER]],
            ['/v1/payment/{id}/', ['id' => self::PARAM_TYPE_INTEGER]],
            ['/v1/merchant_order/{id}/invoices/', ['id' => self::PARAM_TYPE_INTEGER]],
            ['/v1/customer_invoice/{id}/', ['id' => self::PARAM_TYPE_INTEGER]],
            ['/v1/credit_note/{id}/', ['id' => self::PARAM_TYPE_INTEGER]],
        ];

        foreach ($blackListedRoutes as [$blackListedRoute, $params]) {
            $blackListedRouteRegex = array_reduce(
                array_keys($params),
                function ($routeRegex, $variable) use ($params) {
                    return str_replace(sprintf('{%s}', $variable), $params[$variable], $routeRegex);
                },
                $blackListedRoute
            );

            if (preg_match('#^' . $blackListedRouteRegex . '$#', parse_url($requestUrl, PHP_URL_PATH))) {
                return false;
            }
        }

        return true;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
