<?php

namespace Open\IzbergBundle\Api;

use AppB<PERSON>le\Services\AlstomCustomAttributes;
use AppBundle\Services\SecurityService;
use <PERSON><PERSON>\Serializer\SerializerInterface;
use Open\IzbergBundle\ApiRequestParameter;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

/**
 * An async version of the attribute API
 * Class AsyncAttributeApi
 * @package Open\IzbergBundle\Api
 */
class AsyncAttributeApi extends AttributeApi
{
    public function __construct(
        ApiClientManager $apiClientManager,
        TokenStorageInterface $tokenStorage,
        RequestStack $requestStack,
        SerializerInterface $serializer,
        SecurityService $securityService,
        AlstomCustomAttributes $customAttributes
    )
    {
        parent::__construct($apiClientManager, $tokenStorage, $requestStack, $serializer, $securityService, $customAttributes);
    }

    /**
     * async version of createMerchantOrderAttributes
     * this method don't perform any check. Only return the promise that can be used later
     * @param int $merchantOrderId
     * @param array $attributes
     */
    public function createMerchantOrderAttributes(int $merchantOrderId, array $attributes): void
    {
        $this->sendConcurrentApiRequest(
            array_map(
                function($attributeId, $value) use ($merchantOrderId){
                    return new ApiRequestParameter(
                        self::HTTP_POST_OPERATION,
                        'order_attribute_value/',
                        [
                            'attribute' =>  '/v1/order_attribute/' . strval($attributeId) . '/',
                            'application' => '/v1/application/' . $this->getApplicationId() . '/',
                            'value' => $value,
                            'entity' => '/v1/merchant_order/' . strval($merchantOrderId) . '/',
                        ]
                    );
                },
                array_keys($attributes),
                $attributes
            ),
            true,
            5,
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    /**
     * async version of updateMerchantOrderAttributes
     * this method don't perform any check. Only return the promise that can be used later.
     *
     * @param int $merchantOrderId
     * @param array $attributes
     */
    public function updateMerchantOrderAttributes(int $merchantOrderId, array $attributes):void
    {
        $this->sendConcurrentApiRequest(
            array_map(
                function($attributeId, $value) use ($merchantOrderId){
                    return new ApiRequestParameter(
                        self::HTTP_PATCH_OPERATION,
                        'order_attribute_value/'.$attributeId.'/',
                        [
                            'application' => '/v1/application/' . $this->getApplicationId() . '/',
                            'value' => $value,
                            'entity' => '/v1/merchant_order/' . strval($merchantOrderId) . '/',
                        ]
                    );
                },
                array_keys($attributes),
                $attributes
            ),
            true,
            5,
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    /**
     * @param int $merchantOrderId
     * @param int $attributeId
     * @param string $file
     * @return int
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function updateMerchantOrderFileAttribute(int $merchantOrderId, int $attributeId,string $file):int
    {
        $options = [
            'headers' =>  array_filter(
                $this->getAuthorizationHeaders(true),
                function($headerName) {
                    return ($headerName != self::HEADER_CONTENT_TYPE);
                },
                ARRAY_FILTER_USE_KEY
            )
        ];

        // delete attribute value if exists
        $attributeValue = $this->getMerchantOrderAttributeRessourceId($merchantOrderId, $attributeId);


        if($attributeValue !== null) {
            $this->client->request(
                'DELETE',
                $this->getApiUrl() . 'order_attribute_value/'.$attributeValue->id.'/',
                $options
            );
        }

        // upload file
        $multipartData = [
            [
                'name' => 'attribute',
                'contents' => '/v1/order_attribute/' . strval($attributeId) . '/',
            ],
            [
                'name' => 'application',
                'contents' => '/v1/application/' . $this->getApplicationId() . '/',
            ],
            [
                'name' => 'value',
                'contents' => fopen($file, 'r+'),
                'headers' => [self::HEADER_CONTENT_TYPE => 'application/pdf'],
                'filename' => 'merchant-order.pdf',
            ],
            [
                'name' => 'entity',
                'contents' => '/v1/merchant_order/' . strval($merchantOrderId) . '/',
            ],
        ];

        $options['multipart'] = $multipartData;

        $response = $this->client->request(
            'POST',
            $this->getApiUrl() . 'order_attribute_value/',
            $options
        );

        return $response->getStatusCode();
    }
}
