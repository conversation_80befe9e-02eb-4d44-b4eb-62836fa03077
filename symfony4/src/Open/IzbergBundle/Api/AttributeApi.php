<?php

namespace Open\IzbergBundle\Api;

use Open\IzbergBundle\Api;
use Open\IzbergBundle\Dto\AttributeDTO;
use Open\IzbergBundle\Dto\RedisAttributeDTO;
use Open\IzbergBundle\Model\CustomerAttribute;
use Open\IzbergBundle\Model\CustomerAttributeValue;
use Open\IzbergBundle\Model\CustomerAttributeValuesResponse;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;

class AttributeApi extends Api
{
    private const ATTRIBUTE_PATH = "product_attribute/?application=";
    private const ORDER_ATTRIBUTE_PATH = "order_attribute/";

    public function  getUri(){
        return null;
    }

    public function getItemClass(){
        return null;
    }


    public function getAttributes($locale): array
    {
        $headers = [
            Api::HEADER_ACCEPT_LANGUAGE => $locale
        ];

        $attributes = [];
        $requestUrl = self::ATTRIBUTE_PATH . $this->getApplicationId();

        do {
            $response = $this->sendApiRequest(
                self::HTTP_GET_OPERATION,
                $requestUrl,
                null,
                $headers,
                [
                    self::OPTION_IS_OPERATOR => true,
                ]
            );

            $attributeKeyIsValid = function($attributeKey) {
                $validAttributeKey = true;

                foreach([' ', '&'] as $illegalChar) {
                    if(strpos($attributeKey, $illegalChar) !== false) {
                        $this->logger->error(
                            sprintf("izberg attribute %s should not contains '%s' character", $attributeKey, $illegalChar),
                            LogUtil::buildContext([
                                LogUtil::EVENT_NAME => Api::LOG_IZBERG_API_ERROR
                            ])
                        );

                        $validAttributeKey = false;
                    }
                }

                return $validAttributeKey;
            };

            // For all addresses in the json serialize to an Address entity and add it to the collection
            foreach ($response->body->objects as $attribute) {

                if (!call_user_func($attributeKeyIsValid, $attribute->key)) {
                    continue;
                }

                $attributeDTO = new AttributeDTO();
                $attributeDTO->setKey($attribute->key);
                $attributeDTO->setLabel($attribute->name);
                $attributes[$attribute->key] = $attributeDTO;
            }

            $requestUrl = $response->body->meta->next;

        }while($requestUrl !== null);

        return $attributes;
    }

    /**
     * @param RedisAttributeDTO $attribute
     * @return mixed
     */
    public function getOrderAttributeId(RedisAttributeDTO $attribute)
    {
        return $this->getAttributeId('order_attribute', $attribute->getKey());
    }

    /**
     * @param RedisAttributeDTO $attribute
     * @return mixed
     */
    public function getMerchantAttributeId(RedisAttributeDTO $attribute)
    {
        return $this->getAttributeId('merchant_attribute', $attribute->getKey());
    }

    /**
     * @param RedisAttributeDTO|CustomerAttribute $attribute
     * @return mixed
     */
    public function getCustomerAttributeId(RedisAttributeDTO|CustomerAttribute $attribute): mixed {
        return $this->getAttributeId('customer_attribute', $attribute->getKey());
    }

    /**
     * This function will get the order attribute id from Izberg datas.
     *
     * @param RedisAttributeDTO $redisAttribute
     * @return int|null
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getOrderAttributeIzbergId(RedisAttributeDTO $redisAttribute): ?int
    {
        $attribute = $this->getMerchantOrderAttributeRessourceId((int)$redisAttribute->getMerchantOrderId(), (int)$redisAttribute->getKey());
        return $attribute ? $attribute->id : null;
    }
    /**
     * This function will get the merchant order attribute's ressource id.
     *
     * @param int $merchantOrderId
     * @param int $attributeId
     * @return mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getMerchantOrderAttributeRessourceId(int $merchantOrderId, int $attributeId){
        $options = [
            'headers' =>  array_filter(
                $this->getAuthorizationHeaders(true),
                function($headerName) {
                    return ($headerName != self::HEADER_CONTENT_TYPE);
                },
                ARRAY_FILTER_USE_KEY
            )
        ];
        $response = $this->client->request(
            'GET',
            $this->getApiUrl() . 'order_attribute_value/?merchant_order='. $merchantOrderId . '&attribute=' . $attributeId,
            $options
        );
        $content = $response->getBody()->getContents();
        $content = json_decode($content);
        if($content->meta->total_count) {
            $objects = $content->objects;
            return array_shift($objects);
        }
        return null;
    }

    /**
     * create the reconciliation key of a merchant order
     * @param integer $attributeId the identifier of the attribute
     * @param integer $merchantOrderId the identifier of the merchant order
     * @param string $value the value to set for the attribute
     */
    public function updateReconciliationAttribute(int $attributeId, int $merchantOrderId, string $value):void
    {
        $this->createMerchantOrderAttribute($attributeId, $merchantOrderId, $value);
    }

    /**
     * @param int $attributeId
     * @param int $merchantOrderId
     * @param string $value
     */
    public function createMerchantOrderAttribute(int $attributeId, int $merchantOrderId, string $value):void
    {
        $data = [
            'attribute' => '/v1/order_attribute/'.strval($attributeId).'/',
            'application' => '/v1/application/'.$this->getApplicationId().'/',
            'value' => $value,
            'entity' => '/v1/merchant_order/'.strval($merchantOrderId).'/'
        ];


        $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            'order_attribute_value/',
            $data,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    /**
     * This function will update a merchant order attribute.
     *
     * @param int $attributeId
     * @param int $merchantOrderId
     * @param string $value
     */
    public function updateMerchantOrderAttribute(int $attributeId, int $merchantOrderId, string $value):void
    {
        $data = [
            'application' => '/v1/application/'.$this->getApplicationId().'/',
            'value' => $value,
            'entity' => '/v1/merchant_order/'.strval($merchantOrderId).'/'
        ];


        $this->sendApiRequest(
            self::HTTP_PATCH_OPERATION,
            'order_attribute_value/'.$attributeId.'/',
            $data,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    public function updateMerchantAttribute($attributeId, $merchantId, $value)
    {
        $data = [
            "application" => "/v1/application/".$this->getApplicationId()."/",
            "attribute" => "/v1/merchant_attribute/".  $attributeId  ."/",
            "merchant" => "/v1/merchant/".$merchantId."/",
            "value" => $value,
        ];

        $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            'merchant_attribute_value/',
            $data,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    /**
     * create many merchant order attributes
     * @param int $merchantOrderId
     * @param array $attributes (key/value)
     */
    public function createMerchantOrderAttributes(int $merchantOrderId, array $attributes):void
    {
        foreach ($attributes as $attributeId => $value) {
            $this->createMerchantOrderAttribute($attributeId, $merchantOrderId, $value);
        }
    }

    /**
     * update many merchant order attributes
     * @param int $merchantOrderId
     * @param array $attributes (key/value)
     */
    public function updateMerchantOrderAttributes(int $merchantOrderId, array $attributes)
    {
        foreach ($attributes as $attributeId => $value) {
            $this->updateMerchantOrderAttribute($attributeId, $merchantOrderId, $value);
        }
    }

    public function updateCustomerAttribute($attributeId, $userId, $value){
        $data = [
            'attribute' => "/v1/customer_attribute/".strval($attributeId)."/",
            'application' => "/v1/application/".$this->getApplicationId()."/",
            'value' => $value,
            'customer' => '/v1/user/'.strval($userId).'/',
        ];

        $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            'customer_attribute_value/',
            $data,
            [],
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }


    /**
     * @param string $api
     * @param string $key
     * @return mixed
     */
    private function getAttributeId(string $api, string $key)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            $api.'/?key='.$key,
            null,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        if (!count($response->body->objects)) {
            $this->logger->error(
                sprintf(
                    'Could not find order_attribute identified by key %s',
                    $key
                ),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::IZBERG_ATTRIBUTE_NOT_FOUND
                ])
            );
            return null;
        }

        return $response->body->objects[0]->id;
    }

    /**
     * @param int $userId
     * @param CustomerAttribute[] $attributes
     * @return void
     * @throws ApiException
     */
    public function createOrUpdateCustomerAttributes(int $userId, array $attributes): void
    {
        /** @var CustomerAttribute $attribute */
        foreach ($attributes as $attribute) {
            //set custom attribute id, if not already set
            if ($attribute->getId() === null) {
                $attribute->setId(intval($this->getCustomerAttributeId($attribute)));
            }

            $current = $this->getCustomerAttributeValue((int)$attribute->getId(), "" . $userId);

            if ($current === null) {
                //value does not already exist for this buyer
                $this->createCustomerAttribute($userId, $attribute);
                continue;
            }

            //value already exist for this buyer
            if ($current->getValue() === $attribute->getValue()) {
                continue;
            }
            $this->patchCustomerAttribute($current->getId(), $attribute->getValue());
        }
    }

    /**
     * @param int $userId
     * @param CustomerAttribute $customerAttribute
     * @throws ApiException
     */
    public function createCustomerAttribute(int $userId, CustomerAttribute $customerAttribute): void
    {
        $data = [
            'attribute' => "/v1/customer_attribute/" . $customerAttribute->getId() . "/",
            'application' => "/v1/application/" . $this->getApplicationId() . "/",
            'value' => $customerAttribute->getValue(),
            'customer' => '/v1/user/' . $userId . '/',
        ];

        $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            'customer_attribute_value/',
            $data,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
                self::OPTION_NO_DATA_RETURN => true
            ]
        );
    }

    /**
     * @throws ApiException
     */
    public function getCustomerAttributeValue(int $customId, string $customerId): ?CustomerAttributeValue
    {
        $query = [
            'attribute' => $customId,
            'customer' => $customerId
        ];

        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'customer_attribute_value/'. '?' . http_build_query($query),
            null,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        $response = $this->serializer->deserialize($response->raw_body, CustomerAttributeValuesResponse::class, 'json');

        if ($response != null && count($response->getObjects()) > 0) {
            return $response->getObjects()[0];
        }

        return null;
    }

    /**
     * @throws ApiException
     */
    public function patchCustomerAttribute(int $attributeId, string $value): void
    {
        $data = [
            'value' => $value,
        ];

        $this->sendApiRequest(
            self::HTTP_PATCH_OPERATION,
            sprintf('customer_attribute_value/%d/', $attributeId),
            json_encode($data),
            [],
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    /**
     * Get attribute name by key
     *
     * <AUTHOR> Bulochnik <<EMAIL>>
     *
     * @param string $attrKey
     * @param string $locale
     * @return string
     */
    public function getAttributeNameByKey(string $attrKey, string $locale): string
    {
        $queryParams = [
            'key' => $attrKey,
            'only' => 'name',
        ];

        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::ORDER_ATTRIBUTE_PATH . '?' . http_build_query($queryParams),
            null,
            [
                Api::HEADER_ACCEPT_LANGUAGE => $locale
            ],
            [
                self::OPTION_IS_OPERATOR => true,
            ]
        );

        return $response->body->objects[0]->name;
    }
}
