<?php

namespace Open\IzbergBundle\Api;

use Doctrine\Common\Collections\ArrayCollection;
use Open\IzbergBundle\Api;
use Open\IzbergBundle\ApiRequestParameter;
use Open\IzbergBundle\Model\Cart;
use Open\IzbergBundle\Model\CartItem;

/**
 * Provide wrapper for the Cart API
 * Class CartApi
 * @package Open\IzbergBundle\Api
 */
class CartApi extends Api
{
    const BILLING_ADDRESS = 'billing_address';
    const SHIPPING_ADDRESS = 'shipping_address';
    const CART_SLUG = 'cart/';

    const CURRENCY_ATTRIBUTE_NAME = "currency";
    const CURRENCY_ATTRIBUTE_VALUE = "EUR";

    const ITEMS_PATH = '/items/';
    const CART_ITEM_PATH = 'cart_item/';
    const CURRENCY_PATH = '?currency=';

    public function getUri()
    {
        return 'cart';
    }

    public function getItemClass()
    {
        return Cart::class;
    }

    public function createCart(string $currency): Cart
    {
        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::CART_SLUG . '/',
            [
                self::CURRENCY_ATTRIBUTE_NAME => $currency
            ]
        );

        $cart = $this->serializer->deserialize($response->raw_body, Cart::class, 'json');

        if (!$cart instanceof Cart) {
            throw new ApiException('Failed creating Izberg cart', 500);
        }

        return $cart;
    }

    public function createCartShipping(int $cartId, array $data)
    {
        $data = ['objects' => $data];

        return $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::CART_SLUG . $cartId . '/create-shipping/',
            $data
        );
    }

    public function fetchCartShippingOptions(int $cartId): ?array
    {
        $url = sprintf('cart/%d/shipping_options', $cartId);
        $response = $this->sendApiRequest(self::HTTP_GET_OPERATION, $url);
        return $response->body->objects;
    }

    /**
     * Get a cart by its Id
     * @link https://documenter.getpostman.com/view/653426/izberg-api-official-collection/Jsix#eb9b-a0c4-2fa1-277e-da607e673172
     * @param integer $id
     * @param boolean $json
     * @param string $currency Currency of the targeted cart
     * @return mixed
     */
    public function getCart($id = null, $json = false, $currency = null)
    {
        $cart_id = ($id) ? intval($id) : 'mine';

        if (!$currency) {
            $currency = self::CURRENCY_ATTRIBUTE_VALUE;
        }

        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::CART_SLUG . $cart_id . '/' . self::CURRENCY_PATH . $currency
        );

        if (!$json) {
            $cart = $this->serializer->deserialize($response->raw_body, Cart::class, 'json');
            $cart->setItems($this->getCartItems($cart->getId(), $json));
        } else {
            $cart = $response->body;
            $cart->items = $this->getCartItems($cart->id, $json);
        }

        return $cart;
    }

    public function fetchIzbergCart(int $izbergCartId, string $currency = null): Cart
    {
        $route = self::CART_SLUG . $izbergCartId . '/'; //don't forget the ending slash

        if ($currency) {
            $route = $route . self::CURRENCY_PATH . $currency;
        }

        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            $route
        );

        $cart = $this->serializer->deserialize($response->raw_body, Cart::class, 'json');

        if (!$cart instanceof Cart) {
            throw new ApiException('Failed creating Izberg cart', 500);
        }

        $cart->setItems($this->getCartItems($cart->getId(), false));

        return $cart;
    }

    /**
     * Get current user cart
     * @link https://documenter.getpostman.com/view/653426/izberg-api-official-collection/Jsix#e17c5c5-1a89-906e-58cc-d37abda40759
     * @param boolean $json
     * @param string $currency Currency of the targeted cart
     * @return Cart
     */
    public function getUserCart($json = false, $currency = null)
    {
        return $this->getCart(null, $json, $currency);
    }

    /**
     * Get items of a cart by its Id
     * @link https://documenter.getpostman.com/view/653426/izberg-api-official-collection/Jsix#abfe44-065d-6fea-144c-9f72b491682b
     * @param integer $cart_id
     * @param boolean $json
     * @return mixed
     */
    public function getCartItems($cart_id, $json = false)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::CART_SLUG . intval($cart_id) . self::ITEMS_PATH
        );

        if (!$json) {
            /** @var ArrayCollection $cartItems */
            $cart_items = new ArrayCollection();

            foreach ($response->body->objects as $item_json) {
                /** @var CartItem $item */
                $item = $this->serializer->deserialize(json_encode($item_json), CartItem::class, 'json');
                /**
                 * @psalm-suppress InvalidArgument
                 */
                $cart_items->add($item);
            }
        } else {
            $cart_items = $response->body->objects;
        }

        return $cart_items;
    }

    /**
     * Add a product offer to the cart
     * @param integer $cart_id
     * @param integer $offer_id
     * @param string $currency Currency of the targeted cart
     * @param integer $qty
     * @param float $price
     * @param float $tax_rate
     * @param array $extraInfo
     * @link https://documenter.getpostman.com/view/653426/izberg-api-official-collection/Jsix#b209a00-9814-b5a2-52cf-36e42d6130ee
     */
    public function addProductOffer(
        $cart_id,
        $offer_id,
        $currency,
        $qty = 1,
        $price = null,
        $tax_rate = null,
        $extraInfo = []
    )
    {
        $header = [];
        $data = [
            'product_offer_id' => $offer_id,
            'quantity' => $qty,
            'extra_info' => $extraInfo,
        ];

        //overide for the price
        if ($price != null) {
            $data['unit_price'] = abs($price);
            $data['unit_vat'] = ($price * $tax_rate) / 100;
            $data['tax_rate'] = $tax_rate;
        }

        //override for the price need special header
        if ($price != null) {
            $timestamp = time();
            $toEncode = $this->formatObjectToJson($data) . ':' . $timestamp;
            $message_auth = hash_hmac('sha1', $toEncode, $this->getSecretKey());
            $header['Application-signature'] = $message_auth;
            $header['Application-nonce'] = $timestamp;
        }

        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::CART_SLUG . intval($cart_id) . self::ITEMS_PATH . self::CURRENCY_PATH . $currency,
            $data,
            $header,
            [
                self::OPTION_NO_DATA_RETURN => false,
            ]
        );

        $cartItemId = $response->body->id;

        if (!empty($extraInfo)) {
            $data = [
                'extra_info' => $extraInfo
            ];

            $this->sendApiRequest(
                self::HTTP_PUT_OPERATION,
                self::CART_ITEM_PATH . $cartItemId . '/',
                $data
            );
        }
    }

    public function addProductOffers(int $cartId, array $cartItems)
    {
        /*$this->sendConcurrentApiRequest(
            array_map(
                function (\AppBundle\Model\Cart\CartItem $cartItem) use ($cartId) {
                    $data = [
                        'product_offer_id' => $cartItem->getOfferId(),
                        'quantity' => $cartItem->getQuantity(),
                        'unit_price' => abs($cartItem->getUnitPrice()),
                        'unit_vat' => ($cartItem->getUnitPrice() * floatval($cartItem->getTaxRate())) / 100,
                        'tax_rate' => $cartItem->getTaxRate(),
                    ];

                    if ($cartItem->getOffer()->getFrameContract()) {
                        $data['extra_info'] = [
                            'frame_contract' => $cartItem->getOffer()->getFrameContract()
                        ];
                    }

                    $timestamp = time();
                    $signature = hash_hmac(
                        'sha1',
                        $this->formatObjectToJson($data) . ':' . $timestamp,
                        $this->getSecretKey()
                    );

                    $headers = [
                        'Application-signature' => $signature,
                        'Application-nonce' => $timestamp,
                    ];

                    return (new ApiRequestParameter(
                        self::HTTP_POST_OPERATION,
                        self::CART_SLUG . intval($cartId) . self::ITEMS_PATH . self::CURRENCY_PATH . $cartItem->getCurrency(),
                        $data,
                        $headers
                    ));
                },
                $cartItems
            ),
            false,
            1,
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );*/

        foreach ($cartItems as $cartItem) {
            $data = [
                'product_offer_id' => $cartItem->getOfferId(),
                'quantity' => $cartItem->getQuantity(),
                'unit_price' => abs($cartItem->getUnitPrice()),
                'unit_vat' => ($cartItem->getUnitPrice() * floatval($cartItem->getTaxRate())) / 100,
                'tax_rate' => $cartItem->getTaxRate(),
            ];

            if ($cartItem->getOffer()->getFrameContract()) {
                $data['extra_info'] = [
                    'frame_contract' => $cartItem->getOffer()->getFrameContract()
                ];
            }

            $timestamp = time();
            $signature = hash_hmac(
                'sha1',
                $this->formatObjectToJson($data) . ':' . $timestamp,
                $this->getSecretKey()
            );

            $headers = [
                'Application-signature' => $signature,
                'Application-nonce' => $timestamp,
            ];

            $response = $this->sendApiRequest(
                self::HTTP_POST_OPERATION,
                self::CART_SLUG . intval($cartId) . self::ITEMS_PATH . self::CURRENCY_PATH . $cartItem->getCurrency(),
                $data,
                $headers,
                [
                    self::OPTION_NO_DATA_RETURN => false,
                ]
            );

            $cartItemId = $response->body->id;

            if (!empty($data['extra_info'])) {
                $dataExtraInfo = [
                    'extra_info' => $data['extra_info']
                ];

                $this->sendApiRequest(
                    self::HTTP_PUT_OPERATION,
                    self::CART_ITEM_PATH . $cartItemId . '/',
                    $dataExtraInfo
                );
            }
        }
    }

    /**
     * @param string $type
     * @param integer $address_id
     * @param $cart_id
     */
    private function setAddress($type, $address_id, $cart_id = null)
    {
        if ($type != self::SHIPPING_ADDRESS && $type != self::BILLING_ADDRESS) {
            throw new ApiException("CartApi.setAddress: Unknown address type: " . $type);
        }

        $this->sendApiRequest(
            self::HTTP_PATCH_OPERATION,
            self::CART_SLUG . intval($cart_id) . '/',
            [
                $type => '/v' . $this->getVersion() . '/address/' . $address_id . '/',
            ],
            [],
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    /**
     * Update the quantity of a cart item
     * @param $item_id integer
     * @param $quantity integer
     * @return mixed the updated CartItem object
     */
    public function updateItemQuantity($item_id, $quantity = 0)
    {
        if ($quantity < 1) {
            throw new ApiException("CartApi.updateItemQuantity: Invalid quantity: " . $quantity);
        }

        $response = $this->sendApiRequest(
            self::HTTP_PUT_OPERATION,
            self::CART_ITEM_PATH . intval($item_id) . '/',
            [
                'quantity' => intval($quantity),
            ]
        );

        return $this->serializer->deserialize($response->raw_body, CartItem::class, 'json');
    }

    public function updateItemExtraInfo(int $itemId, string $field, ?string $value)
    {
        $extraInfo = $this->getCartItemExtraInfo($itemId);
        $extraInfo[ucfirst($field)] = $value;
        $data = [
            'extra_info' => $extraInfo
        ];
        $this->sendApiRequest(
            self::HTTP_PUT_OPERATION,
            self::CART_ITEM_PATH . $itemId . '/',
            $data
        );
    }

    public function getCartItemExtraInfo(int $cartItemId): array
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::CART_ITEM_PATH . $cartItemId
        );
        return json_decode($response->raw_body, true)['extra_info'];
    }

    /**
     * Remove an item from its cart
     * @param $item_id
     */
    public function removeItem($item_id)
    {
        $this->sendApiRequest(
            self::HTTP_DELETE_OPERATION,
            self::CART_ITEM_PATH . intval($item_id) . '/',
            null,
            [],
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    /**
     * @param int $cartId
     * @param string $paymentType
     * @param string|null $paymentMethod
     * @param string|null $izbergModeId
     */
    public function setPaymentMode(int $cartId, string $paymentType, ?string $paymentMethod, $izbergModeId = null)
    {
        $data = [
            'selected_payment_type' => 'term_payment',
        ];

        if ($izbergModeId === null) {
            $data = [
                'selected_payment_type' => $paymentType,
                'selected_payment_method' => $paymentMethod
            ];
        }

        $this->sendApiRequest(
            self::HTTP_PATCH_OPERATION,
            self::CART_SLUG . intval($cartId) . '/',
            $data,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );

        //if needed, patch each item of the cart
        if ($izbergModeId !== null) {

            /**
             * @var CartItem $item
             */
            foreach ($this->getCartItems($cartId) as $item) {
                $this->sendApiRequest(
                    self::HTTP_PATCH_OPERATION,
                    self::CART_ITEM_PATH . intval($item->getId()) . '/',
                    [
                        'selected_payment_term' => '/v1/payment_term/' . $izbergModeId . '/',
                    ],
                    [],
                    [
                        self::OPTION_NO_DATA_RETURN => true,
                    ]
                );
            }
        }
    }

    /**
     * Set shipping address for
     * @param integer $cart_id
     * @param integer $address_id
     */
    public function setShippingAddress($cart_id, $address_id)
    {
        $this->setAddress(self::SHIPPING_ADDRESS, $address_id, $cart_id);
    }

    /**
     * Set billing address for
     * @param integer $cart_id
     * @param integer $address_id
     */
    public function setBillingAddress($cart_id, $address_id)
    {
        $this->setAddress(self::BILLING_ADDRESS, $address_id, $cart_id);
    }

    public function setDeliveryDate($itemId, $expected_delivery_date, $expected_shipping_date)
    {
        $data = [
            'cart_item' => "/v1/cart_item/" . $itemId . "/",
            'effective_delivery_date' => null,
            'effective_shipping_date' => null,
            'expected_delivery_date' => $expected_delivery_date->format('Y-m-d H:i:s'),
            'expected_shipping_date' => $expected_shipping_date->format('Y-m-d H:i:s'),
            'order_item' => null,
        ];

        $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            'delivery_date/',
            $this->formatObjectToJson($data),
            [],
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    public function setDeliveryDates(array $cartItemDeliveryDates)
    {
        $this->sendConcurrentApiRequest(
            array_map(
                function (array $cartItemDeliveryDate) {
                    [$cartItemId, $expectedDeliveryDate, $expectedShippingDate] = $cartItemDeliveryDate;

                    return (new ApiRequestParameter(
                        self::HTTP_POST_OPERATION,
                        'delivery_date/',
                        [
                            'cart_item' => "/v1/cart_item/" . $cartItemId . "/",
                            'effective_delivery_date' => null,
                            'effective_shipping_date' => null,
                            'expected_delivery_date' => $expectedDeliveryDate->format('Y-m-d H:i:s'),
                            'expected_shipping_date' => $expectedShippingDate->format('Y-m-d H:i:s'),
                            'order_item' => null,
                        ]
                    ));
                },
                $cartItemDeliveryDates
            ),
            true,
            1,
            [
                self::OPTION_429_RETRY => true,
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    public function patch(int $cartId, array $data)
    {
        $this->sendApiRequest(
            self::HTTP_PATCH_OPERATION,
            self::CART_SLUG . $cartId . '/',
            $data,
            [],
            [
                self::OPTION_IS_OPERATOR => true,
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }
}
