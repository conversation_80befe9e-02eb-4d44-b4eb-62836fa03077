<?php

namespace Open\IzbergBundle\Api;

use Open\IzbergBundle\Api;
use Open\IzbergBundle\Model\FetchInvoicesResponse;
use Open\IzbergBundle\Model\Invoice;
use Symfony\Component\Validator\Constraints\Date;
use Unirest;

class InvoiceApi extends Api
{
    private const CUSTOMER_INVOICE_SLUG = 'customer_invoice/';
    private const CREDIT_NOTE = 'credit_note/';

    public const STATUS_PENDING = "pending";
    public const STATUS_EMITTED = "emitted";
    public const STATUS_SENT = "sent";

    private const PARAM_STATUS = "?status=";
    private const PARAM_EXTERNAL_STATUS = "&external_status=";
    private const PARAM_PAYMENT_STATUS = "&payment_status=";

    private const LEGAL_NOTICES = "legal_notices";
    private const LEGAL_NOTICES_SEPARATOR = "\n";

    public function  getUri(){
        return 'customer_invoice';
    }

    public function getItemClass(){
        return Invoice::class;
    }

    /**
     * fetch a customer invoice from its ids
     * @param string $invoiceId
     * @return null|\stdClass the izberg customer invoice
     */
    public function fetchCustomerInvoice($invoiceId):?\stdClass
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::CUSTOMER_INVOICE_SLUG . strval($invoiceId) . '/?full_order_item=true'
        );

        return $response->body;
    }

    public function fetchCustomerInvoicePdf($invoiceId, $hasPdfFile = null): ?string
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::CUSTOMER_INVOICE_SLUG . strval($invoiceId) . (!empty($hasPdfFile) ? '/?only=pdf_file' : '')
        );

        if ($hasPdfFile){
            return (property_exists($response->body, 'pdf_file')) ? $response->body->pdf_file : null;
        }

        return (property_exists($response->body, 'file_url')) ? $response->body->file_url : null;
    }

    /***
     * @return array
     */
    public function getPendingCustomerInvoices()
    {
        $status = array(
            'status' => self::STATUS_PENDING,
            'externalStatus' => 'none'
        );
        return $this->findInvoicesByStatutes($status);

    }

    public function createInvoice(int $merchantId, int $userId): Invoice
    {
        $data = [
            'application' => $this->getApiUrl() . 'application/' . $this->getApplicationId() . '/',
            'issuer' => '/v1/merchant/'.$merchantId.'/',
            'receiver' => '/v1/user/'.$userId.'/',
            'legal_notices' => '-', // this legal notice is updated via izberg webhook "invoice updated"
        ];

        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::CUSTOMER_INVOICE_SLUG,
            $data
        );

        $invoice = $this->serializer->deserialize($response->raw_body, Invoice::class, 'json');

        if (!$invoice instanceof Invoice) {
            throw new ApiException('Failed creating Izberg invoice', 500);
        }

        return $invoice;
    }

    public function submitInvoice(int $invoiceId)
    {
        $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::CUSTOMER_INVOICE_SLUG . $invoiceId . '/submit/',
            null,
            [],
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    /**
     * patch an invoice
     * @param string $id the identifier of the invoice
     * @param array $fields key/value array of fields that need to be updated
     */
    public function patchInvoice($id, $fields)
    {
        $this->sendApiRequest(
            self::HTTP_PATCH_OPERATION,
            self::CUSTOMER_INVOICE_SLUG . $id . '/',
            json_encode($fields),
            [],
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    /**
     * helper to modify external status to ready
     * @param $id
     */
    public function changeCustomerInvoiceExternalStatus($id)
    {
        $this->patchInvoice($id, ["external_status" => "ready"]);
    }

    /**
     * helper to modify external status to sent
     * @param $id
     */
    public function changeCustomerEmittedInvoiceToSend($id)
    {
        $this->patchInvoice($id, ["external_status" => self::STATUS_SENT]);
    }

    /**
     * find invoices by statutes and externalStatus (optional)
     * @param array $status
     * @return array list of invoices with the matching statutes
     */
    public function findInvoicesByStatutes($status)
    {
        $ret = array();
        $query = self::PARAM_STATUS . $status['status'];
        if (array_key_exists('externalStatus', $status)) {
            $query .= self::PARAM_EXTERNAL_STATUS . $status['externalStatus'];
        }

        if (array_key_exists('paymentStatus', $status)) {
            $query .= self::PARAM_PAYMENT_STATUS . $status['paymentStatus'];
        }

        $query .= "&full_order_item=true";

        $limit = 20;
        $offset = 0;

        $query .= "&limit=" . $limit;

        $haveNotNext = false;

        while (!$haveNotNext) {

            $response = $this->sendApiRequest(
                self::HTTP_GET_OPERATION,
                self::CUSTOMER_INVOICE_SLUG . $query . "&offset=" . $offset
            );

            $ret = array_merge($ret, $response->body->objects);

            if ($response->body->meta->next == null) {
                $haveNotNext = true;
            } else {
                $offset = $offset + $limit;
            }
        };

        return $ret;
    }


    /**
     * get list of invoices with the emitted status
     * @return array
     */
    public function getEmittedInvoice()
    {
        $status = array(
            'status' => self::STATUS_EMITTED,
            'externalStatus' => self::STATUS_EMITTED
        );
        return $this->findInvoicesByStatutes($status);

    }


    public function getAttribute($id, $attribute)
    {
        $route = self::CUSTOMER_INVOICE_SLUG . $id;

        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            $route,
            '{}'
        );

        $temp = json_decode($response->raw_body, true);
        if ($temp === null) {
            $this->throwGenericError($response->code,
                $route,
                $response->raw_body);
        }

        // Here we have a correct json response with http 200
        if (isset($temp[$attribute])) {
            return $temp[$attribute];
        }
        return null;
    }


    public function removeFromLegalNotice($id, $txt)
    {
        $oldTxt = $this->getLegalNotice($id);

        $newTxt = str_replace(self::LEGAL_NOTICES_SEPARATOR . $txt, '', $oldTxt);

        $this->setLegalNotice($id, $newTxt);
    }

    public function getLegalNotice($id)
    {
        return $this->getAttribute($id, self::LEGAL_NOTICES);
    }

    public function setLegalNotice($id, $txt)
    {
        $this->patchInvoice($id, [self::LEGAL_NOTICES => $txt]);
    }

    public function fetchInvoiceByReceiverId($receiverId, array $options = [])
    {
        $options = $options + ['receiver' => $receiverId];
        $arguments = [];
        foreach ($options as $name => $value) {
            $arguments[] = $name . '=' . $value;
        }

        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::CUSTOMER_INVOICE_SLUG . '?' . implode('&', $arguments)
        );

        return $response->body;
    }

    /**
     * @param integer $invoiceId the id of the invoice
     * @param bool $fullOrderItem whether we must get the full details on each order item
     * @return Invoice
     */
    public function fetchInvoiceById(int $invoiceId): Invoice
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::CUSTOMER_INVOICE_SLUG . $invoiceId . '/?full_order_item=true'
        );

        $invoice = $this->serializer->deserialize($response->raw_body, Invoice::class, 'json');
        if (!$invoice instanceof Invoice) {
            throw new ApiException('Failed fetching izberg invoice', 500);
        }

        return $invoice;
    }

    public function fetchCreditNoteByInvoiceId($invoiceId)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::CREDIT_NOTE . '?invoice=' . $invoiceId
        );

        return $response->body->objects;
    }

    /**
     * @param int $offset
     * @param int $limit
     * @param \DateTimeImmutable|null $fromDate
     * @return FetchInvoicesResponse
     */
    public function fetchAllInvoices(int $offset = 0, int $limit = 0, ?\DateTimeImmutable $fromDate = null): FetchInvoicesResponse
    {
        $query = [
            'offset' => $offset,
            'limit' => $limit,
            'full_order_item' => true,
        ];

        if ($fromDate !== null) {
            $query['last_modified__gt'] = $fromDate->format('Y-m-d');
        }

        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::CUSTOMER_INVOICE_SLUG . '?' . http_build_query($query)
        );

        $fetchInvoicesResponse = $this->serializer->deserialize($response->raw_body, FetchInvoicesResponse::class, 'json');

        if(!$fetchInvoicesResponse instanceof FetchInvoicesResponse) {
            throw new ApiException('Failed fetching Izberg invoice', 500);
        }

        return $fetchInvoicesResponse;
    }
}
