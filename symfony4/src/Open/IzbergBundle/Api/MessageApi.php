<?php

namespace Open\IzbergBundle\Api;

use Open\IzbergBundle\Api;
use Open\IzbergBundle\Model\Message;
use Open\TicketBundle\Model\MessageActor;
use Symfony\Component\HttpFoundation\File\UploadedFile;

/**
 * Created by PhpStorm.
 * User: QAR14123
 * Date: 22/05/2018
 * Time: 10:35
 */

class MessageApi extends Api
{
    public function getUri():string
    {
        return 'message';
    }

    public function getItemClass(): string
    {
        return Message::class;
    }

    public function contactMerchant($object, $message, $sender, $receiver, UploadedFile ...$attachments){
        $multipartData = [
            [
                'name'     => 'application',
                'contents' => sprintf('/v1/application/%s/', $this->getApplicationId()),
            ],
            [
                'name'     => 'sender',
                'contents' => sprintf('/v1/user/%s/', $sender),
            ],
            [
                'name'     => 'receiver',
                'contents' => sprintf('/v1/merchant/%s/', $receiver),
            ],
            [
                'name'     => 'subject',
                'contents' => $object,
            ],
            [
                'name'     => 'body',
                'contents' => $message,
            ],
        ];

        $multipartData = array_merge(
            $multipartData,
            array_map(
                function(UploadedFile $attachment) {
                    return [
                        'name' => 'attachment',
                        'contents' => fopen($attachment->getPathname(), 'r+'),
                        'headers' => [self::HEADER_CONTENT_TYPE => $attachment->getMimeType()],
                        'filename' => $attachment->getClientOriginalName(),
                    ];
                },
                $attachments
            )
        );

        $options = [
            'headers' =>  array_filter(
                $this->getAuthorizationHeaders(),
                function($headerName) {
                    return ($headerName != self::HEADER_CONTENT_TYPE);
                },
                ARRAY_FILTER_USE_KEY
            ),
            'multipart' => $multipartData
        ];

        $response = $this->client->request(
            'POST',
            $this->getApiUrl() . 'message/',
            $options
        );

        return $response;
    }

    public function disputeMerchantOrder($object, $message, $sender, $receiver, $merchantOrderId)
    {
        $data = array(
            "sender"=> "/v1/user/".$sender."/",
            "receiver" => "/v1/merchant/".$receiver."/",
            "subject" => $object,
            "body" => $message,
            "merchant_order" => "/v1/merchant_order/" . $merchantOrderId . "/"
        );

        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            'message/',
            $data
        );

        return $response->body->id;
    }

    public function answerMerchant($object, $message, $sender, MessageActor $receiver, $urlParent, UploadedFile ...$attachments){

        $receiverUrlBuilder = function(MessageActor $receiverModel) {
            if ($receiverModel->isMerchantType()) {
                return sprintf('/v1/merchant/%d/', $receiverModel->getId());
            }

            if ($receiverModel->isApplicationType()) {
                return sprintf('/v1/application/%d/', $receiverModel->getId());
            }

            return null;
        };

        $multipartData = [
            [
                'name'     => 'application',
                'contents' => sprintf('/v1/application/%s/', $this->getApplicationId()),
            ],
            [
                'name'     => 'sender',
                'contents' => sprintf('/v1/user/%s/', $sender),
            ],
            [
                'name'     => 'receiver',
                'contents' => call_user_func($receiverUrlBuilder, $receiver),
            ],
            [
                'name'     => 'subject',
                'contents' => $object,
            ],
            [
                'name'     => 'body',
                'contents' => $message,
            ],
            [
                'name'     => 'root_msg',
                'contents' => $urlParent,
            ],
            [
                'name'     => 'parent_msg',
                'contents' => $urlParent,
            ],
        ];

        $multipartData = array_merge(
            $multipartData,
            array_map(
                function(UploadedFile $attachment) {
                    return [
                        'name' => 'attachment',
                        'contents' => fopen($attachment->getPathname(), 'r+'),
                        'headers' => [self::HEADER_CONTENT_TYPE => $attachment->getMimeType()],
                        'filename' => $attachment->getClientOriginalName(),
                    ];
                },
                $attachments
            )
        );

        $options = [
            'headers' =>  array_filter(
                $this->getAuthorizationHeaders(),
                function($headerName) {
                    return ($headerName != self::HEADER_CONTENT_TYPE);
                },
                ARRAY_FILTER_USE_KEY
            ),
            'multipart' => $multipartData
        ];

        $response = $this->client->request(
            'POST',
            $this->getApiUrl() . 'message/',
            $options
        );

        return $response->getStatusCode();
    }

    public function getBuyerOutboxMessages(int|string $id, string $getOnlyList = null)
    {
        $ret = array();

        $requestUrl = 'message/?parent_msg=none&from_user=' .$id;
        if (!empty($getOnlyList)) {
            $requestUrl .= '&only=' . $getOnlyList;
        }
        do {
            $response = $this->sendApiRequest(
                self::HTTP_GET_OPERATION,
                $requestUrl
            );

            $ret = array_merge($ret, $response->body->objects);
            $requestUrl = $response->body->meta->next;
        }while($requestUrl !== null);

        return $ret;
    }

    public function getBuyerInboxMessages(int|string $id, string $getOnlyList = null)
    {
        $ret = [];
        $requestUrl = 'message/?parent_msg=none&to_user=' .$id;
        if (!empty($getOnlyList)) {
            $requestUrl .= '&only=' . $getOnlyList;
        }
        do {
            $response = $this->sendApiRequest(
                self::HTTP_GET_OPERATION,
                $requestUrl
            );

            $ret = array_merge($ret, $response->body->objects);

            $requestUrl = $response->body->meta->next;
        }while($requestUrl !== null);

        return $ret;
    }

    public function getUnreadMessageForUser($id)
    {
        try {
            $response = $this->sendApiRequest(
                self::HTTP_GET_OPERATION,
                'message/?status=unread&aggregate_count_on=root_msg&only=root_msg,to_resource_uri&to_user=' . $id
            );
            return $response->body;
        } catch (\Exception) {
            return null;
        }
    }

    public function markAsRead($id)
    {
        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            'message/' . $id . '/read/',
            null,
            [],
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );

        return $response->code;
    }

    public function getMessageById(int|string $id, string $getOnlyList = null)
    {
        $requestUrl = 'message/' . $id;
        if (!empty($getOnlyList)) {
            $requestUrl .= '?only=' . $getOnlyList;
        }
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            $requestUrl
        );

        return $response->body;
    }

    public function getMessagesByRootId($id)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'message/?limit=100&root_msg=' . $id
        );

        return $response->body;
    }

    public function getDisputesByMerchantOrderId($merchantOrderId)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'message/?parent_msg=none&limit=100&merchant_order='.$merchantOrderId
        );

        return $response->body;
    }

    public function getMessageAttachments(int $messageId): array
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'message-attachment/?message=' . $messageId,
            [],
            [],
            [
                self::OPTION_IS_OPERATOR => true
            ]
        );

        return $response->body->objects ?? [];
    }

    public function fetchAttachmentUrl(string $attachmentId): ?string
    {

        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'message-attachment/?id=' . $attachmentId,
            [],
            [],
            [
                self::OPTION_IS_OPERATOR => true
            ]
        );
        $attachments = $response->body->objects ?? [];
        if (count($attachments)) {
            $attachment = array_shift($attachments);

            return $attachment->file_content;
        }

        return null;
    }
}
