<?php

namespace Open\IzbergBundle\Api;

use DateTimeImmutable;
use Open\IzbergBundle\Api;
use Open\IzbergBundle\ApiRequestParameter;
use Open\IzbergBundle\Model\FetchMerchantOrdersResponse;
use Open\IzbergBundle\Model\FetchOrderItemsResponse;
use Open\IzbergBundle\Model\FetchOrdersResponse;
use Open\IzbergBundle\Model\MerchantOrder;
use Open\IzbergBundle\Model\Order;
use Open\IzbergBundle\Model\OrderItem;
use Open\IzbergBundle\Model\OrderMerchant;
use Open\LogBundle\Utils\LogUtil;
use Unirest;

class OrderApi extends Api
{
    private const MERCHANT_ORDER_SLUG = 'merchant_order/';
    private const MERCHANT_ORDER_SHIPPING_OPTIONS_SLUG = 'merchant_order/%s/shipping_options';
    private const ORDER_SLUG = 'order/';
    private const JSON_TYPE = 'json';
    private const CREATE_ODER_SLUG = '/create_order/';
    private const CART_SLUG = 'cart/';
    private const AUTHORIZE_ORDER_SLUG = '/authorize_order/';
    private const CONFIRM_ORDER_SLUG = '/confirm/';
    private const CANCEL_ORDER_SLUG = '/cancel/';
    private const PROCESS_ORDER_SLUG = '/process/';
    private const ORDER_ITEM_SLUG = 'order_item/';

    public const MERCHANT_ORDER_STATUS_INITIAL = '0';
    public const MERCHANT_ORDER_STATUS_PAYMENT_AUTHORIZED = '60';
    public const MERCHANT_ORDER_STATUS_CONFIRMED = '80';
    public const MERCHANT_ORDER_STATUS_PROCESSED = '85';
    public const MERCHANT_ORDER_STATUS_FINALIZED = '110';
    public const MERCHANT_ORDER_STATUS_CANCELLED = '2000';
    public const MERCHANT_ORDER_STATUS_DELETED = '3000';

    public function getUri()
    {
        return 'order';
    }

    public function getItemClass()
    {
        return Order::class;
    }

    /***
     *
     * Retrieve ALL merchants orders with the corresponding status
     *
     * @param string $status
     *
     * @return mixed
     */
    public function getMerchantOrdersByStatus($status)
    {
        $ret = [];

        if (!in_array(
            $status,
            [
                self::MERCHANT_ORDER_STATUS_INITIAL,
                self::MERCHANT_ORDER_STATUS_CANCELLED,
                self::MERCHANT_ORDER_STATUS_CONFIRMED,
                self::MERCHANT_ORDER_STATUS_DELETED,
                self::MERCHANT_ORDER_STATUS_FINALIZED,
                self::MERCHANT_ORDER_STATUS_PAYMENT_AUTHORIZED,
                self::MERCHANT_ORDER_STATUS_PROCESSED,
            ]
        )) {
            return $ret;
        }

        $route = self::MERCHANT_ORDER_SLUG . '?status=' . $status;

        do {
            $response = $this->sendApiRequest(
                self::HTTP_GET_OPERATION,
                $route,
                '{}'
            );

            $fetchResponse = $this->serializer->deserialize($response->raw_body, FetchMerchantOrdersResponse::class, self::JSON_TYPE);

            if (!is_null($fetchResponse)) {
                $objects = $fetchResponse->getObjects();
                foreach ($objects as $object) {
                    $ret[] = $object;
                }
            } else {
                throw new ApiException(__CLASS__ . __METHOD__ . " - unable to deserialize response from Izberg into a ordersResponse object. Content was: " . $response->raw_body);
            }

            $route = $response->body->meta->next;
        } while ($route !== null);

        return $ret;
    }

    public function fetchMerchantOrdersByStatus($status = self::MERCHANT_ORDER_STATUS_INITIAL, int $offset = 0, int $limit = 10): FetchMerchantOrdersResponse
    {
        $route = self::MERCHANT_ORDER_SLUG . '?status=' . $status . '&offset=' . $offset . '&limit=' . $limit;
        $response = $this->sendApiRequest(self::HTTP_GET_OPERATION, $route);

        $fetchMerchantOrdersResponse = $this->serializer->deserialize($response->raw_body, FetchMerchantOrdersResponse::class, self::JSON_TYPE);

        if (!$fetchMerchantOrdersResponse instanceof FetchMerchantOrdersResponse) {
            throw new ApiException('Failed fetching merchant orders by status', 500);
        }

        return $fetchMerchantOrdersResponse;
    }

	/**
	 * Confirm a merchant order
	 */
	public function confirmOrder($merchant_order_id)
	{
        $route = self::MERCHANT_ORDER_SLUG . $merchant_order_id . self::CONFIRM_ORDER_SLUG;
        $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            $route,
            '{}',
            [],
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

	public function cancelOrder($orderId): Order
    {
	    $response = $this->sendApiRequest(
	        self::HTTP_POST_OPERATION,
            self::ORDER_SLUG . $orderId . self::CANCEL_ORDER_SLUG,
            '{}'
        );

        $order = $this->serializer->deserialize($response->raw_body, Order::class, self::JSON_TYPE);

        if (!$order instanceof Order) {
            throw new ApiException('Failed canceling izberg order', 500);
        }

        return $order;
    }

    /**
     * @deprecated
     *
     * @param $orderId
     * @param $slug
     * @return Unirest\Response|null
     */
    private function authorizeOrderOrMerchantOrder($orderId, $slug): ?Unirest\Response
    {
        $data = "{}";
        $timestamp = time();

        //get default headers for authenticated user
        $headers = [];

        //add signature to the request
        $toEncode = $data . ':' . $timestamp;
        $message_auth = hash_hmac('sha1', $toEncode, $this->getSecretKey());
        $headers['Application-signature'] = $message_auth;
        $headers['Application-nonce'] = $timestamp;

        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            $slug . $orderId . self::AUTHORIZE_ORDER_SLUG,
            $data,
            $headers
        );

        return $response;
    }

    /**
     * @deprecated replaced by \Open\IzbergBundle\Api\PaymentApi::authorizePayment
     * @param $orderId
     * @return Order
     */
    public function authorizeOrder($orderId): Order
    {
        $response = $this->authorizeOrderOrMerchantOrder($orderId, self::ORDER_SLUG);
        $order = $this->serializer->deserialize($response->raw_body, Order::class, self::JSON_TYPE);

        if (!$order instanceof Order) {
            throw new ApiException('Failed authorizing Izberg order', 500);
        }

        return $order;
    }

    /**
     * @deprecated replaced by \Open\IzbergBundle\Api\PaymentApi::authorizePayment
     * @param $orderId
     */
    public function authorizeMerchantOrder($orderId)
    {
        $this->authorizeOrderOrMerchantOrder($orderId, self::MERCHANT_ORDER_SLUG);
    }

    public function cancelMerchantOrder($merchantOrderId)
    {
        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::MERCHANT_ORDER_SLUG . $merchantOrderId . self::CANCEL_ORDER_SLUG,
            '{}'
        );

        return $this->serializer->deserialize($response->raw_body, OrderMerchant::class, self::JSON_TYPE);
    }

    public function processMerchantOrder($merchantOrderId)
    {
        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::MERCHANT_ORDER_SLUG . $merchantOrderId . self::PROCESS_ORDER_SLUG,
            '{}'
        );

        return $this->serializer->deserialize($response->raw_body, OrderMerchant::class, self::JSON_TYPE);
    }

    /**
     * Create an order from a cart
     */
    public function createOrderFromCart($cartId, $data = null): Order
    {
        $this->logger->info(
            'OrderApi::createOrderFromCart',
            LogUtil::buildContext([
                'cartID' => $cartId,
            ])
        );

        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            self::CART_SLUG . $cartId . self::CREATE_ODER_SLUG,
            $data
        );

        $this->logger->info(
            'OrderApi::createOrderFromCart - response',
            LogUtil::buildContext([
                'response' => $response->raw_body,
            ])
        );

        $order = $this->serializer->deserialize($response->raw_body, Order::class, self::JSON_TYPE);

        if (!$order instanceof Order) {
            throw new ApiException('Failed creating Izberg order from cart', 500);
        }

        return $order;
    }

    public function fetchAllOrders(int $offset = 0, int $limit = 10, ?DateTimeImmutable $lastSync = null): FetchOrdersResponse
    {
        $lastSyncDate = "";
        if($lastSync instanceof DateTimeImmutable){
            $lastSyncDate = "&last_modified__gte=".$lastSync->format('Y-m-d');
        }
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::ORDER_SLUG . '?offset=' . $offset . '&limit=' . $limit.$lastSyncDate
        );

        $fetchOrdersResponse =  $this->serializer->deserialize($response->raw_body, FetchOrdersResponse::class, self::JSON_TYPE);

        if (!$fetchOrdersResponse instanceof FetchOrdersResponse) {
            throw new ApiException('Failed fetching izberg orders', 500);
        }

        return $fetchOrdersResponse;
    }

    /**
     * fetch an order
     * @param integer $orderId the identifier of the order to fetch
     * @param bool $operator whether this is a operator (true) call or a user call (false)
     * @return Order the fetched order
     */
    public function fetchOrder($orderId)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::ORDER_SLUG . $orderId . '/'
        );

        $order = $this->serializer->deserialize($response->raw_body, Order::class, self::JSON_TYPE);

        if (!$order instanceof Order) {
            throw new ApiException('Failed fetching order', 500);
        }

        return  $order;
    }

    /**
     * fetch an order
     * @param integer $orderId the identifier of the order to fetch
     * @param bool $operator whether this is a operator (true) call or a user call (false)
     * @return mixed the fetched order
     */
    public function fetchStdClassOrder($orderId)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::ORDER_SLUG . $orderId . '/'
        );

        return $response->body;
    }

    /**
     * fetch orders of a user
     * @param integer $userId the identifier of the user
     * @param boolean $operator
     * @return FetchOrdersResponse the fetched order
     */
    public function fetchOrdersByUserId($userId, ?\DateTimeImmutable $fromDate = null, ?\DateTimeImmutable $toDate = null): FetchOrdersResponse
    {
        $route = self::ORDER_SLUG . '?user=' . $userId . '&limit=100';

        if ($fromDate) {
            $route .= "&created_on__gte=" . $fromDate->format("Y-m-d");
        }

        if ($toDate) {
            $route .= "&created_on__lte=" . $toDate->format("Y-m-d");
        }

        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            $route
        );

        $fetchOrdersResponse = $this->serializer->deserialize($response->raw_body, FetchOrdersResponse::class, self::JSON_TYPE);

        if (!$fetchOrdersResponse instanceof FetchOrdersResponse) {
            throw new ApiException('Failed fetching orders by user id', 500);
        }

        return $fetchOrdersResponse;
    }


    public function fetchOrdersByNumOrderId($numOrder)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::ORDER_SLUG . '?id_number=' . $numOrder
        );

        return $this->serializer->deserialize($response->raw_body, FetchOrdersResponse::class, self::JSON_TYPE);
    }

    /**
     * fetch orders of a user
     * @param integer $userId the identifier of the user
     * @param $size
     * @param int $offset
     * @param int $limit
     * @return FetchOrdersResponse the fetched order
     */
    public function fetchOrdersByUserIdWithPagination($userId, &$size, $offset = 0, $limit = 10): FetchOrdersResponse
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::ORDER_SLUG . '?user=' . $userId . '&offset=' . $offset . '&limit=' . $limit
        );

        $size = $response->body->meta->total_count;
        $fetchOrdersResponse = $this->serializer->deserialize($response->raw_body, FetchOrdersResponse::class, self::JSON_TYPE);

        if (!$fetchOrdersResponse instanceof FetchOrdersResponse) {
            throw new ApiException('Failed fetchingOrders by user id', 500);
        }

        return $fetchOrdersResponse;
    }

    /**
     * @param $id
     * @param $isAdmin
     * @return MerchantOrder
     */
    public function fetchMerchantOrderById($id): MerchantOrder
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::MERCHANT_ORDER_SLUG . $id
        );

        $merchantOrder = $this->serializer->deserialize($response->raw_body, MerchantOrder::class, self::JSON_TYPE);

        if (!$merchantOrder instanceof MerchantOrder) {
            throw new ApiException('Failed creating Izberg cart', 500);
        }

        return $merchantOrder;
    }

    /**
     * Fetch merchant orders for a user
     * @param $userId
     * @param $isAdmin
     * @return mixed the list of merchant orders for the specified user
     * @throws ApiException
     */
    public function fetchMerchantOrders($userId)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::MERCHANT_ORDER_SLUG . '?user=' . $userId
        );

        /**
         * @var FetchMerchantOrdersResponse fetchResponse
         */
        $fetchResponse = $this->serializer->deserialize($response->raw_body, FetchMerchantOrdersResponse::class, self::JSON_TYPE);

        if (is_null($fetchResponse)) {
            throw new ApiException(
                "OrderApi.fetchMerchantOrders - unable to deserialize response from Izberg into a ordersResponse object. Content was: " . $response->raw_body
            );
        }

        return $fetchResponse->getObjects();
    }

    /**
     * Fetch merchant orders for a order
     * @param int $userId
     * @return mixed the list of merchant orders for the specified user
     * @throws ApiException
     */
    public function fetchMerchantOrdersByOrder(int $orderId)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::MERCHANT_ORDER_SLUG . '?order=' . $orderId
        );

        /**
         * @var FetchMerchantOrdersResponse fetchResponse
         */
        $fetchResponse = $this->serializer->deserialize($response->raw_body, FetchMerchantOrdersResponse::class, self::JSON_TYPE);

        if (is_null($fetchResponse)) {
            throw new ApiException(
                "OrderApi.fetchMerchantOrdersByOrder - unable to deserialize response from Izberg into a ordersResponse object. Content was: " . $response->raw_body
            );
        }

        return $fetchResponse->getObjects();
    }

    /**
     * Fetch merchant orders for a order
     * @param int $userId
     * @return mixed the list of merchant orders for the specified user
     * @throws ApiException
     */
    public function fetchOrdersItemByMerchantOrder(int $merchantOrder)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::ORDER_ITEM_SLUG . '?merchant_order=' . $merchantOrder.'&full_delivery_dates=true'
        );
        /**
         * @var FetchOrderItemsResponse fetchResponse
         */
        $fetchResponse = $this->serializer->deserialize($response->raw_body, FetchOrderItemsResponse::class, self::JSON_TYPE);

        if (is_null($fetchResponse)) {
            throw new ApiException(
                "OrderApi.fetchOrdersItemByMerchantOrder - unable to deserialize response from Izberg into a OrderItemsResponse object. Content was: " . $response->raw_body
            );
        }

        return $fetchResponse->getObjects();
    }

    /**
     * @param $merchantOrderId
     * @return OrderMerchant
     * @throws ApiException
     */
    public function fetchMerchantsOrderByOrderId($merchantOrderId): OrderMerchant
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::MERCHANT_ORDER_SLUG . $merchantOrderId . '/'
        );

        $orderMerchant = $this->serializer->deserialize($response->raw_body, OrderMerchant::class, self::JSON_TYPE);

        if (!$orderMerchant instanceof OrderMerchant) {
            throw new ApiException(
                "Failed fetching merchant orders by order id - OrderApi.fetchMerchantOrders - unable to deserialize response from Izberg into a ordersResponse object. Content was: " . $response->raw_body,
                500
            );
        }

        return $orderMerchant;
    }

    public function getMerchantOrderLogs($merchantOrderId)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::MERCHANT_ORDER_SLUG . $merchantOrderId . '/logs/'
        );

        return $response->body->objects;
    }

    public function getMerchantOrderInvoices($merchantOrderId, bool $operator = false)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::MERCHANT_ORDER_SLUG . $merchantOrderId . '/invoices/',  //don't forget the ending slash
            null,
            [],
            [self::OPTION_IS_OPERATOR => $operator]
        );

        return $response->body->objects;
    }

    public function fetchMerchantOrderShippingOptions($id): ?array
    {
        $url = sprintf('merchant_order/%d/shipping_options', $id);
        $response = $this->sendApiRequest(self::HTTP_GET_OPERATION, $url);
        return $response->body->objects;
    }

    public function getOrderItemById($orderItemId)
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'order_item/' . $orderItemId . '/?full_delivery_dates=true'  //don't forget the ending slash
        );

        return $response->body;
    }

    public function setDeliveryDates(array $orderItemDeliveryDates)
    {
        $this->sendConcurrentApiRequest(
            array_map(
                function (array $orderItemDeliveryDate) {
                    [$orderItemId, $expectedDeliveryDate, $expectedShippingDate, $orderItemQuantity] = $orderItemDeliveryDate;
                    return (new ApiRequestParameter(
                        self::HTTP_POST_OPERATION,
                        'delivery_date/',
                        [
                            'cart_item' => null,
                            'effective_delivery_date' => null,
                            'effective_shipping_date' => null,
                            'expected_delivery_date' => $expectedDeliveryDate != null ? $expectedDeliveryDate->format('Y-m-d H:i:s') : null,
                            'expected_shipping_date' => $expectedShippingDate != null ? $expectedShippingDate->format('Y-m-d H:i:s') : null,
                            'quantity' => $orderItemQuantity,
                            'order_item' => "/v1/order_item/" . $orderItemId . "/",
                        ]
                    ));
                },
                $orderItemDeliveryDates
            ),
            true,
            1,
            [
                self::OPTION_NO_DATA_RETURN => true,
            ]
        );
    }

    /**
     * @param $orderItemId
     * @return OrderItem
     */
    public function fetchOrderItemById($orderItemId): OrderItem
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::ORDER_ITEM_SLUG . $orderItemId
        );

        $orderItem = $this->serializer->deserialize($response->raw_body, OrderItem::class, self::JSON_TYPE);

        if (!$orderItem instanceof OrderItem) {
            throw new ApiException('Failed fetching order item by id', 500);
        }

        return $orderItem;
    }

    public function getOrderItemBuyerInternalReferenceValue(int $orderItemId): ?string
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::ORDER_ITEM_SLUG . $orderItemId.'/'
        );
        return json_decode($response->raw_body, true)['extra_info']['Buyer-internal-ref'] ?? null;
    }

    public function getOrderItemFrameContractValue(int $orderItemId): ?string
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::ORDER_ITEM_SLUG . $orderItemId.'/'
        );
        return json_decode($response->raw_body, true)['extra_info']['frame_contract'] ?? null;
    }

    public function getOrderItemCartItemCommentValue(int $orderItemId): ?string
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::ORDER_ITEM_SLUG . $orderItemId.'/'
        );
        return json_decode($response->raw_body, true)['extra_info']['Cart-item-comment'] ?? null;
    }

    public function getOrderItemOrderLineValue(int $orderItemId): ?string
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::ORDER_ITEM_SLUG . $orderItemId.'/'
        );
        return json_decode($response->raw_body, true)['extra_info']['order-line'] ?? null;
    }

    public function getMerchantOrderItemExtraInfo(int $orderItemId): array
    {
        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            self::ORDER_ITEM_SLUG . $orderItemId.'/'
        );
        return json_decode($response->raw_body, true)['extra_info'];
    }

    public function updateMerchantOrderItemExtraInfo(int $orderItemId, string $field, ?string $value)
    {
        $extraInfo = $this->getMerchantOrderItemExtraInfo($orderItemId);
        $extraInfo[$field] = $value;
        $data = [
            'extra_info' => $extraInfo
        ];
        $this->sendApiRequest(
            self::HTTP_PUT_OPERATION,
            self::ORDER_ITEM_SLUG . $orderItemId . '/',
            $data
        );
    }


    public function fetchMerchantOrderParcels(int $merchantOrderId)
    {

        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            'parcel/?merchant_order=' . $merchantOrderId
        );

        $body = json_decode($response->raw_body);
        $parcels = !empty($body->objects)?$body->objects:[];

        foreach($parcels as $parcel) {
            if(empty($parcel->id)) continue;
            $parcel->items = $this->fetchParcelItems($parcel->id);
        }

        $this->logger->info(
            'OrderApi::fetchMerchantOrderParcels - response',
            LogUtil::buildContext([
                'parcels' => json_encode($parcels)
            ])
        );
    }

    public function fetchParcelItems(int $parcelId)
    {

        $response = $this->sendApiRequest(
            self::HTTP_GET_OPERATION,
            sprintf('parcel/%d/assignments', $parcelId)
        );

        $body = json_decode($response->raw_body);
        $items = !empty($body->objects)?$body->objects:[];

        $parcelItems = [];
        foreach($items as $item) {
            $parcelItems[] = ['resource_uri' => $item->resource_uri, 'quantity' => $item->quantity];
        }

        return $parcelItems;
    }


    public function createParcel($merchantOrderId, $shippingOption) {

        $data = [
            'application' => '/v1/application/'.$this->getApplicationId().'/',
            'merchant_order' => sprintf('/v1/merchant_order/%d/', (int)$merchantOrderId),
            'order_shipping_choice' => sprintf('/v1/order_shipping_choice/%d/', (int)$shippingOption->id),
            'tracking_number' => '',
        ];

        $response = $this->sendApiRequest(
            self::HTTP_POST_OPERATION,
            'parcel/',
            $data
        );

        $this->logger->info(
            'OrderApi::createParcel',
            LogUtil::buildContext([
                'reponse' => $response,
            ])
        );
    }

    /*
    public function addAnItemToParcel(int $parcelId, int $orderItemId, int $quantity): ParcelItem
    {
        $data = [
            'parcel' => sprintf('/v1/parcel/%d/', $parcelId),
            'order_item' => sprintf('/v1/order_item/%d/', $orderItemId),
            'quantity' => $quantity,
        ];

        $content = $this->post((new Request(self::ENDPOINT))->setData($data));

        $parcelItem = $this->serializer->deserialize($content, ParcelItem::class, 'json');

        return $parcelItem;
    } */

}
