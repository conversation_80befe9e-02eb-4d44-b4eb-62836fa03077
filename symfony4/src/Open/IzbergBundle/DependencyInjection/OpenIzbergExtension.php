<?php

namespace Open\IzbergBundle\DependencyInjection;

use AppBundle\Services\SecurityService;
use Open\IzbergBundle\Api;
use Open\IzbergBundle\Api\ApiClientManager;
use Open\IzbergBundle\Api\ApiConfigurator;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\Config\FileLocator;
use Symfony\Component\DependencyInjection\Reference;
use Symfony\Component\HttpKernel\DependencyInjection\Extension;
use Symfony\Component\DependencyInjection\Loader;

/**
 * This is the class that loads and manages your bundle configuration.
 *
 * @link http://symfony.com/doc/current/cookbook/bundles/extension.html
 */
final class OpenIzbergExtension extends Extension
{
    /**
     * {@inheritdoc}
     */
    public function load(array $configs, ContainerBuilder $container): void
    {
        $configuration = new Configuration();
        $config = $this->processConfiguration($configuration, $configs);

        $container->register(ApiConfigurator::class, ApiConfigurator::class)
            ->addArgument($config)
            ->setFactory([Api\ApiConfiguratorFactory::class, 'createApiConfigurator']);

        $container->register(ApiClientManager::class, ApiClientManager::class)
            ->setConfigurator([new Reference(ApiConfigurator::class), 'configure']);

        $loader = new Loader\YamlFileLoader($container, new FileLocator(__DIR__.'/../Resources/config'));
        $loader->load('services.yml');

        $taggedServices = $container->findTaggedServiceIds('izberg.api');

        foreach($taggedServices as $id => $tags) {
            $definition = $container->findDefinition($id);
            $definition
                ->setArguments([
                    new Reference(ApiClientManager::class),
                    new Reference('security.token_storage'),
                    new Reference('request_stack'),
                    new Reference('jms_serializer'),
                    new Reference(SecurityService::class),
                    new Reference('AppBundle\Services\AlstomCustomAttributes'),
                ]);
        }
    }
}
