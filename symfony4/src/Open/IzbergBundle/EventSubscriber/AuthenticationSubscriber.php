<?php
namespace Open\IzbergBundle\EventSubscriber;

use AppBundle\Entity\Company;
use AppBundle\Entity\User;
use AppBundle\Services\CartService;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\ORMException;
use FOS\UserBundle\Event\UserEvent;
use FOS\UserBundle\FOSUserEvents;
use Open\IzbergBundle\Api;
use Open\IzbergBundle\Api\ApiClientManager;
use Open\IzbergBundle\Api\ApiException;
use Open\IzbergBundle\Api\AuthenticationApi;
use Open\IzbergBundle\Model\AuthResponse;
use Open\IzbergBundle\Service\AttributeService;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Open\TicketBundle\Services\TicketService;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Session\Session;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Http\Event\InteractiveLoginEvent;
use Symfony\Component\Security\Http\Event\SwitchUserEvent;
use Symfony\Component\Security\Http\SecurityEvents;
use Symfony\Contracts\Translation\TranslatorInterface;

class AuthenticationSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private ?AuthenticationApi $authenticationAPI = null;
    private ?SessionInterface $session = null;
    private ?TokenStorageInterface $tokenStorage = null;
    private ?TranslatorInterface $translator = null;
    private ?EntityManagerInterface $em = null;
    private LoggerInterface $logger;
    private TicketService $ticketService;
    private CartService $cartService;
    private AttributeService $attributeService;
    private ApiClientManager $apiClientManager;

    private $requestStack;

    /**
     * AuthenticationSubscriber constructor.
     * @param AuthenticationApi|null $authenticationAPI
     * @param RequestStack|null $requestStack
     * @param TokenStorageInterface|null $tokenStorage
     * @param TranslatorInterface|null $translator
     * @param EntityManagerInterface|null $em
     * @param ApiClientManager $apiClientManager
     * @param TicketService $ticketService
     * @param CartService $cartService
     * @param AttributeService $attributeService
     */
    public function __construct(
        ?AuthenticationApi $authenticationAPI,
        ?RequestStack $requestStack,
        ?TokenStorageInterface $tokenStorage,
        ?TranslatorInterface $translator,
        ?EntityManagerInterface $em,
        ApiClientManager $apiClientManager,
        TicketService $ticketService,
        CartService $cartService,
        AttributeService $attributeService
    )
    {
        $this->authenticationAPI = $authenticationAPI;
        $this->requestStack = $requestStack;
        $this->tokenStorage = $tokenStorage;
        $this->translator = $translator;
        $this->em = $em;
        $this->apiClientManager = $apiClientManager;
        $this->ticketService = $ticketService;
        $this->cartService = $cartService;
        $this->attributeService = $attributeService;
    }

    /**
     * Build an array of events we want to listen to
     * @return array<string, mixed>
     */
    public static function getSubscribedEvents(): array
    {
        // Catch login actions to trigger the SSO authentication with Izberg API
        return array(
            FOSUserEvents::SECURITY_IMPLICIT_LOGIN => 'onImplicitLogin',
            SecurityEvents::INTERACTIVE_LOGIN => 'onInteractiveLogin',
            SecurityEvents::SWITCH_USER => 'onSwitchUser',
        );
    }


    /**
     * Login event callback
     * @param UserEvent $event
     */
    public function onImplicitLogin(UserEvent $event)
    {
        // Retrieve current user
        $user = $event->getUser();

        if (!$user instanceof User) {
            return;
        }

        // Authenticate user with Izberg
        $this->authenticateUser($user);
    }

    /**
     * Login event callback
     * @param InteractiveLoginEvent $event
     */
    public function onInteractiveLogin(InteractiveLoginEvent $event)
    {
        // Retrieve current user
        $user = $event->getAuthenticationToken()->getUser();

        if (!$user instanceof User) {
            return;
        }

        // Authenticate user with Izberg
        $this->authenticateUser($user);
    }

    public function onSwitchUser(SwitchUserEvent $event)
    {
        // Retrieve current user
        $user = $event->getTargetUser();

        if (!$user instanceof User) {
            return;
        }

        // Authenticate user with Izberg
        $this->authenticateUser($user);
    }

    /**
     * Retrieve and Store izberg api token in session
     * @param mixed $user
     */
    private function authenticateUser(User $user): void
    {
        //Izberg user authentication is based on the user company
        //admin user has no company, so this code is run only for buyer users

        $this->session = $this->requestStack->getSession();

        if ($user->getCompany() !== null){
            try {
                /**
                 * @var Company $company
                 */
                $company = $user->getCompany();

                //if email hasn't been previously created
                if (empty($company->getIzbergEmail())) {
                    $company->generateIzbergEmail($this->apiClientManager->getConfiguration()->getSellerEmailDomain());
                    $this->em->persist($company);
                    $this->em->flush();
                }

                /**
                 * @var string | false $escapedCompanyName
                 */

                $escapedCompanyName = $this->escapeCompanyName($company->getName());
                if (!$escapedCompanyName){
                    throw new ApiException("Unable to escape company name: ".$company->getName());
                }

                // Request authentication token

                /**
                 * @var AuthResponse $response
                 */

                //if an anonymous session already exists, use it
                $fromSession = null;
                if ($this->session->has(Api::IZBERG_SESSION_ID)){
                    $fromSession = $this->session->get(Api::IZBERG_SESSION_ID);
                }

                $response = $this->authenticationAPI->authenticateUser(
                    $company->getIzbergEmail(),
                    $company->getIzbergUsername(),
                    $escapedCompanyName,
                    " ",
                    $fromSession
                );


                if($company->getIzbergUsername() === null || $company->getIzbergUserId() === null){

                    $company->setIzbergUsername($response->getUser()->getUsername());
                    $company->setIzbergUserId($response->getUser()->getId());
                    $this->em->persist($company);
                    $this->em->flush();
                }


                //clear old values
                if ($this->session->has(Api::IZBERG_SESSION_ID)){
                    $this->session->remove(Api::IZBERG_SESSION_ID);
                }
                if ($this->session->has(Api::IZBERG_ACCESS_TOKEN)){
                    $this->session->remove(Api::IZBERG_ACCESS_TOKEN);
                }
                if ($this->session->has(Api::IZBERG_USERNAME)){
                    $this->session->remove(Api::IZBERG_USERNAME);
                }
                if ($this->session->has(Api::IZBERG_USER_ID)){
                    $this->session->remove(Api::IZBERG_USER_ID);
                }

                $this->session->set(Api::IZBERG_ACCESS_TOKEN, $response->getAccessToken());
                $this->session->set(Api::IZBERG_USERNAME, $response->getUser()->getUsername());
                $this->session->set(Api::IZBERG_USER_ID, $response->getUser()->getId());

                if(!$company->isCustomAttributesExist()){
                    if($company->getMainAddress()->getCountry()->isInEU()){
                        $this->attributeService->updateCustomerAttributes(
                            $response->getUser()->getId(),
                            [
                                'customer_vat_number' => $company->getIdentification(),
                            ]
                        );
                    }

                    $company->setCustomAttributesExist(true);
                    $this->em->persist($company);
                    $this->em->flush();
                }


            }catch (ApiException | ORMException $e){

                $this->logger->error(
                    "Unexpected error occurred while authenticate izberg user: ".$e->getMessage(),
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::TECHNICAL_ERROR,
                        LogUtil::USER_NAME => $user->getUsername(),
                        "ExceptionType" => get_class($e)
                    ])
                );

                //we were enable to authenticate the user here
                $this->tokenStorage->setToken(null);
                $this->session->invalidate();

                // translate the message
                $message = $this->translator->trans('form.login.failed', array(), 'IzbergBundle');


                if($this->session instanceof Session){
                    $this->session->getFlashBag()->add('error', $message);
                }
            }
        }
    }

    /**
     * escape the company name with the following rules:
     *   - only alpha characters
     *   - max length is 15 characters
     * @param string $companyName
     * @return string the formatted company name or false if an error occurred
     */
    public static function escapeCompanyName (string $companyName): string
    {
        return substr(preg_replace('/[^a-z0-9À-ú\s\-]/i', '', $companyName), 0, 30);
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
