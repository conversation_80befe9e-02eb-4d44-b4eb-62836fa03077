<?php
/**
 * Created by PhpStorm.
 * User: CMA15450
 * Date: 24/03/2017
 * Time: 16:27
 */
namespace Open\IzbergBundle\Model;

use Symfony\Component\Validator\Constraints as Assert;
use <PERSON><PERSON>\Serializer\Annotation\Type;
use <PERSON><PERSON>\Serializer\Annotation as JSM;


/**
 * Entity to map an Address object
 */
#[JSM\ExclusionPolicy('none')]
class Address
{

    #[Type('integer')]
    private $id;

    #[Assert\NotBlank]
    #[Type('string')]
    private $name;

    #[Assert\NotBlank]
    #[Type('string')]
    private $address;

    #[Type('string')]
    private $address2;

    #[Assert\NotBlank]
    #[Type('integer')]
    private $zipcode;

    #[Assert\NotBlank]
    #[Type('string')]
    private $city;

    #[Assert\NotBlank]
    #[Type('Open\IzbergBundle\Model\Country')]
    #[JSM\Accessor(getter: 'getCountry', setter: 'setCountry')]
    private $country;


    /**
     * Get id
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set id
     * @param $id
     * @return integer
     */
    public function setId($id)
    {
        return $this->id = $id;
    }

    /**
     * Get address name
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * set Address name
     * @param $name
     * @return string
     */
    public function setName($name)
    {
        return $this->name = $name;
    }


    /**
     * Get address line 1
     * @return string
     */
    public function getAddress()
    {
        return $this->address;
    }

    /**
     * Set address line 1
     * @param $address
     * @return string
     */
    public function setAddress($address)
    {
        return $this->address = $address;
    }

    /**
     * Get address line 2
     * @return string
     */
    public function getAddress2()
    {
        return $this->address2;
    }

    /**
     * Set address line 2
     * @param $address
     * @return string
     */
    public function setAddress2($address)
    {
        return $this->address2 = $address;
    }

    /**
     * Get Zip Code
     * @return mixed
     */
    public function getZipcode()
    {
        return $this->zipcode;
    }

    /**
     * Set Zip Code
     * @param $zipcode
     * @return mixed
     */
    public function setZipcode($zipcode)
    {
        return $this->zipcode = $zipcode;
    }

    /**
     * Get city name
     * @return string
     */
    public function getCity()
    {
        return $this->city;
    }

    /**
     * Set city name
     * @param $city
     * @return string
     */
    public function setCity($city)
    {
        return $this->city = $city;
    }

    /**
     * Get Country
     * @return mixed
     */
    public function getCountry()
    {
        // Return the resource uri instead of the whole country
        return $this->country;
    }

    /**
     * Set country
     * @param string Country $country
     */
    public function setCountry($country)
    {
        /** @var Country $country */
        $this->country = $country;
    }

}
