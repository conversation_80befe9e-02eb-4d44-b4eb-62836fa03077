<?php
/**
 * Created by PhpStorm.
 * User: CMA15450
 * Date: 24/03/2017
 * Time: 16:25
 */


namespace Open\IzbergBundle\Model;

use Symfony\Component\Validator\Constraints as Assert;
use <PERSON><PERSON>\Serializer\Annotation\Type;
use <PERSON><PERSON>\Serializer\Annotation as J<PERSON>;

/**
 * Entity to map an AuthSSO response object
 */
#[JMS\ExclusionPolicy('none')]
class AuthResponse
{

    #[Assert\NotBlank]
    #[Type('string')]
    private $access_token;

    #[Type('string')]
    private $api_key;


    #[Type('string')]
    private $display_name;

    #[Type('string')]
    private $username;

    /**
     * @var \Open\IzbergBundle\Model\IzbergUser
     */
    #[Assert\NotBlank]
    #[Type('Open\IzbergBundle\Model\IzbergUser')]
    private $user;

    #[Assert\NotBlank]
    #[Type('string')]
    private $email;


    #[J<PERSON>\Exclude]
    private $session;

    /**
     * Get the access token
     * @return string
     */
    public function getAccessToken()
    {
        return $this->access_token;
    }


    /**
     * Set access token
     * @param $token
     * @return mixed
     */
    public function setAccessToken($token)
    {
        return $this->access_token = $token;
    }

    /**
     * Return Authenticated user (Izberg)
     * @return \Open\IzbergBundle\Model\IzbergUser
     */
    public function getUser()
    {
        return $this->user;
    }

    /**
     * Set Authenticated user (Izberg)
     * @param $user
     * @return mixed
     */
    public function setUser($user)
    {
        return $this->user = $user;
    }

    /**
     * Get authenticated user E-Mail address
     * @return mixed
     */
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * Set authenticated user E-mail address
     * @param $email
     * @return mixed
     */
    public function setEmail($email)
    {
        return $this->email = $email;
    }

    /**
     * @return mixed
     */
    public function getSession()
    {
        return $this->session;
    }

    /**
     * @param mixed $session
     */
    public function setSession($session): void
    {
        $this->session = $session;
    }

    /**
     * @return mixed
     */
    public function getApiKey()
    {
        return $this->api_key;
    }

    /**
     * @param mixed $api_key
     */
    public function setApiKey($api_key): void
    {
        $this->api_key = $api_key;
    }

    /**
     * @return mixed
     */
    public function getDisplayName()
    {
        return $this->display_name;
    }

    /**
     * @param mixed $display_name
     */
    public function setDisplayName($display_name): void
    {
        $this->display_name = $display_name;
    }

    /**
     * @return mixed
     */
    public function getUsername()
    {
        return $this->username;
    }

    /**
     * @param mixed $username
     */
    public function setUsername($username): void
    {
        $this->username = $username;
    }


}
