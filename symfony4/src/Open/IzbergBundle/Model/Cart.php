<?php

namespace Open\IzbergBundle\Model;

use Doctrine\Common\Collections\ArrayCollection;
use <PERSON><PERSON>\Serializer\Annotation\Type;
use <PERSON><PERSON>\Serializer\Annotation as JSM;

class Cart
{
    #[Type('double')]
    private $amount_subtotal = 0;


    #[Type('Open\IzbergBundle\Model\Address')]
    private $billing_address = null;

    #[Type('Open\IzbergBundle\Model\Country')]
    #[JSM\Accessor(getter: 'getCountry', setter: 'setCountry')]
    private $country = null;


    #[Type('string')]
    private $currency = "EUR";


    #[Type('double')]
    private $discount_amount_vat_excluded = 0;

    #[Type('double')]
    private $discount_amount_vat_included = 0;

    #[Type('array')]
    private $entered_discount_codes = [];

    #[Type('string')]
    private $estimated_shipping_country = "Metropolitan France and Monaco";

    #[Type('integer')]
    private $id = 0;

    #[Type('integer')]
    private $items_count = 0;



    #[Type('string')]
    private $session_id = null;

    #[Type('Open\IzbergBundle\Model\Address')]
    private $shipping_address = null;

    #[Type('double')]
    private $shipping_amount = 0;

    #[Type('array')]
    private $shipping_details = [];

    #[Type('string')]
    private $shipping_speed = "standard";

    #[Type('string')]
    private $shipping_speed_localized = "Standard";


    #[Type('double')]
    private $total_amount = 0;

    #[Type('double')]
    private $total_amount_before_discount = 0;

    #[Type('double')]
    private $total_amount_before_voucher = 0;

    #[Type('Open\IzbergBundle\Model\IzbergUser')]
    private $user = null;

    #[Type('double')]
    private $voucher_amount = 0;


    #[Type('ArrayCollection<CartItem>')]
    private $items = null;

    /**
     * Get Cart Id
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set Cart Id
     * @param $id
     */
    public function setId($id): self
    {
        $this->id = intval($id);

        return $this;
    }

    /**
     * Get cart Items
     * @return ArrayCollection
     */
    public function getItems()
    {
        if (!$this->items) {
            return new ArrayCollection();
        } else {
            return $this->items;
        }
    }

    /**
     * Set cart items
     * @param ArrayCollection $domain
     */
    public function setItems(ArrayCollection $domain): self
    {
        $this->items = $domain;

        return $this;
    }

    /**
     * Get cart Country
     * @return Country
     */
    public function getCountry()
    {
        return $this->country;
    }

    /**
     * Set cart country
     * @param $country
     */
    public function setCountry($country): self
    {
        $this->country = $country;

        // Some api return a resource uri instead of a country
        if (is_string($country)) {
            $fake_country = new Country();
            $fake_country->setResourceUri($country);

            $this->country = $fake_country;
        }

        return $this;
    }

    /**
     * Get cart amount (vat excluded)
     * @return float
     */
    public function getAmountSubtotal()
    {
        return $this->amount_subtotal;
    }

    /**
     * Set cart amount (vat excluded)
     * @param $amount
     * @return $this
     */
    public function setAmountSubtotal($amount): self
    {
        $this->amount_subtotal = floatval($amount);

        return $this;
    }

    /**
     * @return mixed
     */
    public function getBillingAddress()
    {
        return $this->billing_address;
    }

    /**
     * @param mixed $billing_address
     */
    public function setBillingAddress($billing_address): self
    {
        $this->billing_address = $billing_address;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * @param mixed $currency
     */
    public function setCurrency($currency): self
    {
        $this->currency = $currency;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getDiscountAmountVatExcluded()
    {
        return $this->discount_amount_vat_excluded;
    }

    /**
     * @param mixed $discount_amount_vat_excluded
     */
    public function setDiscountAmountVatExcluded($discount_amount_vat_excluded): self
    {
        $this->discount_amount_vat_excluded = $discount_amount_vat_excluded;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getDiscountAmountVatIncluded()
    {
        return $this->discount_amount_vat_included;
    }

    /**
     * @param mixed $discount_amount_vat_included
     */
    public function setDiscountAmountVatIncluded($discount_amount_vat_included): self
    {
        $this->discount_amount_vat_included = $discount_amount_vat_included;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getEnteredDiscountCodes()
    {
        return $this->entered_discount_codes;
    }

    /**
     * @param mixed $entered_discount_codes
     */
    public function setEnteredDiscountCodes($entered_discount_codes): self
    {
        $this->entered_discount_codes = $entered_discount_codes;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getEstimatedShippingCountry()
    {
        return $this->estimated_shipping_country;
    }

    /**
     * @param mixed $estimated_shipping_country
     */
    public function setEstimatedShippingCountry($estimated_shipping_country): self
    {
        $this->estimated_shipping_country = $estimated_shipping_country;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getItemsCount()
    {
        return $this->items_count;
    }

    /**
     * @param mixed $items_count
     */
    public function setItemsCount($items_count): self
    {
        $this->items_count = $items_count;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getSessionId()
    {
        return $this->session_id;
    }

    /**
     * @param mixed $session_id
     */
    public function setSessionId($session_id): self
    {
        $this->session_id = $session_id;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getShippingAddress()
    {
        return $this->shipping_address;
    }

    /**
     * @param mixed $shipping_address
     */
    public function setShippingAddress($shipping_address): self
    {
        $this->shipping_address = $shipping_address;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getShippingAmount()
    {
        return $this->shipping_amount;
    }

    /**
     * @param mixed $shipping_amount
     */
    public function setShippingAmount($shipping_amount): self
    {
        $this->shipping_amount = $shipping_amount;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getShippingDetails()
    {
        return $this->shipping_details;
    }

    /**
     * @param mixed $shipping_details
     */
    public function setShippingDetails($shipping_details): self
    {
        $this->shipping_details = $shipping_details;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getShippingSpeed()
    {
        return $this->shipping_speed;
    }

    /**
     * @param mixed $shipping_speed
     */
    public function setShippingSpeed($shipping_speed): self
    {
        $this->shipping_speed = $shipping_speed;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getShippingSpeedLocalized()
    {
        return $this->shipping_speed_localized;
    }

    /**
     * @param mixed $shipping_speed_localized
     */
    public function setShippingSpeedLocalized($shipping_speed_localized): self
    {
        $this->shipping_speed_localized = $shipping_speed_localized;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getTotalAmount()
    {
        return $this->total_amount;
    }

    /**
     * @param mixed $total_amount
     */
    public function setTotalAmount($total_amount): self
    {
        $this->total_amount = $total_amount;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getTotalAmountBeforeDiscount()
    {
        return $this->total_amount_before_discount;
    }

    /**
     * @param mixed $total_amount_before_discount
     */
    public function setTotalAmountBeforeDiscount($total_amount_before_discount): self
    {
        $this->total_amount_before_discount = $total_amount_before_discount;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getTotalAmountBeforeVoucher()
    {
        return $this->total_amount_before_voucher;
    }

    /**
     * @param mixed $total_amount_before_voucher
     */
    public function setTotalAmountBeforeVoucher($total_amount_before_voucher): self
    {
        $this->total_amount_before_voucher = $total_amount_before_voucher;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getUser()
    {
        return $this->user;
    }

    /**
     * @param mixed $user
     */
    public function setUser($user): self
    {
        $this->user = $user;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getVoucherAmount()
    {
        return $this->voucher_amount;
    }

    /**
     * @param mixed $voucher_amount
     */
    public function setVoucherAmount($voucher_amount): self
    {
        $this->voucher_amount = $voucher_amount;

        return $this;
    }
}
