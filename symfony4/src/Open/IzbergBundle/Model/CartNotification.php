<?php

namespace Open\IzbergBundle\Model;

use <PERSON><PERSON>\Serializer\Annotation\Type;
use <PERSON>ymfony\Component\Validator\Constraints as Assert;

class CartNotification
{
    #[Assert\NotBlank]
    #[Type('integer')]
    private $id;

    #[Assert\NotBlank]
    #[Type('string')]
    private $status;

    #[Type('Open\IzbergBundle\Model\Cart')]
    private $cart;

    #[Type('Open\IzbergBundle\Model\CartItem')]
    private $cart_item;

    #[Type('string')]
    private $custom_message;

    #[Type('string')]
    private $notif_type;

    public function getId()
    {
        return $this->id;
    }

    public function setId($id): void
    {
        $this->id = $id;
    }

    public function getStatus()
    {
        return $this->status;
    }

    public function setStatus($status): void
    {
        $this->status = $status;
    }

    public function getCart(): ?Cart
    {
        return $this->cart;
    }

    public function setCart($cart): void
    {
        $this->cart = $cart;
    }

    public function getCartItem(): ?CartItem
    {
        return $this->cart_item;
    }

    public function setCartItem($cart_item): void
    {
        $this->cart_item = $cart_item;
    }

    public function getCustomMessage()
    {
        return $this->custom_message;
    }

    public function setCustomMessage($custom_message): void
    {
        $this->custom_message = $custom_message;
    }

    public function getNotifType()
    {
        return $this->notif_type;
    }

    public function setNotifType($notif_type): void
    {
        $this->notif_type = $notif_type;
    }
}
