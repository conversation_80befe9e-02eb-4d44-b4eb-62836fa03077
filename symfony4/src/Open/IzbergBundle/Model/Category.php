<?php
/**
 * Created by PhpStorm.
 * User: CMA15450
 * Date: 24/03/2017
 * Time: 16:17
 */

namespace Open\IzbergBundle\Model;

use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Component\Validator\Constraints as Assert;
use <PERSON><PERSON>\Serializer\Annotation as JSM;
use <PERSON><PERSON>\Serializer\Annotation\Type;


#[JSM\ExclusionPolicy('none')]
class Category
{

    #[Type("integer")]
    private $id;

    #[Type('string')]
    private $external_id;

    #[Assert\NotBlank]
    #[Type('string')]
    private $name;

    #[Type('string')]
    private $image;

    #[Type('ArrayCollection<Open\IzbergBundle\Model\Resource>')]
    private $children;

    #[Type('ArrayCollection<Open\IzbergBundle\Model\Resource>')]
    private $parents;

    #[Assert\NotBlank]
    #[Type('string')]
    private $resource_uri;

    #[Assert\NotBlank]
    #[Type('integer')]
    private $status;

    #[Assert\NotBlank]
    #[Type('integer')]
    private $sort_order = 10;

    #[Assert\NotBlank]
    #[Type('integer')]
    private $weight = 0;

    public function getParents()
    {
        return $this->parents;
    }

    public function getName()
    {
        return $this->name;
    }

    public function getImage()
    {
      return $this->image;
    }

    public function setImage($image)
    {
      $this->image = $image;
    }

  public function getId()
    {
        return $this->id;
    }

    /**
     * @return string|null
     */
    public function getExternalId(): ?string
    {
      return $this->external_id;
    }

    /**
     * @param null|string $external_id
     */
    public function setExternalId(?string $external_id): void
    {
      $this->external_id = $external_id;
    }


  public function hasParents()
    {
        return $this->parents->count() > 0;
    }

    public function hasChildren()
    {
        return $this->children->count() > 0;
    }

    public function getChildren()
    {
        return $this->children;
    }

    /**
     * @return mixed
     */
    public function getResourceUri()
    {
        return $this->resource_uri;
    }

    /**
     * @param mixed $resource_uri
     */
    public function setResourceUri($resource_uri): void
    {
        $this->resource_uri = $resource_uri;
    }

    /**
     * @return mixed
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param mixed $status
     */
    public function setStatus($status): void
    {
        $this->status = $status;
    }

    /**
     * @return mixed
     */
    public function getSortOrder()
    {
        return $this->sort_order;
    }

    /**
     * @param mixed $sort_order
     */
    public function setSortOrder($sort_order): void
    {
        $this->sort_order = $sort_order;
    }

    /**
     * @return mixed
     */
    public function getWeight()
    {
        return $this->weight;
    }

    /**
     * @param mixed $weight
     */
    public function setWeight($weight): void
    {
        $this->weight = $weight;
    }




}
