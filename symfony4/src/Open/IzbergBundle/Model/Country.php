<?php
namespace Open\IzbergBundle\Model;

use <PERSON><PERSON>\Serializer\Annotation\Type;
use <PERSON><PERSON>\Serializer\Annotation as JSM;

/**
 * Entity to map a country object
 */
#[JSM\ExclusionPolicy('none')]
class Country
{

    #[Type('integer')]
    private $id = 0;

    #[Type('string')]
    private $code;
    #[Type('string')]
    private $continent = null;
    #[Type('array')]
    private $languages = [];
    #[Type('double')]
    private $lat = null;
    #[Type('double')]
    private $lng = null;
    #[Type('string')]
    private $name;
    #[Type('string')]
    private $phone_prefix = null;
    #[Type('string')]
    private $resource_uri;
    #[Type('integer')]
    private $sort_order = 10;
    #[Type('integer')]
    private $weight = 100;

    /**
     * Get country code
     * @return string
     */
    public function getCode()
    {
        return $this->code;
    }

    /**
     * Set country code
     * @param $code string
     * @return string
     */
    public function setCode($code)
    {
        return $this->code = $code;
    }

    /**
     * Get continent (type??)
     * @return mixed
     */
    public function getContinent()
    {
        return $this->continent;
    }

    /**
     * Set continent (type??)
     * @param $continent
     * @return
     */
    public function setContinent($continent)
    {
        return $this->continent = $continent;
    }

    /**
     * Get country id
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set country Id
     * @param $id
     * @return integer
     */
    public function setId($id)
    {
        return $this->id = $id;
    }

    /**
     * Get country languages (array of type ?)
     * @return array
     */
    public function getLanguages()
    {
        return $this->languages;
    }

    /**
     * Set country languages
     * @param $languages
     * @return array
     */
    public function setLanguages($languages)
    {
        return $this->languages = $languages;
    }

    /**
     * Get latitude
     * @return mixed
     */
    public function getLat()
    {
        return $this->lat;
    }

    /**
     * Set country latitude
     * @param $lat
     * @return mixed
     */
    public function setLat($lat)
    {
        return $this->lat = $lat;
    }

    /**
     * Get country longitude
     * @return mixed
     */
    public function getLng()
    {
        return $this->lng;
    }

    /**
     * Set country longitude
     * @param $lng
     */
    public function setLng($lng)
    {
        $this->lng = $lng;
    }

    /**
     * Get country name
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Set country name
     * @param $name string
     * @return string
     */
    public function setName($name)
    {
        return $this->name = $name;
    }

    /**
     * Get phone prefix
     * @return string
     */
    public function getPhonePrefix()
    {
        return $this->phone_prefix;
    }

    /**
     * Set phone prefix
     * @param $phone_prefix
     * @return string
     */
    public function setPhonePrefix($phone_prefix)
    {
        return $this->phone_prefix = $phone_prefix;
    }

    /**
     * Get resource URI
     * @return string
     */
    public function getResourceUri()
    {
        return $this->resource_uri;
    }

    /**
     * Set resource URI
     * @param $uri
     * @return string
     */
    public function setResourceUri($uri)
    {
        return $this->resource_uri = $uri;
    }

    /**
     * Get sorting order
     * @return integer
     */
    public function getSortOrder()
    {
        return $this->sort_order;
    }

    /**
     * Set sorting order
     * @param $order
     * @return integer
     */
    public function setSortOrder($order)
    {
        return $this->sort_order = $order;
    }

    /**
     * Get sorting weight
     * @return integer
     */
    public function getWeight()
    {
        return $this->weight;
    }

    /**
     * Set sorting weight
     * @param $weight
     * @return integer
     */
    public function setWeight($weight)
    {
        return $this->weight = $weight;
    }

}
