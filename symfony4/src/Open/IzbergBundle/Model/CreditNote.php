<?php

namespace Open\IzbergBundle\Model;

use <PERSON><PERSON>\Serializer\Annotation as JSM;
use <PERSON><PERSON>\Serializer\Annotation\Type;
use Open\IzbergBundle\Model\Traits\InvoiceCommonCreditNoteTrait;

/**
 * Object to map an izberg credit_note object
 */
#[JSM\ExclusionPolicy('none')]
class CreditNote
{
    use InvoiceCommonCreditNoteTrait;

    #[Type('integer')]
    private $id;

    #[Type('integer')]
    private $pk;

    #[Type('string')]
    private $created_on;

    #[Type('double')]
    private $total_amount;

    #[Type('double')]
    private $total_amount_with_taxes;

    #[Type('string')]
    private $pdf_file;

    #[Type('string')]
    private $issuer_name;

    #[Type('Open\IzbergBundle\Model\IzbergUser')]
    private $receiver;

    #[Type('string')]
    private $currency;

    #[Type('string')]
    private $payment_status;

    #[Type('string')]
    private $status;

    #[Type('ArrayCollection<Open\IzbergBundle\Model\CreditNoteLine>')]
    private $credit_note_lines;

    #[Type('string')]
    private $id_number;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id): void
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getPk()
    {
        return $this->pk;
    }

    /**
     * @param mixed $pk
     */
    public function setPk($pk): void
    {
        $this->pk = $pk;
    }

    /**
     * @return mixed
     */
    public function getCreatedOn()
    {
        return $this->created_on;
    }

    public function created_on()
    {
        return $this->getCreatedOn();
    }

    /**
     * @param mixed $created_on
     */
    public function setCreatedOn($created_on): void
    {
        $this->created_on = $created_on;
    }

    /**
     * @return mixed
     */
    public function getTotalAmount()
    {
        return $this->total_amount;
    }

    public function total_amount()
    {
        return $this->getTotalAmount();
    }

    /**
     * @param mixed $total_amount
     */
    public function setTotalAmount($total_amount): void
    {
        $this->total_amount = $total_amount;
    }

    /**
     * @return mixed
     */
    public function getTotalAmountWithTaxes()
    {
        return $this->total_amount_with_taxes;
    }

    /**
     * @param mixed $total_amount_with_taxes
     */
    public function setTotalAmountWithTaxes($total_amount_with_taxes)
    {
        $this->total_amount_with_taxes = $total_amount_with_taxes;
    }

    /**
     * @return mixed
     */
    public function getPdfFile()
    {
        return $this->pdf_file;
    }

    /**
     * @param mixed $pdf_file
     */
    public function setPdfFile($pdf_file)
    {
        $this->pdf_file = $pdf_file;
    }

    /**
     * @return mixed
     */
    public function getIssuerName()
    {
        return $this->issuer_name;
    }

    /**
     * @param mixed $issuer_name
     */
    public function setIssuerName($issuer_name)
    {
        $this->issuer_name = $issuer_name;
    }

    /**
     * @return mixed
     */
    public function getReceiver()
    {
        return $this->receiver;
    }

    /**
     * @param mixed $receiver
     */
    public function setReceiver($receiver)
    {
        $this->receiver = $receiver;
    }

    /**
     * @return mixed
     */
    public function getPaymentStatus()
    {
        return $this->payment_status;
    }

    /**
     * @param mixed $payment_status
     */
    public function setPaymentStatus($payment_status)
    {
        $this->payment_status = $payment_status;
    }

    /**
     * @return mixed
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param mixed $status
     */
    public function setStatus($status)
    {
        $this->status = $status;
    }

    /**
     * @return mixed
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * @param mixed $currency
     */
    public function setCurrency($currency)
    {
        $this->currency = $currency;
    }

    /**
     * @return mixed
     */
    public function getCreditNoteLines()
    {
        return $this->credit_note_lines;
    }

    /**
     * @param mixed $credit_note_lines
     */
    public function setCreditNoteLines($credit_note_lines)
    {
        $this->credit_note_lines = $credit_note_lines;
    }

    /**
     * @return mixed
     */
    public function getIdNumber()
    {
        return $this->id_number;
    }

    /**
     * @param mixed $id_number
     */
    public function setIdNumber($id_number)
    {
        $this->id_number = $id_number;
    }
}
