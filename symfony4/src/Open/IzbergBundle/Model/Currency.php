<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 19/05/2017
 * Time: 15:54
 */

namespace Open\IzbergBundle\Model;

use Symfony\Component\Validator\Constraints as Assert;
use <PERSON><PERSON>\Serializer\Annotation as JSM;
use <PERSON><PERSON>\Serializer\Annotation\Type;

/**
 * Entity to map a Izberg currency object
 */
#[JSM\ExclusionPolicy('none')]
class Currency
{

    #[Assert\NotBlank]
    #[Type('string')]
    private $code;

    #[Assert\NotBlank]
    #[Type('string')]
    private $pk;

    /**
     * @return mixed
     */
    public function getCode()
    {
        return $this->code;
    }

    /**
     * @param mixed $code
     */
    public function setCode($code)
    {
        $this->code = $code;
    }

    /**
     * @return mixed
     */
    public function getPk(): mixed
    {
        return $this->pk;
    }

    /**
     * @param mixed $pk
     */
    public function setPk(mixed $pk): void
    {
        $this->pk = $pk;
    }

    public function __toString(): string
    {
        return $this->code ?? '';
    }
}
