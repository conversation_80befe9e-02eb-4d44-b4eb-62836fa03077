<?php

namespace Open\IzbergBundle\Model;

use Doctrine\Common\Collections\ArrayCollection;
use <PERSON><PERSON>\Serializer\Annotation as JSM;
use <PERSON><PERSON>\Serializer\Annotation\Type;

#[JSM\ExclusionPolicy('none')]
class FetchCreditNotesResponse
{
    /**
     * @var Meta
     */
    #[Type('Open\IzbergBundle\Model\Meta')]
    private $meta;

    /**
     * @var ArrayCollection
     */
    #[Type('ArrayCollection<Open\IzbergBundle\Model\CreditNote>')]
    private $objects;

    public function getObjects(): ArrayCollection
    {
        return $this->objects;
    }

    public function setObjects(ArrayCollection $objects)
    {
        $this->objects = $objects;
    }

    public function getMeta(): Meta
    {
        return $this->meta;
    }

    public function setMeta(Meta $meta): void
    {
        $this->meta = $meta;
    }

    public function hasCreditNotes(): bool
    {
        return ($this->objects->count() >= 1);
    }

    /**
     * @return CreditNote|bool|null
     */
    public function first()
    {
        return $this->objects->first();
    }
}
