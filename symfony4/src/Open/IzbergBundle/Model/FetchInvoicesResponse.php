<?php

namespace Open\IzbergBundle\Model;

use Doctrine\Common\Collections\ArrayCollection;
use <PERSON><PERSON>\Serializer\Annotation as JSM;
use <PERSON><PERSON>\Serializer\Annotation\Type;

#[JSM\ExclusionPolicy('none')]
class FetchInvoicesResponse
{
    /**
     * @var Meta
     */
    #[Type('Open\IzbergBundle\Model\Meta')]
    private $meta;

    /**
     * @var ArrayCollection<array-key, Invoice>
     */
    #[Type('ArrayCollection<Open\IzbergBundle\Model\Invoice>')]
    private $objects;

    /**
     * @return ArrayCollection<array-key, Invoice>
     */
    public function getObjects(): ArrayCollection
    {
        return $this->objects;
    }

    public function setObjects(ArrayCollection $objects)
    {
        $this->objects = $objects;
    }

    public function getMeta(): Meta
    {
        return $this->meta;
    }

    public function setMeta(Meta $meta): void
    {
        $this->meta = $meta;
    }

    public function hasInvoices(): bool
    {
        return ($this->objects->count() >= 1);
    }

    /**
     * @return Invoice|bool|null
     */
    public function first()
    {
        return $this->objects->first();
    }
}
