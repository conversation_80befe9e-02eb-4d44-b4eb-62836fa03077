<?php

namespace Open\IzbergBundle\Model;

use Doctrine\Common\Collections\ArrayCollection;
use <PERSON><PERSON>\Serializer\Annotation as JSM;
use <PERSON><PERSON>\Serializer\Annotation\Type;

#[JSM\ExclusionPolicy('none')]
class FetchProductOfferAssignedImagesResponse
{
    /**
     * @var ArrayCollection
     */
    #[Type('ArrayCollection<Open\IzbergBundle\Model\Image>')]
    private $assigned_images = null;

    public function getAssignedImages(): ?ArrayCollection
    {
        return $this->assigned_images;
    }

    public function setAssignedImages(?ArrayCollection $assigned_images): self
    {
        $this->assigned_images = $assigned_images;
        return $this;
    }
}
