<?php

namespace Open\IzbergBundle\Model;

use <PERSON><PERSON>\Serializer\Annotation as JSM;
use J<PERSON>\Serializer\Annotation\Type;

/**
 * Object to map an izberg invoice line object
 */
#[JSM\ExclusionPolicy('none')]
class InvoiceLine
{
    #[Type('Open\IzbergBundle\Model\OrderItem')]
    private $orderItem;

    /**
     * @return OrderItem
     */
    public function getOrderItem()
    {
        return $this->orderItem;
    }

    /**
     * @param mixed $orderItem
     */
    public function setOrderItem($orderItem): void
    {
        $this->orderItem = $orderItem;
    }
}
