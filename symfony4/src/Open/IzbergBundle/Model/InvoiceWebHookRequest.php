<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 24/07/2018
 * Time: 16:45
 */

namespace Open\IzbergBundle\Model;


use <PERSON><PERSON>\Serializer\Annotation as Serializer;

class InvoiceWebHookRequest
{

    #[Serializer\Type('array')]
    private $data;

    /**
     * @return mixed
     */
    public function getData()
    {
        return $this->data;
    }

    /**
     * @param mixed $data
     */
    public function setData($data): void
    {
        $this->data = $data;
    }




}
