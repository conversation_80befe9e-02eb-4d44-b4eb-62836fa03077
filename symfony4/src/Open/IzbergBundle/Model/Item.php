<?php

namespace Open\IzbergBundle\Model;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;


/**
 * Object to map an izberg merchant order item object
 */
#[Serializer\ExclusionPolicy('none')]
class Item
{
    public const ITEM_VALID_STATUS = '80';
    public const ITEM_PROCESSED_STATUS = '85';
    public const ITEM_CANCELLED_STATUS = '2000';

    #[Serializer\Type('integer')]
    private $id = 0;


    #[Serializer\Type('string')]
    private $status;

    #[Serializer\Type('Open\IzbergBundle\Model\Currency')]
    private $currency;

    #[Serializer\Type('double')]
    private $amount;

    #[Serializer\Type('double')]
    private $amount_vat_included;

    #[Serializer\Type('string')]
    private $name;

    #[Serializer\Type('integer')]
    private $quantity;

    #[Serializer\Type('integer')]
    private $offer_id;

    /**
     * @var string|null
     */
    #[Serializer\Type('string')]
    private $offer_external_id;

    #[Serializer\Type('string')]
    private $item_image_url;

    private $sellerRef;

    private $manufacturerRef;

    /**
     * @return mixed
     */
    public function getSellerRef()
    {
        return $this->sellerRef;
    }

    /**
     * @param mixed $sellerRef
     */
    public function setSellerRef($sellerRef)
    {
        $this->sellerRef = $sellerRef;
    }

    /**
     * @return mixed
     */
    public function getManufacturerRef()
    {
        return $this->manufacturerRef;
    }

    /**
     * @param mixed $manufacturerRef
     */
    public function setManufacturerRef($manufacturerRef)
    {
        $this->manufacturerRef = $manufacturerRef;
    }

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id): void
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * @param mixed $currency
     */
    public function setCurrency($currency): void
    {
        $this->currency = $currency;
    }

    /**
     * @return mixed
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     * @param mixed $amount
     */
    public function setAmount($amount): void
    {
        $this->amount = $amount;
    }

    /**
     * @return mixed
     */
    public function getAmountVatIncluded()
    {
        return $this->amount_vat_included;
    }

    /**
     * @param mixed $amount_vat_included
     */
    public function setAmountVatIncluded($amount_vat_included): void
    {
        $this->amount_vat_included = $amount_vat_included;
    }

    /**
     * @return mixed
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @param mixed $name
     */
    public function setName($name): void
    {
        $this->name = $name;
    }

    /**
     * @return mixed
     */
    public function getQuantity()
    {
        return $this->quantity;
    }

    /**
     * @param mixed $quantity
     */
    public function setQuantity($quantity): void
    {
        $this->quantity = $quantity;
    }

    /**
     * @return mixed
     */
    public function getItemImageUrl()
    {
        return $this->item_image_url;
    }

    /**
     * @param mixed $item_image_url
     */
    public function setItemImageUrl($item_image_url): void
    {
        $this->item_image_url = $item_image_url;
    }

    /**
     * @return mixed
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param mixed $status
     */
    public function setStatus($status): void
    {
        $this->status = $status;
    }

    /**
     * @return mixed
     */
    public function getOfferId()
    {
        return $this->offer_id;
    }

    /**
     * @param mixed $offer_id
     */
    public function setOfferId($offer_id): void
    {
        $this->offer_id = $offer_id;
    }

    /**
     * @return string|null
     */
    public function getOfferExternalId(): ?string
    {
        return $this->offer_external_id;
    }

    /**
     * @param string|null $offer_external_id
     * @return $this
     */
    public function setOfferExternalId(?string $offer_external_id)
    {
        $this->offer_external_id = $offer_external_id;
        return $this;
    }
}
