<?php

namespace Open\IzbergBundle\Model;

use <PERSON>ymfony\Component\Validator\Constraints as Assert;
use <PERSON><PERSON>\Serializer\Annotation\Type;
use <PERSON><PERSON>\Serializer\Annotation as JSM;

/**
 * Entity to map a Izberg user object
 */
#[JSM\ExclusionPolicy('none')]
class IzbergUser
{
    #[Assert\NotBlank]
    #[Type('string')]
    private $first_name;

    #[Assert\NotBlank]
    #[Type('string')]
    private $last_name;

    #[Assert\NotBlank]
    #[Type('integer')]
    private $id = 0;

    #[Assert\NotBlank]
    #[Type('string')]
    private $username;

    /**
     * Get first name
     * @return string
     */
    public function getFirstName()
    {
        return $this->first_name;
    }

    /**
     * Set first name
     * @param $name
     * @return $this
     */
    public function  setFirstName($name): self
    {
        $this->first_name = $name;

        return $this;
    }

    /**
     * Get last name
     * @return string
     */
    public function getLastName()
    {
        return $this->last_name;
    }

    /**
     * Set last name
     * @param $name
     */
    public function  setLastName($name): self
    {
        $this->last_name = $name;

        return $this;
    }

    /**
     * Get username
     * @return string
     */
    public function getUsername()
    {
        return $this->username;
    }

    /**
     * Set username
     * @param $name
     * @return $this
     */
    public function  setUsername($name): self
    {
        $this->username = $name;

        return $this;
    }

    /**
     * Get user Id
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param $id
     * @return $this
     */
    public function setId($id): self
    {
        $this->id = $id;

        return $this;
    }
}
