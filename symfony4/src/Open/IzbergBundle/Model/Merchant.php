<?php
/**
 * Created by PhpStorm.
 * User: CMA15450
 * Date: 24/03/2017
 * Time: 16:39
 */

namespace Open\IzbergBundle\Model;

use <PERSON>ymfony\Component\Validator\Constraints as Assert;
use <PERSON><PERSON>\Serializer\Annotation\Type;
use <PERSON><PERSON>\Serializer\Annotation as JSM;

/**
 * Entity to map a merchant object
 */
#[JSM\ExclusionPolicy('none')]
class Merchant
{
    #[Assert\NotBlank]
    #[Type('integer')]
    private $id = 0;

    #[Type('string')]
    private $name;

    #[Type('string')]
    private $description;

    #[Type('string')]
    private $long_description;

    #[Type('string')]
    private $logo_image;

    #[Type('string')]
    private $default_currency;

    /**
     * Get merchant Id
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set merchant Id
     * @param $id
     * @return integer
     */
    public function setId($id)
    {
        return $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @param mixed $name
     */
    public function setName($name): void
    {
        $this->name = $name;
    }

    /**
     * @return mixed
     */
    public function getDescription()
    {
        return $this->description;
    }

    /**
     * @param mixed $description
     */
    public function setDescription($description): void
    {
        $this->description = $description;
    }

    /**
     * @return mixed
     */
    public function getLongDescription()
    {
        return $this->long_description;
    }

    /**
     * @param mixed $long_description
     */
    public function setLongDescription($long_description): void
    {
        $this->long_description = $long_description;
    }

    /**
     * @return mixed
     */
    public function getLogoImage()
    {
        return $this->logo_image;
    }

    /**
     * @param mixed $logo_image
     */
    public function setLogoImage($logo_image): void
    {
        $this->logo_image = $logo_image;
    }

    /**
     * @return mixed
     */
    public function getDefaultCurrency()
    {
        return $this->default_currency;
    }

    /**
     * @param mixed $default_currency
     */
    public function setDefaultCurrency($default_currency): void
    {
        $this->default_currency = $default_currency;
    }




}
