<?php

namespace Open\IzbergBundle\Model;

use <PERSON>ymfony\Component\Validator\Constraints as Assert;
use <PERSON><PERSON>\Serializer\Annotation\Type;
use <PERSON><PERSON>\Serializer\Annotation as JSM;
use <PERSON><PERSON>\Request\PreOrder\MerchantOrderInterface;

/**
 * Entity to map a MerchantOrder object
 */
#[JSM\ExclusionPolicy('none')]
class MerchantOrder implements MerchantOrderInterface
{
    #[Assert\NotBlank]
    #[Type('integer')]
    private $id = 0;


    #[Type('double')]
    private $amount_vat_included;

    #[Type('double')]
    private $amount;

    #[Type('double')]
    private $refunded_amount;

    #[Type('string')]
    private $ressource_uri;

    #[Type('Open\IzbergBundle\Model\Order')]
    private $order;

    #[Type('Open\IzbergBundle\Model\Merchant')]
    private $merchant;

    #[Type('ArrayCollection<Open\IzbergBundle\Model\Item>')]
    private $items;

    #[Type('integer')]
    private $status;

    #[Type('array')]
    private $attributes;

    #[Type('Open\IzbergBundle\Model\Currency')]
    private $currency;

    #[Type('Open\IzbergBundle\Model\IzbergUser')]
    private $user;

    #[Type('string')]
    private $confirmation_date;


    #[Type('string')]
    private $payment_type;

    #[Type('string')]
    private $cancel_msg;

    #[Type('string')]
    private string $shipping;

    /**
     * @return mixed
     */
    public function getConfirmationDate()
    {
        return $this->confirmation_date;
    }

    /**
     * @param mixed $confirmation_date
     */
    public function setConfirmationDate($confirmation_date)
    {
        $this->confirmation_date = $confirmation_date;
    }

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getRessourceUri()
    {
        return $this->ressource_uri;
    }

    /**
     * @param mixed $ressource_uri
     */
    public function setRessourceUri($ressource_uri)
    {
        $this->ressource_uri = $ressource_uri;
    }

    /**
     * @return Order
     */
    public function getOrder()
    {
        return $this->order;
    }

    /**
     * @param mixed $order
     */
    public function setOrder($order): void
    {
        $this->order = $order;
    }

    /**
     * @return Merchant
     */
    public function getMerchant()
    {
        return $this->merchant;
    }

    /**
     * @param mixed $merchant
     */
    public function setMerchant($merchant): void
    {
        $this->merchant = $merchant;
    }

    /**
     * @return mixed
     */
    public function getItems()
    {
        return $this->items;
    }

    /**
     * @param mixed $items
     */
    public function setItems($items): void
    {
        $this->items = $items;
    }

    /**
     * @return mixed
     */
    public function getAmountVatIncluded()
    {
        return $this->amount_vat_included;
    }

    /**
     * @param mixed $amount_vat_included
     */
    public function setAmountVatIncluded($amount_vat_included): void
    {
        $this->amount_vat_included = $amount_vat_included;
    }

    /**
     * @return mixed
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     * @param mixed $amount
     */
    public function setAmount($amount): void
    {
        $this->amount = $amount;
    }

    /**
     * @return mixed
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param mixed $status
     */
    public function setStatus($status): void
    {
        $this->status = $status;
    }


    /**
     * @return mixed
     */
    public function getAttributes()
    {
        return $this->attributes;
    }

    /**
     * @param mixed $attributes
     */
    public function setAttributes($attributes)
    {
        $this->attributes = $attributes;
    }

    /**
     * @return Currency
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * @param mixed $currency
     */
    public function setCurrency($currency)
    {
        $this->currency = $currency;
    }


    /**
     * @return mixed
     */
    public function getRefundedAmount()
    {
        return $this->refunded_amount;
    }

    /**
     * @param mixed $refunded_amount
     */
    public function setRefundedAmount($refunded_amount)
    {
        $this->refunded_amount = $refunded_amount;
    }

    /**
     * @return mixed
     */
    public function getUser()
    {
        return $this->user;
    }

    /**
     * @param mixed $user
     */
    public function setUser($user)
    {
        $this->user = $user;
    }

    /**
     * @return mixed
     */
    public function getPaymentType()
    {
        return $this->payment_type;
    }

    /**
     * @param mixed $payment_type
     */
    public function setPaymentType($payment_type): void
    {
        $this->payment_type = $payment_type;
    }

    /**
     * @return mixed
     */
    public function getCancelMsg()
    {
        return $this->cancel_msg;
    }

    /**
     * @param mixed $cancel_msg
     */
    public function setCancelMsg($cancel_msg)
    {
        $this->cancel_msg = $cancel_msg;
    }

    public function getShipping()
    {
        return $this->shipping;
    }

    public function setShipping($shipping): void
    {
        $this->shipping = $shipping;
    }

}
