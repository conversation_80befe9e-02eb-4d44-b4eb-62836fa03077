<?php

namespace Open\IzbergBundle\Model;

use JMS\Serializer\Annotation as JSM;
use JMS\Serializer\Annotation\Type;

final class MerchantOrderCommissionRule
{

    /**
     * @var float
     */
    #[Type('float')]
    private $commission_rate_value;

    public function getCommissionRateValue(): float
    {
        return $this->commission_rate_value;
    }

    public function setCommissionRateValue(float $commission_rate_value): self
    {
        $this->commission_rate_value = $commission_rate_value;
        return $this;
    }
}
