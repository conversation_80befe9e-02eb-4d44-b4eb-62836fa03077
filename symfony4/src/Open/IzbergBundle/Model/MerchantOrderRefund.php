<?php

namespace Open\IzbergBundle\Model;

use <PERSON><PERSON>\Serializer\Annotation\Type;
use <PERSON><PERSON>\Serializer\Annotation as JSM;

final class MerchantOrderRefund
{
    /**
     * @var MerchantOrder
     */
    #[Type('Open\IzbergBundle\Model\MerchantOrder')]
    private $merchant_order;

    /**
     * @var Item[]
     */
    #[Type('array<Open\IzbergBundle\Model\Item>')]
    private $order_items;

    /**
     * @return MerchantOrder
     */
    public function getMerchantOrder(): MerchantOrder
    {
        return $this->merchant_order;
    }

    /**
     * @param MerchantOrder $merchant_order
     * @return $this
     */
    public function setMerchantOrder(MerchantOrder $merchant_order): self
    {
        $this->merchant_order = $merchant_order;
        return $this;
    }

    /**
     * @return array
     */
    public function getOrderItems(): array
    {
        return $this->order_items;
    }

    /**
     * @param array $order_items
     * @return $this
     */
    public function setOrderItems(array $order_items): self
    {
        $this->order_items = $order_items;
        return $this;
    }
}
