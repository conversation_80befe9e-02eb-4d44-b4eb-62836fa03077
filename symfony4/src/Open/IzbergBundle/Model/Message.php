<?php

namespace Open\IzbergBundle\Model;
use <PERSON><PERSON>\Serializer\Annotation\Type;
use <PERSON><PERSON>\Serializer\Annotation as JSM;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Entity to map a message object
 */
#[JSM\ExclusionPolicy('none')]
class Message
{
    #[Assert\NotBlank]
    #[Type('integer')]
    private $id = 0;

    #[Type('string')]
    private $body;

    #[Type('string')]
    private $body_raw;

    #[Type('string')]
    private $from_resource_uri;

    #[Type('string')]
    private $to_resource_uri;

    #[Type('string')]
    private $root_msg;


    #[Type('string')]
    private $from_display_name;

    #[Type('string')]
    private $subject;

    /**
     * @return mixed
     */
    public function getFromDisplayName()
    {
        return $this->from_display_name;
    }

    /**
     * @param mixed $from_display_name
     */
    public function setFromDisplayName($from_display_name)
    {
        $this->from_display_name = $from_display_name;
    }

    /**
     * @return mixed
     */
    public function getBody()
    {
        return $this->body;
    }

    /**
     * @param mixed $body
     */
    public function setBody($body)
    {
        $this->body = $body;
    }

    /**
     * @return mixed
     */
    public function getBodyRaw()
    {
        return $this->body_raw;
    }

    /**
     * @param mixed $body_raw
     */
    public function setBodyRaw($body_raw): void
    {
        $this->body_raw = $body_raw;
    }

    /**
     * @return mixed
     */
    public function getFromResourceUri()
    {
        return $this->from_resource_uri;
    }

    /**
     * @param mixed $from_resource_uri
     */
    public function setFromResourceUri($from_resource_uri)
    {
        $this->from_resource_uri = $from_resource_uri;
    }

    /**
     * @return mixed
     */
    public function getToResourceUri()
    {
        return $this->to_resource_uri;
    }

    /**
     * @param mixed $to_resource_uri
     */
    public function setToResourceUri($to_resource_uri)
    {
        $this->to_resource_uri = $to_resource_uri;
    }

    /**
     * @return mixed
     */
    public function getRootMsg()
    {
        return $this->root_msg;
    }

    /**
     * @param mixed $root_msg
     */
    public function setRootMsg($root_msg): void
    {
        $this->root_msg = $root_msg;
    }

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id): void
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getSubject()
    {
        return $this->subject;
    }

    /**
     * @param mixed $subject
     */
    public function setSubject($subject): void
    {
        $this->subject = $subject;
    }
}
