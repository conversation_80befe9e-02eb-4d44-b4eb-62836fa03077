<?php

namespace Open\IzbergBundle\Model;

use <PERSON><PERSON>\Serializer\Annotation as JSM;
use J<PERSON>\Serializer\Annotation\Type;

#[JSM\ExclusionPolicy('none')]
class Meta
{
    /**
     * @var int
     */
    #[Type('int')]
    private $limit;

    /**
     * @var ?string
     */
    #[Type('string')]
    private $next;

    /**
     * @var int
     */
    #[Type('int')]
    private $offset;

    /**
     * @var ?string
     */
    #[Type('string')]
    private $previous;

    /**
     * @var int
     */
    #[Type('int')]
    private $total_count;

    public function getLimit(): ?int
    {
        return $this->limit;
    }

    public function setLimit(int $limit): void
    {
        $this->limit = $limit;
    }

    public function getNext(): ?string
    {
        return $this->next;
    }

    public function setNext(string $next): void
    {
        $this->next = $next;
    }

    public function getOffset(): ?int
    {
        return $this->offset;
    }

    public function setOffset(int $offset): void
    {
        $this->offset = $offset;
    }

    public function getPrevious(): ?string
    {
        return $this->previous;
    }

    public function setPrevious(string $previous): void
    {
        $this->previous = $previous;
    }

    public function getTotalCount(): int
    {
        return $this->total_count;
    }

    public function setTotalCount(int $total_count): void
    {
        $this->total_count = $total_count;
    }
}
