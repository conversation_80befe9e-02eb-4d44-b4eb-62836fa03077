<?php

namespace Open\IzbergBundle\Model;

use AppBundle\Entity\ExportableEntityTrait;
use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Component\Validator\Constraints as Assert;
use <PERSON><PERSON>\Serializer\Annotation as JSM;
use <PERSON><PERSON>\Serializer\Annotation\Type;

/**
 * Object to map an izberg merchant_order object
 */
#[JSM\ExclusionPolicy('none')]
class Order
{
    public const STATUS_PENDING_CREATION = 11111111;

    use ExportableEntityTrait;

    #[Assert\NotBlank]
    #[Type('integer')]
    private $id;


    #[Assert\NotBlank]
    #[Type('string')]
    private $id_number;

    #[Type('integer')]
    private $cart_id;

    #[Type('Open\IzbergBundle\Model\Cart')]
    private $cart;

    /**
     * @var
     */
    #[Type('integer')]
    private $archived_cart_id;

    #[Assert\NotBlank]
    #[Type('double')]
    private $amount;

    #[Assert\NotBlank]
    #[Type('double')]
    private $amount_vat_included;

    #[Assert\NotBlank]
    #[Type('double')]
    private $amount_vat_included_before_discount;


    #[Assert\NotBlank]
    #[Type('double')]
    private $app_discount_amount;

    #[Assert\NotBlank]
    #[Type('double')]
    private $app_discount_amount_on_items_vat_included;

    #[Type('ArrayCollection<Open\IzbergBundle\Model\OrderItem>')]
    private $items;

    #[Assert\NotBlank]
    #[Type('double')]
    private $app_discount_amount_on_shipping_vat_included;

    #[Assert\NotBlank]
    #[Type('double')]
    private $app_discount_amount_vat_included;

    /**
     * @var Address
     */
    #[Type('Open\IzbergBundle\Model\Address')]
    private $billing_address;

    /**
     * @var Address
     */
    #[Type('Open\IzbergBundle\Model\Address')]
    private $shipping_address;

    #[Assert\NotBlank]
    #[Type('double')]
    private $discount_amount;

    #[Assert\NotBlank]
    #[Type('double')]
    private $discount_amount_vat_included;

    #[Assert\NotBlank]
    #[Type('double')]
    private $eco_tax;

    #[Assert\NotBlank]
    #[Type('double')]
    private $eco_tax_vat_included;

    #[Type('Open\IzbergBundle\Model\OrderDetails')]
    private $order;

    #[Type('double')]
    private $price;

    #[Type('double')]
    private $price_vat_included;

    /**
     * @var IzbergUser
     */
    #[Type('Open\IzbergBundle\Model\IzbergUser')]
    private $user;

    #[Type('double')]
    private $vat;

    #[Type('double')]
    private $vat_on_eco_tax;

    #[Type('double')]
    private $vat_on_products;

    #[Type('double')]
    private $vat_on_shipping;

    #[Type('double')]
    private $vat_rate_on_shipping;

    #[Type('Open\IzbergBundle\Model\Merchant')]
    private $merchant;

    #[Type('ArrayCollection<Open\IzbergBundle\Model\MerchantOrder>')]
    private $merchant_orders;

    #[Assert\NotBlank]
    #[Type('integer')]
    private $status;

    /**
     * Used only by front
     */
    #[Type('integer')]
    private $statusFront;

    #[Assert\NotBlank]
    #[Type('string')]
    private $status_localized;

    #[Assert\NotBlank]
    #[Type('string')]
    private $currency;

    /**
     * @var string
     */
    #[Assert\NotBlank]
    #[Type('string')]
    private $created_on;

    #[Type('Open\IzbergBundle\Model\OrderPayment')]
    private $payment;
    #[Type('string')]
    private $payment_status;

    /**
     * @var array
     */
    private $subTotalVat;

    /**
     * @return null|OrderPayment
     */
    public function getPayment()
    {
        return $this->payment;
    }

    /**
     * @param mixed $payment
     */
    public function setPayment($payment): self
    {
        $this->payment = $payment;

        return $this;
    }


    /**
     * @return string
     */
    public function getCreatedOn()
    {
        return $this->created_on;
    }

    /**
     * @param string $created_on
     */
    public function setCreatedOn($created_on): self
    {
        $this->created_on = $created_on;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param mixed $status
     */
    public function setStatus($status): self
    {
        $this->status = $status;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId(?int $id): self
    {
        $this->id = $id;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     * @param mixed $amount
     */
    public function setAmount($amount): self
    {
        $this->amount = $amount;

        return $this;
    }

    public function getAmountVatIncluded()
    {
        return $this->amount_vat_included;
    }

    /**
     * @param mixed $amount_vat_included
     */
    public function setAmountVatIncluded($amount_vat_included): self
    {
        $this->amount_vat_included = $amount_vat_included;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getAmountVatIncludedBeforeDiscount()
    {
        return $this->amount_vat_included_before_discount;
    }

    /**
     * @param mixed $amount_vat_included_before_discount
     */
    public function setAmountVatIncludedBeforeDiscount($amount_vat_included_before_discount): self
    {
        $this->amount_vat_included_before_discount = $amount_vat_included_before_discount;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getAppDiscountAmount()
    {
        return $this->app_discount_amount;
    }

    /**
     * @param mixed $app_discount_amount
     */
    public function setAppDiscountAmount($app_discount_amount): self
    {
        $this->app_discount_amount = $app_discount_amount;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getAppDiscountAmountOnItemsVatIncluded()
    {
        return $this->app_discount_amount_on_items_vat_included;
    }

    /**
     * @param mixed $app_discount_amount_on_items_vat_included
     */
    public function setAppDiscountAmountOnItemsVatIncluded($app_discount_amount_on_items_vat_included): self
    {
        $this->app_discount_amount_on_items_vat_included = $app_discount_amount_on_items_vat_included;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getItems()
    {
        return $this->items;
    }

    /**
     * @param mixed $items
     */
    public function setItems($items): self
    {
        $this->items = $items;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getAppDiscountAmountOnShippingVatIncluded()
    {
        return $this->app_discount_amount_on_shipping_vat_included;
    }

    /**
     * @param mixed $app_discount_amount_on_shipping_vat_included
     */
    public function setAppDiscountAmountOnShippingVatIncluded($app_discount_amount_on_shipping_vat_included): self
    {
        $this->app_discount_amount_on_shipping_vat_included = $app_discount_amount_on_shipping_vat_included;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getAppDiscountAmountVatIncluded()
    {
        return $this->app_discount_amount_vat_included;
    }

    /**
     * @param mixed $app_discount_amount_vat_included
     */
    public function setAppDiscountAmountVatIncluded($app_discount_amount_vat_included): self
    {
        $this->app_discount_amount_vat_included = $app_discount_amount_vat_included;

        return $this;
    }

    public function getBillingAddress(): Address
    {
        return $this->billing_address;
    }

    /**
     * @param mixed $billing_address
     */
    public function setBillingAddress($billing_address): self
    {
        $this->billing_address = $billing_address;

        return $this;
    }

    public function getShippingAddress(): ?Address
    {
        return $this->shipping_address;
    }

    /**
     * @param mixed $shipping_address
     */
    public function setShippingAddress($shipping_address): self
    {
        $this->shipping_address = $shipping_address;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getDiscountAmount()
    {
        return $this->discount_amount;
    }

    /**
     * @param mixed $discount_amount
     */
    public function setDiscountAmount($discount_amount): self
    {
        $this->discount_amount = $discount_amount;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getDiscountAmountVatIncluded()
    {
        return $this->discount_amount_vat_included;
    }

    /**
     * @param mixed $discount_amount_vat_included
     */
    public function setDiscountAmountVatIncluded($discount_amount_vat_included): self
    {
        $this->discount_amount_vat_included = $discount_amount_vat_included;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getEcoTax()
    {
        return $this->eco_tax;
    }

    /**
     * @param mixed $eco_tax
     */
    public function setEcoTax($eco_tax): self
    {
        $this->eco_tax = $eco_tax;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getEcoTaxVatIncluded()
    {
        return $this->eco_tax_vat_included;
    }

    /**
     * @param mixed $eco_tax_vat_included
     */
    public function setEcoTaxVatIncluded($eco_tax_vat_included): self
    {
        $this->eco_tax_vat_included = $eco_tax_vat_included;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getOrder()
    {
        return $this->order;
    }

    /**
     * @param mixed $order
     */
    public function setOrder($order): self
    {
        $this->order = $order;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getPrice()
    {
        return $this->price;
    }

    /**
     * @param mixed $price
     */
    public function setPrice($price): self
    {
        $this->price = $price;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getPriceVatIncluded()
    {
        return $this->price_vat_included;
    }

    /**
     * @param mixed $price_vat_included
     */
    public function setPriceVatIncluded($price_vat_included): self
    {
        $this->price_vat_included = $price_vat_included;

        return $this;
    }



    /**
     * @return mixed
     */
    public function getVat()
    {
        return $this->vat;
    }

    /**
     * @param mixed $vat
     */
    public function setVat($vat): self
    {
        $this->vat = $vat;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getVatOnEcoTax()
    {
        return $this->vat_on_eco_tax;
    }

    /**
     * @param mixed $vat_on_eco_tax
     */
    public function setVatOnEcoTax($vat_on_eco_tax): self
    {
        $this->vat_on_eco_tax = $vat_on_eco_tax;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getVatOnProducts()
    {
        return $this->vat_on_products;
    }

    /**
     * @param mixed $vat_on_products
     */
    public function setVatOnProducts($vat_on_products): self
    {
        $this->vat_on_products = $vat_on_products;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getVatOnShipping()
    {
        return $this->vat_on_shipping;
    }

    /**
     * @param mixed $vat_on_shipping
     */
    public function setVatOnShipping($vat_on_shipping): self
    {
        $this->vat_on_shipping = $vat_on_shipping;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getVatRateOnShipping()
    {
        return $this->vat_rate_on_shipping;
    }

    /**
     * @param mixed $vat_rate_on_shipping
     */
    public function setVatRateOnShipping($vat_rate_on_shipping): self
    {
        $this->vat_rate_on_shipping = $vat_rate_on_shipping;

        return $this;
    }

    public function getUser(): IzbergUser
    {
        return $this->user;
    }

    /**
     * @param mixed $user
     */
    public function setUser($user): self
    {
        $this->user = $user;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getMerchant()
    {
        return $this->merchant;
    }

    /**
     * @param mixed $merchant
     */
    public function setMerchant($merchant): self
    {
        $this->merchant = $merchant;

        return $this;
    }

    public function getMerchantOrders(): ?ArrayCollection
    {
        return $this->merchant_orders;
    }

    /**
     * @param mixed $merchant_orders
     */
    public function setMerchantOrders($merchant_orders): self
    {
        $this->merchant_orders = $merchant_orders;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getIdNumber()
    {
        return $this->id_number;
    }

    /**
     * @param mixed $id_number
     */
    public function setIdNumber($id_number): self
    {
        $this->id_number = $id_number;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * @param mixed $currency
     */
    public function setCurrency($currency): self
    {
        $this->currency = $currency;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getCartId()
    {
        return $this->cart_id;
    }

    /**
     * @param mixed $cart_id
     */
    public function setCartId($cart_id): self
    {
        $this->cart_id = $cart_id;

        return $this;
    }

    /**
     * @return int|null
     */
    public function getArchivedCartId(): ?int
    {
        return $this->archived_cart_id;
    }

    /**
     * @param int|null $archivedCartId
     * @return $this
     */
    public function setArchivedCartId(?int $archivedCartId)
    {
        $this->archived_cart_id = $archivedCartId;
        return $this;
    }

    /**
     * @return Cart|null
     */
    public function getCart(): ?Cart
    {
        return $this->cart;
    }

    /**
     * @param mixed $cart
     */
    public function setCart($cart): self
    {
        $this->cart = $cart;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getStatusLocalized()
    {
        return $this->status_localized;
    }

    /**
     * @param mixed $status_localized
     */
    public function setStatusLocalized($status_localized): self
    {
        $this->status_localized = $status_localized;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getPaymentStatus()
    {
        return $this->payment_status;
    }

    /**
     * @param mixed $payment_status
     */
    public function setPaymentStatus($payment_status): self
    {
        $this->payment_status = $payment_status;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getStatusFront()
    {
        return $this->statusFront;
    }

    /**
     * @param mixed $statusFront
     */
    public function setStatusFront($statusFront): self
    {
        $this->statusFront = $statusFront;

        return $this;
    }

    /**
     * @return array
     */
    public function getSubTotalVat(): array
    {
        return $this->subTotalVat;
    }

    /**
     * @param array $subTotalVat
     */
    public function setSubTotalVat(array $subTotalVat): self
    {
        $this->subTotalVat = $subTotalVat;

        return $this;
    }
}
