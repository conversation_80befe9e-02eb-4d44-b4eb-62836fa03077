<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 19/05/2017
 * Time: 16:34
 */

namespace Open\IzbergBundle\Model;

use Symfony\Component\Validator\Constraints as Assert;
use <PERSON><PERSON>\Serializer\Annotation as JSM;
use <PERSON><PERSON>\Serializer\Annotation\Type;

/**
 * Object to map an izberg order object
 */
#[JSM\ExclusionPolicy('none')]
class OrderDetails
{

    #[Assert\NotBlank]
    #[Type('integer')]
    private $id;


    #[Type('string')]
    private $created_on;

    #[Type('string')]
    private $id_number;

    #[Type('string')]
    private $last_updated;

    #[Type('string')]
    private $status_localized;

    #[Type('string')]
    private $cart_id;

    #[Assert\NotBlank]
    #[Type('string')]
    private $currency;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getCartId()
    {
      return $this->cart_id;
    }

    /**
     * @param mixed $cart_id
     */
    public function setCartId($cart_id): void
    {
      $this->cart_id = $cart_id;
    }

    /**
     * @return mixed
     */
    public function getIdNumber()
    {
        return $this->id_number;
    }

    /**
     * @param mixed $id_number
     */
    public function setIdNumber($id_number)
    {
        $this->id_number = $id_number;
    }



    /**
     * @return mixed
     */
    public function getStatusLocalized()
    {
        return $this->status_localized;
    }

    /**
     * @param mixed $status_localized
     */
    public function setStatusLocalized($status_localized)
    {
        $this->status_localized = $status_localized;
    }

    /**
     * @return mixed
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * @param mixed $currency
     */
    public function setCurrency($currency): void
    {
        $this->currency = $currency;
    }



    /**
     * Fix WTF from Izberg where Prod env contains microtime wether sandbox dont
     * JMS serializer was expecting ISO8601 "Y-m-d\TH:i:sO" and fail to parse microtime
     * @return mixed
     */
    public function getCreatedOn()
    {
        // Parse date from string
        $time = strtotime($this->created_on);

        // create new date object
        $d =  new \DateTime();

        // return Date
        return $d->setTimestamp($time);
    }

    /**
     * @param mixed $created_on
     */
    public function setCreatedOn($created_on)
    {
        if (is_string($created_on)) {
            $this->created_on = $created_on;
        } else {
            $this->created_on = $created_on->format(\DateTime::ISO8601);
        }
    }

    /**
     * Fix WTF from Izberg where Prod env contains microtime wether sandbox dont
     * JMS serializer was expecting ISO8601 "Y-m-d\TH:i:sO" and fail to parse microtime
     * @return mixed
     */
    public function getLastUpdated()
    {
        // Parse date from string
        $time = strtotime($this->last_updated);

        // create new date object
        $d =  new \DateTime();

        // return Date
        return $d->setTimestamp($time);
    }

    /**
     * @param mixed $last_updated
     */
    public function setLastUpdated($last_updated)
    {
        if (is_string($last_updated)) {
            $this->last_updated = $last_updated;
        } else {
            $this->last_updated = $last_updated->format(\DateTime::ISO8601);
        }
    }



}
