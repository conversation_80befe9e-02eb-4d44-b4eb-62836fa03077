<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 19/05/2017
 * Time: 15:52
 */

namespace Open\IzbergBundle\Model;

use Symfony\Component\Validator\Constraints as Assert;
use <PERSON><PERSON>\Serializer\Annotation as JSM;
use <PERSON><PERSON>\Serializer\Annotation\Type;


/**
 * Entity to map a Izberg orderItem object
 */
#[JSM\ExclusionPolicy('none')]
class OrderItem
{
    #[Assert\NotBlank]
    #[Type('integer')]
    private $id;

    #[Assert\NotBlank]
    #[Type('double')]
    private $amount = 0;

    #[Assert\NotBlank]
    #[Type('double')]
    private $amount_vat_included = 0;

    #[Assert\NotBlank]
    #[Type('Open\IzbergBundle\Model\Currency')]
    private $currency;

    #[Type('string')]
    private $item_image_url;

    #[Type('string')]
    private $name;

    #[Assert\NotBlank]
    #[Type('double')]
    private $price;

    #[Assert\NotBlank]
    #[Type('double')]
    private $vat;

    #[Assert\NotBlank]
    #[Type('integer')]
    private $offer_id;

    #[Type('string')]
    private $offer_external_id;

    #[Assert\NotBlank]
    #[Type('integer')]
    private $product_id;

    #[Type('Open\IzbergBundle\Model\Product')]
    private $product;

    private $delayDelivery;

    #[Type('array')]
    private $deliveryDates;

    /**
     * @var string $incoterm product incoterm
     * @see IncotermEnum
     */
    private $incoterm;

    /**
     * @var string $sellerRef the seller reference
     */
    private $sellerRef;

    private $buyerRef;

    /**
     * @var string
     */
    #[Type('string')]
    private $external_id;

    /**
     * @var int
     */
    #[Type('int')]
    private $invoiced_quantity;

    /**
     * @var int
     */
    #[Type('int')]
    private $invoiceable_quantity;

    /**
     * @var int
     */
    #[Type('int')]
    private $max_invoiceable;

    #[Type('string')]
    private $merchantOrder;

    #[Type('integer')]
    private $status;

    #[Type('array')]
    private $extra_info;

    /**
     * @return mixed
     */
    public function getProduct()
    {
        return $this->product;
    }

    /**
     * @param mixed $product
     */
    public function setProduct($product)
    {
        $this->product = $product;
    }

    #[Assert\NotBlank]
    #[Type('integer')]
    private $quantity;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     * @param mixed $amount
     */
    public function setAmount($amount)
    {
        $this->amount = $amount;
    }

    /**
     * @return mixed
     */
    public function getAmountVatIncluded()
    {
        return $this->amount_vat_included;
    }

    /**
     * @param mixed $amount_vat_included
     */
    public function setAmountVatIncluded($amount_vat_included)
    {
        $this->amount_vat_included = $amount_vat_included;
    }

    /**
     * @return mixed
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * @param mixed $currency
     */
    public function setCurrency($currency)
    {
        $this->currency = $currency;
    }

    /**
     * @return mixed
     */
    public function getitem_image_url()
    {
        return $this->item_image_url;
    }

    /**
     * @param mixed $item_image_url
     */
    public function setItemImageUrl($item_image_url)
    {
        $this->item_image_url = $item_image_url;
    }

    /**
     * @return mixed
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @param mixed $name
     */
    public function setName($name)
    {
        $this->name = $name;
    }

    /**
     * @return mixed
     */
    public function getPrice()
    {
        return $this->price;
    }

    /**
     * @param mixed $price
     */
    public function setPrice($price)
    {
        $this->price = $price;
    }

    /**
     * @return mixed
     */
    public function getVat()
    {
        return $this->vat;
    }

    /**
     * @param mixed $vat
     */
    public function setVat($vat)
    {
        $this->vat = $vat;
    }

    /**
     * @return mixed
     */
    public function getOfferId()
    {
        return $this->offer_id;
    }

    /**
     * @return mixed
     */
    public function getoffer_id()
    {
        return $this->offer_id;
    }

    /**
     * @param mixed $offer_id
     */
    public function setOfferId($offer_id)
    {
        $this->offer_id = $offer_id;
    }

    public function getOfferExternalId(): ?string
    {
        return $this->offer_external_id;
    }

    public function setOfferExternalId(?string $offer_external_id): self
    {
        $this->offer_external_id = $offer_external_id;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getProductId()
    {
        return $this->product_id;
    }

    /**
     * @param mixed $product_id
     */
    public function setProductId($product_id)
    {
        $this->product_id = $product_id;
    }

    /**
     * @return mixed
     */
    public function getQuantity()
    {
        return $this->quantity;
    }

    /**
     * @param mixed $quantity
     */
    public function setQuantity($quantity)
    {
        $this->quantity = $quantity;
    }

    /**
     * @return mixed
     */
    public function getDelayDelivery()
    {
        return $this->delayDelivery;
    }

    /**
     * @param mixed $delayDelivery
     */
    public function setDelayDelivery($delayDelivery)
    {
        $this->delayDelivery = $delayDelivery;
    }

    /**
     * @return null|string
     */
    public function getIncoterm()
    {
        return $this->incoterm;
    }

    /**
     * @param null|string $incoterm
     */
    public function setIncoterm($incoterm)
    {
        $this->incoterm = $incoterm;
    }

    /**
     * @return string
     */
    public function getSellerRef(): ?string
    {
        return $this->sellerRef;
    }

    /**
     * @param string $sellerRef
     */
    public function setSellerRef(?string $sellerRef): void
    {
        $this->sellerRef = $sellerRef;
    }

    /**
     * @return mixed
     */
    public function getMerchantOrder()
    {
        return $this->merchantOrder;
    }

    /**
     * @param mixed $merchantOrder
     */
    public function setMerchantOrder($merchantOrder): void
    {
        $this->merchantOrder = $merchantOrder;
    }

    /**
     * @return mixed
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param mixed $status
     */
    public function setStatus($status): void
    {
        $this->status = $status;
    }

    /**
     * @return mixed
     */
    public function getBuyerRef()
    {
        return $this->buyerRef;
    }

    /**
     * @param mixed $buyerRef
     */
    public function setBuyerRef($buyerRef)
    {
        $this->buyerRef = $buyerRef;
    }

    public function getExternalId(): string
    {
        return $this->external_id;
    }

    public function setExternalId(string $external_id): self
    {
        $this->external_id = $external_id;
        return $this;
    }

    public function getInvoicedQuantity(): int
    {
        return $this->invoiced_quantity;
    }

    public function setInvoicedQuantity(int $invoiced_quantity): self
    {
        $this->invoiced_quantity = $invoiced_quantity;
        return $this;
    }

    public function getInvoiceableQuantity(): int
    {
        return $this->invoiceable_quantity;
    }

    public function setInvoiceableQuantity(int $invoiceable_quantity): self
    {
        $this->invoiceable_quantity = $invoiceable_quantity;
        return $this;
    }

    public function getMaxInvoiceable(): int
    {
        return $this->max_invoiceable;
    }

    public function setMaxInvoiceable(int $max_invoiceable): self
    {
        $this->max_invoiceable = $max_invoiceable;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getDeliveryDates()
    {
        return $this->deliveryDates;
    }

    /**
     * @param mixed $deliveryDates
     */
    public function setDeliveryDates($deliveryDates): void
    {
        $this->deliveryDates = $deliveryDates;
    }

    public function getExtraInfo()
    {
        return $this->extra_info;
    }

    public function setExtraInfo($extra_info)
    {
        $this->extra_info = $extra_info;
        return $this;
    }
}
