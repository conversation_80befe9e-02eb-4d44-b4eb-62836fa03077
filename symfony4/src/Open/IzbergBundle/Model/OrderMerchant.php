<?php

namespace Open\IzbergBundle\Model;

use Symfony\Component\Validator\Constraints as Assert;
use <PERSON><PERSON>\Serializer\Annotation as JSM;
use <PERSON><PERSON>\Serializer\Annotation\Type;

/**
 * Object to map an izberg merchant_order object
 */
#[JSM\ExclusionPolicy('none')]
class OrderMerchant
{

    #[Assert\NotBlank]
    #[Type('integer')]
    private $id = 0;


    #[Assert\NotBlank]
    #[Type('string')]
    private $id_number;


    #[Assert\NotBlank]
    #[Type('double')]
    private $amount;

    #[Assert\NotBlank]
    #[Type('double')]
    private $amount_vat_included;

    #[Assert\NotBlank]
    #[Type('double')]
    private $amount_vat_included_before_discount;


    #[Assert\NotBlank]
    #[Type('double')]
    private $app_discount_amount;

    #[Assert\NotBlank]
    #[Type('double')]
    private $app_discount_amount_on_items_vat_included;

    #[Type('ArrayCollection<Open\IzbergBundle\Model\OrderItem>')]
    private $items;

    #[Assert\NotBlank]
    #[Type('double')]
    private $app_discount_amount_on_shipping_vat_included;

    #[Assert\NotBlank]
    #[Type('double')]
    private $app_discount_amount_vat_included;

    #[Type('Open\IzbergBundle\Model\Address')]
    private $billing_address;

    #[Type('Open\IzbergBundle\Model\Address')]
    private $shipping_address;

    #[Assert\NotBlank]
    #[Type('double')]
    private $discount_amount;

    #[Assert\NotBlank]
    #[Type('double')]
    private $discount_amount_vat_included;

    #[Assert\NotBlank]
    #[Type('double')]
    private $eco_tax;

    #[Assert\NotBlank]
    #[Type('double')]
    private $eco_tax_vat_included;

    #[Type('Open\IzbergBundle\Model\OrderDetails')]
    private $order;

    #[Type('double')]
    private $price;

    #[Type('double')]
    private $price_vat_included;

    #[Type('Open\IzbergBundle\Model\IzbergUser')]
    private $user;

    #[Type('double')]
    private $vat;

    #[Type('double')]
    private $vat_on_eco_tax;

    #[Type('double')]
    private $vat_on_products;

    #[Type('double')]
    private $vat_on_shipping;

    #[Type('double')]
    private $vat_rate_on_shipping;

    #[Type('Open\IzbergBundle\Model\Merchant')]
    private $merchant;

    #[Type('ArrayCollection<Open\IzbergBundle\Model\MerchantOrder>')]
    private $merchant_orders;

    #[Assert\NotBlank]
    #[Type('integer')]
    private $status;

    #[Type('string')]
    private $status_localized;

    #[Assert\NotBlank]
    #[Type('Open\IzbergBundle\Model\Currency')]
    private $currency;

    #[Type('array')]
    private $attributes;

    #[Type('string')]
    private $confirmation_date;

    #[Type('string')]
    private $created_on;

    #[Type('ArrayCollection<Open\IzbergBundle\Model\CreditNote>')]
    private $credit_notes;

    #[Type('string')]
    private $payment_type;

    /**
     * @var array $invoices
     */
    #[Type('ArrayCollection<Open\IzbergBundle\Model\Invoice>')]
    private $invoices;

    /**
     * @var bool $seeDisputes
     */
    private $seeDisputes;

    /**
     * @var array $subTotalVat
     */
    private $subTotalVat;

    /**
     * @return mixed
     */
    public function getConfirmationDate()
    {
        return $this->confirmation_date;
    }

    /**
     * @param mixed $confirmation_date
     */
    public function setConfirmationDate($confirmation_date)
    {
        $this->confirmation_date = $confirmation_date;
    }

    /**
     * @return \DateTimeImmutable
     * @throws \Exception
     */
    public function getCreatedOn(): \DateTimeImmutable
    {
        return new \DateTimeImmutable($this->created_on);
    }

    /**
     * @param mixed $created_on
     */
    public function setCreatedOn($created_on): void
    {
        $this->created_on = $created_on;
    }

    /**
     * @return mixed
     */
    public function getAttributes()
    {
        return $this->attributes;
    }

    /**
     * @param mixed $attributes
     */
    public function setAttributes($attributes)
    {
        $this->attributes = $attributes;
    }

    /**
     * @return mixed
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param mixed $status
     */
    public function setStatus($status)
    {
        $this->status = $status;
    }

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     * @param mixed $amount
     */
    public function setAmount($amount)
    {
        $this->amount = $amount;
    }

    public function getAmountVatIncluded()
    {
        return $this->amount_vat_included;
    }

    /**
     * @param mixed $amount_vat_included
     */
    public function setAmountVatIncluded($amount_vat_included)
    {
        $this->amount_vat_included = $amount_vat_included;
    }

    /**
     * @return mixed
     */
    public function getAmountVatIncludedBeforeDiscount()
    {
        return $this->amount_vat_included_before_discount;
    }

    /**
     * @param mixed $amount_vat_included_before_discount
     */
    public function setAmountVatIncludedBeforeDiscount($amount_vat_included_before_discount)
    {
        $this->amount_vat_included_before_discount = $amount_vat_included_before_discount;
    }

    /**
     * @return mixed
     */
    public function getAppDiscountAmount()
    {
        return $this->app_discount_amount;
    }

    /**
     * @param mixed $app_discount_amount
     */
    public function setAppDiscountAmount($app_discount_amount)
    {
        $this->app_discount_amount = $app_discount_amount;
    }

    /**
     * @return mixed
     */
    public function getAppDiscountAmountOnItemsVatIncluded()
    {
        return $this->app_discount_amount_on_items_vat_included;
    }

    /**
     * @param mixed $app_discount_amount_on_items_vat_included
     */
    public function setAppDiscountAmountOnItemsVatIncluded($app_discount_amount_on_items_vat_included)
    {
        $this->app_discount_amount_on_items_vat_included = $app_discount_amount_on_items_vat_included;
    }

    /**
     * @return mixed
     */
    public function getItems()
    {
        return $this->items;
    }

    /**
     * @param mixed $items
     */
    public function setItems($items)
    {
        $this->items = $items;
    }

    public function getSeeDisputes()
    {
        return $this->seeDisputes;
    }

    /**
     * @return mixed
     */
    public function getAppDiscountAmountOnShippingVatIncluded()
    {
        return $this->app_discount_amount_on_shipping_vat_included;
    }

    /**
     * @param mixed $app_discount_amount_on_shipping_vat_included
     */
    public function setAppDiscountAmountOnShippingVatIncluded($app_discount_amount_on_shipping_vat_included)
    {
        $this->app_discount_amount_on_shipping_vat_included = $app_discount_amount_on_shipping_vat_included;
    }

    /**
     * @return mixed
     */
    public function getAppDiscountAmountVatIncluded()
    {
        return $this->app_discount_amount_vat_included;
    }

    /**
     * @param mixed $app_discount_amount_vat_included
     */
    public function setAppDiscountAmountVatIncluded($app_discount_amount_vat_included)
    {
        $this->app_discount_amount_vat_included = $app_discount_amount_vat_included;
    }

    /**
     * @return mixed
     */
    public function getBillingAddress()
    {
        return $this->billing_address;
    }

    /**
     * @param mixed $billing_address
     */
    public function setBillingAddress($billing_address)
    {
        $this->billing_address = $billing_address;
    }

    /**
     * @return Address|null
     */
    public function getShippingAddress(): ?Address
    {
        return $this->shipping_address;
    }

    /**
     * @param mixed $shipping_address
     */
    public function setShippingAddress($shipping_address)
    {
        $this->shipping_address = $shipping_address;
    }

    /**
     * @return mixed
     */
    public function getDiscountAmount()
    {
        return $this->discount_amount;
    }

    /**
     * @param mixed $discount_amount
     */
    public function setDiscountAmount($discount_amount)
    {
        $this->discount_amount = $discount_amount;
    }

    /**
     * @return mixed
     */
    public function getDiscountAmountVatIncluded()
    {
        return $this->discount_amount_vat_included;
    }

    /**
     * @param mixed $discount_amount_vat_included
     */
    public function setDiscountAmountVatIncluded($discount_amount_vat_included)
    {
        $this->discount_amount_vat_included = $discount_amount_vat_included;
    }

    /**
     * @return mixed
     */
    public function getEcoTax()
    {
        return $this->eco_tax;
    }

    /**
     * @param mixed $eco_tax
     */
    public function setEcoTax($eco_tax)
    {
        $this->eco_tax = $eco_tax;
    }

    /**
     * @return mixed
     */
    public function getEcoTaxVatIncluded()
    {
        return $this->eco_tax_vat_included;
    }

    /**
     * @param mixed $eco_tax_vat_included
     */
    public function setEcoTaxVatIncluded($eco_tax_vat_included)
    {
        $this->eco_tax_vat_included = $eco_tax_vat_included;
    }

    /**
     * @return Order|null
     */
    public function getOrder()
    {
        return $this->order;
    }

    /**
     * @param mixed $order
     */
    public function setOrder($order)
    {
        $this->order = $order;
    }

    /**
     * @return mixed
     */
    public function getPrice()
    {
        return $this->price;
    }

    /**
     * @param mixed $price
     */
    public function setPrice($price)
    {
        $this->price = $price;
    }

    /**
     * @return mixed
     */
    public function getPriceVatIncluded()
    {
        return $this->price_vat_included;
    }

    /**
     * @param mixed $price_vat_included
     */
    public function setPriceVatIncluded($price_vat_included)
    {
        $this->price_vat_included = $price_vat_included;
    }



    /**
     * @return mixed
     */
    public function getVat()
    {
        return $this->vat;
    }

    /**
     * @param mixed $vat
     */
    public function setVat($vat)
    {
        $this->vat = $vat;
    }

    /**
     * @return mixed
     */
    public function getVatOnEcoTax()
    {
        return $this->vat_on_eco_tax;
    }

    /**
     * @param mixed $vat_on_eco_tax
     */
    public function setVatOnEcoTax($vat_on_eco_tax)
    {
        $this->vat_on_eco_tax = $vat_on_eco_tax;
    }

    /**
     * @return mixed
     */
    public function getVatOnProducts()
    {
        return $this->vat_on_products;
    }

    /**
     * @param mixed $vat_on_products
     */
    public function setVatOnProducts($vat_on_products)
    {
        $this->vat_on_products = $vat_on_products;
    }

    /**
     * @return mixed
     */
    public function getVatOnShipping()
    {
        return $this->vat_on_shipping;
    }

    /**
     * @param mixed $vat_on_shipping
     */
    public function setVatOnShipping($vat_on_shipping)
    {
        $this->vat_on_shipping = $vat_on_shipping;
    }

    /**
     * @return mixed
     */
    public function getVatRateOnShipping()
    {
        return $this->vat_rate_on_shipping;
    }

    /**
     * @param mixed $vat_rate_on_shipping
     */
    public function setVatRateOnShipping($vat_rate_on_shipping)
    {
        $this->vat_rate_on_shipping = $vat_rate_on_shipping;
    }

    /**
     * @return IzbergUser|null
     */
    public function getUser(): ?IzbergUser
    {
        return $this->user;
    }

    /**
     * @param mixed $user
     */
    public function setUser($user)
    {
        $this->user = $user;
    }

    public function getMerchant(): ?Merchant
    {
        return $this->merchant;
    }

    public function setMerchant($merchant)
    {
        $this->merchant = $merchant;
    }

    /**
     * @return mixed
     */
    public function getMerchantOrders()
    {
        return $this->merchant_orders;
    }

    /**
     * @param mixed $merchant_orders
     */
    public function setMerchantOrders($merchant_orders)
    {
        $this->merchant_orders = $merchant_orders;
    }

    /**
     * @return mixed
     */
    public function getIdNumber()
    {
        return $this->id_number;
    }

    /**
     * @param mixed $id_number
     */
    public function setIdNumber($id_number)
    {
        $this->id_number = $id_number;
    }

    /**
     * @return mixed
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * @param mixed $currency
     */
    public function setCurrency($currency)
    {
        $this->currency = $currency;
    }

    /**
     * @return mixed
     */
    public function getStatusLocalized()
    {
        return $this->status_localized;
    }

    /**
     * @param mixed $status_localized
     */
    public function setStatusLocalized($status_localized)
    {
        $this->status_localized = $status_localized;
    }

    /**
     * @return mixed
     */
    public function getCreditNotes()
    {
        return $this->credit_notes;
    }

    /**
     * @param mixed $credit_notes
     */
    public function setCreditNotes($credit_notes): void
    {
        $this->credit_notes = $credit_notes;
    }

    /**
     * @return mixed
     */
    public function getPaymentType()
    {
        return $this->payment_type;
    }

    /**
     * @param mixed $payment_type
     */
    public function setPaymentType($payment_type): void
    {
        $this->payment_type = $payment_type;
    }

    /**
     * @return array
     */
    public function getInvoices(): array
    {
        return $this->invoices;
    }

    /**
     * @param array $invoices
     * @return OrderMerchant
     */
    public function setInvoices(array $invoices): OrderMerchant
    {
        $this->invoices = $invoices;
        return $this;
    }

    /**
     * @return bool
     */
    public function isSeeDisputes(): bool
    {
        return $this->seeDisputes;
    }

    /**
     * @param bool $seeDisputes
     * @return OrderMerchant
     */
    public function setSeeDisputes(bool $seeDisputes): OrderMerchant
    {
        $this->seeDisputes = $seeDisputes;
        return $this;
    }

    /**
     * @return array
     */
    public function getSubTotalVat(): array
    {
        return $this->subTotalVat;
    }

    /**
     * @param array $subTotalVat
     * @return OrderMerchant
     */
    public function setSubTotalVat(array $subTotalVat): OrderMerchant
    {
        $this->subTotalVat = $subTotalVat;
        return $this;
    }
}
