<?php

namespace Open\IzbergBundle\Model;

use <PERSON><PERSON>\Serializer\Annotation\Type;

class OrderPayment
{
    public const STATUS_PAYMENT_INITIAL = '0';
    public const STATUS_PAYMENT_PENDING_AUTHORIZATION = '61';
    public const STATUS_PAYMENT_AUTHORIZED = '60';
    public const STATUS_PAYMENT_CANCELLED = '2000';

    #[Type('string')]
    private $external_id;

    #[Type('string')]
    private $id;

    #[Type('string')]
    private $status;

    #[Type('string')]
    private $payment_method_code;

    #[Type('Open\IzbergBundle\Model\PaymentMethod')]
    private $payment_method;

    #[Type('string')]
    private $payment_method_name;

    /**
     * @return mixed
     */
    public function getExternalId()
    {
        return $this->external_id;
    }

    /**
     * @param mixed $external_id
     */
    public function setExternalId($external_id)
    {
        $this->external_id = $external_id;
    }

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param mixed $status
     */
    public function setStatus($status)
    {
        $this->status = $status;
    }

    /**
     * @return mixed
     */
    public function getPaymentMethodCode()
    {
        return $this->payment_method_code;
    }

    /**
     * @param mixed $payment_method_code
     */
    public function setPaymentMethodCode($payment_method_code): void
    {
        $this->payment_method_code = $payment_method_code;
    }

    /**
     * @return PaymentMethod|null
     */
    public function getPaymentMethod()
    {
        return $this->payment_method;
    }

    /**
     * @param mixed $payment_method
     */
    public function setPaymentMethod($payment_method): void
    {
        $this->payment_method = $payment_method;
    }

    /**
     * @return mixed
     */
    public function getPaymentMethodName()
    {
        return $this->payment_method_name;
    }

    /**
     * @param mixed $payment_method_name
     */
    public function setPaymentMethodName($payment_method_name): void
    {
        $this->payment_method_name = $payment_method_name;
    }
}
