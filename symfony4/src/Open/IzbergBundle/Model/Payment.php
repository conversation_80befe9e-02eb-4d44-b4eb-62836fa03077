<?php

namespace Open\IzbergBundle\Model;

use J<PERSON>\Serializer\Annotation\Type;

class Payment
{
    #[Type('string')]
    private $id;

    #[Type('string')]
    private $created_on;

    #[Type('double')]
    private $to_collect_amount;


    #[Type('Open\IzbergBundle\Model\PaymentMethod')]
    private $payment_method;


    public function getId()
    {
        return $this->id;
    }

    public function setId($id): void
    {
        $this->id = $id;
    }

    public function getCreatedOn()
    {
        return $this->created_on;
    }

    public function setCreatedOn($created_on): void
    {
        $this->created_on = $created_on;
    }

    public function getToCollectAmount()
    {
        return $this->to_collect_amount;
    }

    public function setToCollectAmount($to_collect_amount): void
    {
        $this->to_collect_amount = $to_collect_amount;
    }


    public function getPaymentMethod(): ?PaymentMethod
    {
        return $this->payment_method;
    }

    public function setPaymentMethod(?PaymentMethod $payment_method): void
    {
        $this->payment_method = $payment_method;
    }


}
