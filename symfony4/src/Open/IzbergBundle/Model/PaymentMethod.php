<?php

namespace Open\IzbergBundle\Model;

use J<PERSON>\Serializer\Annotation as Serializer;

class PaymentMethod{

    #[Serializer\Type('string')]
    private $code;


    #[Serializer\Type('string')]
    private $pk;

    /**
     * @return mixed
     */
    public function getCode()
    {
        return $this->code;
    }

    /**
     * @param mixed $code
     */
    public function setCode($code): void
    {
        $this->code = $code;
    }

    /**
     * @return mixed
     */
    public function getPk()
    {
        return $this->pk;
    }

    /**
     * @param mixed $pk
     */
    public function setPk($pk): void
    {
        $this->pk = $pk;
    }


}
