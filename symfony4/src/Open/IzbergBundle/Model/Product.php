<?php
/**
 * Created by PhpStorm.
 * User: CMA15450
 * Date: 24/03/2017
 * Time: 16:41
 */

namespace Open\IzbergBundle\Model;

use Symfony\Component\Validator\Constraints as Assert;
use <PERSON><PERSON>\Serializer\Annotation as JSM;
use <PERSON>trine\ORM\Mapping as ORM;


class Product {

    #[ORM\Id]
    #[ORM\Column(name: 'Id', type: 'integer')]
    #[JSM\Type('integer')]
    private $id = 0;

    #[Assert\NotBlank]
    #[JSM\Type('string')]
    private $default_image;

    #[Assert\NotBlank]
    #[JSM\Type('string')]
    private $name;

    #[Assert\NotBlank]
    #[JSM\Type('string')]
    private $description;

    #[Assert\NotBlank]
    #[JSM\Type('string')]
    private $resource_uri;

    #[JSM\Type('Open\IzbergBundle\Model\ProductOffer')]
    private $best_offer = null;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id): void
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getDefaultImage()
    {
        return $this->default_image;
    }

    /**
     * @param mixed $default_image
     */
    public function setDefaultImage($default_image): void
    {
        $this->default_image = $default_image;
    }

    /**
     * @return mixed
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @param mixed $name
     */
    public function setName($name): void
    {
        $this->name = $name;
    }

    /**
     * @return mixed
     */
    public function getDescription()
    {
        return $this->description;
    }

    /**
     * @param mixed $description
     */
    public function setDescription($description): void
    {
        $this->description = $description;
    }

    /**
     * @return mixed
     */
    public function getResourceUri()
    {
        return $this->resource_uri;
    }

    /**
     * @param mixed $resource_uri
     */
    public function setResourceUri($resource_uri): void
    {
        $this->resource_uri = $resource_uri;
    }

    /**
     * @return mixed
     */
    public function getBestOffer()
    {
        return $this->best_offer;
    }

    /**
     * @param mixed $best_offer
     */
    public function setBestOffer($best_offer): void
    {
        $this->best_offer = $best_offer;
    }
}
