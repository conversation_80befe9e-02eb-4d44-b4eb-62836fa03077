<?php
/**
 * Created by PhpStorm.
 * User: CMA15450
 * Date: 24/03/2017
 * Time: 16:42
 */

namespace Open\IzbergBundle\Model;

use Symfony\Component\Validator\Constraints as Assert;
use <PERSON><PERSON>\Serializer\Annotation as JSM;


class ProductOffer
{

    #[JSM\Type('integer')]
    private $id = 0;

    #[Assert\NotBlank]
    #[JSM\Type('string')]
    private $default_image;

    #[Assert\NotBlank]
    #[JSM\Type('string')]
    private $name;

    #[JSM\Type('integer')]
    private $stock;

    #[Assert\NotBlank]
    #[JSM\Type('string')]
    private $description;

    #[Assert\NotBlank]
    #[JSM\Type('integer')]
    private $created_on;

    #[Assert\NotBlank]
    #[JSM\Type('Open\IzbergBundle\Model\Product')]
    private $product;

    #[JSM\Type('Open\IzbergBundle\Model\Merchant')]
    private $merchant;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getDefaultImage()
    {
        return $this->default_image;
    }

    /**
     * @param mixed $default_image
     */
    public function setDefaultImage($default_image)
    {
        $this->default_image = $default_image;
    }

    /**
     * @return mixed
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @param mixed $name
     */
    public function setName($name)
    {
        $this->name = $name;
    }

    /**
     * @return mixed
     */
    public function getDescription()
    {
        return $this->description;
    }

    /**
     * @param mixed $description
     */
    public function setDescription($description)
    {
        $this->description = $description;
    }

    /**
     * @return mixed
     */
    public function getStock()
    {
        return $this->stock;
    }

    /**
     * @param mixed $stock
     */
    public function setStock($stock)
    {
        $this->stock = $stock;
    }

    /**
     * @return mixed
     */
    public function getCreatedOn()
    {
        return $this->created_on;
    }

    /**
     * @param mixed $created_on
     */
    public function setCreatedOn($created_on)
    {
        $this->created_on = $created_on;
    }

    public function getProduct(): ?Product
    {
        return $this->product;
    }

    public function setProduct(?Product $product): void
    {
        $this->product = $product;
    }

    public function getMerchant(): ?Merchant
    {
        return $this->merchant;
    }

    public function setMerchant(?Merchant $merchant): void
    {
        $this->merchant = $merchant;
    }
}
