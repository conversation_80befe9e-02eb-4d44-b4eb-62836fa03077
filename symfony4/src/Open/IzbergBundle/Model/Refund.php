<?php

namespace Open\IzbergBundle\Model;

use <PERSON><PERSON>\Serializer\Annotation\Type;

class Refund
{
    #[Type('integer')]
    private $id = 0;

    #[Type('Open\IzbergBundle\Model\Resource')]
    private $merchant_order;

    #[Type('string')]
    private $customer_invoice;

    #[Type('double')]
    private $amount_vat_included;

    #[Type('string')]
    private $created_on;

    #[Type('double')]
    private $total_refund_amount;

    #[Type('string')]
    private $memo;

    public function getId()
    {
        return $this->id;
    }

    public function setId($id): void
    {
        $this->id = $id;
    }

    public function getMerchantOrder():? Resource
    {
        return $this->merchant_order;
    }

    public function setMerchantOrder(?Resource $merchant_order): void
    {
        $this->merchant_order = $merchant_order;
    }

    public function getCustomerInvoice()
    {
        return $this->customer_invoice;
    }

    public function setCustomerInvoice($customer_invoice): void
    {
        $this->customer_invoice = $customer_invoice;
    }

    public function getAmountVatIncluded()
    {
        return $this->amount_vat_included;
    }

    public function setAmountVatIncluded($amount_vat_included): void
    {
        $this->amount_vat_included = $amount_vat_included;
    }

    public function getCreatedOn()
    {
        return $this->created_on;
    }

    public function setCreatedOn($created_on): void
    {
        $this->created_on = $created_on;
    }

    public function getTotalRefundAmount()
    {
        return $this->total_refund_amount;
    }

    public function setTotalRefundAmount($total_refund_amount): void
    {
        $this->total_refund_amount = $total_refund_amount;
    }

    public function getMemo()
    {
        return $this->memo;
    }

    public function setMemo($memo)
    {
        $this->memo = $memo;
    }
}
