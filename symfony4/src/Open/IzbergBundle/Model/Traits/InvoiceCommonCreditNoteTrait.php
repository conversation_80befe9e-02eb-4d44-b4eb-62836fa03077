<?php

declare(strict_types=1);

namespace Open\IzbergBundle\Model\Traits;

use JMS\Serializer\Annotation\Type;
use Open\IzbergBundle\Model\Merchant;

trait InvoiceCommonCreditNoteTrait
{
    #[Type('string')]
    private ?string $payment_details = null;

    #[Type('Open\IzbergBundle\Model\Merchant')]
    private Merchant $issuer;

    #[Type('string')]
    private ?string $issuer_siret_number = null;

    #[Type('string')]
    private ?string $legal_notices = null;

    public function getIssuer(): Merchant
    {
        return $this->issuer;
    }

    public function setIssuer(Merchant $issuer): void
    {
        $this->issuer = $issuer;
    }

    public function getIssuerSiretNumber(): ?string
    {
        return $this->issuer_siret_number;
    }

    public function setIssuerSiretNumber(?string $issuer_siret_number): void
    {
        $this->issuer_siret_number = $issuer_siret_number;
    }

    public function getLegalNotices(): ?string
    {
        return $this->legal_notices;
    }

    public function setLegalNotices(?string $legal_notices): void
    {
        $this->legal_notices = $legal_notices;
    }

    public function getPaymentDetails(): ?string
    {
        return $this->payment_details;
    }

    public function setPaymentDetails(?string $payment_details): void
    {
        $this->payment_details = $payment_details;
    }
}
