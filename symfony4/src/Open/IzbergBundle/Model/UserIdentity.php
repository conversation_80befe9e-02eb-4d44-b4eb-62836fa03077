<?php

declare(strict_types=1);

namespace Open\IzbergBundle\Model;

use <PERSON><PERSON>\Serializer\Annotation\Type;

final class UserIdentity
{
    public function __construct(
        public readonly string $uuid,
        public readonly string $domain_id,
        public readonly string $domain_alias,
        public readonly string $application,
        public readonly string $email,
        public readonly string $first_name,
        public readonly string $last_name,
        public readonly string $status,
        public readonly string $created_at,
        public readonly string $last_login,
        public readonly string $last_token_refresh,
        public readonly string $created_by,
        #[Type('array')]
        public readonly array $merchant_scopes,
        public readonly string $user_type,
    ) {
    }

    /**
     * @return MerchantScope[]
     */
    public function getMerchantScopes(): array
    {
        $merchantScopes = [];
        foreach ($this->merchant_scopes as $merchantId => $merchantScope) {
            $merchantScopes[] = new MerchantScope(id: $merchantId, scope: $merchantScope);
        }

        return $merchantScopes;
    }

    public function hasMerchantScopes(): bool
    {
        return \count($this->merchant_scopes) > 0;
    }
}
