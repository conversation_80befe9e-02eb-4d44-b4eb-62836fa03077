<?php

namespace Open\IzbergBundle\Model;
use J<PERSON>\Serializer\Annotation\Type;
use J<PERSON>\Serializer\Annotation as JSM;

final class WebHookMerchantOrderRefundRequest extends WebHookRequest
{
    /**
     * @var MerchantOrderRefund
     */
    #[Type('Open\IzbergBundle\Model\MerchantOrderRefund')]
    private $data;

    public function getData(): MerchantOrderRefund
    {
        return $this->data;
    }

    public function setData($data): self
    {
        $this->data = $data;
        return $this;
    }
}
