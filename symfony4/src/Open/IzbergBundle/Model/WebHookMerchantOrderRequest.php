<?php

namespace Open\IzbergBundle\Model;
use J<PERSON>\Serializer\Annotation\Type;
use J<PERSON>\Serializer\Annotation as JSM;

/**
 * Entity to map a MerchantOrder object
 */
#[JSM\ExclusionPolicy('none')]
class WebHookMerchantOrderRequest extends WebHookRequest
{
    #[Type('Open\IzbergBundle\Model\MerchantOrder')]
    private $data;

    /**
     * @return mixed
     */
    public function getData()
    {
        return $this->data;
    }

    /**
     * @param mixed $data
     */
    public function setData($data): void
    {
        $this->data = $data;
    }
}
