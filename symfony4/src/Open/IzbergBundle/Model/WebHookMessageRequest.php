<?php

namespace Open\IzbergBundle\Model;
use J<PERSON>\Serializer\Annotation\Type;
use <PERSON><PERSON>\Serializer\Annotation as JSM;

/**
 * Entity to map a Message object
 */
#[JSM\ExclusionPolicy('none')]
class WebHookMessageRequest extends WebHookRequest
{
    #[Type('Open\IzbergBundle\Model\Message')]
    private $data;

    public function getData(): ?Message
    {
        return $this->data;
    }

    public function setData($data): void
    {
        $this->data = $data;
    }
}
