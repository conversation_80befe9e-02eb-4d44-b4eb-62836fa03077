<?php

namespace Open\IzbergBundle\Model;
use JMS\Serializer\Annotation\Type;
use J<PERSON>\Serializer\Annotation as JSM;

/**
 * Entity to map a Order object
 */
#[JSM\ExclusionPolicy('none')]
class WebHookOrderRequest extends WebHookRequest
{
    #[Type('Open\IzbergBundle\Model\Order')]
    private $data;

    public function getData()
    {
        return $this->data;
    }

    public function setData($data): void
    {
        $this->data = $data;
    }
}
