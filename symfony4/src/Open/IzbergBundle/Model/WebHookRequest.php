<?php

namespace Open\IzbergBundle\Model;
use <PERSON><PERSON>\Serializer\Annotation\Type;
use <PERSON><PERSON>\Serializer\Annotation as JSM;

#[JSM\ExclusionPolicy('none')]
abstract class WebHookRequest
{
    #[Type('string')]
    private $environment;

    #[Type('string')]
    private $version;

    #[Type('int')]
    private $webhook_trigger_id;

    #[Type('int')]
    private $webhook_id;

    #[Type('int')]
    private $attempt;

    /**
     * @return mixed
     */
    public function getEnvironment()
    {
        return $this->environment;
    }

    /**
     * @param mixed $environment
     */
    public function setEnvironment($environment): void
    {
        $this->environment = $environment;
    }

    /**
     * @return mixed
     */
    public function getVersion()
    {
        return $this->version;
    }

    /**
     * @param mixed $version
     */
    public function setVersion($version): void
    {
        $this->version = $version;
    }

    /**
     * @return mixed
     */
    public function getWebhookTriggerId()
    {
        return $this->webhook_trigger_id;
    }

    /**
     * @param mixed $webhook_trigger_id
     */
    public function setWebhookTriggerId($webhook_trigger_id): void
    {
        $this->webhook_trigger_id = $webhook_trigger_id;
    }

    /**
     * @return mixed
     */
    public function getWebhookId()
    {
        return $this->webhook_id;
    }

    /**
     * @param mixed $webhook_id
     */
    public function setWebhookId($webhook_id): void
    {
        $this->webhook_id = $webhook_id;
    }

    /**
     * @return mixed
     */
    public function getAttempt()
    {
        return $this->attempt;
    }

    /**
     * @param mixed $attempt
     */
    public function setAttempt($attempt): void
    {
        $this->attempt = $attempt;
    }

    abstract public function getData();


    abstract public function setData($data);
}
