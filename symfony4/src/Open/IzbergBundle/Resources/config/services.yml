services:
    _defaults:
        autowire: true
        autoconfigure: true
        public: false

    Open\IzbergBundle\Controller\:
        resource: '../../Controller/'
        tags: [ 'controller.service_arguments' ]

    Open\IzbergBundle\Api\AuthenticationApi:
        class: Open\IzbergBundle\Api\AuthenticationApi
        tags: [ 'izberg.api' ]

    Open\IzbergBundle\Api\MerchantApi:
        class: Open\IzbergBundle\Api\MerchantApi
        calls:
            - [ setCountryService, [ '@AppBundle\Services\CountryService' ] ]
        tags: [ 'izberg.api' ]

    Open\IzbergBundle\Api\KycApi:
        class: Open\IzbergBundle\Api\KycApi
        tags: [ 'izberg.api' ]

    Open\IzbergBundle\Api\CreateMerchantUserApi:
        class: Open\IzbergBundle\Api\CreateMerchantUserApi
        tags: [ 'izberg.api' ]

    Open\IzbergBundle\Api\ApiIdentity\UserIdentityApi:
        class: Open\IzbergBundle\Api\ApiIdentity\UserIdentityApi
        tags: [ 'izberg.api' ]

    Open\IzbergBundle\Api\MessageApi:
        class: Open\IzbergBundle\Api\MessageApi
        tags: [ 'izberg.api' ]

    Open\IzbergBundle\Api\UserApi:
        class: Open\IzbergBundle\Api\UserApi
        tags: [ 'izberg.api' ]

    Open\IzbergBundle\Api\AddressApi:
        class: Open\IzbergBundle\Api\AddressApi
        tags: [ 'izberg.api' ]

    Open\IzbergBundle\Api\CountryApi:
        class: Open\IzbergBundle\Api\CountryApi
        tags: [ 'izberg.api' ]

    Open\IzbergBundle\Api\CategoryApi:
        class: Open\IzbergBundle\Api\CategoryApi
        tags: [ 'izberg.api' ]

    Open\IzbergBundle\Api\CartApi:
        class: Open\IzbergBundle\Api\CartApi
        tags: [ 'izberg.api' ]

    Open\IzbergBundle\Api\CartNotificationApi:
        class: Open\IzbergBundle\Api\CartNotificationApi
        tags: [ 'izberg.api' ]

    Open\IzbergBundle\Api\ProductOfferApi:
        class: Open\IzbergBundle\Api\ProductOfferApi
        tags: [ 'izberg.api' ]

    Open\IzbergBundle\Api\OrderApi:
        class: Open\IzbergBundle\Api\OrderApi
        tags: [ 'izberg.api' ]

    Open\IzbergBundle\Api\OrderItemApi:
        class: Open\IzbergBundle\Api\OrderItemApi
        tags: [ 'izberg.api' ]

    Open\IzbergBundle\Api\AttributeApi:
        class: Open\IzbergBundle\Api\AsyncAttributeApi
        tags: [ 'izberg.api' ]

    Open\IzbergBundle\Api\PaymentApi:
        class: Open\IzbergBundle\Api\PaymentApi
        tags: [ 'izberg.api' ]

    Open\IzbergBundle\Api\GatewayApi:
        class: Open\IzbergBundle\Api\GatewayApi
        tags: [ 'izberg.api' ]

    Open\IzbergBundle\Api\InvoiceApi:
        class: Open\IzbergBundle\Api\InvoiceApi
        tags: [ 'izberg.api' ]

    Open\IzbergBundle\Api\InvoiceLineApi:
        class: Open\IzbergBundle\Api\InvoiceLineApi
        tags: [ 'izberg.api' ]

    Open\IzbergBundle\Api\CreditNoteApi:
        class: Open\IzbergBundle\Api\CreditNoteApi
        tags: [ 'izberg.api' ]

    Open\IzbergBundle\Api\RefundApi:
        class: Open\IzbergBundle\Api\RefundApi
        tags: [ 'izberg.api' ]

    Open\IzbergBundle\Api\PermissionApi:
        class: Open\IzbergBundle\Api\PermissionApi
        tags: [ 'izberg.api' ]

    Open\IzbergBundle\Api\MerchantOrderCommissionRuleApi:
        class: Open\IzbergBundle\Api\MerchantOrderCommissionRuleApi
        tags: [ 'izberg.api' ]

    Open\IzbergBundle\EventSubscriber\AuthenticationSubscriber:
        class: Open\IzbergBundle\EventSubscriber\AuthenticationSubscriber
        arguments: [ '@Open\IzbergBundle\Api\AuthenticationApi',
                     '@request_stack',
                     '@security.token_storage',
                     '@translator.default',
                     '@doctrine.orm.entity_manager',
                     '@Open\IzbergBundle\Api\ApiClientManager',
                     '@Open\TicketBundle\Services\TicketService',
                     '@AppBundle\Services\CartService',
                     '@Open\IzbergBundle\Service\AttributeService' ]
        tags:
            - { name: kernel.event_subscriber }

    Open\IzbergBundle\Algolia\AlgoliaServiceEn:
        class: Open\IzbergBundle\Algolia\AlgoliaServiceEn
        arguments: [ '%env(IZBERG_ALGOLIA_APPLICATION_ID)%', '%env(IZBERG_ALGOLIA_READ_APIKEY)%', '%env(IZBERG_ALGOLIA_INDEX_EN_BASE_INDEX)%', '%env(IZBERG_ALGOLIA_WRITE_APIKEY)%' ]
        calls:
            - [ setTransportCategory, [ '%env(IZBERG_TRANSPORT_ID)%' ] ]

    Open\IzbergBundle\Algolia\AlgoliaServiceFr:
        class: Open\IzbergBundle\Algolia\AlgoliaServiceFr
        arguments: [ '%env(IZBERG_ALGOLIA_APPLICATION_ID)%', '%env(IZBERG_ALGOLIA_READ_APIKEY)%', '%env(IZBERG_ALGOLIA_INDEX_FR_BASE_INDEX)%', '%env(IZBERG_ALGOLIA_WRITE_APIKEY)%' ]
        calls:
            - [ setTransportCategory, [ '%env(IZBERG_TRANSPORT_ID)%' ] ]

    Open\IzbergBundle\Algolia\AlgoliaServiceEs:
        class: Open\IzbergBundle\Algolia\AlgoliaServiceEs
        arguments: [ '%env(IZBERG_ALGOLIA_APPLICATION_ID)%', '%env(IZBERG_ALGOLIA_READ_APIKEY)%', '%env(IZBERG_ALGOLIA_INDEX_ES_BASE_INDEX)%', '%env(IZBERG_ALGOLIA_WRITE_APIKEY)%' ]
        calls:
            - [ setTransportCategory, [ '%env(IZBERG_TRANSPORT_ID)%' ] ]

    Open\IzbergBundle\Algolia\AlgoliaServiceDe:
        class: Open\IzbergBundle\Algolia\AlgoliaServiceDe
        arguments: [ '%env(IZBERG_ALGOLIA_APPLICATION_ID)%', '%env(IZBERG_ALGOLIA_READ_APIKEY)%', '%env(IZBERG_ALGOLIA_INDEX_DE_BASE_INDEX)%', '%env(IZBERG_ALGOLIA_WRITE_APIKEY)%' ]
        calls:
            - [ setTransportCategory, [ '%env(IZBERG_TRANSPORT_ID)%' ] ]

    Open\IzbergBundle\Algolia\AlgoliaServiceIt:
        class: Open\IzbergBundle\Algolia\AlgoliaServiceIt
        arguments: [ '%env(IZBERG_ALGOLIA_APPLICATION_ID)%', '%env(IZBERG_ALGOLIA_READ_APIKEY)%', '%env(IZBERG_ALGOLIA_INDEX_IT_BASE_INDEX)%', '%env(IZBERG_ALGOLIA_WRITE_APIKEY)%' ]
        calls:
            - [ setTransportCategory, [ '%env(IZBERG_TRANSPORT_ID)%' ] ]

    Open\IzbergBundle\Algolia\AlgoliaServiceNl:
        class: Open\IzbergBundle\Algolia\AlgoliaServiceNl
        arguments: [ '%env(IZBERG_ALGOLIA_APPLICATION_ID)%', '%env(IZBERG_ALGOLIA_READ_APIKEY)%', '%env(IZBERG_ALGOLIA_INDEX_NL_BASE_INDEX)%', '%env(IZBERG_ALGOLIA_WRITE_APIKEY)%' ]
        calls:
            - [ setTransportCategory, [ '%env(IZBERG_TRANSPORT_ID)%' ] ]

    Open\IzbergBundle\Algolia\QuerySuggestionServiceEn:
        class: Open\IzbergBundle\Algolia\QuerySuggestionServiceEn
        arguments: [ '%env(IZBERG_ALGOLIA_APPLICATION_ID)%', '%env(IZBERG_ALGOLIA_READ_APIKEY)%', '%env(IZBERG_ALGOLIA_QUERY_SUGGESTION_INDEX_EN)%' ]

    Open\IzbergBundle\Algolia\QuerySuggestionServiceFr:
        class: Open\IzbergBundle\Algolia\QuerySuggestionServiceFr
        arguments: [ '%env(IZBERG_ALGOLIA_APPLICATION_ID)%', '%env(IZBERG_ALGOLIA_READ_APIKEY)%', '%env(IZBERG_ALGOLIA_QUERY_SUGGESTION_INDEX_FR)%' ]

    Open\IzbergBundle\Algolia\QuerySuggestionServiceEs:
        class: Open\IzbergBundle\Algolia\QuerySuggestionServiceEs
        arguments: [ '%env(IZBERG_ALGOLIA_APPLICATION_ID)%', '%env(IZBERG_ALGOLIA_READ_APIKEY)%', '%env(IZBERG_ALGOLIA_QUERY_SUGGESTION_INDEX_ES)%' ]

    Open\IzbergBundle\Algolia\QuerySuggestionServiceDe:
        class: Open\IzbergBundle\Algolia\QuerySuggestionServiceDe
        arguments: [ '%env(IZBERG_ALGOLIA_APPLICATION_ID)%', '%env(IZBERG_ALGOLIA_READ_APIKEY)%', '%env(IZBERG_ALGOLIA_QUERY_SUGGESTION_INDEX_DE)%' ]

    Open\IzbergBundle\Algolia\QuerySuggestionServiceIt:
        class: Open\IzbergBundle\Algolia\QuerySuggestionServiceIt
        arguments: [ '%env(IZBERG_ALGOLIA_APPLICATION_ID)%', '%env(IZBERG_ALGOLIA_READ_APIKEY)%', '%env(IZBERG_ALGOLIA_QUERY_SUGGESTION_INDEX_IT)%' ]

    Open\IzbergBundle\Algolia\QuerySuggestionServiceNl:
        class: Open\IzbergBundle\Algolia\QuerySuggestionServiceNl
        arguments: [ '%env(IZBERG_ALGOLIA_APPLICATION_ID)%', '%env(IZBERG_ALGOLIA_READ_APIKEY)%', '%env(IZBERG_ALGOLIA_QUERY_SUGGESTION_INDEX_NL)%' ]

    Open\IzbergBundle\Service\CategoryService:
        public: true
        class: Open\IzbergBundle\Service\CategoryService
        arguments: [ '@Open\IzbergBundle\Api\CategoryApi', '@Open\IzbergBundle\Service\RedisService', '@request_stack' ]
#        calls:
#            - [ setContainer,[ '@service_container' ] ]

    Open\IzbergBundle\Service\AttributeService:
        class: Open\IzbergBundle\Service\AttributeService
        arguments: [ '@Open\IzbergBundle\Api\AttributeApi', '@Open\IzbergBundle\Service\RedisService', '%env(json:SUPPORTED_LOCALES)%' ]

    Open\IzbergBundle\Service\RedisService:
        class: Open\IzbergBundle\Service\RedisService
        arguments: [ '%env(REDIS_HOST)%', '%env(int:REDIS_PORT)%' ]
