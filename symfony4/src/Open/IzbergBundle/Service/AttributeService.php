<?php

namespace Open\IzbergBundle\Service;

use AppBundle\Services\AlstomCustomAttributes;
use Open\IzbergBundle\Api;
use Open\IzbergBundle\Api\AttributeApi;
use Open\IzbergBundle\ApiRequestParameter;
use Open\IzbergBundle\Dto\AttributeDTO;
use Open\IzbergBundle\Dto\RedisAttributeDTO;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;

class AttributeService implements LoggerAwareInterface
{
    private const DEFAULT_LOCALE = "en";
    private const CACHE_KEYS = "IZBERG_ATTRIBUTES";
    private const CACHE_ATTRIBUTE_ID = "IZBERG_ATTRIBUTE_ID";
    private const CACHE_IZBERG_MERCHANT_ORDER_ATTRIBUTE_ID = "CACHE_IZBERG_MERCHANT_ORDER_ATTRIBUTE_ID";
    private const CACHE_NAMES = "IZBERG_ATTRIBUTES_NAMES";

    private array $supportedLocales;
    private RedisService $cacheService;
    private AttributeApi $attributeApi;
    private LoggerInterface $logger;

    public function __construct(
        AttributeApi $attributeApi,
        RedisService $cacheService,
        array $supportedLocales
    ) {
        $this->attributeApi = $attributeApi;
        $this->cacheService = $cacheService;
        $this->supportedLocales = $supportedLocales;
    }


    /**
     * @param string $locale the locale to use to get the attribute
     * @param string $attributeName the name of the attribute to get
     * @return AttributeDTO the attribute
     */
    public function getAttribute ($attributeName, $locale){
        $attributes = $this->getCachedAttributes();

        if (array_key_exists($locale, $attributes) && array_key_exists($attributeName, $attributes[$locale]) && $attributes[$locale][$attributeName]->getLabel() !== null){
            return $attributes[$locale][$attributeName];
        }
        //if not found, check for default english version
        if ($locale !== self::DEFAULT_LOCALE){
            return $this->getAttribute($attributeName, self::DEFAULT_LOCALE);
        }

        //if still no return here, we build a default attribute
        $attribute = new AttributeDTO();
        $attribute->setKey($attributeName);
        $attribute->setLabel($attributeName);
        return $attribute;
    }

    /**
     * @param string $locale the locale to use the attribute
     * @return array
     */
    public function getAttributesForLocale($locale){
        return $this->getCachedAttributes()[$locale];
    }


    /**
     * get the izberg identifier of the reconciliation attribute
     * @return mixed
     */
    public function getReconciliationAttributeId(){
        return $this->getOrderAttributeId('reconciliation_key');
    }

    public function getOrderAttributeId(string $key, bool $force = false)
    {
        return $this->getAttributeId(new RedisAttributeDTO($key), 'getOrderAttributeId', $force);
    }

    /**
     * @param int $merchantOrderId
     * @param int $attributeId
     * @param bool $force
     * @return int|null
     */
    public function getOrderAttributeIzbergId(int $merchantOrderId, int $attributeId, bool $force = false):?int
    {
        $cacheId = sprintf('%s_%s', self::CACHE_IZBERG_MERCHANT_ORDER_ATTRIBUTE_ID, $merchantOrderId);
        return $this->getAttributeId(new RedisAttributeDTO((string)$attributeId, $merchantOrderId), 'getOrderAttributeIzbergId', $force,$cacheId );
    }

    public function getMerchantAttributeId(string $key, bool $force = false)
    {
        return $this->getAttributeId(new RedisAttributeDTO($key), 'getMerchantAttributeId', $force);
    }

    public function getCustomerAttributeId($key, bool $force = false)
    {
        return $this->getAttributeId(new RedisAttributeDTO($key), 'getCustomerAttributeId', $force);
    }

    private function getAttributeId(RedisAttributeDTO $redisAttribute, string $attributeApiMethod, bool $force = false, string $cacheId = self::CACHE_ATTRIBUTE_ID)
    {
        if (!method_exists($this->attributeApi, $attributeApiMethod) ) {
            throw new \InvalidArgumentException(
                sprintf(
                    '%s method does not exists in %s class',
                    $attributeApiMethod,
                    get_class($this->attributeApi)
                )
            );
        }

        $key = $redisAttribute->getKey();
        $cacheKey = sprintf('%s_%s', $cacheId, $key);
        $identifier = $this->cacheService->getItem($cacheKey);
        if (empty($identifier) || $force){
            $identifier = $this->attributeApi->$attributeApiMethod($redisAttribute);
            if (!$this->saveInCache($cacheKey, $identifier)) {
                $this->logger->error(
                    "error getting attribute ID from Izberg",
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::IZBERG_ATTRIBUTE_NOT_FOUND,
                        'attribute_key' => $key,
                    ])
                );
            }
        }

        return $identifier;
    }

    /**
     * update the reconciliation key of a merchant order
     * @param integer $attributeId the identifier of the attribute
     * @param integer $merchantOrderId the identifier of the merchant order
     * @param string $value the value to set for the attribute
     */
    public function updateReconciliationAttribute($attributeId, $merchantOrderId, $value){
        $this->attributeApi->updateReconciliationAttribute($attributeId, $merchantOrderId, $value);
    }

    public function updateMerchantOrderPdfAttribute(int $merchantOrderId, $file)
    {
        $attributeId = $this->getOrderAttributeId('ZZD-Merchant-Order-detail-file');

        /**
         * @var Api\AsyncAttributeApi $asyncAttributeApi
         */
        $asyncAttributeApi = $this->attributeApi;
        $asyncAttributeApi->updateMerchantOrderFileAttribute($merchantOrderId, $attributeId, $file);

    }

    public function updateMerchantOrderExtraInfo($merchantOrderId, array $orderExtraInfo)
    {
        $defaultOrderExtraInfo = [
            'ZSA-Contact-Info' => '',
            'ZTA-Contact-First-Name' => '',
            'ZUA-Contact-Last-Name' => '',
            'ZVA-Contact-Email' => '',
            'ZWA-Contact-Main-Phone' => '',
            'ZXA-Contact-Phone' => '',
            'ZYA-Job-Title' => '',
            'ZZA-Contact-Comment' => '',
        ];

        $orderExtraInfo = $orderExtraInfo + $defaultOrderExtraInfo;

        $attributes = [];
        foreach($orderExtraInfo as $key => $value) {
            $attributes[$this->getOrderAttributeId($key)] = $value;
        }

        $this->attributeApi->createMerchantOrderAttributes($merchantOrderId, $attributes);
    }

    public function setMerchantsOrdersExtraInfo(array $merchantOrderIds, array $attributes)
    {
        $defaultAttributes = [
            'ZSA-Contact-Info' => '',
            'ZTA-Contact-First-Name' => '',
            'ZUA-Contact-Last-Name' => '',
            'ZVA-Contact-Email' => '',
            'ZWA-Contact-Main-Phone' => '',
            'ZXA-Contact-Phone' => '',
            'ZYA-Job-Title' => '',
            'ZZA-Contact-Comment' => '',
        ];

        $attributes = $attributes + $defaultAttributes;
        $attributes = array_combine(
            array_map(
                function($key) {
                    return $this->getOrderAttributeId($key);
                },
                array_keys($attributes)
            ),
            array_values($attributes)
        );

        $data = [];
        foreach($merchantOrderIds as $merchantOrderId) {
            foreach($attributes as $attributeId => $attributeValue) {
                if (!empty($attributeValue)) {
                    $data[] = [$merchantOrderId, $attributeId, $attributeValue];
                }
            }
        }

        $this->attributeApi->sendConcurrentApiRequest(
            array_map(
                function(array $datum) {
                    [$merchantOrderId, $attributeId, $attributeValue] = $datum;

                    return new ApiRequestParameter(
                        Api::HTTP_POST_OPERATION,
                        'order_attribute_value/',
                        [
                            'attribute' => '/v1/order_attribute/'.strval($attributeId).'/',
                            'application' => '/v1/application/'.$this->attributeApi->getApplicationId().'/',
                            'value' => $attributeValue,
                            'entity' => '/v1/merchant_order/'.strval($merchantOrderId).'/'
                        ]
                    );
                },
                $data
            ),
            true,
            5,
            [
                AttributeApi::OPTION_NO_DATA_RETURN,
            ]
        );
    }

    /**
     * Update merchant order attribute.
     *
     * @param int $merchantOrderId
     * @param array $attributes
     */
    public function updateMerchantOrderAttributes(int $merchantOrderId, array $attributes):void{
        $attrToCreate = [];
        $attrToUpdate = [];
        foreach($attributes as $key => $value) {
            $attributeId = $this->getOrderAttributeId($key);
            $attributeizbergId = $this->getOrderAttributeIzbergId($merchantOrderId,$attributeId);
            if($attributeizbergId != null){
                $attrToUpdate[$attributeizbergId] = $value;
                continue;
            }

            $attrToCreate[$attributeId] = $value;

        }
        if(!empty($attrToCreate)){
            $this->attributeApi->createMerchantOrderAttributes($merchantOrderId, $attrToCreate);
        }
        if(!empty($attrToUpdate)){
            $this->attributeApi->updateMerchantOrderAttributes($merchantOrderId, $attrToUpdate);
        }

    }

    public function updateCustomerAttributes($userId, array $customerAttributes){
        $defaultCustomerAttributes = [
            'customer_vat_number' => '',
        ];

        $customerAttributes = $customerAttributes + $defaultCustomerAttributes;

        foreach($customerAttributes as $key => $value) {
            $this->attributeApi->updateCustomerAttribute($this->getCustomerAttributeId($key), $userId, $value);
        }
    }

    /**
     * get all the attributes from the cache
     * @param bool $force
     * @return array|mixed an array that contains the attributes in each languages [locale => [attributeKey => attributeValue]
     */
    public function getCachedAttributes(bool $force = false){
        $attributes = $this->cacheService->getItem(self::CACHE_KEYS);
        if ($attributes === null || $force) {
            $attributes = [];
            foreach ($this->supportedLocales as $locale){
                $attributes[$locale] = $this->attributeApi->getAttributes($locale);
            }

            //cache attributes for 24 hours
            $this->saveInCache(self::CACHE_KEYS, $attributes);
        }
        return $attributes;
    }

    public function fetchTechnicalAttributes(string $locale): array
    {
        $attributes = $this->getCachedAttributes();

        if (!isset($attributes[$locale])) {
            return [];
        }

        $technicalAttributes = array_filter(array_keys($attributes[$locale]), function($attributeName) {
            return strpos($attributeName, 'D') === 0;
        });

        sort($technicalAttributes, SORT_STRING);

        return array_map(function($attributeName) {
            return AlstomCustomAttributes::createFullAttributeName($attributeName);
        }, $technicalAttributes);
    }

    private function saveInCache($cacheKey, $value, int $expiration = null): bool
    {
        // invalidate Item if exists
        $this->cacheService->removeItem($cacheKey);

        if (is_array($value) && !count($value)) {
            return false;
        }

        if (!$value) {
            return false;
        }

        $this->cacheService->saveItem($cacheKey, $value, $expiration);
        return true;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    public function createOrUpdateCustomerAttributes(int $userId, array $attributes)
    {
        $this->attributeApi->createOrUpdateCustomerAttributes($userId, $attributes);
    }

    /**
     * Get attribute name by key
     *
     * <AUTHOR> Bulochnik <<EMAIL>>
     *
     * @param string $attrKey
     * @param string $locale
     * @return mixed
     */
    public function getAttributeNameByKey(string $attrKey, string $locale)
    {
        $attributes = $this->cacheService->getItem(self::CACHE_NAMES);

        if ($attributes && array_key_exists($locale, $attributes) && array_key_exists($attrKey, $attributes[$locale])) {
            return $attributes[$locale][$attrKey];
        }

        $attributes[$locale][$attrKey] = $this->attributeApi->getAttributeNameByKey($attrKey, $locale);

        // Cache attributes for 24 hours
        $this->cacheService->saveItem(self::CACHE_NAMES, $attributes);

        return $attributes[$locale][$attrKey];
    }
}
