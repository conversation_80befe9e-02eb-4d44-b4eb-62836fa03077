<?php

namespace Open\IzbergBundle\Service;

use AppBundle\Entity\SearchFacetValue;
use AppBundle\Util\Locale;
use Open\IzbergBundle\Api\CategoryApi;
use Open\IzbergBundle\Dto\CategoryDTO;
use Open\IzbergBundle\Model\Category;
use Open\IzbergBundle\Model\Resource;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class CategoryService
{

    private const CACHE_KEY = 'IZBERG_CATEGORIES';
    private const CACHE_KEY_ALL = 'IZBERG_ALL_CATEGORIES';

    private const ICONS_PARAMETER = 'izberg_category_icons';
    private const ICONS_PATH_PARAMETER = 'izberg_category_icons_path';

    /**
     * @var CategoryApi the category object to request the izberg API
     */
    private $categoryApi;

    /**
     * @var RedisService
     */
    private $cacheService;

    /**
     * @var string
     */
    private $locale;

    private $params;

    /**
     * CategoryService constructor.
     */
    public function __construct(
        CategoryApi $categoryApi,
        RedisService $cacheService,
        RequestStack $requestStack,
        ParameterBagInterface $params
    ) {
        $this->categoryApi = $categoryApi;
        $this->cacheService = $cacheService;

        // If service is initialize with a request, set request locale to service
        $this->setLocale(Locale::fetchFromRequestStack($requestStack));
        $this->params = $params;
    }

    public function find(string $categoryId):? Category
    {
        $categories = $this->getCachedCategories();

        /** @var Category $category */
        foreach ($categories as $category) {
            if ($category->getId() === (int) $categoryId) {
                return $category;
            }
        }

        return null;
    }

    /*
     * @param $categoryId
     *
     * @return mixed|null|string
     */
    public function getCategoryIconPath($categoryId)
    {
        $path = $this->params->has(self::ICONS_PATH_PARAMETER)
            ? $this->params->get(self::ICONS_PATH_PARAMETER)
            : '';

        if ($this->params->has(self::ICONS_PARAMETER)) {
            /** @var array $icons */
            $icons = $this->params->get(self::ICONS_PARAMETER);
            if (isset($icons[$categoryId])) {
                $ret = '/'.$path.'/'.$icons[$categoryId];
                $ret = str_replace('//', '/', $ret);

                $appPath = $this->params->get('kernel.project_dir');
                $webPath = realpath($appPath.'/../web');
                $filePath = $webPath.$ret;

                if (file_exists($filePath)) {
                    return $ret;
                }

                return null;
            }

            return null;
        }

        return null;
    }

    /**
     * @param $id
     * @return string|null
     */
    public function getCategorieName($id)
    {
        $categories = $this->getCachedCategories();
        foreach ($categories as $category) {
            if ($category->getId() == $id) {
                return $category->getName();
            }
        }

        return null;
    }

    /*
     * @param $id
     *
     * @return null
     * @throws \Psr\Cache\InvalidArgumentException
     */
    public function getCategorieExternalId($id)
    {
        $categories = $this->getCachedCategories();
        foreach ($categories as $category) {
            if ($category->getId() == $id) {
                return $category->getExternalId();
            }
        }

        return null;
    }

    /**
     * @return array
     */
    public function getClassifiedCategories()
    {
        $categories = $this->categoryApi->getAllCategories($this->locale);

        foreach ($categories as $key => $category) {
            $categories[$key]->setImage($this->getCategoryIconPath($category->getId()));
        }

        //get all the roots directory
        $roots = $this->getRootCategories($categories);

        //we want to sort our root categories
        usort($roots, array($this, "compareCat"));

        //now explode each root directories
        foreach ($roots as $root) {
            $this->explode($root, $categories);
        }
        return $roots;
    }

    /**
     * @return array|mixed
     */
    public function getCachedClassifiedCategories()
    {
        $categories = $this->cacheService->getItem(self::CACHE_KEY.'.'.$this->locale);
        if (null === $categories) {
            $categories = $this->getClassifiedCategories();
            $this->cacheService->saveItem(self::CACHE_KEY.'.'.$this->locale, $categories);
        }

        return $categories;
    }

    /**
     * @return \Doctrine\Common\Collections\ArrayCollection|mixed|null
     */
    public function getCachedCategories()
    {
        $categories = $this->cacheService->getItem(self::CACHE_KEY_ALL.'.'.$this->locale);
        if (null === $categories) {
            $categories = $this->categoryApi->getAllCategories($this->locale);
            $this->cacheService->saveItem(self::CACHE_KEY_ALL.'.'.$this->locale, $categories);
        }

        return $categories;
    }

    // @throws \Psr\Cache\InvalidArgumentException
    public function getParent(string $cat): ?string
    {
        $categories = $this->getCachedCategories();

        /** @var Category $category */
        foreach ($categories as $category) {
            if ($category->getId() == $cat) {
                $parents = $category->getParents();
                if (isset($parents[0])) {
                    return $parents[0]->getId();
                }
            }
        }

        return null;
    }

    /**
     * Filtre les facets correspondantes au niveau juste en dessous de $parentCategory (rien que le niveau inférieur)
     * Si on est au bout, on garde la facet correpondante au niveau en cours
     * Should return an array 'id' => qty
     *
     * @param Category|null $parentCategory
     * @param array $facetValues
     * @return array
     */
    public function fetchSubLevelCategories(?Category $parentCategory, array $facetValues): array
    {
        $categories = array_map(function (CategoryDTO $category) {
            return $category->getId();
        }, $this->getCachedClassifiedCategories());

        if ($parentCategory) {
            $categories = [$parentCategory->getId()];

            if ($parentCategory->hasChildren()) {
                $categories = array_map(function(Resource $category) {
                    return $category->getId();
                }, $parentCategory->getChildren()->toArray());
            }
        }

        $facetValues = array_combine(
            array_map(function(SearchFacetValue $facetValue) {return $facetValue->getValue()??'';}, $facetValues),
            array_map(function(SearchFacetValue $facetValue) {return $facetValue->getTotal();}, $facetValues)
        );

        return array_filter($facetValues, function($id) use ($categories){
            return in_array($id, $categories);
        }, ARRAY_FILTER_USE_KEY);
    }

    /**
     * @return string
     */
    public function getLocale(): string
    {
        return $this->locale;
    }

    /**
     * @param string $locale
     */
    public function setLocale(string $locale): void
    {
        $this->locale = $locale;
    }

    /**
     * @param CategoryDTO $root
     * @param $categories array list of categories
     */
    private function explode(&$root, $categories)
    {
        /**
         * @var Category $izbergCategory
         */
        $izbergCategory = $this->findCategoryById($root->getId(), $categories);


        foreach ($izbergCategory->getChildren() as $childResource) {
            /**
             * @var Resource $childResource
             */
            $child = $this->findCategoryById($childResource->getId(), $categories);
            if (null !== $child) {
                /**
                 * @var CategoryDTO
                 */
                $childDTO = $this->mapToDTO($child);
                $root->addChildren($childDTO);
                //also explode the child
                $this->explode($childDTO, $categories);
            }
        }

        //sorting children
        if ($root->getChildren() !== null){
            $children = $root->getChildren();
            usort($children, array($this, "compareCat"));
            $root->setChildren($children);
        }

    }

    private function compareCat(CategoryDTO $a, CategoryDTO $b){
        return strcmp($a->getName(), $b->getName());
    }

    private function mapToDTO(Category $category)
    {
        $dto = new CategoryDTO();
        $dto->setId($category->getId());
        $dto->setImage($category->getImage());
        $dto->setOrder($category->getSortOrder());
        $dto->setName($category->getName());
        $dto->setExternalId($category->getExternalId());

        return $dto;
    }

    private function getRootCategories($categories)
    {
        $roots = [];
        /**
         * @var Category
         */
        foreach ($categories as $category) {
            if ($category->getParents()->isEmpty()) {
                $roots[] = $this->mapToDTO($category);
            }
        }

        return $roots;
    }

    /*
     * @param $id
     * @param $categories
     *
     * @return null|\Open\IzbergBundle\Model\Category
     */
    private function findCategoryById($id, $categories)
    {
        /**
         * @var Category
         */
        foreach ($categories as $category) {
            if ($category->getId() === $id) {
                return $category;
            }
        }

        return null;
    }
}
