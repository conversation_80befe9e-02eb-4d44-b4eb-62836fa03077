<?php
namespace Open\IzbergBundle\Service;

use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Cache\CacheItemPoolInterface;
use Psr\Cache\InvalidArgumentException;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Redis;

class RedisService implements LoggerAwareInterface
{
    private CacheItemPoolInterface $cache;
    private LoggerInterface $logger;
    private Redis $redis;

    /**
     * RedisService constructor.
     * @param CacheItemPoolInterface $cache
     * @param string $host
     * @param int $port
     */
    public function __construct(string $host, int $port, CacheItemPoolInterface $cache)
    {
        $this->cache = $cache;
        $this->redis = new Redis();
        $this->redis->connect($host, $port);

    }

    /**
     * get a item from the cache
     * @param string $key
     * @return mixed|null
     */
    public function getItem(string $key) {
        /** @psalm-suppress InvalidCatch */
        try {
            $item = $this->cache->getItem($key);
            if ($item->isHit()) {
                return $item->get();
            }
        } catch (InvalidArgumentException $invalidArgumentException) {
            $this->logger->error(
                'Failed getting item from Redis cache',
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::TECHNICAL_ERROR
                ])
            );
        }

        return null;
    }

    /**
     * save an item in the cache
     * @param string $key the key of the item
     * @param  mixed|array $value the value of the item
     * @param int $expiration validity time (in minute)
     */
    public function saveItem(string $key, $value, $expiration = null):bool
    {
        /** @psalm-suppress InvalidCatch */
        try {
            $item = $this->cache->getItem($key);

            if ($expiration) {
                $expired = new \DateTime();
                $expired->add(new \DateInterval('PT'.strval($expiration).'M'));
                $item->expiresAt($expired);
            }

            $item->set($value);
            $this->cache->save($item);
            return $this->cache->commit();
        } catch(InvalidArgumentException $exception) {
            $this->logger->error(
                'Failed saving item in Redis cache',
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::TECHNICAL_ERROR
                ])
            );
        }
        return false;
    }

    /**
     * remove an item from the cache
     * @param string $key
     */
    public function removeItem(string $key){
        /** @psalm-suppress InvalidCatch */
        try {
            if ($this->cache->hasItem($key)){
                $this->cache->deleteItem($key);
                $this->cache->commit();
            }
        } catch(InvalidArgumentException $exception) {
            $this->logger->error(
                'Failed removing item in Redis cache',
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::TECHNICAL_ERROR
                ])
            );
        }
    }

    /**
     * @return array
     */
    public function getAllItems(): array
    {
        $allRedisKeys = $this->redis->keys('*');
        natcasesort($allRedisKeys);
        return $allRedisKeys;
    }

    /**
     * @param string $key
     * @return bool
     */
    public function deleteItem(string $key): bool
    {
        if($this->redis->keys($key)) {
            $this->redis->del($key);
            return true;
        }
        return false;
    }


    public function deleteAllItems()
    {
        $this->redis->flushAll();
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
