<?php
namespace Open\LogBundle\EventSubscriber;

use FOS\UserBundle\Event\FilterUserResponseEvent;
use FOS\UserBundle\Event\FormEvent;
use FOS\UserBundle\Event\UserEvent;
use FOS\UserBundle\FOSUserEvents;


use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Security\Http\Event\LoginFailureEvent;
use Symfony\Component\Security\Http\Event\InteractiveLoginEvent;
use Symfony\Component\Security\Http\SecurityEvents;

class LogAccountEventSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    const ON_FILTER_USER_RESPONSE_EVENT = 'onFilterUserResponseEvent';

    private LoggerInterface $logger;

    public static function getSubscribedEvents()
    {
        // Catch login actions to trigger logs actions
        return array(
            FOSUserEvents::SECURITY_IMPLICIT_LOGIN => 'onUserEvent',
            SecurityEvents::INTERACTIVE_LOGIN => 'onInteractiveLogin',
            \Symfony\Component\Security\Http\Event\LoginFailureEvent::class => 'onAuthenticationFailure',
            FOSUserEvents::REGISTRATION_CONFIRMED => self::ON_FILTER_USER_RESPONSE_EVENT,
            FOSUserEvents::REGISTRATION_SUCCESS => 'onUserFormEvent',
            FOSUserEvents::RESETTING_RESET_COMPLETED => self::ON_FILTER_USER_RESPONSE_EVENT,
            FOSUserEvents::PROFILE_EDIT_COMPLETED => self::ON_FILTER_USER_RESPONSE_EVENT
        );
    }

    /**
     * On Interactive event management
     * @param InteractiveLoginEvent $event
     * @param $eventName string
     */
    public function onInteractiveLogin(InteractiveLoginEvent $event, $eventName)
    {
        // Retrieve current user
        $user_name = $event->getAuthenticationToken()->getUser()->getUsername();
        $this->logger->info(
            "InteractiveLogin : ",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME => $eventName,
                LogUtil::USER_NAME => $user_name,
            ])
        );

    }

    /**
     * manage all user events
     * @param UserEvent $event the user event
     * @param $eventName string
     */

    public function onUserEvent(UserEvent $event, $eventName)
    {
        $this->logger->info(
            "UserEvent : ",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME => $eventName,
                LogUtil::USER_NAME => $event->getUser()->getUsername(),
            ])
        );
    }

    /**
     * manage all form events
     * @param FormEvent $event the user event
     * @param $eventName string
     */

    public function onUserFormEvent(FormEvent $event, $eventName)
    {
        $this->logger->info(
            "UserFormEvent : ",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME => $eventName,
                LogUtil::USER_NAME => $event->getForm()->getData()->getUsername(),
            ])
        );
    }


    /**
     * Authentication Failure Event management
     * @param LoginFailureEvent $event
     */
    public function onAuthenticationFailure(LoginFailureEvent $event)
    {
        $this->logger->info(
            "AuthenticationFailure : ",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME => LoginFailureEvent::class,
                LogUtil::USER_NAME => $event->getRequest()->request->get('_username', 'unknown'),
            ])
        );
    }

    /**
     * Manage all filter user response event
     * @param FilterUserResponseEvent $event
     * @param $eventName string
     */
    public function onFilterUserResponseEvent(FilterUserResponseEvent $event, $eventName)
    {
        $this->logger->info(
            "FilterUserResponseEvent : ",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME => $eventName,
                LogUtil::USER_NAME => $event->getUser()->getUsername(),
            ])
        );
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
