<?php

namespace Open\LogBundle\EventSubscriber;

use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\HttpKernel\KernelEvents;


class LogExceptionSubscriber implements EventSubscriberInterface, LoggerAwareInterface
{
    private LoggerInterface $logger;

    public static function getSubscribedEvents()
    {
        // Catch kernel errors to trigger logs actions
        return array(
            KernelEvents::EXCEPTION => 'onKernelException'
        );
    }

    public function onKernelException(ExceptionEvent $event)
    {

        if ($event->getThrowable() != null) {
            $event->getThrowable()->getCode();

            $this->logger->critical(
                $event->getThrowable()->getMessage(),
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::TECHNICAL_ERROR,
                    'error_file' => $event->getThrowable()->getFile(),
                    'error_name' => get_class($event->getThrowable()),
                    'error_code' => $event->getThrowable()->getCode()
                ])
            );
        } else {
            $this->logger->critical(
                "An unknown error has occurred :",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::TECHNICAL_ERROR,
                    'error_file' => 'unknown',
                    'error_name' => 'unknown'
                ])
            );
        }
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
