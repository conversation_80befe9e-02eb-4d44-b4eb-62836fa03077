<?php

namespace Open\LogBundle\Processor;

use AppBundle\Services\ProcessIdProvider;
use Monolog\LogRecord;
use Open\LogBundle\Utils\LogUtil;

class ProcessIdProcessor
{
    private ProcessIdProvider $processIdProvider;

    public function __construct(ProcessIdProvider $processIdProvider)
    {
        $this->processIdProvider = $processIdProvider;
    }

    /**
     * @param array|LogRecord $record
     *
     * @return array|LogRecord
     */
    public function __invoke($record)
    {
        $processId = $this->processIdProvider->getProcessId();

        if ($processId === null) {
            return $record;
        }

        if ($record instanceof LogRecord) {
            $record->extra[LogUtil::COMPANY_EMAIL] = $processId->getCompanyEmail();
            $record->extra['order_id'] = $processId->getOrderId();
            $record->extra['order_number'] = $processId->getOrderNumber();
            $record->extra['merchant_order_id'] = $processId->getMerchantOrderId();
            $record->extra['transaction_id'] = $processId->getTransactionId();
            $record->extra['refund_id'] = $processId->getRefundId();
        } else {
            $record['context'][LogUtil::COMPANY_EMAIL] = $processId->getCompanyEmail();
            $record['context']['order_id'] = $processId->getOrderId();
            $record['context']['order_number'] = $processId->getOrderNumber();
            $record['context']['merchant_order_id'] = $processId->getMerchantOrderId();
            $record['context']['transaction_id'] = $processId->getTransactionId();
            $record['context']['refund_id'] = $processId->getRefundId();
        }

        return $record;
    }
}
