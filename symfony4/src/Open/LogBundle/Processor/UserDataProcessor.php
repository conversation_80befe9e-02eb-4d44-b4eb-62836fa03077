<?php
namespace Open\LogBundle\Processor;

use AppBundle\Entity\Company;
use AppBundle\Entity\Contact;
use AppBundle\Entity\User;
use AppBundle\Services\SecurityService;
use Monolog\LogRecord;
use Open\LogBundle\Utils\LogUtil;

/**
 * Class UserDataProcessor
 */
class UserDataProcessor
{
    private SecurityService $securityService;

    /**
     * UserDataProcessor constructor.
     *
     * @param SecurityService $securityService
     */
    public function __construct(SecurityService $securityService)
    {
        $this->securityService = $securityService;
    }

    /**
     * @param array|LogRecord $record
     *
     * @return array|LogRecord
     */
    // this method is called for each log record; optimize it to not hurt performance
    public function __invoke($record)
    {
        /**
         * @var User $user
         */
        $user = $this->securityService->getUser();
        if (!$user instanceof User) {
            return $record;
        }
        $email = $user->getEmail();

        if ($record instanceof LogRecord) {
            $record->extra[LogUtil::USER_EMAIL] = sha1($email);
        } else {
            $record['context'][LogUtil::USER_EMAIL] = sha1($email);
        }

        $company = $user->getCompany();
        if (!$company instanceof Company) {
            return $record;
        }

        $mainContact = $company->getMainContact();
        if (!$mainContact instanceof Contact) {
            return $record;
        }
        $companyEmail = (string) $mainContact->getEmail();
        if ($record instanceof LogRecord) {
            $record->extra[LogUtil::COMPANY_EMAIL] = sha1($companyEmail);
        } else {
            $record['context'][LogUtil::COMPANY_EMAIL] = sha1($companyEmail);
        }

        return $record;
    }
}
