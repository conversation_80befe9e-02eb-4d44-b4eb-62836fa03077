<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 07/02/2018
 * Time: 10:55
 */

namespace Open\TicketBundle\Entity;

use AppBundle\Entity\Company;
use AppBundle\Entity\ExportableEntityTrait;
use AppBundle\Entity\TechnicalIdentifierTrait;
use AppBundle\Entity\TimestampedTrait;
use Doctrine\ORM\Mapping as ORM;
use Open\TicketBundle\Entity\Traits\IdentifiedEntityTrait;
use Open\TicketBundle\Model\TicketConstant;
use Symfony\Component\Validator\Constraints as Assert;


#[ORM\Table(name: 'ticket')]
#[ORM\Entity]
#[ORM\HasLifecycleCallbacks]
class Ticket implements \JsonSerializable
{

    use TimestampedTrait;
    use IdentifiedEntityTrait;
    use ExportableEntityTrait;
    use TechnicalIdentifierTrait;


    public function __construct(bool $createByCustomer = false)
    {
        $this->messages = [];

        if($createByCustomer){
            $this->operatorRead = true;
            $this->customerRead = false;
            $this->operatorNew = true;
            $this->customerNew = false;
        }else{
            $this->operatorRead = false;
            $this->customerRead = true;
            $this->operatorNew = false;
            $this->customerNew = true;
        }

        $this->customerReplied = false;
        $this->operatorReplied = false;
    }

    /**
     * @var string $ticketNumber
     */
    #[ORM\Column(name: 'ticket_number', type: 'string', length: 100, nullable: false)]
    private $ticketNumber;

    /**
     * @var bool $isCreatedByAdmin is this ticket has been created by a administrator
     */
    #[ORM\Column(name: 'is_created_by_admin', type: 'boolean', length: 100, nullable: true)]
    private $isCreatedByAdmin;

    /**
     * @var string $subject subject of the ticket
     */
    #[Assert\NotBlank]
    #[Assert\Length(min: 5)]
    #[ORM\Column(name: 'suject', type: 'string', length: 200, nullable: true)]
    private $subject;

    /**
     * @var int status status of the message
     * @see TicketConstant
     */
    #[ORM\Column(name: 'status', type: 'smallint', nullable: true)]
    private $status;

    /**
     * @var Company $recipient the recipient for this message. Company or nul if author is administrator
     */
    #[ORM\JoinColumn(name: 'company_recipient_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\Company::class, inversedBy: 'ticketsAsRecipient')]
    private $recipient;


    /**
     * @var Company $author the author of the message. Null for a anonymous message
     */
    #[ORM\JoinColumn(name: 'company_author_id', referencedColumnName: 'id', nullable: true)]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\Company::class, inversedBy: 'ticketsAsAuthor')]
    private $author;


    #[ORM\JoinColumn(name: 'company_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \AppBundle\Entity\Company::class, inversedBy: 'tickets')]
    private $company;

    /**
     * @var array $messages list of messages for this ticket
     */
    #[Assert\Valid]
    #[ORM\OneToMany(targetEntity: \Open\TicketBundle\Entity\TicketMessage::class, mappedBy: 'ticket', cascade: ['persist'])]
    private $messages;


    /**
     * @var string $anonymousEmail for an anonymous ticket: the email of the creator
     */
    #[Assert\NotBlank(groups: ['TicketAnonymous'])]
    #[Assert\Length(min: 2, max: 50, groups: ['TicketAnonymous'])]
    #[Assert\Email(groups: ['TicketAnonymous'])]
    #[ORM\Column(name: 'anonymous_email', type: 'string', length: 50, nullable: true)]
    private $anonymousEmail;

    /**
     * @var string $anonymousFirstname for an anonymous ticket: the first name of the creator
     */
    #[Assert\NotBlank(groups: ['TicketAnonymous'])]
    #[Assert\Length(min: 2, max: 50, groups: ['TicketAnonymous'])]
    #[ORM\Column(name: 'anonymous_first_name', type: 'string', length: 50, nullable: true)]
    private $anonymousFirstName;

    /**
     * @var string $anonymousCompany for an anonymous ticket: the company of the creator
     */
    #[Assert\NotBlank(groups: ['TicketAnonymous'])]
    #[ORM\Column(name: 'anonymous_company', type: 'string', length: 50, nullable: true)]
    private $anonymousCompany;

    /**
     * @var string $anonymousFunction for an anonymous ticket: the function of the creator
     */
    #[ORM\Column(name: 'anonymous_function', type: 'string', length: 50, nullable: true)]
    private $anonymousFunction;

    /**
     * @var string $anonymousLastname for an anonymous ticket: the last name of the creator
     */
    #[Assert\NotBlank(groups: ['TicketAnonymous'])]
    #[Assert\Length(min: 2, max: 50, groups: ['TicketAnonymous'])]
    #[ORM\Column(name: 'anonymous_last_name', type: 'string', length: 50, nullable: true)]
    private $anonymousLastName;

    /**
     * @var string $anonymousPhone for an anonymous ticket: the phone of the creator
     */
    #[Assert\Regex(pattern: '/^[\+]?[0-9]+$/', match: true, message: 'form.contact.phone_regex', groups: ['TicketAnonymous'])]
    #[ORM\Column(name: 'anonymous_phone', type: 'string', length: 50, nullable: true)]
    private $anonymousPhone;

    /**
     * @var string $anonymousLocale if the ticket is anonymous, contains the locale of this user
     */
    #[ORM\Column(name: 'anonymous_locale', type: 'string', length: 5, nullable: true)]
    private $anonymousLocale;

    /**
     * @var TicketMessage $lastMessage the $lastMessage of the thread
     */
    #[ORM\JoinColumn(name: 'last_message_id', referencedColumnName: 'id')]
    #[ORM\OneToOne(targetEntity: \Open\TicketBundle\Entity\TicketMessage::class)]
    private $lastMessage;




    /**
     * @var bool $operatorRead true if there something new to read
     */
    #[ORM\Column(name: 'operator_read', type: 'boolean', nullable: false)]
    private $operatorRead;

    /**
     * @var bool $customerRead true if there something new to read
     */
    #[ORM\Column(name: 'customer_read', type: 'boolean', nullable: false)]
    private $customerRead;

    /**
     * @var bool $customerReplied true if customer already replied once
     */
    #[ORM\Column(name: 'customer_replied', type: 'boolean', nullable: false)]
    private $customerReplied;

    /**
     * @var bool $operatorReplied true if operator already replied once
     */
    #[ORM\Column(name: 'operator_replied', type: 'boolean', nullable: false)]
    private  $operatorReplied;

    /**
     * @var bool $operatorNew true if the ticket is new for operator
     */
    #[ORM\Column(name: 'operator_new', type: 'boolean', nullable: false)]
    private $operatorNew;

    /**
     * @var bool $customerNew true if the ticket is new for customer
     */
    #[ORM\Column(name: 'customer_new', type: 'boolean', nullable: false)]
    private $customerNew;

    /**
     * @var string $fromEmail the email of the creator of the ticket
     */
    #[ORM\Column(name: 'from_email', type: 'string', length: 50, nullable: true)]
    private $fromEmail;

    /**
     * @var bool $from_izb used for map with izberg ticket
     */
    private $from_izb;

    /**
     * @var \DateTime $izbDate used for map with izberg ticket
     */
    private $izbDate;

    /**
     * @var bool $notReadIzberg
     */
    private $notReadIzberg;

    private $ticketCreator;

    private $vendor;

    public function __toString()
    {
        $result = "(";
        foreach ($this->jsonSerialize() as $key => $value){
            $result .= $key."=".$value.",";
        }

        return $result . ")";
    }

    public function jsonSerialize(): array {
        $result =
         [
             "anonymousPhone" =>$this->anonymousPhone,
             "anonymousLastName" => $this->anonymousLastName,
             "anonymousFirstName" => $this->anonymousFirstName,
             "anonymousEmail" => $this->anonymousEmail,
             "anonymousCompany" => $this->anonymousCompany,
             "anonymousFunction" => $this->anonymousFunction,
             "status" => $this->status,
             "ticketNumber" => $this->ticketNumber,
             "subject" => $this->subject,
        ];
        if ($this->recipient){
            $result ["recipient.id"] = $this->recipient->getTechnicalId();
            $result ["recipient.company"] = $this->recipient->getName();
        }
        if ($this->lastMessage){
            $result["lastMessage.id"] = $this->lastMessage->getId();
        }
        return $result;
    }


    /**
     * @return null|string the display string of a recipient
     */
    public function getRecipientDisplayName(){
        if ($this->recipient !== null){
            return $this->recipient->getName() ;
        }
        else{
            return null;
        }
    }

    public function addMessage (TicketMessage $message){
        $this->messages [] = $message;
    }

    public function getStatusAsString(){
        return TicketConstant::STATUSES[$this->status];
    }

    public function getTicketCreator(){
        return $this->ticketCreator;
    }

    public function setTicketCreator($user){
        $this->ticketCreator = $user;
    }

    /**
     * @return string
     */
    public function getSubject(): ?string
    {
        return $this->subject;
    }

    /**
     * @param string $subject
     */
    public function setSubject(?string $subject): void
    {
        $this->subject = $subject;
    }

    /**
     * @return int
     */
    public function getStatus(): ?int
    {
        return $this->status;
    }

    /**
     * @param int $status
     */
    public function setStatus(?int $status): void
    {
        $this->status = $status;
    }



    /**
     * @return array
     */
    public function getMessages()
    {
        return $this->messages;
    }

    /**
     * @param array $messages
     */
    public function setMessages($messages): void
    {
        $this->messages = $messages;
    }

    /**
     * @return string
     */
    public function getAnonymousEmail(): ?string
    {
        return $this->anonymousEmail;
    }

    /**
     * @param string $anonymousEmail
     */
    public function setAnonymousEmail(?string $anonymousEmail): void
    {
        $this->anonymousEmail = $anonymousEmail;
    }

    /**
     * @return string
     */
    public function getAnonymousFirstName(): ?string
    {
        return $this->anonymousFirstName;
    }

    /**
     * @param string $anonymousFirstName
     */
    public function setAnonymousFirstName(?string $anonymousFirstName): void
    {
        $this->anonymousFirstName = $anonymousFirstName;
    }

    /**
     * @return string
     */
    public function getAnonymousLastName(): ?string
    {
        return $this->anonymousLastName;
    }

    /**
     * @param string $anonymousLastName
     */
    public function setAnonymousLastName(?string $anonymousLastName): void
    {
        $this->anonymousLastName = $anonymousLastName;
    }

    /**
     * @return Company
     */
    public function getRecipient(): ?Company
    {
        return $this->recipient;
    }

    /**
     * @param Company $recipient
     */
    public function setRecipient(?Company $recipient): void
    {
        $this->recipient = $recipient;
    }

    /**
     * @return string
     */
    public function getTicketNumber(): ?string
    {
        return strtoupper($this->ticketNumber);
    }

    /**
     * @param string $ticketNumber
     */
    public function setTicketNumber(?string $ticketNumber): void
    {
        $this->ticketNumber = $ticketNumber;
    }

    /**
     * @return bool
     */
    public function isCreatedByAdmin(): ?bool
    {
        return $this->isCreatedByAdmin;
    }

    /**
     * @param bool $isCreatedByAdmin
     */
    public function setIsCreatedByAdmin(?bool $isCreatedByAdmin): void
    {
        $this->isCreatedByAdmin = $isCreatedByAdmin;
    }

    /**
     * @return TicketMessage
     */
    public function getLastMessage(): ?TicketMessage
    {
        return $this->lastMessage;
    }

    /**
     * @param TicketMessage $lastMessage
     */
    public function setLastMessage(?TicketMessage $lastMessage): void
    {
        $this->lastMessage = $lastMessage;
    }

    /**
     * @return string
     */
    public function getAnonymousPhone(): ?string
    {
        return $this->anonymousPhone;
    }

    /**
     * @param string $anonymousPhone
     */
    public function setAnonymousPhone(?string $anonymousPhone): void
    {
        $this->anonymousPhone = $anonymousPhone;
    }

    /**
     * @return string
     */
    public function getAnonymousLocale(): ?string
    {
        return $this->anonymousLocale;
    }

    /**
     * @param string $anonymousLocale
     */
    public function setAnonymousLocale(string $anonymousLocale): void
    {
        $this->anonymousLocale = $anonymousLocale;
    }

    /**
     * @return string
     */
    public function getAnonymousCompany(): ?string
    {
        return $this->anonymousCompany;
    }

    /**
     * @param string $anonymousCompany
     */
    public function setAnonymousCompany(?string $anonymousCompany): void
    {
        $this->anonymousCompany = $anonymousCompany;
    }

    /**
     * @return string
     */
    public function getAnonymousFunction(): ?string
    {
        return $this->anonymousFunction;
    }

    /**
     * @param string $anonymousFunction
     */
    public function setAnonymousFunction(?string $anonymousFunction): void
    {
        $this->anonymousFunction = $anonymousFunction;
    }

    /**
     * @return Company
     */
    public function getAuthor(): ?Company
    {
        return $this->author;
    }

    /**
     * @param Company $atuthor
     */
    public function setAuthor(?Company $author): void
    {
        $this->author = $author;
    }

    /**
     * @return mixed
     */
    public function getCompany()
    {
        return $this->company;
    }

    /**
     * @param mixed $company
     */
    public function setCompany($company): void
    {
        $this->company = $company;
    }

    /**
     * @return bool
     */
    public function isOperatorRead(): bool
    {
        return $this->operatorRead;
    }

    /**
     * @param bool $operatorRead
     */
    public function setOperatorRead(bool $operatorRead): void
    {
        $this->operatorRead = $operatorRead;
    }

    /**
     * @return bool
     */
    public function isCustomerRead(): bool
    {
        return $this->customerRead;
    }

    /**
     * @param bool $customerRead
     */
    public function setCustomerRead(bool $customerRead): void
    {
        $this->customerRead = $customerRead;
    }

    /**
     * @return bool
     */
    public function isCustomerReplied(): bool
    {
        return $this->customerReplied;
    }

    /**
     * @param bool $customerReplied
     */
    public function setCustomerReplied(bool $customerReplied): void
    {
        $this->customerReplied = $customerReplied;
    }

    /**
     * @return bool
     */
    public function isOperatorReplied(): bool
    {
        return $this->operatorReplied;
    }

    /**
     * @param bool $operatorReplied
     */
    public function setOperatorReplied(bool $operatorReplied): void
    {
        $this->operatorReplied = $operatorReplied;
    }

    /**
     * @return bool
     */
    public function isOperatorNew(): bool
    {
        return $this->operatorNew;
    }

    /**
     * @param bool $operatorNew
     */
    public function setOperatorNew(bool $operatorNew): void
    {
        $this->operatorNew = $operatorNew;
    }

    /**
     * @return bool
     */
    public function isCustomerNew(): bool
    {
        return $this->customerNew;
    }

    /**
     * @param bool $customerNew
     */
    public function setCustomerNew(bool $customerNew): void
    {
        $this->customerNew = $customerNew;
    }

    /**
     * @return bool
     */
    public function isFromIzb(): ?bool
    {
        return $this->from_izb;
    }

    /**
     * @param bool $from_izb
     */
    public function setFromIzb(bool $from_izb)
    {
        $this->from_izb = $from_izb;
    }

    /**
     * @return mixed
     */
    public function getIzbDate()
    {
        return $this->izbDate;
    }

    /**
     * @param $izbDate
     */
    public function setIzbDate($izbDate)
    {
        $this->izbDate = $izbDate;
    }

    /**
     * @return bool
     */
    public function isNotReadIzberg(): ?bool
    {
        return $this->notReadIzberg;
    }

    /**
     * @param bool $notReadIzberg
     */
    public function setNotReadIzberg(bool $notReadIzberg)
    {
        $this->notReadIzberg = $notReadIzberg;
    }

    /**
     * @return mixed
     */
    public function getVendor()
    {
        return $this->vendor;
    }

    /**
     * @param mixed $vendor
     */
    public function setVendor($vendor)
    {
        $this->vendor = $vendor;
    }

    public function getFromEmail(): string
    {
        return $this->fromEmail;
    }

    public function setFromEmail(string $fromEmail): void
    {
        $this->fromEmail = $fromEmail;
    }

}
