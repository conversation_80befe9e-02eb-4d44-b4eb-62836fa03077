<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 07/02/2018
 * Time: 10:55
 */

namespace Open\TicketBundle\Entity;
use AppBundle\Entity\TechnicalIdentifierTrait;
use AppBundle\Entity\TimestampedTrait;
use AppBundle\Entity\User;
use Doctrine\ORM\Mapping as ORM;
use Faker\Provider\DateTime;
use Open\TicketBundle\Entity\Traits\IdentifiedEntityTrait;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\Validator\Constraints as Assert;


#[ORM\Table(name: 'ticket_message')]
#[ORM\Entity]
#[ORM\HasLifecycleCallbacks]
class TicketMessage implements \JsonSerializable
{
    use TimestampedTrait;
    use IdentifiedEntityTrait;
    use TechnicalIdentifierTrait;



    public function __construct()
    {
        $this->userCreated = [];
    }

    /**
     * @var array $userCreated the user that has created this ticket
     */
    #[ORM\JoinTable(name: 'ticket_user')]
    #[ORM\JoinColumn(name: 'ticket_id', referencedColumnName: 'id', unique: true)]
    #[ORM\InverseJoinColumn(name: 'user_id', referencedColumnName: 'id')]
    #[ORM\ManyToMany(targetEntity: \AppBundle\Entity\User::class)]
    private $userCreated;


    /**
     * @var string $content the content of the message
     */
    #[Assert\NotBlank(groups: ['Default'])]
    #[ORM\Column(name: 'content', type: 'text', nullable: true)]
    private $content;


    #[ORM\OneToMany(targetEntity: \TicketMessageAttachment::class, mappedBy: 'message', cascade: ['persist'])]
    private $attachments;



    /**
     * @var Ticket $ticket the ticket for this message
     */
    #[ORM\JoinColumn(name: 'ticket_id', referencedColumnName: 'id')]
    #[ORM\ManyToOne(targetEntity: \Open\TicketBundle\Entity\Ticket::class, inversedBy: 'messages')]
    private $ticket;

    /**
     * @var bool $resolved an helper to indicate that the ticket must be considered has resolved
     */
    private $resolved;


    /**
     * @var string $izbName used for map with izberg ticket
     */
    private $izbName;

    /**
     * @var \DateTime $izbDate used for map with izberg ticket
     */
    private $izbDate;

    public function __toString()
    {
        $result = "(";
        foreach ($this->jsonSerialize() as $key => $value){
            $result .= $key."=".$value.",";
        }

        return $result . ")";
    }

    public function jsonSerialize(): array {
        $result =
            [
                "content" =>$this->content,
                "createdAt" => $this->createdAt,
                "nbAttachments" => count($this->attachments)
            ];
        if ($this->ticket){
            $result ["ticketId"] = $this->ticket->getTechnicalId();
        }
        return $result;
    }


    public function getDisplayName(){
        if ($this->getUserCreated()){
			return $this->getUserCreated()->getFirstname() . ' ' . $this->getUserCreated()->getLastname();
        } else {
            return $this->getTicket()->getAnonymousFirstName().' '.$this->getTicket()->getAnonymousLastName();
        }
    }

    public function getFullDisplayName(){
        if ($this->getUserCreated()){
            return $this->getUserCreated()->getFirstname() . ' ' . $this->getUserCreated()->getLastname() . ' (' . $this->getUserCreated()->getEmail() .')';
        } else {
            return $this->getTicket()->getAnonymousFirstName().' '.$this->getTicket()->getAnonymousLastName() . ' (' . $this->getTicket()->getAnonymousEmail() .')';
        }
    }

	/**
	 * Check if the response is from an admin account
	 * @return bool
	 */
    public function isAdminResponse() {

    	if ($this->getUserCreated()) {
			return $this->getUserCreated()->hasRole('ROLE_OPERATOR') || $this->getUserCreated()->hasRole('ROLE_SUPER_ADMIN');
		} else {
    		return false;
		}
	}

    /**
     * @return User the user that has created this message
     */
    public function getUserCreated(): ?User
    {
        if (!empty($this->userCreated)){
            return $this->userCreated[0];
        }
        return null;

    }

    /**
     * set the user that has created this message
     * @param User $userCreated
     */
    public function setUserCreated(?User $userCreated): void
    {
        if (empty($this->userCreated)){
            $this->userCreated [] = $userCreated;
        }
        else{
            $this->userCreated [0] = $userCreated;
        }
    }

    /**
     * get the content of this message
     * @return string the content of the message or null if no content was set
     */
    public function getContent(): ?string
    {
        return $this->content;
    }

    /**
     * set the content for this message
     * @param string $content the content of the message
     */
    public function setContent(?string $content): void
    {
        $this->content = $content;
    }

    /**
     * get the ticket of this message
     * @return Ticket the message of this message
     */
    public function getTicket(): ?Ticket
    {
        return $this->ticket;
    }

    /**
     * set the ticket of this message
     * @param Ticket $ticket the ticket of this message
     */
    public function setTicket(?Ticket $ticket): void
    {
        $this->ticket = $ticket;
    }

    /**
     * @return bool
     */
    public function isResolved(): ?bool
    {
        return $this->resolved;
    }

    /**
     * @param bool $resolved
     */
    public function setResolved(?bool $resolved): void
    {
        $this->resolved = $resolved;
    }


    /**
     * @return mixed
     */
    public function getAttachments()
    {
        return $this->attachments;
    }

    /**
     * @param mixed $attachments
     */
    public function setAttachments($attachments): void
    {
        $this->attachments = $attachments;
    }

    /**
     * @return mixed
     */
    public function getIzbName()
    {
        return $this->izbName;
    }

    /**
     * @param mixed $izbName
     */
    public function setIzbName($izbName)
    {
        $this->izbName = $izbName;
    }

    /**
     * @return mixed
     */
    public function getIzbDate()
    {
        return $this->izbDate;
    }

    /**
     * @param mixed $izbDate
     */
    public function setIzbDate($izbDate)
    {
        $this->izbDate = $izbDate;
    }

}
