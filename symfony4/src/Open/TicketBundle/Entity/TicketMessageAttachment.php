<?php

namespace Open\TicketBundle\Entity;

use AppBundle\Entity\TimestampedTrait;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\ManyToOne;
use JsonSerializable;


/**
 * Document
 */
#[ORM\Table(name: 'ticket_message_attachment')]
#[ORM\Entity]
#[ORM\HasLifecycleCallbacks]
class TicketMessageAttachment implements JsonSerializable
{
    const MIME_CONSTRAINT = ["application/pdf", "image/jpeg", "image/gif", "image/png", "image/tiff", "application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"];
    const SIZE_CONSTRAINT = 10 * 1024 * 1024;

    use TimestampedTrait;

    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;


    /**
     * Many Features have One Product.
     */
    #[ManyToOne(targetEntity: \TicketMessage::class, inversedBy: 'attachments')]
    private $message;

    /**
     * @var string
     */
    #[ORM\Column(name: 'filename', type: 'string', length: 260)]
    private $filename;

    /**
     * @var string
     */
    #[ORM\Column(name: 'mimetype', type: 'string', length: 75)]
    private $mimeType;


    /**
     * @var string
     */
    #[ORM\Column(name: 'size', type: 'integer')]
    private $size;

    #[ORM\Column(name: 'binary_file', type: 'blob')]
    protected $binary;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     * @return TicketMessageAttachment
     */
    public function setId($id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getMessage()
    {
        return $this->message;
    }

    /**
     * @param mixed $message
     * @return TicketMessageAttachment
     */
    public function setMessage($message): self
    {
        $this->message = $message;
        return $this;
    }


    /**
     * @return string
     */
    public function getFilename()
    {
        return $this->filename;
    }

    /**
     * @param string $filename
     * @return TicketMessageAttachment
     */
    public function setFilename($filename): self
    {
        $this->filename = $filename;
        return $this;
    }

    /**
     * @return string
     */
    public function getMimeType()
    {
        return $this->mimeType;
    }

    /**
     * @param string $type
     * @return TicketMessageAttachment
     */
    public function setMimeType($type): self
    {
        $this->mimeType = $type;
        return $this;
    }

    /**
     * @return int
     */
    public function getSize(): int
    {
        return intval($this->size);
    }

    /**
     * @param int $size
     * @return TicketMessageAttachment
     */
    public function setSize(int $size): self
    {
        $this->size = strval($size);
        return $this;
    }


    /**
     * @return mixed
     */
    public function getBinary()
    {
        return $this->binary;
    }

    /**
     * @param mixed $binary
     * @return TicketMessageAttachment
     */
    public function setBinary($binary): self
    {
        $this->binary = $binary;
        return $this;
    }

    public function __toString()
    {
        return $this->filename;
    }

    public function jsonSerialize(): array
    {
        return [
            'filename' => $this->filename,
            'type' => $this->mimeType,
        ];
    }
}
