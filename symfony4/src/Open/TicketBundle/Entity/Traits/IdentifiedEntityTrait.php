<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 07/02/2018
 * Time: 11:00
 */

namespace Open\TicketBundle\Entity\Traits;

use Doctrine\ORM\Mapping as ORM;

trait IdentifiedEntityTrait
{
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: "AUTO")]
    #[ORM\Column(name: "id", type: "integer")]
    private $id;

    /**
     * @return int
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId(int $id): void
    {
        $this->id = $id;
    }



}
