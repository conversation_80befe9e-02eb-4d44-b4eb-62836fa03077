<?php

namespace Open\TicketBundle\FilterQueryBuilder;

use App<PERSON><PERSON>le\FilterQueryBuilder\EntityQueryBuilder;
use AppBundle\FilterQueryBuilder\FilterQueryBuilderInterface;
use Open\TicketBundle\Model\TicketConstant;
use Doctrine\ORM\QueryBuilder;

class TicketQueryBuilder extends EntityQueryBuilder implements FilterQueryBuilderInterface
{

  /***
   * @param \Doctrine\ORM\QueryBuilder $qb
   * @param $data
   */
    public function build(QueryBuilder $qb, $data)
    {
        $qb->leftJoin("e.lastMessage", "l");
        $qb->leftJoin("e.company", "c");
        $qb->leftJoin('e.author', 'u');

        // CompanyId
        $this->addWhereClause($qb, 'id', 'companyId', $data, '=', 'c', true);

        // Others
        $this->addWhereClause($qb, 'id', 'id', $data, '=', 'e', true);
        $this->addWhereClause($qb, 'email','authorEmail', $data, self::LIKE_OPERATOR, 'u',false);
        $this->addWhereClause($qb, 'name','companyName', $data, self::LIKE_OPERATOR, 'u',false);
        $this->addWhereClause($qb, 'subject','subject', $data, self::LIKE_OPERATOR, 'e',false);

        // Created At: From <DATE> 00:00:00 to <DATE> 23:59:59
        if ($this->isValidDate($data, 'createdAt'))
        {
            $dateFrom = clone $data['createdAt'];
            $dateFrom->setTime(0, 0, 0);
            $dateTo = clone $dateFrom;
            $dateTo->setTime(23, 59, 59);

            $and = $qb->expr()->andX();
            $and->add($qb->expr()->gte('e.createdAt', ':dateCreatedFrom'));
            $qb->setParameter('dateCreatedFrom', $dateFrom);
            $qb->andWhere($and);

            $and = $qb->expr()->andX();
            $and->add($qb->expr()->lte('e.createdAt', ':dateCreatedTo'));
            $qb->setParameter('dateCreatedTo', $dateTo);
            $qb->andWhere($and);
        }

        // Last message At: From <DATE> 00:00:00 to <DATE> 23:59:59
        if ($this->isValidDate($data, 'lastMessageAt'))
        {
            $dateFrom = clone $data['lastMessageAt'];
            $dateFrom->setTime(0, 0, 0);
            $dateTo = clone $dateFrom;
            $dateTo->setTime(23, 59, 59);

            $and = $qb->expr()->andX();
            $and->add($qb->expr()->gte('l.createdAt', ':dateLastFrom'));
            $qb->setParameter('dateLastFrom', $dateFrom);
            $qb->andWhere($and);

            $and = $qb->expr()->andX();
            $and->add($qb->expr()->lte('l.createdAt', ':dateLastTo'));
            $qb->setParameter('dateLastTo', $dateTo);
            $qb->andWhere($and);
        }

        if (!empty($data['author'])) {
            $qb->andWhere('c.name LIKE :author');
            $qb->setParameter(':author', '%' . $data['author'] . '%');
            $qb->orWhere('e.anonymousCompany LIKE :anonymousCompany');
            $qb->setParameter(':anonymousCompany', '%' . $data['author'] . '%');
        }

        if (!empty($data['object'])) {
            $qb->andWhere('e.subject LIKE :object');
            $qb->setParameter(':object', '%' . $data['object'] . '%');
        }

        if (!empty($data['creationMin'])) {
            $qb->andWhere('e.createdAt >= :creationMin');
            $qb->setParameter(':creationMin', $data['creationMin']);
        }
        if (!empty($data['creationMax'])) {
            $qb->andWhere('e.createdAt <= :creationMax');
            $qb->setParameter(':creationMax', $data['creationMax']->setTime(23,59,59));
        }

        if (!empty($data['modificationMin'])) {
            $qb->andWhere('e.updatedAt >= :modificationMin');
            $qb->setParameter(':modificationMin', $data['modificationMin']);
        }
        if (!empty($data['modificationMax'])) {
            $qb->andWhere('e.updatedAt <= :modificationMax');
            $qb->setParameter(':modificationMax', $data['modificationMax']->setTime(23,59,59));
        }
    }




    private function isValidDate($data, $index)
    {
        return array_key_exists($index, $data) && ! is_null($data[$index]) && $data[$index] instanceof \DateTime;
    }
}
