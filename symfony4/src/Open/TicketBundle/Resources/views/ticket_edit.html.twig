{% extends base %}

{% if form is defined %}
    {% if front %}
        {% form_theme form 'Form/appli_layout.html.twig' %}
    {% else %}
        {% form_theme form 'bootstrap_4_layout.html.twig' %}
    {% endif %}
{% endif %}

{% trans_default_domain "AppBundle" %}

{% block title %}
    {{ 'ticket.edit.title' | trans}}&nbsp;#{{ ticket.ticketNumber }}
{% endblock %}

{% block body %}
    {% set timeFormat = 'ticket.edit.timeFormat'|trans %}

    <div class="container-inner">

        {% if isAdmin == true %}
            <div style="width:100%; padding: 0 0 20px 5px">
                <a href="{% if ticket.status != 14 %}{{ path('admin.ticket.list') }}{% else %}{{ path('admin.ticket.list', {'status': 'closed'}) }}{% endif %}" style="display:flex; align-items: center; color: #9600FF">
                    <div style="width: 10px; height: 10px; border: solid; border-color: #9600FF; border-width: 0 2px 2px 0;transform: rotate(135deg); margin-right: 5px"></div>
                    {{ 'back.back_to_list' | trans }}
                </a>
            </div>
        {% endif %}

        {% if seller is defined %}
            {% set seller_name = seller.to_display_name %}
        {% endif %}
        {% if not fromIZB %}
            {% if ticket.author is not null %}
                {% set firstname = ticket.ticketCreator.firstname %}
                {% set lastname = ticket.ticketCreator.lastname %}
                {% set email = ticket.ticketCreator.email %}
                {% set phone = '' %}
            {% else %}
                {% set firstname = ticket.anonymousFirstName %}
                {% set lastname = ticket.anonymousLastName %}
                {% set email = ticket.anonymousEmail %}
                {% set phone = ticket.anonymousPhone %}
            {% endif %}
        {% endif %}

        {% if not isAdmin and ticketType is defined and ticketType != 3 %}
            <div class="link-previous">
                <a href="{{ path('buyer.ticket.list') }}"><i class="arrow left"></i>{{ 'ticket.edit.link'|trans([], 'AppBundle')|upper }}</a>
            </div>
        {% endif %}

        <div class="Ticket-detail">
            {% if isAdmin %}
                <div>
                    <p><b>{{ 'ticket.edit.id'|trans }}&nbsp;:</b>&nbsp;{{ ticket.ticketNumber }}</p>
                    <p><b>{{ 'ticket.edit.subject'|trans }}&nbsp;:</b>&nbsp;{{ ticket.subject }}</p>
                    <p><b>{{ 'ticket.edit.author'|trans }}&nbsp;:</b>&nbsp;{% if ticket.createdByAdmin == true %} {{ 'ticket.edit.operator' | trans }} {% elseif ticket.author is not null%}{{ firstname }} {{ lastname }}{% else %}{{ firstname }} {{ lastname }}{% endif %} </p>
                    {% if ticket.author is not null %}
                        <p><b>{{ 'ticket.create.email'|trans }} : </b>{{ email }}</p>
                        <p><b>Company name: </b><a href="{{ path('admin.company.generalInfo', {'id':ticket.author.id}) }}">{{ ticket.author.name }}</a></p>
                    {% else %}
                        <p><b>Company name: </b>{{  ticket.anonymousCompany }}</p>
                    {% endif %}

                    <p>{{ phone }}</p>
                    {% if ticket.recipientDisplayName is not null %}
                        <p><b>{{ 'ticket.edit.recipient'|trans }}&nbsp;:</b><a href="{{ path('admin.company.generalInfo', {id:ticket.recipient.id}) }}">{{ ticket.recipient.name }}</a></p>
                    {% endif %}
                    {% if fromIZB %}
                        {% set ticketDate = ticket.izbDate | format_datetime('full', 'medium', locale=locale, timezone='Europe/Paris') %}
                    {% else %}
                        {% set ticketDate = ticket.createdAt | format_datetime('full', 'medium') %}
                    {% endif %}
                    <p><b>{{ 'ticket.edit.date'|trans}}</b>&nbsp;{{ ticketDate }}</p>
                    {% if ticket.status == 14 %}
                        {% set closedDate = ticket.updatedAt | format_datetime('full', 'medium') %}
                        <p><b>{{ 'ticket.edit.closed'|trans}}</b>&nbsp;{{ closedDate }}</p>
                    {% endif %}
                </div>
            {% else %}
                <div>
                    <p><b>{{ 'ticket.edit.id'|trans|upper }}&nbsp;:</b>&nbsp;{{ ticket.ticketNumber }}</p>
                    <p><b>{{ 'ticket.edit.subject'|trans|upper }} :</b> {{ ticket.subject }}</p>
                </div>
            {% endif %}
        </div>
        <div class="messages-label"><strong>{{ 'ticket.edit.message_label'|trans([], 'AppBundle')|upper }} :</strong></div>
        {% for message in ticket.messages %}
            <div class="card Ticket-response" style="margin-bottom: 15px">
                {% if isAdmin %}
                    <div class="card-header">
                        <div class="card-header-author">
                            <svg class="Icon">
                                <use xlink:href="#icon-message"></use>
                            </svg>
                            {% if message.isAdminResponse %}
                                {{ 'ticket.common.administrator_user'|trans({'%fullname%': message.displayName }) }}
                            {% else %}
                                {% if fromIZB %}
                                    {{ message.izbName }}
                                {% else %}
                                    {{ message.fullDisplayName }}
                                {% endif %}
                            {% endif %}
                        </div>
                        <div class="card-header-date">
                            <svg class="Icon">
                                <use xlink:href="#icon-calendar"></use>
                            </svg>
                            {% if fromIZB %}
                                {{ message.izbDate | format_datetime('full', 'medium', locale=locale, timezone='Europe/Paris') }}
                            {% else %}
                                {{ message.createdAt | format_datetime('full', 'medium', locale=locale) }}
                            {% endif %}
                        </div>
                    </div>
                {% endif %}
               {% if isAdmin and message.content %}
                <div class="card-body">
                    <div class="card-message">
                        {{ message.content | nl2br}}
                    </div>
                    <div class="card-attachments" style="margin:10px 20px;">
                    {% if message.attachments is not empty %}
                        {% for attachment in message.attachments %}
                            <a target="_blank" href="{{ path('attachment.view', {'token' : ticket.ticketNumber ,'attachmentId' : attachment.id}) }}">
                                <svg class="Icon" style="height: 15px;width: 15px;margin:0px 5px;display:inline;">
                                    <use xlink:href="#icon-paperclip"></use>
                                </svg>
                                {{ attachment.filename }}
                            </a>
                        {% endfor %}
                    {% endif %}
                    </div>
                </div>
               {% elseif isAdmin == false %}
                   <div class="card-body">
                       <div class="card-info">
                           <div class="info-author">
                            {% if message.isAdminResponse %}
                                {{ 'ticket.edit.operator' | trans }}
                            {% else %}
                               {% if fromIZB %}
                                   {{ message.izbName }}
                               {% else %}
                                   {{ message.displayName }}
                               {% endif %}
                            {% endif %}
                           </div>
                           <div class="info-date">
                               {% if fromIZB %}
                                   {{ message.izbDate | format_datetime('none', 'none', locale=locale, timezone='Europe/Paris', pattern='Y-MM-d') }}
                               {% else %}
                                   {{ message.createdAt | format_datetime('none', 'none', locale=locale, pattern='Y-MM-d') }}
                               {% endif %}
                           </div>
                       </div>
                       <div class="card-message">
                           {% if fromIZB %}
                               {{ message.content | raw | nl2br}}
                           {% else %}
                               {{ message.content | nl2br}}
                           {% endif %}
                       </div>
                       <div class="card-attachments">
                           {% if message.attachments is not empty %}
                               {% for attachment in message.attachments %}
                                   <a target="_blank" href="{{ path('attachment.view', {'token' : ticket.ticketNumber ,'attachmentId' : attachment.id}) }}">
                                       <svg class="Icon" style="height: 15px;width: 15px;">
                                           <use xlink:href="#icon-paperclip"></use>
                                       </svg>
                                       {{ attachment.filename }}
                                   </a>
                               {% endfor %}
                           {% endif %}
                       </div>
                   </div>
               {% endif %}
            </div>
        {% endfor %}

        {% if form is defined %}
            <div class="Ticket-form">
                {{  form_start(form, {'attr': {'id': 'js-message-form', 'enctype': 'multipart/form-data'}}) }}
                <label>{{ 'ticket.edit.new_message'|trans([], 'AppBundle')|upper }}</label>
                <div class="row-container">
                    {% if not fromIZB %}
                        <div class="ticket-textarea">
                            {{ form_widget(form.content) }}
                        </div>
                        <div class="attachments ticket--attachment">
                            <div class="flex jc-sb ai-c">
                                {{ 'ticket.common.authorized_files_extensions_message' | trans }}
                                <div class="flex ai-c" style="margin-left:auto">
                                    {{ form_widget(form.attachments, {'attr': {'class': 'btn-primary margin-1'}}) }}
                                    <button type="button" class="Button Button--upload js-file-reset-button button--reset margin-1" onclick="resetFiles(this)" style="display: none;">
                                        <svg class="Icon"><use xlink:href="#icon-trash"/></svg>
                                    </button>
                                </div>
                            </div>
                            <ul id="list-attachments" class="attachment-files">
                                <li class="file--warning">{{ 'ticket.common.nofiles'|trans }}</li>
                            </ul>
                        </div>


                    {% else %}
                        <div class="ticket-textarea">
                            {{ form_widget(form.message) }}
                        </div>
                        <div class="attachments ticket--attachment">
                            <div class="flex jc-sb ai-c">
                                {{ form_label(form.attachments, null, {'label': 'ticket.common.add_file'}) }}
                                <div class="flex ai-c">
                                    {{ form_widget(form.attachments, {'attr': {'class': 'btn-primary margin-1'}}) }}
                                    <button type="button" class="Button Button--upload js-file-reset-button button--reset margin-1" onclick="resetFiles(this)" style="display: none;">
                                        <svg class="Icon"><use xlink:href="#icon-trash"/></svg>
                                    </button>
                                </div>
                            </div>
                            <ul id="list-attachments" class="attachment-files">
                                <li class="file--warning">{{ 'ticket.common.nofiles'|trans }}</li>
                            </ul>
                        </div>
                    {% endif %}
                </div>

                <div class="form-buttons">
                    {% if not fromIZB %}
                        {{ form_row(form.resolved) }}
                    {% endif %}
                    {{  form_end(form) }}
                </div>
            </div>
        {% endif %}

        {% if isAdmin == true and ticket.status == 14  %}
            <a href="{{ path('admin.ticket.reopen', {'id': ticket.ticketNumber}) }}">
                <button type="button" class="btn btn-primary">{{ 'ticket.edit.reopen'|trans }}</button>
            </a>
        {% endif %}
    </div>
    {% if form is defined %}
    {% include '@OpenTicket/shared/_javascript.html.twig' with {form:form} %}
    {% endif %}

{% endblock %}

{% block javascripts %}
    <script type="text/javascript">


        $('#js-button-ticket_message_attachments, #js-button-ticket_izberg_attachments').on('click', function() {
            $('#ticket_message_attachments, #ticket_izberg_attachments').trigger('click');
        });

        $('#ticket_message_attachments, #ticket_izberg_attachments').on('change', function() {
            if(this.files.length === 0) {
                return null;
            } else {
                $('#list-attachments').empty();
                for (let i = 0; i < this.files.length; i++)
                {
                    let file = $('<li>'+this.files[i].name+'</li>');
                    $('#list-attachments').append(file);
                }
                $('.js-file-reset-button').css('display', 'block');
            }
        });

        $('#ticket_message_resolved').on('change', function() {
           if ($(this).is(':checked') && $('#ticket_message_content').val().length == 0) {
               {% set currentDate = "now"|date("m-d-Y") %}
               {% if locale == 'fr' %}
                    {% set currentDate = "now"|date("d-m-Y") %}
               {% endif %}
               {% if app.user %}
                    {% set message = 'ticket.edit.close_thread'|trans({'%firstname%': app.user.firstname , '%lastname%': app.user.lastname, '%date%': currentDate}) %}
               {% else %}
                    {% set message = 'ticket.edit.close_thread_anonymous'|trans({'%date%': currentDate}) %}
               {% endif %}
               $('#ticket_message_content').val('{{ message }}');
           }
        });

        function resetFiles() {
            var noFileLabel = '{{ 'ticket.common.nofiles'|trans }}';
            $('#ticket_message_attachments, #ticket_message_attachments').val("");
            $('#list-attachments').empty();
            $('#list-attachments').append($('<li class="file--warning">'+noFileLabel+'</li>'));
            $(this).hide();
        }
    </script>
{% endblock %}
