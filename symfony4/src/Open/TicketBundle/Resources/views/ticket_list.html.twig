{% extends base %}
{% trans_default_domain "AppBundle" %}

{% block title %}
    {% if app.request.query.get('status') == 'closed'%}
        {{ 'ticket.list.title.resolved' | trans}}
    {% else %}
        {{ 'ticket.list.title.standard' | trans}}
    {% endif %}
{% endblock %}

{% block body %}
    <div class="Page-inner">
    {% set status = app.request.get('status') %}

    {% if is_granted('ROLE_SUPER_ADMIN') or is_granted('ROLE_OPERATOR') %}
        {% form_theme form 'bootstrap_4_layout.html.twig' %}

        <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.7.1/css/bootstrap-datepicker.css" rel="stylesheet" type="text/css" />
        <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.7.1/js/bootstrap-datepicker.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.7.1/locales/bootstrap-datepicker.fr.min.js"></script>

        <div>
            <h4>{{ 'back.ticket.filter.title' | trans }}</h4>
        </div>

        <div class="form-container company-filter">

            {{ form_start(form, {'class': 'myclass', 'attr': {'class': 'form-inline'}}) }}

            <div class="input-group col-md-10">
                <div class="input-group col-md-6">
                    {{ form_label(form.object) }}
                    {{ form_errors(form.object) }}
                    {{ form_widget(form.object, {'attr' : {'class' : 'resetable_input'}}) }}
                </div>

                <div class="input-group col-md-6">
                    {{ form_label(form.author) }}
                    {{ form_errors(form.author) }}
                    {{ form_widget(form.author, {'attr' : {'class' : 'resetable_input'}}) }}
                </div>

                <div class="input-group col-md-6">
                    {{ form_label(form.creationMin) }}
                    {{ form_errors(form.creationMin) }}
                    {{ form_widget(form.creationMin, {'attr' : {'class' : 'resetable_input col-md-3 datepicker'}}) }}

                    {{ form_label(form.creationMax) }}
                    {{ form_errors(form.creationMax) }}
                    {{ form_widget(form.creationMax, {'attr' : {'class' : 'resetable_input col-md-3 datepicker'}}) }}
                </div>

                <div class="input-group col-md-6">
                    {{ form_label(form.modificationMin) }}
                    {{ form_errors(form.modificationMin) }}
                    {{ form_widget(form.modificationMin, {'attr' : {'class' : 'resetable_input col-md-3 datepicker'}}) }}

                    {{ form_label(form.modificationMax) }}
                    {{ form_errors(form.modificationMax) }}
                    {{ form_widget(form.modificationMax, {'attr' : {'class' : 'resetable_input col-md-3 datepicker'}}) }}
                </div>
            </div>
            <div class="input-group col-md-2">
                {{ form_widget(form.save, {'attr' : {'class' : 'btn-info'}}) }}
                <button id="clearForm" class="btn btn-outline-dark" type="button">{{ 'back.company.filter_clear' | trans }}</button>
            </div>
        </div>
    {% else %}
        <div class="buttons">
            <div style="margin-bottom: 15px;">
                {% if app.request.query.get('status') != 'closed' %}
                    <a class="btn btn-primary button" style="display: inline-block" href="{{ path(addRoute) }}" >{{ 'ticket.list.add'|trans }}</a>
                {% endif %}
            </div>
        </div>
    {% endif %}


    {% if is_granted('ROLE_OPERATOR') or is_granted('ROLE_SUPER_ADMIN') %}
    {% else %}
        <div class="card-title">
            <a class="{{ status ==null ? ' active'}} tab-a" href="{{ path('buyer.ticket.list') }}">{{ 'ticket.list.opened'|trans }}</a>
            <a class="{{ status == 'closed' ? ' active'}} tab-a" href="{{ path('buyer.ticket.list', {'status':'closed'}) }}">{{ 'ticket.list.closed'|trans }}</a>
        </div>
    {% endif %}

    {% if pagination.getTotalItemCount > 0 %}

        {% if isAdmin %}
            <div class="tickets--desktop">
                <table class="table table-hover">
                    <thead>
                    <tr>
                            <th scope="col" colspan="1">{{ 'ticket.list.number'|trans }}</th>
                            <th scope="col" class="col--left">{{ 'ticket.list.sujet'|trans }}</th>
                            <th scope="col">{{ 'ticket.list.company'|trans }}</th>
                            <th scope="col">{{ 'back.ticket.filter.main_contact'|trans }}</th>
                            <th scope="col">{{ 'ticket.list.lastAt'|trans }}</th>
                            <th scope="col" class="col--action">{{ 'ticket.list.createdAt'|trans }}</th>
                            <th>{{ 'ticket.list.status'|trans }}</th>
                            <th>{{ 'ticket.list.nb_messages'|trans }}</th>
                            <th scope="col">{{ 'ticket.list.actions'|trans }}</th>
                    </tr>
                    </thead>

                    <tbody>
                    {% for ticket in pagination %}
                        <tr {% if (isOperator and ticket.operatorRead==true) or (isOperator == false and  ticket.customerRead == true) or ticket.notReadIzberg  %}class="ticket--new"{% endif %}>

                            <td class="col--id">
                                {% if not ticket.fromIZB %}
                                    <a href="{{ path(editRoute, {'id':ticket.ticketNumber}) }}" title="{{ ticket.ticketNumber }}">{{ ticket.ticketNumber|slice(-5,5) }}</a>
                                {% else %}
                                    <a href="{{ path(editRouteFromIZB, {'id':ticket.ticketNumber}) }}" title="{{ ticket.ticketNumber }}">{{ ticket.ticketNumber|slice(-5,5) }}</a>
                                {% endif %}
                            </td>
                            <td class="col--left">{{ ticket.subject }}</td>
                            <td>
                                {% if ticket.author is null %}
                                    {% set author = ticket.anonymousCompany %}
                                {% else %}
                                    {% if app.user.company is not null and ticket.author.id == app.user.company.id %}
                                        {% set author = 'ticket.list.me' | trans %}
                                    {% else %}
                                        {% set author = ticket.author.name %}
                                    {% endif %}
                                {% endif %}
                                {{ author }} {% if ticket.createdByAdmin == true %} {{ 'ticket.edit.operator' | trans }} {% endif %}
                            </td>
                            {% if isAdmin and ticket.ticketCreator.firstname is defined %}
                                <td>
                                    {{ ticket.ticketCreator.firstname }} {{ ticket.ticketCreator.lastname }}
                                </td>
                            {% else %}
                                <td>{{ ticket.anonymousFirstName ~ ' ' ~ ticket.anonymousLastName }}</td>
                            {% endif %}
                            {% if not ticket.fromIZB %}
                                <td>{{ ticket.lastMessage.createdAt | format_datetime('short', 'medium', locale=locale) }}</td>
                                <td>{{ ticket.createdAt | format_datetime('short', 'medium', locale=locale) }}</td>
                            {% else %}
                                <td>{{ ticket.lastMessage.izbDate | format_datetime('short', 'medium', locale=locale, timezone='Europe/Paris') }}</td>
                                <td>{{ ticket.izbDate | format_datetime('short', 'medium', locale=locale, timezone='Europe/Paris') }}</td>
                            {% endif %}
                                <td>
                                    {{ ('ticket.status.' ~ ticket.statusAsString)|trans }}
                                </td>
                                <td>
                                    {{ ticket.messages|length }}
                                </td>
                                <td>
                                    <a class="action" href="{{ path(editRoute, {'id':ticket.ticketNumber}) }}">
                                <span title={{ 'back.commons.view'|trans }}>
                                    <svg class="Icon" alt={{ 'back.commons.view'|trans }}>
                                        <use xlink:href="#eye"></use>
                                    </svg>
                                </span>
                                    </a>
                                </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>

                {# display navigation #}
                {#<div class="d-flex justify-content-left">
                    <div class="navigation col-md-10">

                        {{ knp_pagination_render(pagination, 'OpenTicketBundle:pagination:pagination.html.twig') }}
                    </div>
                </div>#}

            </div>

        {% else %}
            <table class="table table-hover">
                <thead>
                <tr>
                    <th scope="col">{{ 'ticket.list.number'|trans }}</th>
                    <th scope="col">{{ 'ticket.list.sujet'|trans }}</th>
                    <th scope="col">{{ 'ticket.list.author'|trans }}</th>
                    <th scope="col">{{ 'ticket.list.sent_to'|trans }}</th>
                    <th scope="col">{{ knp_pagination_sortable(pagination, 'ticket.list.lastAt'|trans, 'l.createdAt') }}</th>
                    <th scope="col" class="col--action">{{ knp_pagination_sortable(pagination, 'ticket.list.createdAt'|trans, 'e.createdAt') }}</th>
                </tr>
                </thead>
                <tbody>
                {% for ticket in pagination %}

                    <tr {% if (isOperator and ticket.operatorRead==true) or (isOperator == false and  ticket.customerRead == true) or ticket.notReadIzberg  %}
                        class="ticket--new"
                    {% endif %}
                            {% if not ticket.fromIZB %}
                        onclick="window.location ='{{ path(editRoute, {'id':ticket.ticketNumber}) }}';"
                    {% else %}
                        onclick="window.location ='{{ path(editRouteFromIZB, {'id':ticket.ticketNumber}) }}';"
                            {% endif %}>
                        <td class="col--id">
                            <h4 class="label mobile-max-only">{{ 'ticket.list.number'|trans }}</h4>
                            {% if not ticket.fromIZB %}
                                <a  title="{{ ticket.ticketNumber }}">{{ ticket.ticketNumber|slice(-5,5) }}</a>
                            {% else %}
                                <a  title="{{ ticket.ticketNumber }}">{{ ticket.ticketNumber|slice(-5,5) }}</a>
                            {% endif %}
                        </td>
                        <td>
                            <h4 class="label mobile-max-only">{{ 'ticket.list.sujet'|trans }}</h4>
                            {{ ticket.subject }}
                        </td>
                        <td>
                            <h4 class="label mobile-max-only">{{ 'ticket.list.author'|trans }}</h4>
                            {% if ticket.author is not null %}
                                {{ ticket.author.name }}
                            {% elseif ticket.fromIZB is not null %}
                                {{ ticket.vendor }}
                            {% else %}
                                {{ 'ticket.edit.operator' | trans }}
                            {% endif %}
                        </td>
                        <td>
                            {% if ticket.recipient is not null %}
                                {{ ticket.recipient.name }}
                            {% elseif ticket.fromIZB is not null %}
                                {{ ticket.vendor }}
                            {% else %}
                                {{ 'ticket.edit.operator' | trans }}
                            {% endif %}
                        </td>

                        {% if not ticket.fromIZB %}
                            <td>
                                <h4 class="label mobile-max-only">{{ 'ticket.list.lastAt'|trans }}</h4>
                                {{ ticket.lastMessage.createdAt | format_datetime('short', 'medium', locale=locale) }}
                            </td>
                            <td>
                                <h4 class="label mobile-max-only">{{ 'ticket.list.createdAt'|trans }}</h4>
                                {{ ticket.createdAt | format_datetime('short', 'medium', locale=locale) }}
                            </td>
                        {% else %}
                            <td>
                                <h4 class="label mobile-max-only">{{ 'ticket.list.lastAt'|trans }}</h4>
                                {{ ticket.lastMessage.izbDate | format_datetime('short', 'medium', locale=locale, timezone='Europe/Paris') }}
                            </td>
                            <td>
                                <h4 class="label mobile-max-only">{{ 'ticket.list.createdAt'|trans }}</h4>
                                {{ ticket.izbDate | format_datetime('short', 'medium', locale=locale, timezone='Europe/Paris') }}
                            </td>
                        {% endif %}
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        {% endif %}

        <div class="">
            {% if isAdmin %}
                <div class="navigation col-md-10">
                    {{ knp_pagination_render(pagination) }}
                </div>
            {% else %}
                {{ knp_pagination_render(pagination, '@OpenTicket/pagination/pagination.html.twig') }}
            {% endif %}
        </div>

    {% else %}
        <div class="d-flex justify-content-left empty-div">
            <span>{{ 'ticket.list.empty'| trans }}</span>
        </div>
    {% endif %}
    </div>
    <script type="application/javascript">
        document.addEventListener('DOMContentLoaded', function() {
            $('.datepicker').datepicker({
                language: 'fr'
            });

            $(".filter_select").change(function() {
                $('form[name="filter_ticket"]').submit();
            });

            $("#clearForm").click(function() {
                $(".resetable_input").val("");
                $('form[name="filter_ticket"]').submit();
            });
        });
    </script>
{% endblock %}
