<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 07/02/2018
 * Time: 15:37
 */

namespace Open\TicketBundle\Services;


use AppBundle\Entity\ThreadMessage;
use AppBundle\Entity\User;
use AppBundle\Services\AbstractPaginatedService;
use AppBundle\Services\SecurityService;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\NonUniqueResultException;
use Knp\Component\Pager\PaginatorInterface;
use Open\IzbergBundle\Api\MessageApi;
use Open\TicketBundle\Entity\Ticket;
use Open\TicketBundle\Entity\TicketMessage;
use Open\TicketBundle\FilterQueryBuilder\TicketQueryBuilder;
use Open\TicketBundle\Model\TicketConstant;

class TicketService extends AbstractPaginatedService
{

    /**
     * @var SecurityService $securityService
     */
    private $securityService;

    /**
     * @var MessageApi $messageApi
     */
    private $messageApi;

    public function __construct(
        EntityManager $em,
        PaginatorInterface $paginator,
        SecurityService $securityService,
        TicketQueryBuilder $ticketQueryBuilder,
        MessageApi $messageApi
    ){
        parent::__construct($em, Ticket::class, $paginator, $ticketQueryBuilder);

        $this->securityService = $securityService;
        $this->messageApi = $messageApi;
    }

    /**
 * @param User $user
 * @param $request
 * @return mixed the paginator to iterate on the ticket list
 */
    public function getCustomPaginator ($user, $request, $filter, $numberPerPage){

        /**
         * whether only the closed ticket must be displayed
         */
        $closed = false;
        $status = $request->get("status");
        if (!empty($status) && $status==="closed"){
            $closed=true;
        }



        $qb = $this->getDefaultQueryBuilder();

        if($filter){
            $this->filterQueryBuilder->build($qb, $filter);
        }

        //if this is not a admin, add a filter
        if ($this->securityService->isAdmin($user)) {
            $qb->leftJoin("e.author", "a")
                ->addSelect("a");
        }


        //if this is not a admin, add a filter
        if (!$this->securityService->isAdmin($user)) {
            $qb->leftJoin("e.company", "c")->
            andWhere("c.id = :userCompanyId")->
            setParameter("userCompanyId", $user->getCompany()->getId());
        }

        if ($closed){
            $qb->andWhere("e.status = :status");
        }
        else{
            $qb->andWhere("e.status != :status");
        }
        $qb->setParameter("status", TicketConstant::STATUS_RESOLVED);


        return $this->getPaginatorByQb($qb, 0, $numberPerPage, $request, array('defaultSortFieldName' => 'e.createdAt', 'defaultSortDirection' => 'desc'));
    }

    /**
     * @param User $user
     * @param $request
     * @return mixed the paginator to iterate on the ticket list
     */
    public function getInternTicket ($user, $request){

        /**
         * whether only the closed ticket must be displayed
         */
        $closed = false;
        $status = $request->get("status");
        if (!empty($status) && $status==="closed"){
            $closed=true;
        }

        $qb = $this->getDefaultQueryBuilder();

        $qb->leftJoin("e.lastMessage", "l");



        //if this is not a admin, add a filter
        if (!$this->securityService->isAdmin($user)) {
            $qb->leftJoin("e.company", "c")->
            where("c.id = :userCompanyId")->
            setParameter("userCompanyId", $user->getCompany()->getId());
        }
        if ($closed){
            $qb->andWhere("e.status = :status");
        }
        else{
            $qb->andWhere("e.status != :status");
        }
        $qb->setParameter("status", TicketConstant::STATUS_RESOLVED);


        return $qb->getQuery()->getResult();
    }

    /**
     * @param $request
     * @param $filter
     * @param $isDashboard
     * @return mixed the paginator to iterate on the ticket list
     */
    public function getAdminTicketsCustomPaginator ($request, $filter){
        $qb = $this->getDefaultQueryBuilder();

        $this->filterQueryBuilder->build($qb, $filter);

        return $this->getPaginatorByQb($qb, 0, 10, $request, array('defaultSortFieldName' => 'e.createdAt', 'defaultSortDirection' => 'desc'));

    }

    public function getAdminNotReadTicket($page, $numberPerPage, $request){
        $qb = $this->getDefaultQueryBuilder();
        $qb->andWhere("e.operatorRead = 1");
        $qb->andWhere("e.isCreatedByAdmin = 0");
        $qb->orderBy('e.updatedAt', 'DESC');

        return $this->paginator->paginate(
            $qb->getQuery(),
            $request->query->getInt('notReadTicket', 1),
            $numberPerPage,
            array(
                'pageParameterName' => 'notReadTicket'
            )
        );

    }

    /**
     * @param $companyId
     * @return mixed
     */
    private function getUserNotReadTicket($companyId){
        $qb = $this->getDefaultQueryBuilder()
            ->andWhere("e.customerRead = 1")
            ->andWhere("e.company = :companyId")
            ->setParameter("companyId", $companyId);
        return $qb->getQuery()->getResult();
    }

    /**
     * @param $filter
     * @return mixed the query result
     */
    public function getAdminTicketsByFilter($filter){
        $qb = $this->getDefaultQueryBuilder();

        $this->filterQueryBuilder->build($qb, $filter);

        $qb->orderBy('e.createdAt', 'desc');
        $query = $qb->getQuery();

        return $query->getResult();
    }

    /**
 * persist a ticket in the database
 * @param Ticket $ticket the ticket to save in the db
 * @throws \Doctrine\ORM\ORMException
 */
    public function createTicket ($ticket){
        $this->em->persist($ticket);
        $this->em->flush();
    }

    /**
     * update a ticket in the database
     * @param Ticket $ticket the ticket to save in the db
     * @throws \Doctrine\ORM\ORMException
     */
    public function updateTicket ($ticket){
        $this->em->merge($ticket);
        $this->em->flush();
    }

    /**
     * persist a ticket in the database
     * @param Ticket $ticket the ticket to save in the db
     * @throws \Doctrine\ORM\ORMException
     */
    public function createMessage ($message){
        $this->em->persist($message);
        $this->em->flush();
    }

    public function clearTicket ($ticket){
        $this->em->clear();
    }

    /**
     * @param $ticketNumber
     * @return mixed
     * @throws \Doctrine\ORM\NoResultException
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function getTicketByNumber ($ticketNumber){

        return $this->em->createQueryBuilder()->
        select("t, m")->
        from("OpenTicketBundle:Ticket", "t")->
        leftJoin("t.messages", "m")->
        where("t.ticketNumber = :number")->
        setParameter("number", $ticketNumber)->
        orderBy("m.createdAt", "DESC")->
        getQuery()->
        getSingleResult();
    }

    /**
     * @param $ticketNumber
     * @return mixed
     * @throws \Doctrine\ORM\NoResultException
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function findTicketByNumber ($ticketNumber){

        return $this->em->createQueryBuilder()->
        select("t, m")->
        from("OpenTicketBundle:Ticket", "t")->
        leftJoin("t.messages", "m")->
        where("t.ticketNumber = :number")->
        setParameter("number", $ticketNumber)->
        orderBy("m.createdAt", "DESC")->
        getQuery()->
        getOneOrNullResult();
    }

    /**
     * Get a message by its id
     * @param $messageId
     * @return null|TicketMessage the message or null if not found
     * @throws NonUniqueResultException
     */
    public function findMessageById ($messageId){
        return $this->em->createQueryBuilder()->
            select("m")->
            from("OpenTicketBundle:TicketMessage", "m")->
            where("m.id = :id")->
            setParameter("id", $messageId)->getQuery()->getOneOrNullResult();
    }

    /**
     * @param $attachmentId
     * @return null|TicketMessage the message or null if not found
     * @throws NonUniqueResultException
     */
    public function findAttachmentById($attachmentId)
    {
        return $this->em->createQueryBuilder()->
        select("a")->
        from("OpenTicketBundle:TicketMessageAttachment", "a")->
        where("a.id = :id")->
        setParameter("id", $attachmentId)->getQuery()->getOneOrNullResult();
    }

    public function fetchAttachmentUrl(string $attachmentId): ?string
    {
        return $this->messageApi->fetchAttachmentUrl($attachmentId);
    }

    /**
     * @param $ticketNumber
     */
    public function reopenTicket($ticketNumber){
        $ticket = $this->getTicketByNumber($ticketNumber);
        if($ticket){
            /** @var Ticket $ticket */
            $ticket->setStatus(TicketConstant::STATUS_OPEN);
            $this->updateTicket($ticket);
        }
    }

    /**
     * @param $izbergUserId
     * @param $companyId
     * @return int
     */
    public function getCountUnreadMessage($izbergUserId, $companyId): int
    {
        // return count($this->getUnreadMessageIds($izbergUserId)) + count($this->getUserNotReadTicket($companyId));
        return $this->countUnreadMessageIdsFromDB($izbergUserId) + count($this->getUserNotReadTicket($companyId));
    }

    public function countUnreadMessageIdsFromDB($izbergUserId)
    {
        return $this->em->getRepository(ThreadMessage::class)->countUnreadMessagesByUserId($izbergUserId);
    }

    public function getUnreadMessageIds($izbergUserId): array
    {
        $unreadMessagesIzb = $this->messageApi->getUnreadMessageForUser($izbergUserId);
        if (null === $unreadMessagesIzb) {
            return [];
        }

        return array_unique(
            array_filter(
                array_map(
                    function($izbergUnreadMessage) {
                        if (!preg_match('#/user/#',$izbergUnreadMessage->to_resource_uri)) {
                            return null;
                        }

                        $rootMessageId = $izbergUnreadMessage->root_msg;
                        $rootMessageId = substr($rootMessageId, 0, -1);
                        $rootMessageId = substr($rootMessageId, strrpos($rootMessageId, '/') + 1);

                        return $rootMessageId;
                    },
                    $unreadMessagesIzb->objects
                )
            )
        );
    }

}
