<?php

namespace Open\WebhelpBundle;

use AppBundle\Services\SecurityService;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Open\WebhelpBundle\Api\ApiConnection;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Unirest;

class Api implements LoggerAwareInterface
{
    public const HTTP_GET_OPERATION = "GET";
    public const HTTP_POST_OPERATION = "POST";
    public const HTTP_PUT_OPERATION = "PUT";
    public const HTTP_PATCH_OPERATION = "PATCH";
    public const HTTP_DELETE_OPERATION = "DELETE";

    public const HEADER_CONTENT_TYPE = 'Content-Type';
    public const HEADER_ACCEPT_LANGUAGE = 'Accept-Language';
    public const HEADER_AUTHORIZATION = 'Authorization';

    public const CONTENT_JSON = 'application/json';

    private SecurityService $securityService;
    private LoggerInterface $logger;
    private ApiConnection $apiConnection;

    public function __construct(
        ApiConnection $apiConnection,
        SecurityService $securityService
    )
    {
        $this->apiConnection = $apiConnection;
        $this->securityService = $securityService;
    }

    /**
     * format a object to json to be posted as body to the WEBHELP api
     * @param array $data the array to format in json
     * @return string the json as a string
     * @throws ApiException
     */
    public function formatObjectToJson($data)
    {
        try {
            return Unirest\Request\Body::json($data);
        } catch (Unirest\Exception $e) {
            $this->writeGenericErrorLog(sprintf("Error while formating data to be posted to the WEBHELP API : %s", print_r($data, true)), []);
            throw new ApiException(sprintf("Error while formating data to be posted to the WEBHELP API : %s", print_r($data, true)), $e);
        }
    }

    /**
     * write a error log for the API
     * @param string $message
     * @param array $context
     */
    public function writeGenericErrorLog(string $message, array $context)
    {
        $this->logger->error(
            $message,
            LogUtil::buildContext(array_merge([
                LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_GENERAL_ERROR,
                LogUtil::USER_NAME => $this->securityService->getCurrentUserName(),
            ],$context))
        );
    }


    /***
     * @param $message
     * @param $apiCall
     * @param $request
     * @param $response
     */
    public function writeGenericInfoLog($message, $apiCall, $request, $response)
    {
        $data = [
            'api_call' => $apiCall,
            'request' => $request,
            'response' => $response
        ];

        $this->logger->info(
            $message,
            LogUtil::buildContext(array_merge([
                LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_GENERAL_SUCCESS,
                LogUtil::USER_NAME => $this->securityService->getCurrentUserName(),
            ],$data))
        );
    }

    /**
     * Throw a basic error
     * @param string $code the http status code from the api
     * @param string $apiCall the API URL called
     * @param string $message the WEBHELP message
     * @param array $externalContext additional key/value information for this error
     * @throws ApiException
     */
    public function throwGenericError($code, $apiCall, $message = null, array $externalContext = [])
    {
        if (empty($message)) {
            $exception = new ApiException('An error occurred while invoking WEBHELP API.', $code);
        } else {
            $exception = new ApiException(
                sprintf(
                    'An error occurred while invoking WEBHELP API: %s code : [%s] API CALL : [%s]',
                    $message,
                    $code,
                    $apiCall
                ),
                $code
            );
            $exception->setWEBHELPMessage($message);

            //try to get an WEBHELP code
            $errors = $this->decodeJson($message);

            if (is_array($errors)) {
                $exception->setWEBHELPCode($errors[0]->code);
            } else {
                if (isset($errors->code)) {
                    $exception->setWEBHELPCode($errors->code);

                } else {
                    $exception->setWEBHELPCode(0);
                }
            }
        }

        $exception->setApiCall($apiCall);
        $exception->setExternalContext($externalContext);

        $this->writeGenericErrorLog(
            'An error occurred while invoking WEBHELP API. WEBHELP code:',
            $exception->getContext()
        );

        throw $exception;
    }

    /**
     * decode a json string
     * @param string $string the string to decode
     * @return mixed
     */
    private function decodeJson($string)
    {
        $result = json_decode($string);
        if (json_last_error() == JSON_ERROR_NONE) {
            return $result;
        }

        return null;
    }

    public function getApiURL()
    {
        return $this->apiConnection->getScheme() . '://' . $this->apiConnection->getDomain();
    }

    public function headers(): array
    {
        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Basic ' . base64_encode($this->apiConnection->getUsername() . ":" . $this->apiConnection->getPassword()),
        ];
        return $headers;
    }

    public function sendApiRequest(string $method, string $route, $data = null, array $headers = []): Unirest\Response
    {
        $method = strtolower($method);
        $authorizedMethods = [
            self::HTTP_GET_OPERATION,
            self::HTTP_POST_OPERATION,
            self::HTTP_DELETE_OPERATION,
            self::HTTP_PUT_OPERATION,
            self::HTTP_PATCH_OPERATION
        ];

        if (!in_array(strtoupper($method), $authorizedMethods)) {
            throw new \InvalidArgumentException(
                sprintf(
                    'HTTP method %s is not authorized. Only the following methods are authorized %s',
                    $method,
                    implode(', ', $authorizedMethods)
                )
            );
        }

        $requestUrl = $this->getApiUrl() . $route;
        if (strpos($route, 'http') === 0) {
            $requestUrl = $route;
        }

        $body = $data;
        if (!is_null($data) && !is_string($data)) {
            $body = $this->formatObjectToJson($data);
        }

        $startTime = microtime(true);
        $this->logger->info(
            sprintf('Request Webhelp API %s - Method: %s', $requestUrl, $method),
            LogUtil::buildContext([
                LogUtil::EVENT_NAME => EventNameEnum::WEBHELP_API,
                'data' => $data,
            ])
        );

        /** @var Unirest\Response $response */
        $response = Unirest\Request::$method(
            $requestUrl,
            $this->headers() + $headers,
            $body
        );

        $endTime = microtime(true);

        if (!in_array($response->code, [200, 201,/* 202, 204, 206, 207*/])) {
            $this->throwGenericError($response->code, $requestUrl, $response->raw_body, [
                'response time' => $endTime - $startTime,
                'request data' => $data,
            ]);
        }

        $this->logger->info(
            sprintf(
                'Response Webhelp API %s - Method: %s',
                $requestUrl,
                $method
            ),
            LogUtil::buildContext([
                LogUtil::EVENT_NAME => EventNameEnum::WEBHELP_API,
                'response time' => $endTime - $startTime,
                'payload' => $response->body
            ])
        );

        return $response;
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }
}
