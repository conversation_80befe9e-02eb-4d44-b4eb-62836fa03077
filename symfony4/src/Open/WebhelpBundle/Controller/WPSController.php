<?php

namespace Open\WebhelpBundle\Controller;

use \AppBundle\Controller\MkoController;
use AppBundle\Entity\Cart;
use AppBundle\Services\CartService;
use AppBundle\Services\OrderService;
use AppBundle\Services\ProcessService;
use AppBundle\Services\SerializerService;
use JMS\Serializer\Naming\IdenticalPropertyNamingStrategy;
use JMS\Serializer\SerializerBuilder;
use Open\IzbergBundle\Api\ApiClientManager;
use Open\IzbergBundle\Api\ApiConfigurator;
use Open\IzbergBundle\Api\CartApi;
use Open\IzbergBundle\Api\InvoiceApi;
use Open\IzbergBundle\Api\IzbergUtils;
use Open\IzbergBundle\Api\MerchantApi;
use Open\IzbergBundle\Api\OrderApi;
use Open\IzbergBundle\Api\RefundApi;
use Open\IzbergBundle\Model\Refund;
use Open\LogBundle\Utils\LogUtil;
use Open\WebhelpBundle\Api\TransactionApi;
use Open\WebhelpBundle\Model\AutomaticResponse;
use Open\WebhelpBundle\Model\RefundTransaction;
use Open\WebhelpBundle\Model\SubTransaction;
use Open\WebhelpBundle\WPSPushException;
use \Symfony\Component\HttpFoundation\Request;
use \Open\LogBundle\Utils\EventNameEnum;
use \AppBundle\Services\WPSService;
use \AppBundle\Services\MailService;
use \AppBundle\Entity\User;
use \Open\IzbergBundle\Model\MerchantOrder;
use \Open\IzbergBundle\Api\GatewayApi;
use \AppBundle\Command\Payment\Utils\PaymentUtils;


use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

class WPSController extends MkoController
{
    private const LOG_WPS_EVENT = "wps_event";
    private const LOG_WH_CONTENT = "hook_content";

    private const AUTOMATIC_RESPONSE_STATUS_REFUSED = "REFUSED";
    private const AUTOMATIC_RESPONSE_STATUS_COMPLETE = "COMPLETE";
    private const AUTOMATIC_RESPONSE_STATUS_ABORTED = "ABORTED";
    private const AUTOMATIC_RESPONSE_STATUS_CAPTURED = "CAPTURED";

    /**
     *
     * @param Request          $request
     * @param ProcessService   $processService
     *
     * @param WPSService       $WPSService
     *
     *
     * @param ApiClientManager $apiClientManager
     * @param ApiConfigurator  $apiConfigurator
     * @return JsonResponse
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/wps/push/automaticResponse', name: 'wps.payment.automatic', methods: ['POST'])]
    public function automaticResponsePushAction (Request $request, ProcessService $processService, WPSService $WPSService, ApiClientManager $apiClientManager, ApiConfigurator $apiConfigurator)
    {
        $this->configureApiConnection('webhook', $apiClientManager, $apiConfigurator);

        try{
            //building serializer
            $serializeBuilder = SerializerBuilder::create();
            $serializeBuilder->setPropertyNamingStrategy(new IdenticalPropertyNamingStrategy());
            $serializer = $serializeBuilder->build();

            /**
             * @var AutomaticResponse $automaticResponse
             */
            $automaticResponse = $serializer->deserialize($request->getContent(), AutomaticResponse::class, 'json');
            $processService->processBeginFor($automaticResponse);

            //log incoming push
            $this->logger->info("WPS push received: automaticResponsePushAction",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::WEBHELP_PUSH,
                    self::LOG_WPS_EVENT => "automaticResponsePushAction",
                    self::LOG_WH_CONTENT => $request->getContent()
                ])
            );

            //process to run will depend on the status of the received pushed
            if ($automaticResponse->getStatus() === self::AUTOMATIC_RESPONSE_STATUS_ABORTED){
                $WPSService->manageAbortedAutomaticResponse($automaticResponse);
            }
            else if ($automaticResponse->getStatus() === self::AUTOMATIC_RESPONSE_STATUS_REFUSED){
                $WPSService->manageRefusedAutomaticResponse($automaticResponse);
            }
            else if ($automaticResponse->getStatus() === self::AUTOMATIC_RESPONSE_STATUS_COMPLETE || $automaticResponse->getStatus() === self::AUTOMATIC_RESPONSE_STATUS_CAPTURED){
                $WPSService->manageCompleteAutomaticResponse($automaticResponse);
            }
            else{
                $this->logger->error(" * unable to process wps push: unknown status ".$automaticResponse->getStatus(),
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::WEBHELP_PUSH,
                        self::LOG_WPS_EVENT => "automaticResponsePushAction",
                        "status" => $automaticResponse->getStatus(),
                        "transactionId" => $automaticResponse->getCodeTransactionWps(),
                        "reason" => $automaticResponse->getReason()
                    ])
                );
                throw new WPSPushException("unable to process wps push: unknown status ".$automaticResponse->getStatus());
            }

            $processService->processEndFor();

            //everything's ok => return empty 200 http status
            return $this->json([]);

        }catch(\Exception $e){
            $this->logger->error(" * error while processing automaticResponsePushAction event: ",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_GENERAL_ERROR,
                    self::LOG_WPS_EVENT => "automaticResponsePushAction",
                    "message" => $e->getMessage()
                ])
            );
            //error occurred while processing the wps push => return empty 500 http status
            return new JsonResponse(new \ArrayObject(), 500);
        }
    }


    /**
     *
     * @param Request          $request
     * @param ProcessService   $processService
     * @param WPSService       $WPSService
     * @param OrderApi         $orderApi
     * @param CartApi          $cartApi
     * @param OrderService     $orderService
     * @param MerchantApi      $merchantApi
     * @param InvoiceApi       $invoiceApi
     * @param GatewayApi       $gatewayApi
     * @param TransactionApi   $transactionApi
     * @param MailService      $mailService
     * @param ApiClientManager $apiClientManager
     * @param ApiConfigurator  $apiConfigurator
     * @return JsonResponse
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/wps/push/reconciliationDetails', name: 'wps.payment.accept', methods: ['POST'])]
    public function paymentReconciledPushAction(Request $request, ProcessService $processService, WPSService $WPSService, OrderApi $orderApi, CartApi $cartApi, OrderService $orderService,
                                                MerchantApi $merchantApi, InvoiceApi $invoiceApi, GatewayApi $gatewayApi,TransactionApi $transactionApi, MailService $mailService, ApiClientManager $apiClientManager, ApiConfigurator $apiConfigurator):JsonResponse
    {
        $this->configureApiConnection('webhook', $apiClientManager, $apiConfigurator);

        //log incoming push
        $this->logger->info("WPS push received: reconciliationDetails",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME => EventNameEnum::WEBHELP_PUSH,
                self::LOG_WPS_EVENT => "reconciliationDetails",
                self::LOG_WH_CONTENT => $request->getContent()
            ])
        );

        try {
            $serializeBuilder = SerializerBuilder::create();
            $serializeBuilder->setPropertyNamingStrategy(new IdenticalPropertyNamingStrategy());
            $serializer = $serializeBuilder->build();

            /**
             * @var SubTransaction $transaction
             */
            $transactions = $serializer->deserialize($request->getContent(), 'array<'.SubTransaction::class.'>', 'json');

            foreach ($transactions as $transaction) {

                $processService->processBeginFor($transaction);

                $this->logger->info("WPS push received: payment reconciled",
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::WEBHELP_PUSH,
                        self::LOG_WPS_EVENT => "reconciliationDetails",
                        "amount" => $transaction->getAmountReconciled(),
                        "subTransactionId" => $transaction->getCodeSubTransactionWps()
                    ])
                );

                $this->validateSubTransaction($transaction);

                /////////////////////////////////////////////////////////////////////////
                //GET IZBERG PSP GATEWAY
                /////////////////////////////////////////////////////////////////////////
                $gateway = $WPSService->getSubTransactionGateway($transaction->getCodeSubTransactionWps());

                /////////////////////////////////////////////////////////////////////////
                // Testing payment mode
                /////////////////////////////////////////////////////////////////////////
                $response =null;
                if ($gateway->gateway_type === GatewayApi::TYPE_PREPAYMENT) {
                    $response = $this->reconcilePrePayment($transaction, $gateway, $request, "payment_reconciled_pre", $orderApi,$cartApi, $WPSService, $orderService);
                } else {
                    $response = $this->reconcileTermPayment($transaction, $gateway, $request, "payment_reconciled_term",$invoiceApi,$gatewayApi,$transactionApi,$orderApi,$merchantApi, $mailService);
                }

                $processService->processEndFor();
                return $response;
            }
        }catch(\Exception $e){
            $this->logger->error(" * error while processing reconciliationDetails event: ",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_GENERAL_ERROR,
                    self::LOG_WPS_EVENT => "reconciliationDetails",
                    self::LOG_WH_CONTENT => $request->getContent(),
                    "message" => $e->getMessage()
                ])
            );

            return new JsonResponse(new \ArrayObject(), 500);
        }

        return new JsonResponse(new \ArrayObject(), 500);

    }

    /**
     * @param SubTransaction|null $subTransaction
     *
     * @throws WPSControllerException
     */
    private function validateSubTransaction(?SubTransaction $subTransaction){
        if ($subTransaction === null){
            $this->logger->error("invalid sub transaction received => null content",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_PROCESS
                ])
            );
            throw new WPSControllerException("invalid sub transaction received => null content");
        }
        if (empty($subTransaction->getCodeSubTransactionWps())){
            $this->logger->error("invalid sub transaction received => transaction code is empty or null",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_PROCESS
                ])
            );
            throw new WPSControllerException("invalid sub transaction received => transaction code is empty or null");
        }
    }

    /**
     * reconcile payment for pre payment mode
     * @param SubTransaction $transaction
     * @param \stdClass $gateway the izberg gateway
     * @param string $logEvent
     * @param $request
     * @return JsonResponse
     */
    private function reconcilePrePayment(SubTransaction $transaction, \stdClass $gateway, Request $request, $logEvent,
                                          OrderApi $orderApi, CartApi $cartApi, WPSService $WPSService, OrderService $orderService){

        $this->logger->info(" - detecting prepayment case",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_PROCESS,
                self::LOG_WPS_EVENT => $logEvent
            ])
        );

        $codeSubTransactionWps = $transaction->getCodeSubTransactionWps();

        try {
            ///////////////////////////////////////////////////////////////////////////
            /// Fetching the merchant order
            ///////////////////////////////////////////////////////////////////////////

            $merchantOrder = $orderApi->fetchMerchantOrderById(IzbergUtils::parseIzbergResourceAndGetId($gateway->merchant_order));

            //get cart to check the payment mode
            $cart = $cartApi->getCart($merchantOrder->getOrder()->getCartId(), true, $merchantOrder->getCurrency()->getCode());

            //in case the payment mode is not "Credit Card", we perform reconciliation payment
            if($cart->selected_payment_type == "prepayment" && $cart->selected_payment_method->code != "cb") {
                ///////////////////////////////////////////////////////////////////////////
                /// Performing charge on the sub transaction
                ///////////////////////////////////////////////////////////////////////////

                //charge the amount of the merchant order
                $WPSService->charge($merchantOrder->getAmountVatIncluded(), $codeSubTransactionWps);
                $this->logger->info(" - charging sub transaction",
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_PROCESS,
                        "amount" => $merchantOrder->getAmountVatIncluded(),
                        "merchantOrderId" => $merchantOrder->getId(),
                        "subTransactionCode" => $codeSubTransactionWps,
                        self::LOG_WPS_EVENT => $logEvent,
                    ])
                    );

                ///////////////////////////////////////////////////////////////////////////
                /// If merchant order status is initial, authorize order in prepayment
                ///////////////////////////////////////////////////////////////////////////
                if ($merchantOrder->getStatus() === 0 && $merchantOrder->getPaymentType() === 'prepayment') {
                    $orderService->authorizeOrder($merchantOrder->getOrder());
                    $this->logger->info(" - authorizing order",
                        LogUtil::buildContext([
                            LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_PROCESS,
                            "orderId" => $merchantOrder->getOrder()->getId(),
                            self::LOG_WPS_EVENT => $logEvent,
                        ])
                    );
// DISABLE EMAIL NOTIFICATION - MANAGED BY IZBERG (https://jira.open-groupe.com/browse/S1TMA-112)
//                    //notify sellers
//                    $payment_nb_hours_before_refund = $this->getParameter("payment_nb_hours_before_refund");
//                    $this->get(MerchantApi::class)->getMerchant($merchantOrder->getMerchant()->getId());
//                    $this->sendEmailsToMerchant($this->get(MerchantApi::class)->getMerchant($merchantOrder->getMerchant()->getId()),
//                        MailService::SELLER_NEW_COMMAND_PAID, [
//                            'orderNumber' => $merchantOrder->getOrder()->getIdNumber(),
//                            'paymentNbHoursBeforeRefund' => $payment_nb_hours_before_refund,
//                            'buyer' => $merchantOrder->getUser()->getFirstName()
//                        ]);
                }
            } else {
                $this->logger->info(" - reconciliation is about a Credit Card transaction: ignore it",
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_PROCESS,
                        "cart" => $cart->id,
                        "merchantOrderId" => $merchantOrder->getId()
                    ])
                );
            }

            //in all cases, returning success
            return $this->json(array());

        } catch (\Exception $e){
            $this->logger->error(" * Error while processing payment_reconciled event with pre payment mode",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_GENERAL_ERROR,
                    WPSService::LOG_STATUS_CODE => "PAYMENT_PROCESS_ERROR",
                    self::LOG_WPS_EVENT => $logEvent,
                    self::LOG_WH_CONTENT => $request->getContent(),
                    "message" => $e->getMessage()
                ])
            );
            return new JsonResponse([],500);
        }
    }

    /**
     * Reconcile payment for term payment mode
     *
     * @param SubTransaction $transaction
     * @param \stdClass      $gateway the izberg gateway
     * @param Request        $request
     * @param string         $logEvent
     * @param InvoiceApi     $invoiceApi
     * @param GatewayApi     $gatewayApi
     * @param TransactionApi $transactionApi
     * @param OrderApi       $orderApi
     * @param MerchantApi    $merchantApi
     * @param MailService    $mailService
     *
     * @return JsonResponse
     */
    private function reconcileTermPayment(
        SubTransaction $transaction,
        \stdClass $gateway,
        Request $request,
        $logEvent,
        InvoiceApi $invoiceApi,
        GatewayApi $gatewayApi,
        TransactionApi $transactionApi,
        OrderApi $orderApi,
        MerchantApi $merchantApi,
        MailService $mailService
    )
    {

        $this->logger->info(
            " - Detecting term payment case",
            LogUtil::buildContext([
                LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_PROCESS,
                self::LOG_WPS_EVENT => $logEvent
            ])
        );

        $amount = $transaction->getAmountReconciled();
        $codeSubTransactionWps = $transaction->getCodeSubTransactionWps();

        try {
            /////////////////////////////////////////////////////////////////////////
            //GET INVOICE
            /////////////////////////////////////////////////////////////////////////

            $invoiceId = IzbergUtils::parseIzbergResourceAndGetId($gateway->invoice);

            $this->logger->info("  - Fetching invoice with id ".$invoiceId,
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::WEBHELP_PUSH,
                    self::LOG_WPS_EVENT => $logEvent,
                    "invoiceId" => $invoiceId
                ])
            );
            /** @var \stdClass $invoice */
            $id = IzbergUtils::parseIzbergResourceAndGetId($gateway->invoice);
            if(is_string ($id)){
                $invoice = $invoiceApi->fetchCustomerInvoice($id);

            }

            /////////////////////////////////////////////////////////////////////////
            //compute transferred amount
            /////////////////////////////////////////////////////////////////////////
            $transferredAmount = round((floatval($amount) - floatval($invoice->paid_amount)), 2);

            /////////////////////////////////////////////////////////////////////////
            //performing some checks on amounts (if reconciled amount is 0 or if payment already done)
            /////////////////////////////////////////////////////////////////////////
            if ($this->validateInvoiceAndAmount($invoice, $transferredAmount, $gateway, $logEvent) === false){
                return $this->json([]);
            }

            /////////////////////////////////////////////////////////////////////////
            //Update the gateway with the amount
            /////////////////////////////////////////////////////////////////////////
            $this->logger->info(" - Updating izberg gateway",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::WEBHELP_PUSH,
                    self::LOG_WPS_EVENT => $logEvent,
                    "externalId" => $gateway->external_id,
                    "amount" => $transferredAmount
                ])
            );
            $gatewayApi->payGateway($gateway->external_id, $transferredAmount);

            /////////////////////////////////////////////////////////////////////////
            //If all payment has been done, performing charge
            /////////////////////////////////////////////////////////////////////////
            if ($transferredAmount >= $invoice->remaining_amount){
                $this->logger->info(" - Charging subtransaction",
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::WEBHELP_PUSH,
                        self::LOG_WPS_EVENT => $logEvent,
                        "codeSubTransaction" => $codeSubTransactionWps,
                        "amount" => $amount
                    ])
                );
                $transactionApi->charge($amount, $codeSubTransactionWps);


                $notificationBuyer = MailService::BUYER_FULL_PAYMENT_RECEIVED;
                $notificationSeller = MailService::SELLER_FULL_PAYMENT_RECEIVED;
            } else {
                $notificationBuyer = MailService::BUYER_PARTIAL_PAYMENT_RECEIVED;
                $notificationSeller = MailService::SELLER_PARTIAL_PAYMENT_RECEIVED;
            }

            /////////////////////////////////////////////////////////////////////////
            //Preparing data for all notification stuff
            /////////////////////////////////////////////////////////////////////////
            /** @var MerchantOrder $merchantOrder */
            $merchantOrder = $orderApi->fetchMerchantOrderById(
                IzbergUtils::parseIzbergResourceAndGetId(PaymentUtils::getMerchantOrderFromInvoice($invoice))
            );

            /** @var \Open\IzbergBundle\Model\Order $order */
            $order = $orderApi->fetchOrder($merchantOrder->getOrder()->getId());
            $merchant = $merchantApi->getMerchant($merchantOrder->getMerchant()->getId());

            /////////////////////////////////////////////////////////////////////////
            //Notify buyer
            /////////////////////////////////////////////////////////////////////////
            $this->sendEmailToBuyers($merchantOrder, $notificationBuyer, [
                "orderNumber" => $order->getIdNumber(),
                "amount" => $transferredAmount,
                "currency" => PaymentUtils::getCurrencyFromInvoice($invoice),
                "buyer" => $merchantOrder->getUser()->getFirstName()
            ], $logEvent,$mailService);

            /////////////////////////////////////////////////////////////////////////
            //Notifying seller
            /////////////////////////////////////////////////////////////////////////

            $this->sendEmailsToMerchant($merchant, $notificationSeller, [
                "orderNumber" => $merchantOrder->getOrder()->getId(),
                "amount" => $transferredAmount,
                "currency" => PaymentUtils::getCurrencyFromInvoice($invoice),
                "vendor" => $merchant->name
            ], $mailService);


        }catch (\Exception $e){
            $this->logger->error(" * Error during payment process",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_GENERAL_ERROR,
                    WPSService::LOG_STATUS_CODE => "PAYMENT_PROCESS_ERROR",
                    self::LOG_WPS_EVENT => $logEvent,
                    "message" => $e->getMessage(),
                    self::LOG_WH_CONTENT => $request->getContent()
                ])
            );

            return new JsonResponse(new \ArrayObject(), 500);
        }

        return $this->json([]);
    }

    /**
     * Use to notify a merchant
     *
     * @param \stdClass   $merchant the merchant to notify
     * @param string      $template the email template to be used
     * @param array       $params the variables to be used to populate the template
     * @param MailService $mailService
     */
    private function sendEmailsToMerchant(\stdClass $merchant, string $template, array $params, MailService $mailService){
        if (count($merchant->addresses) > 0 && $merchant->addresses[0]->contact_email != null) {
            $params[MailService::LAST_NAME_VAR] = $merchant->addresses[0]->contact_last_name;
            $params[MailService::FIRST_NAME_VAR] = $merchant->addresses[0]->contact_first_name;
            $mailService->sendEmailMessage(
                $template,
                $merchant->prefered_language,
                $merchant->addresses[0]->contact_email,
                $params
            );
        } else {
            $this->logger->error(" * Unable to notify merchant because he has no email address",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_PROCESS,
                    "merchant" => $merchant->id,
                ])
            );
        }
    }


    /**
     * Use to notify a buyer
     *
     * @param MerchantOrder $merchantOrder
     * @param string        $template the name of the email templates
     * @param array         $param the list of the params to be used to populate the template
     * @param               $logEvent
     * @param MailService   $mailService
     */
    private function sendEmailToBuyers (MerchantOrder $merchantOrder, string $template, $param, $logEvent, MailService $mailService){
        $mailService->sendEmailToBuyersMerchantOrder($merchantOrder, $template, $param, $logEvent);
    }

    /**
     * @param \stdClass  $invoice
     * @param float      $transferredAmount
     * @param \stdClass  $gateway izberg gateway
     * @param string     $logEvent
     *
     * @return boolean true if data are valid, false otherwise
     */
    private function validateInvoiceAndAmount($invoice, $transferredAmount, $gateway, $logEvent){
        //has already taken into account
        if ($transferredAmount === 0.0){
            $this->logger->info("transferred amount is null: skip it",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::WEBHELP_PUSH,
                    self::LOG_WPS_EVENT => $logEvent,
                    "externalId" => $gateway->external_id,
                    "reconciledAmount" => $transferredAmount
                ])
            );
            return false;
        }
        //payment already saved => do nothing
        else if (floatval($invoice->remaining_amount) === 0.0){
            $this->logger->info("all payment already received for this gateway => do nothing",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::WEBHELP_PUSH,
                    self::LOG_WPS_EVENT => $logEvent,
                    "externalId" => $gateway->external_id,
                ])
            );
            return false;
        }
        return true;
    }

    /**
     *
     * @param Request           $request
     * @param ProcessService    $processService
     * @param OrderApi          $orderApi
     * @param CartService       $cartService
     * @param RefundApi         $refundApi
     * @param WPSService        $wpsService
     * @param MailService       $mailService
     * @param SerializerService $serializer
     * @return JsonResponse
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/wps/pre_payment/refund/tooMuch', name: 'wps.prePayment.refund.tooMuch', methods: ['POST'])]
    public function refundTooMuchAmount (Request $request,ProcessService $processService,
                                         OrderApi $orderApi, CartService $cartService, RefundApi $refundApi, WPSService $wpsService,
                                         MailService $mailService, SerializerService $serializer)
    {
        return $this->refundPaymentNotification(
            $request->getContent(),
            MailService::BUYER_REFUND_PAYMENT_TOO_MUCH,
            "refund_payment_too_much",
            'refund too much payment',
            $processService,
            $orderApi,
            $cartService,
            $refundApi,
            $wpsService,
            $mailService,
            $serializer
        );
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/wps/pre_payment/refund/notEnough', name: 'wps.prePayment.refund.notEnough', methods: ['POST'])]
    public function refundNotEnoughAmount (Request $request,ProcessService $processService,
                                           OrderApi $orderApi, CartService $cartService, RefundApi $refundApi, WPSService $wpsService,
                                           MailService $mailService, SerializerService $serializer)
    {
        return $this->refundPaymentNotification(
            $request->getContent(),
            MailService::BUYER_REFUND_PAYMENT_NOT_ENOUGH,
            "refund_payment_not_enough",
            "refund not enough payment",
            $processService,
            $orderApi,
            $cartService,
            $refundApi,
            $wpsService,
            $mailService,
            $serializer
        );
    }

    private function refundPaymentNotification($content, $template, $logEvent, $logMessage, ProcessService $processService,
                                               OrderApi $orderApi, CartService $cartService, RefundApi $refundApi, WPSService $wpsService,
                                               MailService $mailService, SerializerService $serializer)
    {

        /**
         * @var RefundTransaction $refundTransaction
         */
        $refundTransaction = $serializer->deserialize($content, RefundTransaction::class);

        $processService->processBeginFor($refundTransaction);

        $this->logger->info(
            sprintf("WPS push received: %s", $logMessage),
            LogUtil::buildContext([
                LogUtil::EVENT_NAME => EventNameEnum::WEBHELP_PUSH,
                self::LOG_WPS_EVENT => $logEvent,
                self::LOG_WH_CONTENT => $content,
            ])
        );

        /////////////////////////////////////////////////////////////////////////
        //GET IZBERG PSP GATEWAY
        /////////////////////////////////////////////////////////////////////////

        if (!$refundTransaction->getTransactionId()) {
            $this->logger->error("Prepayment transfer refund payment: Unable to notify user because no transactionId",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_GENERAL_ERROR,
                    WPSService::LOG_STATUS_CODE => "NOTIFICATION_ERROR",
                    self::LOG_WPS_EVENT => $logEvent
                ])
            );

            return new JsonResponse("Prepayment transfer refund payment: Unable to notify user because no transactionId", 500);
        }

        $gateway = $wpsService->getSubTransactionGateway($refundTransaction->getTransactionId());
        if (!$gateway->merchant_order) {
            $this->logger->error("Prepayment transfer refund payment: Unable to notify user because no merchantOrderId",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_GENERAL_ERROR,
                    WPSService::LOG_STATUS_CODE => "NOTIFICATION_ERROR",
                    self::LOG_WPS_EVENT => $logEvent
                ])
            );

            return new JsonResponse("Prepayment transfer refund payment: Unable to notify user because no merchantOrderId", 500);
        }

        ///////////////////////////////////////////////////////////////////////////
        /// Fetching the merchant order
        ///////////////////////////////////////////////////////////////////////////
        $merchantOrder = $orderApi->fetchMerchantOrderById(IzbergUtils::parseIzbergResourceAndGetId($gateway->merchant_order));

        /** @var Cart $cart */
        $cart = $cartService->getCartByWPSTransactionId($refundTransaction->getTransactionId());

        if (!$cart) {
            $this->logger->error("Prepayment transfer refund payment: Unable to notify user because no cart",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_GENERAL_ERROR,
                    WPSService::LOG_STATUS_CODE => "NOTIFICATION_ERROR",
                    "transactionId" => $refundTransaction->getTransactionId(),
                    self::LOG_WPS_EVENT => $logEvent
                ])
            );

            return new JsonResponse("Prepayment transfer refund payment: Unable to notify user because no cart", 500);
        }

        $historic = $cart->getCartHistoric();
        if (!empty($historic)) {

            /** @var User $user */
            $user = $historic[0]->getUser();

            if (!$user || !$user->isEnabled()) {
                $this->logger->error("Prepayment transfer refund payment: Unable to notify user because it is unknown",
                    LogUtil::buildContext([
                        LogUtil::EVENT_NAME => EventNameEnum::PAYMENT_GENERAL_ERROR,
                        WPSService::LOG_STATUS_CODE => "NOTIFICATION_ERROR",
                        "transactionId" => $refundTransaction->getTransactionId(),
                        self::LOG_WPS_EVENT => $logEvent
                    ])
                );

                return new JsonResponse("Prepayment transfer refund payment: Unable to notify user because it is unknown", 500);
            }

            $params = [];
            $params['orderNumber'] = $merchantOrder->getOrder()->getIdNumber();
            $params['amountReceived'] = $refundTransaction->getAmountReceived();
            $params['amountExpected'] = $merchantOrder->getOrder()->getAmountVatIncluded();
            if ($template === MailService::BUYER_REFUND_PAYMENT_TOO_MUCH) {
                $params['amountRefunded'] = null;

                /** @var Refund $refund */
                $refund = $refundApi->findRefundByOrderNumber($merchantOrder->getOrder()->getIdNumber());

                $params['amountRefunded'] = $refund->getTotalRefundAmount();
            }

            $mailService->sendEmailMessage($template,
                $user->getLocale(), $user->getEmail(), [
                    MailService::LAST_NAME_VAR => $user->getLastname(),
                    MailService::FIRST_NAME_VAR => $user->getFirstname(),
                    $params
                ]
            );
        }

        $processService->processEndFor();

        return $this->json([]);
    }
}
