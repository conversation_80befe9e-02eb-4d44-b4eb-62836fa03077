<?php
/**
 * Created by PhpStorm.
 * User: PPH04712
 * Date: 09/07/2018
 * Time: 16:54
 */

namespace Open\WebhelpBundle\Model;


use <PERSON><PERSON>\Serializer\Annotation as JSM;
use <PERSON><PERSON>\Serializer\Annotation\Type;

/**
 * Class SubTransaction
 * @package Open\WebhelpBundle\Model
 */
#[JSM\ExclusionPolicy('none')]
class AutomaticResponse
{

    #[Type('string')]
    private $codeTransactionWps;

    #[Type('string')]
    private $reason;

    #[Type('string')]
    private $status;

    #[Type('string')]
    private $ticketCard;

    /**
     * @return mixed
     */
    public function getCodeTransactionWps()
    {
        return $this->codeTransactionWps;
    }

    /**
     * @param mixed $codeTransactionWps
     */
    public function setCodeTransactionWps($codeTransactionWps): void
    {
        $this->codeTransactionWps = $codeTransactionWps;
    }

    /**
     * @return mixed
     */
    public function getReason()
    {
        return $this->reason;
    }

    /**
     * @param mixed $reason
     */
    public function setReason($reason): void
    {
        $this->reason = $reason;
    }

    /**
     * @return mixed
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param mixed $status
     */
    public function setStatus($status): void
    {
        $this->status = $status;
    }

    /**
     * @return mixed
     */
    public function getTicketCard()
    {
        return $this->ticketCard;
    }

    /**
     * @param mixed $ticketCard
     */
    public function setTicketCard($ticketCard): void
    {
        $this->ticketCard = $ticketCard;
    }



}
