<?php
/**
 * Created by PhpStorm.
 * User: QAR14123
 * Date: 23/01/2019
 * Time: 10:10
 */

namespace Open\WebhelpBundle\Model;

use <PERSON><PERSON>\Serializer\Annotation\Type;
use <PERSON><PERSON>\Serializer\Annotation as JSM;

/**
 * Class RefundTransaction
 * @package Open\WebhelpBundle\Model
 */
#[JSM\ExclusionPolicy('none')]
class RefundTransaction
{

    #[Type('double')]
    private $amountReceived;

    #[Type('string')]
    private $transactionId;

    /**
     * @return mixed
     */
    public function getAmountReceived()
    {
        return $this->amountReceived;
    }

    /**
     * @param mixed $amountReceived
     */
    public function setAmountReceived($amountReceived)
    {
        $this->amountReceived = $amountReceived;
    }

    /**
     * @return mixed
     */
    public function getTransactionId()
    {
        return $this->transactionId;
    }

    /**
     * @param mixed $transactionId
     */
    public function setTransactionId($transactionId)
    {
        $this->transactionId = $transactionId;
    }


}
