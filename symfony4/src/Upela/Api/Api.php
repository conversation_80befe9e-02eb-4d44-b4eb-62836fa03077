<?php

namespace Upela\Api;

use Doctrine\Common\Annotations\AnnotationReader;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Component\PropertyInfo\Extractor\PhpDocExtractor;
use Symfony\Component\Serializer\Encoder\JsonEncoder;
use Symfony\Component\Serializer\Mapping\Factory\ClassMetadataFactory;
use Symfony\Component\Serializer\NameConverter\CamelCaseToSnakeCaseNameConverter;
use Symfony\Component\Serializer\Normalizer\ArrayDenormalizer;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;
use Symfony\Component\Serializer\Serializer;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\HttpClient\Exception\ExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\HttpExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Component\Serializer\Mapping\Loader\AttributeLoader;

abstract class Api implements LoggerAwareInterface
{
    protected ?HttpClientInterface $httpClient = null;
    protected LoggerInterface $logger;
    protected SerializerInterface $serializer;
    private string $username;
    private string $password;
    private string $baseUrl;

    /**
     * Api constructor.
     * @param string $baseUrl
     * @param string $username
     * @param string $password
     */
    public function __construct(string $baseUrl, string $username, string $password)
    {
        $this->baseUrl = $baseUrl;
        $this->username = $username;
        $this->password = $password;

        $this->initSerializer();
    }

    public function setLogger(LoggerInterface $logger): void
    {
        $this->logger = $logger;
    }

    /**
     * @param string $relativePath
     * @return string
     * @throws Exception
     */
    protected function buildUrl(string $relativePath)
    {
        if (strpos($relativePath, '/') !== 0) {
            throw new Exception('Invalid upela relative path passed. The relative path should start with "/"');
        }

        return sprintf('%s%s', $this->baseUrl, $relativePath);
    }

    /**
     * @throws Exception
     */
    protected function initHttpClient()
    {
        if ($this->httpClient instanceof HttpClientInterface) {
            return $this->httpClient;
        }

        try {
            $loginCheckUrl = sprintf('%s/api/login_check', $this->baseUrl);

            $httpClient = HttpClient::create();
            $response = $httpClient->request(
                'POST',
                $loginCheckUrl,
                [
                    'json' => [
                        'username' => $this->username,
                        'password' => $this->password,
                    ]
                ]
            );

            if (!in_array($response->getStatusCode(), [200, 201, 202, 204, 206, 207])) {
                throw new Exception('Cannot login to UPELA');
            }

            $token = $response->toArray()['token'] ?? null;

            if (!$token) {
                throw new \UnexpectedValueException();
            }

            $this->httpClient = HttpClient::create([
                'auth_bearer' => $token
            ]);
        } catch (HttpExceptionInterface|ExceptionInterface $exception ) {
            throw new Exception(sprintf('Cannot login to UPELA : %s', $exception->getMessage()));
        }

    }

    private function initSerializer()
    {
        $classMetadataFactory = new ClassMetadataFactory(new AttributeLoader());
        $objectNormalizer = new ObjectNormalizer($classMetadataFactory, new CamelCaseToSnakeCaseNameConverter(), null, new PhpDocExtractor());
        $arrayDeNormalizer = new ArrayDenormalizer();
        $jsonEncoder = new JsonEncoder();
        $this->serializer = new Serializer([$arrayDeNormalizer, $objectNormalizer], [$jsonEncoder]);
    }
}
